<?php

namespace app\admin\controller\functioncontrol;

use app\common\controller\Backend;
use think\Request;

/**
 * 闭馆日表-->改为日历表，记录当前月以及下个月每天开闭馆情况
 *
 * @icon fa fa-circle-o
 */
class Holidays extends Backend
{

    /**
     * Holidays模型对象
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Holidays;
    }
    // 获取当前月的开闭馆数据
    public function getlist()
    {
        $month = $this->request->get('month'); // 2025-07
        if (!$month || !preg_match('/^\d{4}-\d{2}$/', $month)) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 获取月起始、结束日期
        $start = date("$month-01");
        $end = date("Y-m-d", strtotime("first day of next month", strtotime($start)));

        $list = $this->model
            ->where('holidays_date', '>=', $start)
            ->where('holidays_date', '<', $end)
            ->select();

        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'title' => '', // 可为空，使用 eventContent 自定义展示
                'start' => substr($item['holidays_date'], 0, 10),
                'allDay' => true,
                'extendedProps' => [
                    'is_close' => $item['is_close']
                ]
            ];
        }

        return json(['code' => 1, 'data' => $data]);
    }


    public function update()
    {
        $date = $this->request->post("date");
        $is_close = $this->request->post("is_close");

        if (!$date || !in_array($is_close, ['1'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        // 日期格式校验
        if (!preg_match("/^\d{4}-\d{2}-\d{2}$/", $date)) {
            return json(['code' => 0, 'msg' => '日期格式错误']);
        }

        // 转换为 datetime 类型
        $datetime = $date . " 00:00:00";

        // 查询是否存在该日期记录
        $row = $this->model->where('holidays_date', $datetime)->find();

        if ($row) {
            // 已存在，更新 is_close 字段
            $row->is_close = $is_close;
            $row->save();
        } else {
            // 不存在，创建一条新的记录
            $week = date('N', strtotime($datetime)); // 周几，1=周一，7=周日
            $this->model->create([
                'holidays_date' => $datetime,
                'holidays_week' => $week,
                'is_close'      => $is_close
            ]);
        }

        return json(['code' => 1, 'msg' => '开馆成功']);
    }

    public function close()
    {
        $date = $this->request->post("date");
        $is_close = $this->request->post("is_close"); // 应为 '0'
        $title = $this->request->post("title");
        $content = $this->request->post("content");

        if (!$date || !in_array($is_close, ['0'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        if (!preg_match("/^\d{4}-\d{2}-\d{2}$/", $date)) {
            return json(['code' => 0, 'msg' => '日期格式错误']);
        }

        $datetime = $date . " 00:00:00";

        // 查询该日期记录
        $row = $this->model->where('holidays_date', $datetime)->find();

        if ($row) {
            // 更新闭馆状态
            $row->is_close = $is_close;
            $row->save();
        } else {
            // 不存在则插入新记录
            $week = date('N', strtotime($datetime));
            $this->model->create([
                'holidays_date' => $datetime,
                'holidays_week' => $week,
                'is_close'      => $is_close
            ]);
        }

        // 如需记录闭馆原因、公告内容等，可写入日志或扩展字段（示意）
        // Log::info("闭馆通知：$title 内容：$content 日期：$date");

        return json(['code' => 1, 'msg' => '闭馆成功']);
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
}
