
/* 导入字体图标 */
/* 字体图标样式 */
@font-face {
  font-family: 'cuIcon';
  src: url('//at.alicdn.com/t/font_0_0.ttf') format('truetype');
}
[class*="cuIcon-"] {
  font-family: cuIcon;
  font-size: inherit;
  font-style: normal;
}
.cuIcon-loading2:before {
  content: "\e7f1";
}
.cuIcon-qr_code:before {
  content: "\e61b";
}
.cuIcon-dianhua:before {
  content: "\e64d";
}
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/font_0_0.ttf') format('truetype');
}
.iconfont {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  font-family: iconfont !important;
  font-size: 16px;
  font-style: normal;
}
.icon-user:before {
  content: "\e7ae";
}
.icon-password:before {
  content: "\e8b2";
}
.icon-code:before {
  content: "\e699";
}
.icon-setting:before {
  content: "\e6cc";
}
.icon-share:before {
  content: "\e739";
}
.icon-edit:before {
  content: "\e60c";
}
.icon-version:before {
  content: "\e63f";
}
.icon-service:before {
  content: "\e6ff";
}
.icon-friendfill:before {
  content: "\e726";
}
.icon-community:before {
  content: "\e741";
}
.icon-people:before {
  content: "\e736";
}
.icon-dianzan:before {
  content: "\ec7f";
}
.icon-right:before {
  content: "\e7eb";
}
@font-face {
  font-family: 'yticon';
  src: url('data:font/truetype;charset=utf-8;base64,') format('truetype');
}
.yticon {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: yticon !important;
  font-size: 16px;
  font-style: normal;
}
.icon-yiguoqi1:before {
  content: "\e700";
}
.icon-iconfontshanchu1:before {
  content: "\e619";
}
.icon-iconfontweixin:before {
  content: "\e611";
}
.icon-alipay:before {
  content: "\e636";
}
.icon-shang:before {
  content: "\e624";
}
.icon-shouye:before {
  content: "\e626";
}
.icon-shanchu4:before {
  content: "\e622";
}
.icon-xiaoxi:before {
  content: "\e618";
}
.icon-jiantour-copy:before {
  content: "\e600";
}
.icon-fenxiang2:before {
  content: "\e61e";
}
.icon-pingjia:before {
  content: "\e67b";
}
.icon-daifukuan:before {
  content: "\e68f";
}
.icon-pinglun-copy:before {
  content: "\e612";
}
/* 全局样式重置 */
* {
  box-sizing: border-box;
}
/* 全局页面样式 */
page {
  background-color: #f1f1f1;
  color: #333;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 全局输入框样式 */
.uni-input {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, sans-serif !important;
}
/* 通用样式类 */
.round {
  border-radius: 5000rpx;
}
.radius {
  border-radius: 6rpx;
}
.radius-lg {
  border-radius: 12rpx;
}
/* Flex布局 */
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.align-start {
  align-items: flex-start;
}
.align-center {
  align-items: center;
}
.align-end {
  align-items: flex-end;
}
.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.flex-1 {
  flex: 1;
}
/* 文本样式 */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-bold {
  font-weight: bold;
}
.text-normal {
  font-weight: normal;
}
/* 文本颜色 */
.text-primary {
  color: #007aff;
}
.text-success {
  color: #4cd964;
}
.text-warning {
  color: #f0ad4e;
}
.text-danger {
  color: #dd524d;
}
.text-info {
  color: #5bc0de;
}
.text-white {
  color: #ffffff;
}
.text-black {
  color: #000000;
}
.text-gray {
  color: #999999;
}
.text-gray-light {
  color: #cccccc;
}
.text-gray-dark {
  color: #666666;
}
/* 文本大小 */
.text-xs {
  font-size: 20rpx;
}
.text-sm {
  font-size: 24rpx;
}
.text-base {
  font-size: 28rpx;
}
.text-lg {
  font-size: 32rpx;
}
.text-xl {
  font-size: 36rpx;
}
.text-xxl {
  font-size: 40rpx;
}
/* 背景颜色 */
.bg-primary {
  background-color: #007aff;
}
.bg-success {
  background-color: #4cd964;
}
.bg-warning {
  background-color: #f0ad4e;
}
.bg-danger {
  background-color: #dd524d;
}
.bg-info {
  background-color: #5bc0de;
}
.bg-white {
  background-color: #ffffff;
}
.bg-gray {
  background-color: #f8f8f8;
}
.bg-gray-light {
  background-color: #fafafa;
}
/* 边距样式 */
.m-0 { margin: 0;
}
.m-1 { margin: 10rpx;
}
.m-2 { margin: 20rpx;
}
.m-3 { margin: 30rpx;
}
.m-4 { margin: 40rpx;
}
.mt-0 { margin-top: 0;
}
.mt-1 { margin-top: 10rpx;
}
.mt-2 { margin-top: 20rpx;
}
.mt-3 { margin-top: 30rpx;
}
.mt-4 { margin-top: 40rpx;
}
.mb-0 { margin-bottom: 0;
}
.mb-1 { margin-bottom: 10rpx;
}
.mb-2 { margin-bottom: 20rpx;
}
.mb-3 { margin-bottom: 30rpx;
}
.mb-4 { margin-bottom: 40rpx;
}
.ml-0 { margin-left: 0;
}
.ml-1 { margin-left: 10rpx;
}
.ml-2 { margin-left: 20rpx;
}
.ml-3 { margin-left: 30rpx;
}
.ml-4 { margin-left: 40rpx;
}
.mr-0 { margin-right: 0;
}
.mr-1 { margin-right: 10rpx;
}
.mr-2 { margin-right: 20rpx;
}
.mr-3 { margin-right: 30rpx;
}
.mr-4 { margin-right: 40rpx;
}
.p-0 { padding: 0;
}
.p-1 { padding: 10rpx;
}
.p-2 { padding: 20rpx;
}
.p-3 { padding: 30rpx;
}
.p-4 { padding: 40rpx;
}
.pt-0 { padding-top: 0;
}
.pt-1 { padding-top: 10rpx;
}
.pt-2 { padding-top: 20rpx;
}
.pt-3 { padding-top: 30rpx;
}
.pt-4 { padding-top: 40rpx;
}
.pb-0 { padding-bottom: 0;
}
.pb-1 { padding-bottom: 10rpx;
}
.pb-2 { padding-bottom: 20rpx;
}
.pb-3 { padding-bottom: 30rpx;
}
.pb-4 { padding-bottom: 40rpx;
}
.pl-0 { padding-left: 0;
}
.pl-1 { padding-left: 10rpx;
}
.pl-2 { padding-left: 20rpx;
}
.pl-3 { padding-left: 30rpx;
}
.pl-4 { padding-left: 40rpx;
}
.pr-0 { padding-right: 0;
}
.pr-1 { padding-right: 10rpx;
}
.pr-2 { padding-right: 20rpx;
}
.pr-3 { padding-right: 30rpx;
}
.pr-4 { padding-right: 40rpx;
}
/* 按钮样式 */
.cu-btn {
  position: relative;
  border: 0rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}
.cu-btn.block {
  display: flex;
  width: 100%;
}
.cu-btn.lg {
  padding: 0 40rpx;
  height: 80rpx;
  font-size: 32rpx;
}
.cu-btn.sm {
  padding: 0 20rpx;
  height: 48rpx;
  font-size: 24rpx;
}
.cu-btn.round {
  border-radius: 5000rpx;
}
.cu-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}
/* 输入框样式 */
.input-item {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 20rpx;
  border-radius: 10rpx;
  border: 1rpx solid #e5e5e5;
  transition: border-color 0.3s ease;
}
.input-item.focus {
  border-color: #007aff;
}
.input-item .icon {
  margin-right: 20rpx;
  font-size: 32rpx;
  color: #999;
}
.input-item .input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.input-item .input::-webkit-input-placeholder {
  color: #999;
}
.input-item .input::placeholder {
  color: #999;
}
/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.card-body {
  padding: 30rpx;
}
.card-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}
/* 列表样式 */
.list-item {
  background-color: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}
.list-item:active {
  background-color: #f8f8f8;
}
.list-item:last-child {
  border-bottom: none;
}
/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #999;
}
/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;
}
.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}
.empty-text {
  font-size: 28rpx;
}
/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}