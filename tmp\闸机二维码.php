<?php

class VenueQrCodeCipher
{
    // COMM_KEY 等同于 Java 中的 byte[]{105,106,109,101,100,111,107,109,102,97,111,104,102,97,111,105}
    private static $COMM_KEY = [105,106,109,101,100,111,107,109,102,97,111,104,102,97,111,105];

    /**
     * 加密函数：传入原始字符串，返回 venue: 开头的 HEX 字符串
     */
    public static function encrypt($plaintext)
    {
        $bytes = mb_convert_encoding($plaintext, 'GBK', 'UTF-8'); // 编码兼容 GBK
        $byteArray = unpack('C*', $bytes);
        $encrypted = [];

        $keyLen = count(self::$COMM_KEY);
        foreach ($byteArray as $i => $b) {
            $j = ($i - 1) % $keyLen;
            $encrypted[] = $b ^ self::$COMM_KEY[$j];
        }

        $hex = strtoupper(implode('', array_map(function($v) {
            return str_pad(dechex($v), 2, '0', STR_PAD_LEFT);
        }, $encrypted)));

        return 'venue:' . $hex;
    }

    /**
     * 解密函数：传入 venue: 开头的密文，返回原始 JSON 字符串
     */
    public static function decrypt($cipherText)
    {
        if (strpos($cipherText, 'venue:') === 0) {
            $cipherText = substr($cipherText, 6);
        }

        $bytes = [];
        for ($i = 0; $i < strlen($cipherText); $i += 2) {
            $bytes[] = hexdec(substr($cipherText, $i, 2));
        }

        $keyLen = count(self::$COMM_KEY);
        $decrypted = '';
        foreach ($bytes as $i => $b) {
            $j = $i % $keyLen;
            $decrypted .= chr($b ^ self::$COMM_KEY[$j]);
        }

        return mb_convert_encoding($decrypted, 'UTF-8', 'GBK');
    }
}

// 示例：加密一段 JSON
$data = json_encode(
[
    "id" => 782278,
    "venueId" => 8058,
    "userId" => 317887,
    "userLinkmanId" => 583804,
    "subscribeState" => "1", //凭证状态,0:已取消,1:正常,2:已过期(该状态由系统自行判断,数据库中只有0，1)
    "type" => 0,
    "number" => 1,
    "signState" => "0",//签到状态,0:未签到(默认),1:已签到
    "isClose" => "0",
    "linkId" => 583804,
    "linkmanName" => "龙测试",
    "linkmanPhone" => "13800138000",
    "linkmanAge" => 44,
    "linkmanCertificate" => "440711198009056023",
    "linkmanCertificateType" => "0",
    "showTime" => "2025-07-02 11:31:00"
]




, JSON_UNESCAPED_UNICODE);

$encrypted = VenueQrCodeCipher::encrypt($data);
echo "加密结果：$encrypted\n";


$encrypted =  "venue:1248040146555C55545358514A43190C071F082C004D515556575B4444141C0C1B2309475E5C5A5A5E59584444141C0C1B26040B0F020A032F054D5253595C51595E4147171A091E0513060A03321B081D0F4F5F465E4941441516180343555945480310090D0E1F445B5E444412060E07391904100A495744514D4444081C2A05051E004655495D444D4D040F0F04200D4857505C5C535D524D4D040F0F040408042304090A495744A095DA84ABBB4B4548010C0A04060C0831070708044D534B5B5E5D545F5A5E5E515F58444D4D050004060805012A0A0343555C524D4D0500040608050128081415060E0F020E1D0C485747505B5B5A57505E515E515F50595F5B55565C4941440D06060D0C0E072A0F1F110D09020E07150A3C1F110A4B53485D47484D180509163B010B044D534B585D5751425B5A4B515D485755555D5F5059524612";


// 解密回原始内容
$decrypted = VenueQrCodeCipher::decrypt($encrypted);
echo "解密内容：$decrypted\n";
