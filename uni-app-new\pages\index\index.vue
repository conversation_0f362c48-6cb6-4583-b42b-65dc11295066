<template>
  <view class="content">
    <my-header
      v-if="showHeader"
      :isBack="false"
      :isShowHome="false"
      title="宝安科技馆"
      color="#fff"
      background="transparent"
    ></my-header>
    <view class="title">线上智能服务平台</view>
    <view class="btn_list">
      <button
        v-for="(item, index) in jumpList"
        :key="index"
        type="default"
        :style="{
          background: `url(${item.bg}) no-repeat center center`,
          backgroundSize: '100% 100%'
        }"
        @tap="jumpTo(item.path, item.id)"
      >
        <view class="nav_name">{{ item.name }}</view>
        <view class="icon">
          <image class="set_icon" mode="aspectFill" :src="item.icon"></image>
        </view>
      </button>
    </view>
    <uni-notice-bar
      v-if="notice"
      class="scrollText"
      :scrollable="true"
      :speed="41.8"
      :single="true"
      background-color="#ffffff"
      :text="notice"
    ></uni-notice-bar>
    <privacy-popup
      v-if="showPrivacy"
      id="privacy-popup"
      @agree="onPrivacyAgree"
      @reject="onPrivacyReject"
    ></privacy-popup>
  </view>
</template>

<script>
import { myRequest } from '@/api/api.js'
import config from '@/config.js'
import MyHeader from '@/components/my-header/my-header.vue'
import PrivacyPopup from '@/components/privacy-popup/privacy-popup.vue'

export default {
  components: {
    MyHeader,
    PrivacyPopup
  },
  data() {
    return {
      iSzgm: config.iSzgm,
      appId: "",
      initCode: "",
      jumpList: [
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/entervenue/index",
          name: "参观预约",
          id: 0
        },
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/vieworder/index",
          name: "观影预约",
          id: 1
        },
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/curriculum/index",
          name: "课程预约",
          id: 2
        },
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/user/index",
          name: "个人中心",
          id: 3
        }
      ],
      notice: "",
      path: null,
      privacyAllow: false,
      showHeader: true,
      showPrivacy: false,
      isNavigating: false // 防止重复导航
    }
  },
  onShow() {
    this.getNotice()
  },
  onLoad() {
    this.initPrivacyCheck()
  },
  onReady() {
    // 页面渲染完成后检查隐私政策状态
    this.checkPrivacyDisplay()
  },
  onUnload() {
    // 页面卸载时清理资源
    this.notice = ''
    this.path = null
  },
  onHide() {
    // 页面隐藏时清理定时器等资源
    // 如果有定时器，在这里清理
  },
  methods: {
    initPrivacyCheck() {
      // 检查隐私政策同意状态
      const privacyAgreed = uni.getStorageSync('privacy_agreed')
      if (privacyAgreed) {
        this.privacyAllow = true
        this.showPrivacy = false
      }

      // 平台兼容的隐私授权检查
      // #ifdef MP-WEIXIN
      try {
        uni.requirePrivacyAuthorize({
          success: () => {
            this.privacyAllow = true
            console.log('微信隐私授权成功')
          },
          fail: (error) => {
            console.log('微信隐私授权失败:', error)
            // 显示自定义隐私政策弹窗
            this.showPrivacy = true
          },
          complete: () => {}
        })
      } catch (error) {
        console.log('requirePrivacyAuthorize API不可用:', error)
        // 降级处理，显示自定义隐私政策弹窗
        if (!privacyAgreed) {
          this.showPrivacy = true
        }
      }
      // #endif

      // #ifndef MP-WEIXIN
      // 非微信小程序平台的处理
      if (privacyAgreed) {
        this.privacyAllow = true
        this.showPrivacy = false
      } else {
        // 显示自定义隐私政策弹窗
        this.showPrivacy = true
      }
      // #endif
    },
    async jumpTo(path, id) {
      // 防抖处理，避免重复点击
      if (this.isNavigating) {
        return
      }

      if (!this.privacyAllow) {
        uni.showToast({
          title: "请先同意隐私政策",
          icon: "none",
          duration: 3000
        })
        return
      }

      try {
        this.isNavigating = true
        this.path = path

        await uni.navigateTo({
          url: this.path
        })
      } catch (error) {
        console.error('页面跳转失败:', error)
        uni.showToast({
          title: "页面跳转失败",
          icon: "none",
          duration: 2000
        })
      } finally {
        // 延迟重置导航状态，防止快速连续点击
        setTimeout(() => {
          this.isNavigating = false
        }, 500)
      }
    },
    getNotice() {
      myRequest({
        url: "/apitp/announcement/getInfo",
        noLoadingFlag: true // 不显示加载提示，避免影响用户体验
      }).then((res) => {
        if (res.data && res.data.code === 200) {
          this.notice = res.data.msg || res.data.data
        }
      }).catch((error) => {
        console.error('获取公告信息失败:', error)
        // 静默处理错误，不影响页面正常显示
      })
    },
    onPrivacyAgree() {
      this.privacyAllow = true
      this.showPrivacy = false
      console.log('用户同意隐私政策')

      // 保存用户同意状态
      uni.setStorageSync('privacy_agreed', true)
    },
    onPrivacyReject() {
      this.privacyAllow = false
      this.showPrivacy = false
      console.log('用户拒绝隐私政策')

      // 显示提示信息
      uni.showModal({
        title: '提示',
        content: '需要同意隐私政策才能使用应用功能',
        showCancel: false,
        confirmText: '我知道了'
      })
    },
    checkPrivacyDisplay() {
      // 检查是否需要显示隐私政策弹窗
      const privacyAgreed = uni.getStorageSync('privacy_agreed')
      if (!privacyAgreed) {
        this.showPrivacy = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100vh;
  background-color: #f8f8f8;
  background: url(/static/img/home/<USER>
  background-size: contain;
  color: #fff;
  overflow: scroll;

  .title {
    margin-top: 54rpx;
    margin-bottom: 62rpx;
    font-size: 58rpx;
    font-family: 'YouSheBiaoTiHei', 'PingFang SC', sans-serif;
    text-align: center;
    font-weight: bold;
  }

  .btn_list {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-top: 158rpx;

    button {
      width: 692rpx;
      height: 192rpx;
      margin: 0 auto 76rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 0 174rpx 0 158rpx;
      font-weight: 600;
      border-radius: 20rpx;

      &::after {
        border: none;
      }

      .nav_name {
        font-size: 42rpx;
        font-family: 'PingFang SC', sans-serif;
        color: #FFFFFF;
        font-weight: 600;
      }

      .icon {
        width: 160rpx;
        height: 160rpx;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .set_icon {
          width: 115rpx;
          height: 105rpx;
        }
      }
    }
  }



  .scrollText {
    width: 727rpx;
    display: block;
    margin: 262rpx auto 138rpx auto;
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.content {
  background-attachment: scroll;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>