<?php

namespace app\admin\controller\report;

use app\common\controller\Backend;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use think\Db;

/**
 * 课程场次管理
 *
 * @icon fa fa-circle-o
 */
class Reportcourse extends Backend
{

    /**
     * Reportcourse模型对象
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\course\Session;
    }

    public function getCourseReport()
    {
        // 如果是 Selectpage 请求
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        // 获取分页参数

        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }

        // 构建基本 SQL
        $query = $this->model->alias('c')
            ->join(['holidays' => 'h'], 'DATE_FORMAT(h.holidays_date,"%y%m%d") = DATE_FORMAT(c.course_start_time,"%y%m%d")', 'LEFT')
            // ->where($where)
            ->field('
                c.id,
                h.is_close as isClose,
                c.del_flag as isDel,
                c.course_name as courseName,
                c.course_start_time as courseStartTime,
                c.course_end_time as courseEndTime,
                c.course_poll as coursePoll,
                c.inventory_votes as inventoryVotes
            ');
        if ($startDate && $endDate) {
            $query->whereRaw("DATE_FORMAT(c.course_start_time, '%y%m%d') >= DATE_FORMAT(:startDate, '%y%m%d')", ['startDate' => $startDate])
                ->whereRaw("DATE_FORMAT(c.course_end_time, '%y%m%d') <= DATE_FORMAT(:endDate, '%y%m%d')", ['endDate' => $endDate]);
        } elseif ($startDate && !$endDate) {
            $query->whereRaw("DATE_FORMAT(c.course_start_time, '%y%m%d') >= DATE_FORMAT(:now, '%y%m%d')", ['now' => date('Y-m-d')]);
        } elseif ($endDate && !$startDate) {
            $query->whereRaw("DATE_FORMAT(c.course_end_time, '%y%m%d') <= DATE_FORMAT(:now, '%y%m%d')", ['now' => date('Y-m-d')]);
        }
        // 查询列表
        $list = $query
            ->order($sort, $order)
            ->paginate($limit);
        // ->select(false);  print_r($list);die;
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 获取课程预约明细
     */
    public function show($ids = null)
    {
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }

        if (!$ids) {
            $this->error("缺少ID");
        }

        // 使用 FastAdmin 的 buildparams 自动构造分页、排序、筛选条件
        [$where_buildparams, $sort, $order, $offset, $limit] = $this->buildparams();




        // 判断是否闭馆 / 是否删除
        $isClose = $this->model
            ->alias('c')
            ->join(['holidays' => 'h'], 'DATE_FORMAT(h.holidays_date,"%y%m%d") = DATE_FORMAT(c.course_start_time,"%y%m%d")', 'LEFT')
            ->where('c.id', $ids)
            ->value('h.is_close');

        $isDel = $this->model
            ->where('id', $ids)
            ->value('del_flag');

        $isClose = strval($isClose); // 确保是字符串
        $isDel = strval($isDel);


        // 构建查询条件
        $where = [];
        $where['s.course_session_id'] = ['=', $ids];

        // 根据状态追加过滤
        if ($isDel == '2') {
            $where['s.del_flag'] = ['=', 2];
            $where['s.is_del'] = ['=', 1];
        } elseif ($isClose == '0') {
            $where['s.del_flag'] = ['=', 2];
            $where['s.is_close'] = ['=', 1];
        } elseif ($isClose == '1' && $isDel == '0') {
            $where['s.del_flag'] = ['=', 0];
        }

        $list =  Db::table('course_subscribe')->alias('s')
            ->field([
                's.id',
                'l.linkman_name',
                'l.linkman_phone',
                'l.linkman_age as linkmanAge',
                "IF(s.sign_state=0, '未签到', '已签到') as signState",
                'l.linkman_certificate as linkmanCertificate',
                'e.linkman_phone as ugentPhone'
            ])
            ->join(['user_linkman' => 'l'], 'l.id = s.user_linkman_id', 'LEFT')
            ->join(['emergency_contact' => 'e'], 'e.user_id = l.user_id', 'LEFT')
            ->where($where)
            ->where($where_buildparams)
            ->order($sort, $order)
            // ->select(false);        print_r($list);        die;
            ->paginate($limit);


        // 使用 each  脱敏处理
        $list->each(function ($row) {
            $row['linkman_name'] = desensitize_name($row['linkman_name']);
            $row['linkman_phone'] = desensitize_phone($row['linkman_phone']);
            $row['linkmanCertificate'] = desensitize_certificate($row['linkmanCertificate']);

            return $row;
        });

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    public function export()
    {
        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }

        // 获取查询结果
        $query = Db::table('course_subscribe')->alias('s')
            ->join(['course_session' => 'c'], 'c.id = s.course_session_id')
            ->join(['user_linkman' => 'l'], 'l.id = s.user_linkman_id')
            ->join(['emergency_contact' => 'ec'], 'ec.user_id = s.user_id')
            ->field([
                'c.id as courseId',
                's.user_id as userId',
                "DATE_FORMAT(c.course_start_time, '%Y-%m-%d') as courseStartTime",
                'c.course_name as courseName',
                "CONCAT(DATE_FORMAT(c.course_start_time,'%H:%i:%s'), '-', DATE_FORMAT(c.course_end_time,'%H:%i:%s')) as courseEndTime",
                'c.course_address as courseAddress',
                'c.course_age_prop as courseAgeProp',
                'c.course_poll as coursePoll',
                'l.linkman_name as linkmanName',
                'l.linkman_phone as linkmanPhone',
                'l.linkman_age as linkmanAge',
                'l.linkman_certificate as linkmanCertificate',
                'ec.linkman_name as emergencyContact',
                'ec.linkman_phone as emergencyPhone',
                "IF(s.sign_state=0,'未签到','已签到') as signState",
                's.is_close as isClose',
                's.is_del as isDel',
                'c.del_flag as delFlag'
            ]);

        // 添加时间条件
        if ($startDate) {
            $query->whereRaw("DATE_FORMAT(c.course_start_time,'%y%m%d') >= ?", [date('ymd', strtotime($startDate))]);
        }
        if ($endDate) {
            $query->whereRaw("DATE_FORMAT(c.course_end_time,'%y%m%d') <= ?", [date('ymd', strtotime($endDate))]);
        }


        $list = $query->select() ;

        // 分组处理
        $grouped = [];
        foreach ($list as $item) {
            $courseId = $item['courseId'];
            $userId = $item['userId'];
            $grouped[$courseId][$userId][] = $item;
        }

        $result = [];
        foreach ($grouped as $courseId => $users) {
            foreach ($users as $userId => $contacts) {
                $base = $contacts[0];

                // 状态过滤逻辑（和Java一致）
                $valid = false;
                if ($base['isClose'] == '1' && $base['isDel'] == '0') {
                    $valid = true;
                } elseif ($base['isDel'] == '1' && $base['isClose'] == '0') {
                    $valid = true;
                } elseif ($base['isDel'] == '0' && $base['isClose'] == '0' && $base['delFlag'] == '0') {
                    $valid = true;
                }
                if (!$valid) continue;

                $excelRow = [
                    'courseStartTime'    => $base['courseStartTime'],
                    'courseName'         => $base['courseName'],
                    'courseEndTime'      => $base['courseEndTime'],
                    'courseAddress'      => $base['courseAddress'],
                    'courseAgeProp'      => $base['courseAgeProp'],
                    'coursePoll'         => $base['coursePoll'],
                    'num'                => count($contacts),
                    'signState'          => $base['signState'],
                    'emergencyContact'   => $base['emergencyContact'],
                    'emergencyPhone'     => $base['emergencyPhone'],
                    'isClose'            => $base['isClose'] == '1' ? '正常' : '闭馆',
                    'isDel'              =>$base['isDel'] == '0' ? '正常' : '删除',
                ];
                // 最多导出5个联系人
                for ($i = 0; $i < 5; $i++) {
                    if (!isset($contacts[$i])) continue;
                    $vo = $contacts[$i];
                    $excelRow["linkmanAge" . ($i + 1)] = $vo['linkmanAge'];              
                    $excelRow["linkmanName" . ($i + 1)] = desensitize_name($vo['linkmanName']);
                    $excelRow["linkmanPhone" . ($i + 1)] = desensitize_phone($vo['linkmanPhone']);
                    $excelRow["linkmanCertificate" . ($i + 1)] = desensitize_certificate($vo['linkmanCertificate']);
                }

                $result[] = $excelRow;
            }
        }
        $columns = [
            'courseStartTime'      => '预约日期',
            'isClose'              => '场馆状态',
            'isDel'                => '课程状态',
            'courseName'           => '课程名称',
            'courseEndTime'        => '时间',
            'courseAddress'        => '地点',
            'courseAgeProp'        => '适龄',
            'coursePoll'           => '可预约量',
            'num'                  => '预约人数',
        
            'linkmanName1'         => '预约人员名称一',
            'linkmanPhone1'        => '手机号一',
            'linkmanAge1'          => '年龄一',
            'linkmanCertificate1'  => '身份证一',
        
            'linkmanName2'         => '预约人员名称二',
            'linkmanPhone2'        => '手机号二',
            'linkmanAge2'          => '年龄二',
            'linkmanCertificate2'  => '身份证二',
        
            'linkmanName3'         => '预约人员名称三',
            'linkmanPhone3'        => '手机号三',
            'linkmanAge3'          => '年龄三',
            'linkmanCertificate3'  => '身份证三',
        
            'linkmanName4'         => '预约人员名称四',
            'linkmanPhone4'        => '手机号四',
            'linkmanAge4'          => '年龄四',
            'linkmanCertificate4'  => '身份证四',
        
            'linkmanName5'         => '预约人员名称五',
            'linkmanPhone5'        => '手机号五',
            'linkmanAge5'          => '年龄五',
            'linkmanCertificate5'  => '身份证五',
        
            'emergencyContact'     => '紧急联系人',
            'emergencyPhone'       => '紧急联系方式',
            'signState'            => '是否签到',
        ];
        //  按字段顺序导出数据
        $orderedData = [];
        foreach ($result as $row) {
            $newRow = [];
            foreach ($columns as $field => $title) {
                $newRow[] = isset($row[$field]) ? $row[$field] : '';
            }
            $orderedData[] = $newRow;
        }


        // 写入 Excel（中文表头 + 正确顺序）
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray(array_values($columns), null, 'A1'); // 中文表头

        $sheet->fromArray($orderedData, null, 'A2');           // 数据行        

        //处理 手机号变成科学计数法
        $phoneIndex = array_search('courseStartTime', array_values($columns)) +2;
        $colLetter = Coordinate::stringFromColumnIndex($phoneIndex);
  
        $sheet->getStyle("{$colLetter}1:{$colLetter}1000")
        ->getNumberFormat()
        ->setFormatCode(NumberFormat::FORMAT_TEXT);


        // 输出下载
        $filename = '课程导出_' . date('YmdHis') . '.xlsx';
        ob_clean(); // 清空缓冲区
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=\"{$filename}\"");
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        $writer->save("php://output");
        exit;
    }
}
