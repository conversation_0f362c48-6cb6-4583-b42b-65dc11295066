"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../utils/request.js");
const utils_common = require("../../utils/common.js");
const _sfc_main = {
  name: "FilmDetails",
  data() {
    return {
      // 是否选择了联系人
      isChooseContact: true,
      // 预约人数选项
      checkItem: [
        { label: "1人", value: "1" },
        { label: "2人", value: "2" },
        { label: "3人", value: "3" },
        { label: "4人", value: "4" },
        { label: "5人", value: "5" }
      ],
      checkIndex: 0,
      // 联系人列表
      linkList: [],
      // 电影信息
      film: {},
      filmSessionId: null,
      // 电影类型
      filmTypeList: ["球幕电影", "4D电影"],
      // 星期列表
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      // 弹窗控制
      isShowMaskBlack: false,
      blackTips: null,
      blackTipsDate: null,
      // 倒计时
      countDown: null,
      isShowCountDown: false,
      timer: null,
      // 定位弹窗
      isShowMaskLocation: false,
      // 预约相关
      batchnumber: null,
      subscribeSuccess: false,
      reservationsSuccess: false
    };
  },
  computed: {
    // 隐藏身份证号中间部分
    hideCardNum() {
      return (index) => {
        if (!this.linkList[index] || !this.linkList[index].linkmanCertificate) {
          return "";
        }
        const num = this.linkList[index].linkmanCertificate;
        return num.replace(num.substring(4, 15), "*******");
      };
    }
  },
  onLoad(option) {
    this.filmSessionId = option.filmSessionId;
  },
  onShow() {
    this.getFilm();
    if (common_vendor.index.getStorageSync("gyyy_link")) {
      this.isChooseContact = true;
      this.linkList = JSON.parse(common_vendor.index.getStorageSync("gyyy_link"));
      this.checkIndex = this.linkList.length - 1;
    } else {
      this.isChooseContact = false;
    }
  },
  onHide() {
    common_vendor.index.removeStorageSync("gyyy_link");
    clearInterval(this.timer);
  },
  onUnload() {
    clearInterval(this.timer);
  },
  methods: {
    // 选择预约人数
    checkNum(num) {
      this.checkIndex = parseInt(num) - 1;
      common_vendor.index.navigateTo({
        url: `/pages_app/contacts/index?num=${num}&type=gyyy_link`
      });
    },
    // 获取电影信息
    async getFilm() {
      try {
        const res = await this.$myRequest({
          url: "/web/fileSession/personalCenterFilmByFilmSessionId",
          method: "get",
          data: {
            filmSessionId: this.filmSessionId
          }
        });
        if (res.code === 200) {
          this.film = {
            ...res.data.data,
            filmStartTime: utils_common.formatTime(res.data.data.filmStartTime.replace(/-/g, "/"), false),
            filmEndTime: utils_common.formatTime(res.data.data.filmEndTime.replace(/-/g, "/"), false)
          };
          this.getCountDown();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/vieworder/filmdes.vue:282", "获取电影信息失败:", error);
        common_vendor.index.showToast({
          title: "获取电影信息失败",
          icon: "error"
        });
      }
    },
    // 获取倒计时
    getCountDown() {
      clearInterval(this.timer);
      const signEndTime = (/* @__PURE__ */ new Date(
        this.film.filmArrangedDate.replace(/-/g, "/") + " " + this.film.filmStartTime
      )).getTime();
      const filmArrangeDateTime = new Date(this.film.filmArrangedDate.replace(/-/g, "/")).getTime();
      let nowTime = (/* @__PURE__ */ new Date()).getTime();
      const time = signEndTime - nowTime + filmArrangeDateTime;
      this.isShowCountDown = time > filmArrangeDateTime && time < 18e5 + filmArrangeDateTime;
      this.timer = setInterval(() => {
        nowTime = (/* @__PURE__ */ new Date()).getTime();
        if (signEndTime <= nowTime) {
          clearInterval(this.timer);
          common_vendor.index.showModal({
            title: "提示",
            content: "影片预约时间已过，返回上一页",
            showCancel: false,
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.navigateBack();
              }
            }
          });
          return;
        }
        this.isShowCountDown = time > filmArrangeDateTime && time < 18e5 + filmArrangeDateTime;
        const remainTime = signEndTime - nowTime;
        this.countDown = this.formatCountDown(remainTime);
      }, 1e3);
    },
    // 格式化倒计时显示
    formatCountDown(time) {
      if (time <= 0)
        return "00:00";
      const minutes = Math.floor(time / 6e4);
      const seconds = Math.floor(time % 6e4 / 1e3);
      return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    },
    // 提交预约订单
    async submitOrder() {
      if (!this.linkList || this.linkList.length === 0) {
        common_vendor.index.showToast({
          title: "请选择联系人",
          icon: "error"
        });
        return;
      }
      if (this.film.inventoryVotes <= 0) {
        common_vendor.index.showToast({
          title: "该场次已满",
          icon: "error"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "提交中..."
        });
        const res = await this.$myRequest({
          url: "/web/fileSession/filmSubscribe",
          method: "post",
          data: {
            filmSessionId: this.filmSessionId,
            linkmanIds: this.linkList.map((item) => item.id).join(","),
            subscribeNum: this.linkList.length
          }
        });
        if (res.code === 200) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "预约成功"
          });
          setTimeout(() => {
            common_vendor.index.redirectTo({
              url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${res.data.data.batchNumber}&filmSessionId=${this.filmSessionId}`
            });
          }, 1500);
        } else {
          throw new Error(res.msg || "预约失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages_app/vieworder/filmdes.vue:389", "提交预约失败:", error);
        if (error.message && error.message.includes("黑名单")) {
          this.showBlackMask(error.message);
        } else {
          common_vendor.index.showToast({
            title: error.message || "预约失败",
            icon: "error"
          });
        }
      }
    },
    // 显示黑名单弹窗
    showBlackMask(message) {
      this.blackTips = message;
      this.isShowMaskBlack = true;
    },
    // 关闭黑名单弹窗
    closeBlackMask() {
      this.isShowMaskBlack = false;
      common_vendor.index.navigateBack();
    },
    // 关闭定位弹窗
    closeLocationMask() {
      this.isShowMaskLocation = false;
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "观影登记",
      isBack: true,
      isShowHome: true,
      background: "transparent",
      color: "#ffffff"
    }),
    b: $data.film.filmCover,
    c: common_vendor.t($data.film.filmName),
    d: common_vendor.t($data.filmTypeList[$data.film.filmType - 1]),
    e: common_vendor.t($data.film.filmStartTime),
    f: common_vendor.t($data.film.filmEndTime),
    g: common_vendor.t($data.film.filmArrangedDate),
    h: common_vendor.t($data.weekList[new Date($data.film.filmArrangedDate).getDay()]),
    i: common_vendor.t($data.countDown),
    j: common_vendor.n($data.isShowCountDown ? "" : "hidden"),
    k: common_vendor.t($data.film.inventoryVotes),
    l: common_vendor.n($data.isShowCountDown ? "green" : ""),
    m: common_vendor.n($data.film.inventoryVotes == 0 ? "gray" : ""),
    n: common_vendor.t($data.film.fileIntroduce),
    o: common_vendor.f(20, (item, index, i0) => {
      return {
        a: index
      };
    }),
    p: common_vendor.f($data.checkItem, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: common_vendor.n($data.checkIndex === index ? "isCheck" : ""),
        d: common_vendor.o(($event) => $options.checkNum(item.value), index)
      };
    }),
    q: common_vendor.f(20, (item, index, i0) => {
      return {
        a: index
      };
    }),
    r: common_vendor.f($data.linkList, (link, index, i0) => {
      return {
        a: common_vendor.t(link.linkmanName),
        b: common_vendor.t($options.hideCardNum(index)),
        c: common_vendor.t(link.linkmanPhone),
        d: common_vendor.t(link.linkmanAge),
        e: link.id
      };
    }),
    s: common_vendor.n($data.isChooseContact ? "isChooseContact" : ""),
    t: common_vendor.o((...args) => $options.submitOrder && $options.submitOrder(...args)),
    v: $data.isChooseContact ? "block" : "none",
    w: common_vendor.t($data.blackTips),
    x: $data.blackTipsDate
  }, $data.blackTipsDate ? {
    y: common_vendor.t($data.blackTipsDate)
  } : {}, {
    z: common_vendor.o((...args) => $options.closeBlackMask && $options.closeBlackMask(...args)),
    A: $data.isShowMaskBlack,
    B: common_vendor.o((...args) => $options.closeLocationMask && $options.closeLocationMask(...args)),
    C: $data.isShowMaskLocation
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3a54848b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/vieworder/filmdes.js.map
