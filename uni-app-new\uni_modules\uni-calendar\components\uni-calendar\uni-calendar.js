var t = require("./util.js"),
  e = require("../../../../common/vendor.js"),
  n = require("./i18n/index.js");
require("./calendar.js");
var a = e.initVueI18n(n.messages).t,
  i = {
    components: {
      calendarItem: function() {
        return "./uni-calendar-item.js"
      }
    },
    emits: ["close", "confirm", "change", "monthSwitch"],
    props: {
      date: {
        type: String,
        default: ""
      },
      selected: {
        type: Array,
        default: function() {
          return []
        }
      },
      lunar: {
        type: Boolean,
        default: !1
      },
      startDate: {
        type: String,
        default: ""
      },
      endDate: {
        type: String,
        default: ""
      },
      range: {
        type: Boolean,
        default: !1
      },
      insert: {
        type: Boolean,
        default: !0
      },
      showMonth: {
        type: Boolean,
        default: !0
      },
      clearDate: {
        type: Boolean,
        default: !0
      }
    },
    data: function() {
      return {
        show: !1,
        weeks: [],
        calendar: {},
        nowDate: "",
        aniMaskShow: !1
      }
    },
    computed: {
      okText: function() {
        return a("uni-calender.ok")
      },
      cancelText: function() {
        return a("uni-calender.cancel")
      },
      todayText: function() {
        return a("uni-calender.today")
      },
      monText: function() {
        return a("uni-calender.MON")
      },
      TUEText: function() {
        return a("uni-calender.TUE")
      },
      WEDText: function() {
        return a("uni-calender.WED")
      },
      THUText: function() {
        return a("uni-calender.THU")
      },
      FRIText: function() {
        return a("uni-calender.FRI")
      },
      SATText: function() {
        return a("uni-calender.SAT")
      },
      SUNText: function() {
        return a("uni-calender.SUN")
      }
    },
    watch: {
      date: function(t) {
        this.init(t)
      },
      startDate: function(t) {
        this.cale.resetSatrtDate(t), this.cale.setDate(this.nowDate.fullDate), this.weeks = this.cale.weeks
      },
      endDate: function(t) {
        this.cale.resetEndDate(t), this.cale.setDate(this.nowDate.fullDate), this.weeks = this.cale.weeks
      },
      selected: function(t) {
        this.cale.setSelectInfo(this.nowDate.fullDate, t), this.weeks = this.cale.weeks
      }
    },
    created: function() {
      this.cale = new t.Calendar({
        selected: this.selected,
        startDate: this.startDate,
        endDate: this.endDate,
        range: this.range
      }), this.init(this.date)
    },
    methods: {
      clean: function() {},
      bindDateChange: function(t) {
        var e = t.detail.value + "-1";
        console.log(this.cale.getDate(e)), this.init(e)
      },
      init: function(t) {
        this.cale.setDate(t), this.weeks = this.cale.weeks, this.nowDate = this.calendar = this.cale.getInfo(t)
      },
      open: function() {
        var t = this;
        this.clearDate && !this.insert && (this.cale.cleanMultipleStatus(), this.init(this.date)), this.show = !0, this.$nextTick((function() {
          setTimeout((function() {
            t.aniMaskShow = !0
          }), 50)
        }))
      },
      close: function() {
        var t = this;
        this.aniMaskShow = !1, this.$nextTick((function() {
          setTimeout((function() {
            t.show = !1, t.$emit("close")
          }), 300)
        }))
      },
      confirm: function() {
        this.setEmit("confirm"), this.close()
      },
      change: function() {
        this.insert && this.setEmit("change")
      },
      monthSwitch: function() {
        var t = this.nowDate,
          e = t.year,
          n = t.month;
        this.$emit("monthSwitch", {
          year: e,
          month: Number(n)
        })
      },
      setEmit: function(t) {
        var e = this.calendar,
          n = e.year,
          a = e.month,
          i = e.date,
          s = e.fullDate,
          o = e.lunar,
          c = e.extraInfo;
        this.$emit(t, {
          range: this.cale.multipleStatus,
          year: n,
          month: a,
          date: i,
          fulldate: s,
          lunar: o,
          extraInfo: c || {}
        })
      },
      choiceDate: function(t) {
        t.disable || (this.calendar = t, this.cale.setMultiple(this.calendar.fullDate), this.weeks = this.cale.weeks, this.change())
      },
      backtoday: function() {
        console.log(this.cale.getDate(new Date).fullDate);
        var t = this.cale.getDate(new Date).fullDate;
        this.init(t), this.change()
      },
      pre: function() {
        var t = this.cale.getDate(this.nowDate.fullDate, -1, "month").fullDate;
        this.setDate(t), this.monthSwitch()
      },
      next: function() {
        var t = this.cale.getDate(this.nowDate.fullDate, 1, "month").fullDate;
        this.setDate(t), this.monthSwitch()
      },
      setDate: function(t) {
        this.cale.setDate(t), this.weeks = this.cale.weeks, this.nowDate = this.cale.getInfo(t)
      }
    }
  };
Array || e.resolveComponent("calendar-item")();
var s = e._export_sfc(i, [
  ["render", function(t, n, a, i, s, o) {
    return e.e({
      a: !a.insert && s.show
    }, !a.insert && s.show ? {
      b: s.aniMaskShow ? 1 : "",
      c: e.o((function() {
        return o.clean && o.clean.apply(o, arguments)
      }))
    } : {}, {
      d: a.insert || s.show
    }, a.insert || s.show ? e.e({
      e: !a.insert
    }, a.insert ? {} : {
      f: e.t(o.cancelText),
      g: e.o((function() {
        return o.close && o.close.apply(o, arguments)
      })),
      h: e.t(o.okText),
      i: e.o((function() {
        return o.confirm && o.confirm.apply(o, arguments)
      }))
    }, {
      j: e.o((function() {
        return o.pre && o.pre.apply(o, arguments)
      })),
      k: e.t((s.nowDate.year || "") + " 年 " + (s.nowDate.month || "") + " 月 "),
      l: a.date,
      m: e.o((function() {
        return o.bindDateChange && o.bindDateChange.apply(o, arguments)
      })),
      n: e.o((function() {
        return o.next && o.next.apply(o, arguments)
      })),
      o: a.showMonth
    }, a.showMonth ? {
      p: e.t(s.nowDate.month)
    } : {}, {
      q: e.t(o.SUNText),
      r: e.t(o.monText),
      s: e.t(o.TUEText),
      t: e.t(o.WEDText),
      v: e.t(o.THUText),
      w: e.t(o.FRIText),
      x: e.t(o.SATText),
      y: e.f(s.weeks, (function(t, n, i) {
        return {
          a: e.f(t, (function(t, n, c) {
            return {
              a: e.o(o.choiceDate, n),
              b: "c3423b62-0-" + i + "-" + c,
              c: e.p({
                weeks: t,
                calendar: s.calendar,
                selected: a.selected,
                lunar: a.lunar
              }),
              d: n
            }
          })),
          b: n
        }
      })),
      z: a.insert ? "" : 1,
      A: s.aniMaskShow ? 1 : ""
    }) : {})
  }],
  ["__scopeId", "data-v-c3423b62"]
]);
wx.createComponent(s);