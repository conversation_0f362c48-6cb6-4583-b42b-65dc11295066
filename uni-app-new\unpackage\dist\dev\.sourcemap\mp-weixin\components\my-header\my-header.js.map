{"version": 3, "file": "my-header.js", "sources": ["../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovbXlfcHJvamVjdC9rZWppZ3Vhbi91bmktYXBwLW5ldy9jb21wb25lbnRzL215LWhlYWRlci9teS1oZWFkZXIudnVl"], "sourcesContent": ["import Component from 'D:/my_project/kejiguan/uni-app-new/components/my-header/my-header.vue'\nwx.createComponent(Component)"], "names": ["Component"], "mappings": ";;AACA,GAAG,gBAAgBA,SAAS,OAAA;"}