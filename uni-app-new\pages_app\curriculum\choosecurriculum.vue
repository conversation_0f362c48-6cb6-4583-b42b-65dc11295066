<template>
  <view class="choose-curriculum">
    <!-- 自定义头部 -->
    <my-header 
      title="课程详情" 
      :isBack="true" 
      :isShowHome="true"
      background="transparent"
      color="#ffffff"
    />
    
    <view class="curriculum-container">
      <!-- 课程封面 -->
      <view class="curriculum-header">
        <image 
          class="curriculum-bg" 
          mode="aspectFill" 
          src="/static/img/curriculum/curriculum_bg.png"
        />
        <view class="curriculum-info">
          <image 
            v-if="courseInfo.courseCover" 
            class="course-cover" 
            mode="aspectFill" 
            :src="getImages(courseInfo.courseCover)"
          />
          <view class="course-details">
            <text class="course-name">{{ courseInfo.courseName || '课程加载中...' }}</text>
            <view class="course-meta">
              <text class="meta-item">适龄：{{ courseInfo.courseAgeProp || '--' }}</text>
              <text class="meta-item">时间：{{ courseInfo.courseStartTime || '--' }} - {{ courseInfo.courseEndTime || '--' }}</text>
              <text class="meta-item">地点：{{ courseInfo.courseAddress || '--' }}</text>
              <text class="meta-item">剩余名额：{{ courseInfo.inventoryVotes || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 课程详情 -->
      <view class="curriculum-content">
        <view class="content-section">
          <view class="section-title">
            <image class="title-icon" src="/static/img/curriculum/curriculum_title_inner.png" mode="aspectFit" />
            <text class="title-text">课程介绍</text>
          </view>
          <view class="section-content">
            <rich-text :nodes="courseInfo.courseIntroduce || '暂无课程介绍'"></rich-text>
          </view>
        </view>
        
        <view class="content-section">
          <view class="section-title">
            <image class="title-icon" src="/static/img/curriculum/curriculum_title_inner.png" mode="aspectFit" />
            <text class="title-text">预约须知</text>
          </view>
          <view class="section-content notice-content">
            <view class="notice-item">
              <text class="notice-title">一、报名方式</text>
              <view class="notice-list">
                <text class="notice-text">1、本次公益培训课程仅接受微信报名，名额有限，先到先得，额满为止</text>
                <text class="notice-text">2、课程咨询热线：0755-27880235</text>
                <text class="notice-text">3、公益课开始上课后自动关闭报名入口</text>
                <text class="notice-text">4、学员可通过个人中心-课程预约-查看凭证，管理查询个人预约信息</text>
              </view>
            </view>
            <view class="notice-item">
              <text class="notice-title">二、温馨提示</text>
              <view class="notice-list">
                <text class="notice-text">1、学员必须以本人真实信息报名，如现场确认时发现报名信息不符合则取消资格</text>
                <text class="notice-text">2、课程期间，请学员严格遵守上课时间，为保证教学质量迟到超过10分钟则不能进入教室学习</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系人选择 -->
      <view class="contacts-section">
        <view class="section-title">
          <image class="title-icon" src="/static/img/curriculum/curriculum_title_inner.png" mode="aspectFit" />
          <text class="title-text">选择联系人</text>
        </view>
        
        <view v-if="contactsList.length > 0" class="contacts-list">
          <view 
            v-for="(contact, index) in contactsList" 
            :key="contact.linkId"
            class="contact-item"
            @tap="selectContact(contact, index)"
          >
            <view class="contact-info">
              <text class="contact-name">{{ contact.linkmanName }}</text>
              <text class="contact-id">{{ hideIdCard(contact.linkmanCertificate) }}</text>
            </view>
            <view class="contact-select">
              <view :class="['select-circle', { 'selected': selectedContacts.includes(index) }]"></view>
            </view>
          </view>
        </view>
        
        <view v-else class="no-contacts">
          <text class="no-contacts-text">暂无联系人，请先添加联系人</text>
          <button class="add-contact-btn" @tap="goToAddContact">添加联系人</button>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <button 
        class="submit-btn" 
        :disabled="!canSubmit" 
        @tap="submitReservation"
      >
        {{ courseInfo.inventoryVotes > 0 ? '立即预约' : '名额已满' }}
      </button>
    </view>
  </view>
</template>

<script>
import { getImages, myRequest } from '../../api/api.js'
import config from '../../config.js'
import Utils from '../../utils/index.js'

export default {
  name: 'ChooseCurriculum',
  components: {
    MyHeader: () => import('../../components/my-header/my-header.vue')
  },
  data() {
    return {
      courseId: null,
      courseInfo: {},
      contactsList: [],
      selectedContacts: [],
      isSubmitting: false
    }
  },
  computed: {
    canSubmit() {
      return this.courseInfo.inventoryVotes > 0 && 
             this.selectedContacts.length > 0 && 
             !this.isSubmitting
    }
  },
  onLoad(options) {
    if (options.id) {
      this.courseId = options.id
      this.getCourseInfo()
      this.getContactsList()
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'error'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  methods: {
    getImages,
    
    // 获取课程信息
    async getCourseInfo() {
      try {
        const response = await myRequest({
          url: '/web/session/getSessionById',
          method: 'get',
          data: {
            id: this.courseId
          }
        })
        
        if (response.data.code === 200) {
          const data = response.data.data
          data.courseEndTime = Utils.changeTime(data.courseEndTime)
          data.courseStartTime = Utils.changeTime(data.courseStartTime)
          this.courseInfo = data
        } else {
          throw new Error(response.data.msg || '获取课程信息失败')
        }
      } catch (error) {
        console.error('获取课程信息失败:', error)
        uni.showToast({
          title: error.message || '获取课程信息失败',
          icon: 'error'
        })
      }
    },
    
    // 获取联系人列表
    async getContactsList() {
      try {
        const response = await myRequest({
          url: '/web/linkman/list',
          method: 'get'
        })
        
        if (response.data.code === 200) {
          this.contactsList = response.data.rows || []
        } else {
          throw new Error(response.data.msg || '获取联系人列表失败')
        }
      } catch (error) {
        console.error('获取联系人列表失败:', error)
        uni.showToast({
          title: error.message || '获取联系人列表失败',
          icon: 'error'
        })
      }
    },
    
    // 选择联系人
    selectContact(contact, index) {
      const position = this.selectedContacts.indexOf(index)
      if (position > -1) {
        this.selectedContacts.splice(position, 1)
      } else {
        this.selectedContacts.push(index)
      }
    },
    
    // 前往添加联系人
    goToAddContact() {
      uni.navigateTo({
        url: '/pages_app/contacts/addcontact'
      })
    },
    
    // 提交预约
    async submitReservation() {
      if (!this.canSubmit) return
      
      this.isSubmitting = true
      
      try {
        uni.showLoading({
          title: '提交中...',
          mask: true
        })
        
        const selectedPeople = this.selectedContacts.map(index => {
          return this.contactsList[index].linkId
        })
        
        const response = await myRequest({
          url: '/web/session/subscribe',
          method: 'post',
          data: {
            sessionId: this.courseId,
            linkmanIds: selectedPeople.join(',')
          }
        })
        
        uni.hideLoading()
        
        if (response.data.code === 200) {
          const batchNumber = response.data.data
          uni.redirectTo({
            url: `/pages_app/schemesuccess/curriculumsuccess?batchNumber=${batchNumber}`
          })
        } else {
          throw new Error(response.data.msg || '预约失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('预约失败:', error)
        uni.showToast({
          title: error.message || '预约失败',
          icon: 'error'
        })
      } finally {
        this.isSubmitting = false
      }
    },
    
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      
      return idCard.replace(idCard.substring(4, 15), '*******')
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-curriculum {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; // 为底部操作栏留出空间
}

.curriculum-container {
  padding: 20rpx 30rpx;
}

.curriculum-header {
  position: relative;
  height: 360rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  
  .curriculum-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  
  .curriculum-info {
    position: relative;
    z-index: 2;
    display: flex;
    padding: 40rpx;
    height: 100%;
    box-sizing: border-box;
    align-items: center;
    
    .course-cover {
      width: 180rpx;
      height: 180rpx;
      border-radius: 12rpx;
      margin-right: 30rpx;
      background-color: rgba(255, 255, 255, 0.2);
    }
    
    .course-details {
      flex: 1;
      color: #ffffff;
      
      .course-name {
        font-size: 36rpx;
        font-weight: 600;
        margin-bottom: 20rpx;
        display: block;
      }
      
      .course-meta {
        .meta-item {
          font-size: 26rpx;
          display: block;
          margin-bottom: 10rpx;
          opacity: 0.9;
        }
      }
    }
  }
}

.curriculum-content {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .content-section {
    margin-bottom: 40rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      .title-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 10rpx;
      }
      
      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #333333;
      }
    }
    
    .section-content {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
    }
    
    .notice-content {
      .notice-item {
        margin-bottom: 30rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .notice-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333333;
          margin-bottom: 10rpx;
          display: block;
        }
        
        .notice-list {
          .notice-text {
            font-size: 26rpx;
            color: #666666;
            line-height: 1.6;
            display: block;
            margin-bottom: 8rpx;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.contacts-section {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .title-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 10rpx;
    }
    
    .title-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }
  }
  
  .contacts-list {
    .contact-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .contact-info {
        .contact-name {
          font-size: 28rpx;
          color: #333333;
          font-weight: 500;
          margin-bottom: 6rpx;
          display: block;
        }
        
        .contact-id {
          font-size: 24rpx;
          color: #999999;
          display: block;
        }
      }
      
      .contact-select {
        .select-circle {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          border: 2rpx solid #dddddd;
          position: relative;
          
          &.selected {
            border-color: #1976d2;
            background-color: #1976d2;
            
            &:after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 20rpx;
              height: 12rpx;
              border-left: 4rpx solid #ffffff;
              border-bottom: 4rpx solid #ffffff;
              transform: translate(-50%, -60%) rotate(-45deg);
            }
          }
        }
      }
    }
  }
  
  .no-contacts {
    padding: 40rpx 0;
    text-align: center;
    
    .no-contacts-text {
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 20rpx;
      display: block;
    }
    
    .add-contact-btn {
      display: inline-block;
      background-color: #1976d2;
      color: #ffffff;
      font-size: 28rpx;
      padding: 16rpx 40rpx;
      border-radius: 40rpx;
      border: none;
      
      &::after {
        border: none;
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  
  .submit-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;
    border: none;
    
    &::after {
      border: none;
    }
    
    &:disabled {
      opacity: 0.6;
    }
  }
}
</style>