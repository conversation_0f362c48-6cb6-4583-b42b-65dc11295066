{"version": 3, "file": "errorCode.js", "sources": ["utils/errorCode.js"], "sourcesContent": ["/**\r\n * 错误码配置\r\n * 适配uni-app环境\r\n */\r\n\r\nexport const errorCode = {\r\n  200: \"操作成功\",\r\n  400: \"请求参数错误\",\r\n  401: \"认证失败，无法访问系统资源\",\r\n  403: \"当前操作没有权限\",\r\n  404: \"访问资源不存在\",\r\n  500: \"服务器内部错误\",\r\n  502: \"网关错误\",\r\n  503: \"服务不可用\",\r\n  504: \"网关超时\",\r\n  default: \"系统未知错误，请反馈给管理员\"\r\n}\r\n\r\n/**\r\n * 获取错误信息\r\n * @param {number} code 错误码\r\n * @returns {string} 错误信息\r\n */\r\nexport function getErrorMessage(code) {\r\n  return errorCode[code] || errorCode.default\r\n}\r\n\r\n/**\r\n * 检查是否为成功状态码\r\n * @param {number} code 状态码\r\n * @returns {boolean} 是否成功\r\n */\r\nexport function isSuccess(code) {\r\n  return code === 200\r\n}\r\n\r\n/**\r\n * 检查是否为客户端错误\r\n * @param {number} code 状态码\r\n * @returns {boolean} 是否为客户端错误\r\n */\r\nexport function isClientError(code) {\r\n  return code >= 400 && code < 500\r\n}\r\n\r\n/**\r\n * 检查是否为服务器错误\r\n * @param {number} code 状态码\r\n * @returns {boolean} 是否为服务器错误\r\n */\r\nexport function isServerError(code) {\r\n  return code >= 500 && code < 600\r\n}\r\n\r\n// 默认导出\r\nexport default {\r\n  errorCode,\r\n  getErrorMessage,\r\n  isSuccess,\r\n  isClientError,\r\n  isServerError\r\n}"], "names": [], "mappings": ";AAKY,MAAC,YAAY;AAAA,EACvB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,SAAS;AACX;;"}