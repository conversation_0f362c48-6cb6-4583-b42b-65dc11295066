<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:76:"D:\my_project\kejiguan\php\public/../application/admin\view\venue\index.html";i:1753348167;s:69:"D:\my_project\kejiguan\php\application\admin\view\layout\default.html";i:1753348167;s:66:"D:\my_project\kejiguan\php\application\admin\view\common\meta.html";i:1753348167;s:68:"D:\my_project\kejiguan\php\application\admin\view\common\script.html";i:1753348167;}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo (isset($title) && ($title !== '')?$title:''); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="robots" content="noindex, nofollow">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<!-- Loading Bootstrap -->
<link href="/assets/css/backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">

<?php if(\think\Config::get('fastadmin.adminskin')): ?>
<link href="/assets/css/skins/<?php echo htmlentities(\think\Config::get('fastadmin.adminskin') ?? ''); ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">
<?php endif; ?>

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config:  <?php echo json_encode($config ?? ''); ?>
    };
</script>

    </head>

    <body class="inside-header inside-aside <?php echo defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''; ?>">
        <div id="main" role="main">
            <div class="tab-content tab-addtabs">
                <div id="content">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <section class="content-header hide">
                                <h1>
                                    <?php echo __('Dashboard'); ?>
                                    <small><?php echo __('Control panel'); ?></small>
                                </h1>
                            </section>
                            <?php if(!IS_DIALOG && !\think\Config::get('fastadmin.multiplenav') && \think\Config::get('fastadmin.breadcrumb')): ?>
                            <!-- RIBBON -->
                            <div id="ribbon">
                                <ol class="breadcrumb pull-left">
                                    <?php if($auth->check('dashboard')): ?>
                                    <li><a href="dashboard" class="addtabsit"><i class="fa fa-dashboard"></i> <?php echo __('Dashboard'); ?></a></li>
                                    <?php endif; ?>
                                </ol>
                                <ol class="breadcrumb pull-right">
                                    <?php foreach($breadcrumb as $vo): ?>
                                    <li><a href="javascript:;" data-url="<?php echo htmlentities($vo['url'] ?? ''); ?>"><?php echo htmlentities($vo['title'] ?? ''); ?></a></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <!-- END RIBBON -->
                            <?php endif; ?>
                            <div class="content">
                                <div class="panel panel-default panel-intro">
  <?php echo build_heading(); ?>
  <!-- 本地 FullCalendar CSS -->
  <link rel="stylesheet" href="/assets/libs/fullcalendar/main.min.css">

  <!-- 本地 FullCalendar JS -->
  <script src="/assets/libs/fullcalendar/main.min.js"></script>
  <script src="/assets/libs/fullcalendar/locales/zh-cn.min.js"></script>
  <style>
    /* 去掉事件默认背景色 */
    .fc-daygrid-event {
      background-color: transparent !important;
      padding: 0 !important;
      border: none !important;
    }

    /* 星期栏：周一、周二、周三…… */
    .fc .fc-col-header-cell-cushion {
      color: #000 !important;
    }

    /* 日期数字：1日、2日、3日…… */
    .fc .fc-daygrid-day-number {
      color: #000 !important;
      /* 日期数字居中 */
      display: block;
      text-align: center;
      width: 100%;
    }

    /* 开馆日 / 闭馆日 标签居中 */
    .fc .fc-event {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }

    .fc-daygrid-event {
      pointer-events: none !important;
      /* 让事件不捕获点击 */
    }

    .calendar-open {
      color: #1b8c00;
      /*    background-color: #e8f5e9;*/
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
    }

    .calendar-close {
      color: #c62828;
      /*    background-color: #ffebee;*/
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
    }

    /* 1. 去掉今日的背景颜色 */
    .fc-day-today {
      background-color: transparent !important;
    }

    /* 2. 把“今日”的日期文字改为蓝色 */
    .fc-day-today .fc-daygrid-day-number {
      color: #1890ff !important;
      font-weight: bold;
    }

    /*当前选中日期的样式*/
    .fc-selected {
      border: 2px solid #007bff !important;
      background-color: rgba(0, 123, 255, 0.1);
      border-radius: 4px;
    }

    /*带打钩图标）*/
    .fc-daygrid-day.fc-selected::after {
      content: "✓";
      color: #007bff;
      font-weight: bold;
      font-size: 16px;
      position: absolute;
      top: 2px;
      right: 4px;
      pointer-events: none;
    }

    .fc-daygrid-day.fc-selected {
      position: relative;
    }

    /*    让“2025年11月”看起来更小一些、更加紧凑。*/
    .fc-toolbar-title {
      font-size: 18px !important;
      /* 原本可能是 24px 或更大 */
      font-weight: 600;
      /* 可选：更紧凑一点 */
      line-height: 1.2;
    }
  </style>


  <div class="panel-body">
    <div id="myTabContent" class="tab-content">
      <div class="tab-pane fade active in" id="one">
        <div class="widget-body no-padding">

          <div class="row">
            <!-- 左侧日历 -->
            <div class="col-md-6">
              <div id="holiday-calendar"></div>
            </div>

            <!-- 右侧场次表 -->
            <div class="col-md-6">
              <div class="box box-info">
                <div class="box-header with-border">
                  <h3 class="box-title">预约场次</h3>
                </div>
                <div class="box-body table-responsive no-padding">
                  <table class="table table-bordered table-hover">
                    <thead>
                      <tr class="info">
                        <th style="width: 100px;">场次</th>
                        <th style="width: 120px;">时间</th>
                        <th style="width: 150px;">预约量</th>
                        <th style="width: 200px;">操作</th>
                      </tr>
                    </thead>
                    <tbody id="timeslot-table-body">
                      <tr>
                        <td>上午场</td>
                        <td></td>
                        <td>
                          <span class="text-primary"></span><br>
                          <span class="text-success"></span>
                        </td>
                        <td>
                          <button class="btn btn-xs btn-danger"><i class="fa fa-ban"></i> 停止预约</button>
                          <button class="btn btn-xs btn-primary btn-edit-time">
                            <i class="fa fa-clock-o"></i> 修改时间
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>下午场</td>
                        <td></td>
                        <td>
                          <span class="text-primary"></span><br>
                          <span class="text-success"></span>
                        </td>
                        <td>
                          <button class="btn btn-xs btn-danger"><i class="fa fa-ban"></i> 停止预约</button>
                          <button class="btn btn-xs btn-primary btn-edit-time">
                            <i class="fa fa-clock-o"></i> 修改时间
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>

                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

    </div>
  </div>
</div>
<!-- 添加弹窗 HTML 模板 -->
<div id="editTimeModal" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <strong>修改场次时间</strong>
      </div>
      <div class="modal-body">
        <form>
          <div class="form-group">
            <label>开始时间</label>
            <input type="time" class="form-control" id="edit-start-time">
          </div>
          <div class="form-group">
            <label>结束时间</label>
            <input type="time" class="form-control" id="edit-end-time">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="btn-save-time" class="btn btn-primary btn-sm">保存</button>
        <button class="btn btn-default btn-sm" data-dismiss="modal">取消</button>
      </div>
    </div>
  </div>
</div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/assets/js/require.min.js" data-main="/assets/js/require-backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.js?v=<?php echo htmlentities($site['version'] ?? ''); ?>"></script>

    </body>
</html>
