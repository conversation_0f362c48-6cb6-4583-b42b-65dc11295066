<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\library\Sms;
use fast\Random;
use think\Config;
use think\Validate;
use EasyWeChat\Factory;
use app\common\model\WebUser;
use think\Cache;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['wxlogin'];
    protected $noNeedRight = '*';

    protected $miniPrograms = [
        'wx7fdbf0566b7e1707' => [  // 小程序ID => 配置
            'app_id'  => 'wx7fdbf0566b7e1707',
            'secret'  => '857ba77e8d439c966dd0806f24edf3bd',
        ],
    ];

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

    }

    /**
     * 微信登录
     * \baoanquest      
     */
    public function wxlogin()
    {


        $code = $this->request->get('code');
        $appid = $this->request->get('appid');
        if (empty($code)) {
            $this->error('请稍后再试', ['msg' => '缺少 code 参数'], 500);
        }

        if (!isset($this->miniPrograms[$appid])) {
            $this->error('请稍后再试', ['msg' => '无效的 AppID'], 500);
        }
        //本地无法获取微信数据，返回测试数据
        $data = ["msg"=>"登录成功","code"=>200,"data"=>["user"=>["userId"=>null,"userName"=>"1bqq1hhbbj3erjr870f8i","nickName"=>"1bqq1hhbbj3erjr870f8i","userType"=>null,"avatar"=>"","email"=>null,"phonenumber"=>"","name"=>null,"openid"=>null,"sessionKey"=>null,"sex"=>"0","delFlag"=>"0"],"token"=>"eyJhbGciOiJIUzUxMiJ9.eyJ3eCI6IjVkMDczZDE5LTcwODMtNDY3NS05MDEzLWYzOTdiYTdhMDdmMCJ9.giD257mwyKZkcYuGqxAb-_zN2YJaWBJp24_YaNNbtwnWMH3YfTJS9SLiW18kWlis7uAus97yk158DzfloVoVzQ"]];
        $this->success('登录成功', ['token' => "eyJhbGciOiJIUzUxMiJ9.eyJ3eCI6IjVkMDczZDE5LTcwODMtNDY3NS05MDEzLWYzOTdiYTdhMDdmMCJ9.giD257mwyKZkcYuGqxAb-_zN2YJaWBJp24_YaNNbtwnWMH3YfTJS9SLiW18kWlis7uAus97yk158DzfloVoVzQ", 'user' =>  $data ]);
        $config = $this->miniPrograms[$appid];
        $app = Factory::miniProgram($config);

        $session = $app->auth->session($code);

        if (isset($session['errcode'])) {
            $this->error('请稍后再试', ['msg' => $session['errmsg']], 500);
        }

        $openid = $session['openid'];
        $sessionKey = $session['session_key'];


        $userId = null;
        $userData = [];
        $user = WebUser::where('openid', $openid)->find();

        if ($user) {
            //用户存在， 更新信息
            $userUpdateDate = [];
            $flagUpdate = false;
            if ($user['del_flag'] == '2') {
                $userUpdateDate['del_flag'] = '0';
                $flagUpdate = true;
            }
            if (empty($user['user_name'])) {
                $userUpdateDate['user_name'] = $openid;
                $flagUpdate = true;
            }
            if (empty($user['nick_name'])) {
                $userUpdateDate['nick_name'] = $openid;
                $flagUpdate = true;
            }
            if ($flagUpdate) {
                WebUser::where('openid', $openid)->update($userUpdateDate);
            }


            $userData = [
                'user_id'     => $user['user_id'],
                'user_name'     => $user['user_name'],
                'nick_name'     => $user['nick_name'],
                'openid'     => $user['openid'],
            ];
        } else {
            $user = WebUser::create([
                'openid'      => $openid,
                'session_key' => $sessionKey,
                'user_type'   => '01',
                'user_name'   => $openid,
                'nick_name'   => $openid,
                'del_flag'    => '0',
                'status'    => '0',
            ]);
            if ($user) {
                $userData = [
                    'user_id'     => $user->user_id,
                    'user_name'     => $user->user_name,
                    'nick_name'     => $user->nick_name,
                    'openid'     => $user->openid,
                ];
            }
        }

        // Step 3: 写 token + 缓存
        $token  = uniqid('wx_', true);
        Cache::store('redis')->set('wx:token:' . $token, $userData, 7200);
        // unset($userData['user_id']);
        $this->success('登录成功', ['token' => $token, 'user' => $userData]);
    }


    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $bio = $this->request->post('bio');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        $user->bio = $bio;
        $user->avatar = $avatar;
        $user->save();
        $this->success();
    }



    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->post("platform");
        $code = $this->request->post("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }
}
