var e = require("../../../../@babel/runtime/helpers/typeof"),
  t = require("../../../../@babel/runtime/helpers/regeneratorRuntime"),
  a = require("../../../../@babel/runtime/helpers/asyncToGenerator"),
  r = require("../../../../common/vendor.js"),
  n = {
    props: {
      localdata: {
        type: [Array, Object],
        default: function() {
          return []
        }
      },
      spaceInfo: {
        type: Object,
        default: function() {
          return {}
        }
      },
      collection: {
        type: String,
        default: ""
      },
      action: {
        type: String,
        default: ""
      },
      field: {
        type: String,
        default: ""
      },
      orderby: {
        type: String,
        default: ""
      },
      where: {
        type: [String, Object],
        default: ""
      },
      pageData: {
        type: String,
        default: "add"
      },
      pageCurrent: {
        type: Number,
        default: 1
      },
      pageSize: {
        type: Number,
        default: 500
      },
      getcount: {
        type: [<PERSON>olean, String],
        default: !1
      },
      getone: {
        type: [<PERSON>olean, String],
        default: !1
      },
      gettree: {
        type: [<PERSON>olean, String],
        default: !1
      },
      manual: {
        type: Boolean,
        default: !1
      },
      value: {
        type: [Array, String, Number],
        default: function() {
          return []
        }
      },
      modelValue: {
        type: [Array, String, Number],
        default: function() {
          return []
        }
      },
      preload: {
        type: Boolean,
        default: !1
      },
      stepSearh: {
        type: Boolean,
        default: !0
      },
      selfField: {
        type: String,
        default: ""
      },
      parentField: {
        type: String,
        default: ""
      },
      multiple: {
        type: Boolean,
        default: !1
      },
      map: {
        type: Object,
        default: function() {
          return {
            text: "text",
            value: "value"
          }
        }
      }
    },
    data: function() {
      return {
        loading: !1,
        errorMessage: "",
        loadMore: {
          contentdown: "",
          contentrefresh: "",
          contentnomore: ""
        },
        dataList: [],
        selected: [],
        selectedIndex: 0,
        page: {
          current: this.pageCurrent,
          size: this.pageSize,
          count: 0
        }
      }
    },
    computed: {
      isLocalData: function() {
        return !this.collection.length
      },
      isCloudData: function() {
        return this.collection.length > 0
      },
      isCloudDataList: function() {
        return this.isCloudData && !this.parentField && !this.selfField
      },
      isCloudDataTree: function() {
        return this.isCloudData && this.parentField && this.selfField
      },
      dataValue: function() {
        return (Array.isArray(this.modelValue) ? this.modelValue.length > 0 : null !== this.modelValue || void 0 !== this.modelValue) ? this.modelValue : this.value
      },
      hasValue: function() {
        return "number" == typeof this.dataValue || null != this.dataValue && this.dataValue.length > 0
      }
    },
    created: function() {
      var e = this;
      this.$watch((function() {
        var t = [];
        return ["pageCurrent", "pageSize", "spaceInfo", "value", "modelValue", "localdata", "collection", "action", "field", "orderby", "where", "getont", "getcount", "gettree"].forEach((function(a) {
          t.push(e[a])
        })), t
      }), (function(t, a) {
        for (var r = 2; r < t.length && t[r] == a[r]; r++);
        t[0] != a[0] && (e.page.current = e.pageCurrent), e.page.size = e.pageSize, e.onPropsChange()
      })), this._treeData = []
    },
    methods: {
      onPropsChange: function() {
        this._treeData = []
      },
      loadData: function() {
        var e = this;
        return a(t().mark((function a() {
          return t().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                e.isLocalData ? e.loadLocalData() : e.isCloudDataList ? e.loadCloudDataList() : e.isCloudDataTree && e.loadCloudDataTree();
              case 1:
              case "end":
                return t.stop()
            }
          }), a)
        })))()
      },
      loadLocalData: function() {
        var r = this;
        return a(t().mark((function a() {
          var n;
          return t().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                r._treeData = [], r._extractTree(r.localdata, r._treeData), void 0 !== (n = r.dataValue) && (Array.isArray(n) && (n = n[n.length - 1], "object" == e(n) && n[r.map.value] && (n = n[r.map.value])), r.selected = r._findNodePath(n, r.localdata));
              case 3:
              case "end":
                return t.stop()
            }
          }), a)
        })))()
      },
      loadCloudDataList: function() {
        var e = this;
        return a(t().mark((function a() {
          var r;
          return t().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                if (e.loading) {
                  t.next = 15;
                  break
                }
                return e.loading = !0, t.prev = 2, t.next = 5, e.getCommand();
              case 5:
                r = t.sent.result.data, e._treeData = r, e._updateBindData(), e._updateSelected(), e.onDataChange(), t.next = 12;
                break;
              case 9:
                t.prev = 9, t.t0 = t.catch(2), e.errorMessage = t.t0;
              case 12:
                return t.prev = 12, e.loading = !1, t.finish(12);
              case 15:
              case "end":
                return t.stop()
            }
          }), a, null, [
            [2, 9, 12, 15]
          ])
        })))()
      },
      loadCloudDataTree: function() {
        var e = this;
        return a(t().mark((function a() {
          var r, n;
          return t().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                if (e.loading) {
                  t.next = 17;
                  break
                }
                return e.loading = !0, t.prev = 2, r = {
                  field: e._cloudDataPostField(),
                  where: e._cloudDataTreeWhere()
                }, e.gettree && (r.startwith = "".concat(e.selfField, "=='").concat(e.dataValue, "'")), t.next = 7, e.getCommand(r);
              case 7:
                n = t.sent.result.data, e._treeData = n, e._updateBindData(), e._updateSelected(), e.onDataChange(), t.next = 14;
                break;
              case 11:
                t.prev = 11, t.t0 = t.catch(2), e.errorMessage = t.t0;
              case 14:
                return t.prev = 14, e.loading = !1, t.finish(14);
              case 17:
              case "end":
                return t.stop()
            }
          }), a, null, [
            [2, 11, 14, 17]
          ])
        })))()
      },
      loadCloudDataNode: function(e) {
        var r = this;
        return a(t().mark((function a() {
          var n, i;
          return t().wrap((function(t) {
            for (;;) switch (t.prev = t.next) {
              case 0:
                if (r.loading) {
                  t.next = 16;
                  break
                }
                return r.loading = !0, t.prev = 2, n = {
                  field: r._cloudDataPostField(),
                  where: r._cloudDataNodeWhere()
                }, t.next = 6, r.getCommand(n);
              case 6:
                i = t.sent.result.data, e(i), t.next = 13;
                break;
              case 10:
                t.prev = 10, t.t0 = t.catch(2), r.errorMessage = t.t0;
              case 13:
                return t.prev = 13, r.loading = !1, t.finish(13);
              case 16:
              case "end":
                return t.stop()
            }
          }), a, null, [
            [2, 10, 13, 16]
          ])
        })))()
      },
      getCloudDataValue: function() {
        return this.isCloudDataList ? this.getCloudDataListValue() : this.isCloudDataTree ? this.getCloudDataTreeValue() : void 0
      },
      getCloudDataListValue: function() {
        var e = this,
          t = [],
          a = this._getForeignKeyByField();
        return a && t.push("".concat(a, " == '").concat(this.dataValue, "'")), t = t.join(" || "), this.where && (t = "(".concat(this.where, ") && (").concat(t, ")")), this.getCommand({
          field: this._cloudDataPostField(),
          where: t
        }).then((function(t) {
          return e.selected = t.result.data, t.result.data
        }))
      },
      getCloudDataTreeValue: function() {
        var e = this;
        return this.getCommand({
          field: this._cloudDataPostField(),
          getTreePath: {
            startWith: "".concat(this.selfField, "=='").concat(this.dataValue, "'")
          }
        }).then((function(t) {
          var a = [];
          return e._extractTreePath(t.result.data, a), e.selected = a, a
        }))
      },
      getCommand: function() {
        var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
          t = r.Ds.database(this.spaceInfo),
          a = e.action || this.action;
        a && (t = t.action(a));
        var n = e.collection || this.collection;
        t = t.collection(n);
        var i = e.where || this.where;
        !i || !Object.keys(i).length || (t = t.where(i));
        var l = e.field || this.field;
        l && (t = t.field(l));
        var o = e.orderby || this.orderby;
        o && (t = t.orderBy(o));
        var u = void 0 !== e.pageCurrent ? e.pageCurrent : this.page.current,
          s = void 0 !== e.pageSize ? e.pageSize : this.page.size,
          c = void 0 !== e.getcount ? e.getcount : this.getcount,
          d = void 0 !== e.gettree ? e.gettree : this.gettree,
          h = {
            getCount: c,
            getTree: d
          };
        return e.getTreePath && (h.getTreePath = e.getTreePath), t = t.skip(s * (u - 1)).limit(s).get(h)
      },
      _cloudDataPostField: function() {
        var e = [this.field];
        return this.parentField && e.push("".concat(this.parentField, " as parent_value")), e.join(",")
      },
      _cloudDataTreeWhere: function() {
        var e = [],
          t = this.selected,
          a = this.parentField;
        if (a && e.push("".concat(a, " == null || ").concat(a, ' == ""')), t.length)
          for (var r = 0; r < t.length - 1; r++) e.push("".concat(a, " == '").concat(t[r].value, "'"));
        var n = [];
        return this.where && n.push("(".concat(this.where, ")")), e.length && n.push("(".concat(e.join(" || "), ")")), n.join(" && ")
      },
      _cloudDataNodeWhere: function() {
        var e = [],
          t = this.selected;
        return t.length && e.push("".concat(this.parentField, " == '").concat(t[t.length - 1].value, "'")), e = e.join(" || "), this.where ? "(".concat(this.where, ") && (").concat(e, ")") : e
      },
      _getWhereByForeignKey: function() {
        var e = [],
          t = this._getForeignKeyByField();
        return t && e.push("".concat(t, " == '").concat(this.dataValue, "'")), this.where ? "(".concat(this.where, ") && (").concat(e.join(" || "), ")") : e.join(" || ")
      },
      _getForeignKeyByField: function() {
        for (var e = this.field.split(","), t = null, a = 0; a < e.length; a++) {
          var r = e[a].split("as");
          if (!(r.length < 2) && "value" === r[1].trim()) {
            t = r[0].trim();
            break
          }
        }
        return t
      },
      _updateBindData: function(e) {
        var t = this._filterData(this._treeData, this.selected),
          a = t.dataList,
          r = t.hasNodes,
          n = !1 === this._stepSearh && !r;
        return e && (e.isleaf = n), this.dataList = a, this.selectedIndex = a.length - 1, !n && this.selected.length < a.length && this.selected.push({
          value: null,
          text: "请选择"
        }), {
          isleaf: n,
          hasNodes: r
        }
      },
      _updateSelected: function() {
        for (var e = this.dataList, t = this.selected, a = this.map.text, r = this.map.value, n = 0; n < t.length; n++)
          for (var i = t[n].value, l = e[n], o = 0; o < l.length; o++) {
            var u = l[o];
            if (u[r] === i) {
              t[n].text = u[a];
              break
            }
          }
      },
      _filterData: function(e, t) {
        var a = [],
          r = !0;
        a.push(e.filter((function(e) {
          return null === e.parent_value || void 0 === e.parent_value || "" === e.parent_value
        })));
        for (var n = function() {
            var n = t[i].value,
              l = e.filter((function(e) {
                return e.parent_value === n
              }));
            l.length ? a.push(l) : r = !1
          }, i = 0; i < t.length; i++) n();
        return {
          dataList: a,
          hasNodes: r
        }
      },
      _extractTree: function(e, t, a) {
        for (var r = this.map.value, n = 0; n < e.length; n++) {
          var i = e[n],
            l = {};
          for (var o in i) "children" !== o && (l[o] = i[o]);
          null != a && "" !== a && (l.parent_value = a), t.push(l);
          var u = i.children;
          u && this._extractTree(u, t, i[r])
        }
      },
      _extractTreePath: function(e, t) {
        for (var a = 0; a < e.length; a++) {
          var r = e[a],
            n = {};
          for (var i in r) "children" !== i && (n[i] = r[i]);
          t.push(n);
          var l = r.children;
          l && this._extractTreePath(l, t)
        }
      },
      _findNodePath: function(e, t) {
        for (var a = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : [], r = this.map.text, n = this.map.value, i = 0; i < t.length; i++) {
          var l = t[i],
            o = l.children,
            u = l[r],
            s = l[n];
          if (a.push({
              value: s,
              text: u
            }), s === e) return a;
          if (o) {
            var c = this._findNodePath(e, o, a);
            if (c.length) return c
          }
          a.pop()
        }
        return []
      }
    }
  };
exports.dataPicker = n;