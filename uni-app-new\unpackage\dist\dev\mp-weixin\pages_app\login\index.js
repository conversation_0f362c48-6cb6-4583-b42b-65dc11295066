"use strict";
const common_vendor = require("../../common/vendor.js");
const api_login = require("../../api/login.js");
const utils_jsencrypt = require("../../utils/jsencrypt.js");
const config = require("../../config.js");
const common_assets = require("../../common/assets.js");
const MyHeader = () => "../../components/my-header/my-header.js";
const _sfc_main = {
  name: "Login",
  components: {
    MyHeader
  },
  data() {
    return {
      // 表单数据
      loginForm: {
        username: "",
        password: "",
        code: "",
        uuid: ""
      },
      // 验证码相关
      codeUrl: "",
      captchaEnabled: true,
      // 控制状态
      register: true,
      isLoading: false,
      showWechatLogin: true,
      // 表单验证错误
      errors: {
        username: "",
        password: "",
        code: ""
      }
    };
  },
  created() {
    this.getCode();
  },
  methods: {
    // 获取验证码
    getCode() {
      api_login.getCodeImg().then((res) => {
        if (res.code === 200) {
          this.captchaEnabled = res.captchaEnabled !== false;
          if (this.captchaEnabled) {
            this.codeUrl = "data:image/gif;base64," + res.img;
            this.loginForm.uuid = res.uuid;
          }
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages_app/login/index.vue:161", "获取验证码失败:", err);
      });
    },
    // 表单验证
    validateUsername() {
      if (!this.loginForm.username.trim()) {
        this.errors.username = "请输入账号";
        return false;
      }
      this.errors.username = "";
      return true;
    },
    validatePassword() {
      if (!this.loginForm.password.trim()) {
        this.errors.password = "请输入密码";
        return false;
      }
      this.errors.password = "";
      return true;
    },
    validateCode() {
      if (this.captchaEnabled && !this.loginForm.code.trim()) {
        this.errors.code = "请输入验证码";
        return false;
      }
      this.errors.code = "";
      return true;
    },
    // 表单整体验证
    validateForm() {
      const usernameValid = this.validateUsername();
      const passwordValid = this.validatePassword();
      const codeValid = this.validateCode();
      return usernameValid && passwordValid && codeValid;
    },
    // 处理登录
    async handleLogin() {
      if (!this.validateForm()) {
        return;
      }
      this.isLoading = true;
      try {
        common_vendor.index.showLoading({
          title: "正在登录中"
        });
        await this.pwdLogin();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/login/index.vue:218", "登录失败:", error);
        common_vendor.index.showToast({
          title: "登录失败",
          icon: "error"
        });
        if (this.captchaEnabled) {
          this.getCode();
        }
      } finally {
        this.isLoading = false;
        common_vendor.index.hideLoading();
      }
    },
    // 密码登录
    async pwdLogin() {
      const encryptedPassword = utils_jsencrypt.encrypt(this.loginForm.password);
      const res = await api_login.login(
        this.loginForm.username,
        encryptedPassword,
        this.loginForm.code,
        this.loginForm.uuid
      );
      if (res.code === 200) {
        common_vendor.index.showToast({
          title: "登录成功"
        });
        common_vendor.index.setStorageSync("token", res.token);
        try {
          const userInfo = await this.getUserInfo();
          common_vendor.index.setStorageSync("userInfo", userInfo);
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages_app/login/index.vue:263", "获取用户信息失败:", error);
          common_vendor.index.reLaunch({
            url: "/pages/index/index"
          });
        }
      } else {
        throw new Error(res.msg || "登录失败");
      }
    },
    // 获取用户信息
    async getUserInfo() {
      return {
        avatar: null,
        nickName: null,
        sex: null,
        phonenumber: null
      };
    },
    // 微信登录
    wechatLogin(e) {
      if (!e.detail.userInfo) {
        common_vendor.index.showToast({
          title: "您拒绝了授权",
          icon: "error"
        });
        return;
      }
      this.isLoading = true;
      common_vendor.index.login({
        provider: "weixin",
        success: (res) => {
          const params = {
            data: {
              code: res.code,
              appid: config.config.appId
            },
            url: "/api/user/wxlogin",
            method: "get"
          };
          this.$myRequest(params).then((result) => {
            common_vendor.index.showToast({
              title: "登录成功"
            });
            const user = result.data.data.user;
            common_vendor.index.setStorageSync("token", result.data.data.token);
            common_vendor.index.setStorageSync("userInfo", user);
            common_vendor.index.reLaunch({
              url: "/pages/index/index"
            });
          }).catch((err) => {
            common_vendor.index.__f__("error", "at pages_app/login/index.vue:324", "微信登录失败:", err);
            common_vendor.index.showToast({
              title: "微信登录失败",
              icon: "error"
            });
          }).finally(() => {
            this.isLoading = false;
          });
        },
        fail: () => {
          this.isLoading = false;
          common_vendor.index.showToast({
            title: "微信登录失败",
            icon: "error"
          });
        }
      });
    },
    // 跳转注册页面
    handleUserRegister() {
      common_vendor.index.navigateTo({
        url: "/pages_app/register/index"
      });
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "用户登录",
      isBack: true,
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      color: "#ffffff"
    }),
    b: common_assets._imports_0,
    c: common_vendor.o((...args) => $options.validateUsername && $options.validateUsername(...args)),
    d: $data.loginForm.username,
    e: common_vendor.o(($event) => $data.loginForm.username = $event.detail.value),
    f: $data.errors.username
  }, $data.errors.username ? {
    g: common_vendor.t($data.errors.username)
  } : {}, {
    h: common_vendor.o((...args) => $options.validatePassword && $options.validatePassword(...args)),
    i: $data.loginForm.password,
    j: common_vendor.o(($event) => $data.loginForm.password = $event.detail.value),
    k: $data.errors.password
  }, $data.errors.password ? {
    l: common_vendor.t($data.errors.password)
  } : {}, {
    m: $data.captchaEnabled
  }, $data.captchaEnabled ? common_vendor.e({
    n: common_vendor.o((...args) => $options.validateCode && $options.validateCode(...args)),
    o: $data.loginForm.code,
    p: common_vendor.o(($event) => $data.loginForm.code = $event.detail.value),
    q: $data.codeUrl
  }, $data.codeUrl ? {
    r: $data.codeUrl,
    s: common_vendor.o((...args) => $options.getCode && $options.getCode(...args))
  } : {
    t: common_vendor.o((...args) => $options.getCode && $options.getCode(...args))
  }, {
    v: $data.errors.code
  }, $data.errors.code ? {
    w: common_vendor.t($data.errors.code)
  } : {}) : {}, {
    x: common_vendor.t($data.isLoading ? "登录中..." : "登录"),
    y: common_vendor.o((...args) => $options.handleLogin && $options.handleLogin(...args)),
    z: $data.isLoading,
    A: common_vendor.o((...args) => $options.handleUserRegister && $options.handleUserRegister(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f8d81299"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/login/index.js.map
