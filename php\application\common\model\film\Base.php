<?php

namespace app\common\model\film;

use think\Model;


class Base extends Model
{





    // 表名
    protected $table = 'film';

    // 定义时间戳字段名
    protected $autoWriteTimestamp = 'datetime'; // 改为 'datetime'
    protected $dateFormat = 'Y-m-d H:i:s'; // 定义输出格式
    protected $createTime = 'create_time'; // 创建时间字段名
    protected $updateTime = 'update_time'; // 更新时间字段名

    // 定义下拉用的数据映射
    protected $append = ['film_type_text'];
    public function getFilmTypeTextAttr($value, $data)
    {
        $list = [1 => '球幕电影', 2 => '4D电影'];
        return $list[$data['film_type']] ?? '-';
    }
    public function getFilmTypeList()
    {
        return [
            1 => '球幕电影',
            2 => '4D电影'
        ];
    }
}
