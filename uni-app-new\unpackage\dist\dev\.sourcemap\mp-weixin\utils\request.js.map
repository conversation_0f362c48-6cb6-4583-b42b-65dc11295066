{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["import config from '../config.js'\r\nimport { getToken } from './auth.js'\r\nimport { errorCode } from './errorCode.js'\r\nimport { tansParams, toast } from './common.js'\r\nimport { generateAesKey, encryptWithAes, encryptBase64 } from './crypto.js'\r\nimport { encrypt } from './jsencrypt.js'\r\n\r\nconst baseUrl = config.baseUrl\r\nconst iSzgm = config.iSzgm\r\nconst appId = config.appId\r\n\r\n// 请求封装 - 适配uni-app并添加平台兼容性处理\r\nexport function request(options) {\r\n  return new Promise((resolve, reject) => {\r\n    // 设置请求头\r\n    options.header = options.header || {}\r\n\r\n    const isToken = !(options.headers || {}).isToken === false\r\n    const isEncrypt = (options.headers || {}).isEncrypt === true\r\n\r\n    // 添加token\r\n    if (!options.url.includes(`/wx/user/${appId}/login`) && getToken() && isToken) {\r\n      options.header.Authorization = 'wx ' + getToken()\r\n    }\r\n\r\n    // 处理参数\r\n    if (options.params) {\r\n      let paramStr = options.url + '?' + tansParams(options.params)\r\n      options.url = paramStr.slice(0, -1)\r\n    }\r\n\r\n    // 处理加密\r\n    if (isEncrypt && (options.method === 'POST' || options.method === 'PUT')) {\r\n      const aesKey = generateAesKey()\r\n      options.header['encrypt-key'] = encrypt(encryptBase64(aesKey))\r\n      options.data = typeof options.data === 'object'\r\n        ? encryptWithAes(JSON.stringify(options.data), aesKey)\r\n        : encryptWithAes(options.data, aesKey)\r\n    }\r\n\r\n    // 平台兼容性处理\r\n    const requestConfig = {\r\n      method: options.method || 'GET',\r\n      timeout: options.timeout || 10000,\r\n      url: baseUrl + options.url,\r\n      data: options.data,\r\n      header: options.header,\r\n      dataType: 'json'\r\n    }\r\n\r\n    // #ifdef H5\r\n    // H5平台可能需要处理跨域\r\n    requestConfig.header['Content-Type'] = 'application/json'\r\n    // #endif\r\n\r\n    // #ifdef MP-WEIXIN\r\n    // 微信小程序特殊处理\r\n    if (options.enableHttp2) {\r\n      requestConfig.enableHttp2 = true\r\n    }\r\n    // #endif\r\n\r\n    // #ifdef APP-PLUS\r\n    // App平台可能需要特殊配置\r\n    requestConfig.sslVerify = false // 根据需要配置SSL验证\r\n    // #endif\r\n\r\n    // 发起请求 - 关键适配点：使用uni.request\r\n    uni.request({\r\n      ...requestConfig,\r\n      success: (response) => {\r\n        const data = response.data\r\n        const code = data.code || 200\r\n        const msg = errorCode[code] || data.msg || errorCode.default\r\n\r\n        if (code === 200) {\r\n          resolve(data)\r\n        } else if (code === 401) {\r\n          // 401错误特殊处理 - 可能需要重新登录\r\n          handleAuthError()\r\n          reject('无效的会话，或者会话已过期，请重新登录。')\r\n        } else if (code === 500) {\r\n          toast(msg)\r\n          reject('500')\r\n        } else {\r\n          toast(msg)\r\n          reject(code)\r\n        }\r\n      },\r\n      fail: (error) => {\r\n        let errMsg = error.errMsg || '请求失败'\r\n        \r\n        // 平台特定错误处理\r\n        // #ifdef MP-WEIXIN\r\n        if (errMsg === 'request:fail url not in domain list') {\r\n          errMsg = '请求域名不在白名单中，请联系管理员'\r\n        }\r\n        // #endif\r\n        \r\n        // #ifdef H5\r\n        if (errMsg.includes('CORS')) {\r\n          errMsg = '跨域请求被阻止，请联系管理员'\r\n        }\r\n        // #endif\r\n        \r\n        // 通用错误处理\r\n        if (errMsg === 'Network Error' || errMsg === 'request:fail') {\r\n          errMsg = '后端接口连接异常'\r\n        } else if (errMsg.includes('timeout')) {\r\n          errMsg = '系统接口请求超时'\r\n        } else if (errMsg.includes('Request failed with status code')) {\r\n          errMsg = '系统接口' + errMsg.substr(errMsg.length - 3) + '异常'\r\n        }\r\n        \r\n        toast(errMsg)\r\n        reject(error)\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\n// 处理认证错误\r\nfunction handleAuthError() {\r\n  // 清除token\r\n  import('./auth.js').then(auth => {\r\n    auth.removeToken()\r\n  })\r\n  \r\n  // 跳转到登录页面\r\n  // #ifdef MP-WEIXIN\r\n  uni.reLaunch({\r\n    url: '/pages_app/login/index'\r\n  })\r\n  // #endif\r\n  \r\n  // #ifdef H5\r\n  uni.reLaunch({\r\n    url: '/pages_app/login/index'\r\n  })\r\n  // #endif\r\n  \r\n  // #ifdef APP-PLUS\r\n  uni.reLaunch({\r\n    url: '/pages_app/login/index'\r\n  })\r\n  // #endif\r\n}\r\n\r\n// 封装常用请求方法\r\nexport function get(url, params, options = {}) {\r\n  return request({\r\n    url,\r\n    method: 'GET',\r\n    params,\r\n    ...options\r\n  })\r\n}\r\n\r\nexport function post(url, data, options = {}) {\r\n  return request({\r\n    url,\r\n    method: 'POST',\r\n    data,\r\n    ...options\r\n  })\r\n}\r\n\r\nexport function put(url, data, options = {}) {\r\n  return request({\r\n    url,\r\n    method: 'PUT',\r\n    data,\r\n    ...options\r\n  })\r\n}\r\n\r\nexport function del(url, params, options = {}) {\r\n  return request({\r\n    url,\r\n    method: 'DELETE',\r\n    params,\r\n    ...options\r\n  })\r\n}\r\n\r\nexport { baseUrl, iSzgm }"], "names": ["config", "getToken", "tansParams", "generateAesKey", "encrypt", "encryptBase64", "encryptWithAes", "uni", "errorCode", "toast"], "mappings": ";;;;;;;;AAOK,MAAC,UAAUA,OAAM,OAAC;AACTA,OAAAA,OAAO;AACrB,MAAM,QAAQA,OAAM,OAAC;AAGd,SAAS,QAAQ,SAAS;AAC/B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,YAAQ,SAAS,QAAQ,UAAU,CAAE;AAErC,UAAM,UAAU,EAAE,QAAQ,WAAW,CAAA,GAAI,YAAY;AACrD,UAAM,aAAa,QAAQ,WAAW,CAAE,GAAE,cAAc;AAGxD,QAAI,CAAC,QAAQ,IAAI,SAAS,YAAY,KAAK,QAAQ,KAAKC,WAAQ,SAAA,KAAM,SAAS;AAC7E,cAAQ,OAAO,gBAAgB,QAAQA,WAAAA,SAAU;AAAA,IAClD;AAGD,QAAI,QAAQ,QAAQ;AAClB,UAAI,WAAW,QAAQ,MAAM,MAAMC,aAAU,WAAC,QAAQ,MAAM;AAC5D,cAAQ,MAAM,SAAS,MAAM,GAAG,EAAE;AAAA,IACnC;AAGD,QAAI,cAAc,QAAQ,WAAW,UAAU,QAAQ,WAAW,QAAQ;AACxE,YAAM,SAASC,aAAAA,eAAgB;AAC/B,cAAQ,OAAO,aAAa,IAAIC,gBAAAA,QAAQC,aAAAA,cAAc,MAAM,CAAC;AAC7D,cAAQ,OAAO,OAAO,QAAQ,SAAS,WACnCC,aAAc,eAAC,KAAK,UAAU,QAAQ,IAAI,GAAG,MAAM,IACnDA,4BAAe,QAAQ,MAAM,MAAM;AAAA,IACxC;AAGD,UAAM,gBAAgB;AAAA,MACpB,QAAQ,QAAQ,UAAU;AAAA,MAC1B,SAAS,QAAQ,WAAW;AAAA,MAC5B,KAAK,UAAU,QAAQ;AAAA,MACvB,MAAM,QAAQ;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB,UAAU;AAAA,IACX;AASD,QAAI,QAAQ,aAAa;AACvB,oBAAc,cAAc;AAAA,IAC7B;AASDC,kBAAAA,MAAI,QAAQ;AAAA,MACV,GAAG;AAAA,MACH,SAAS,CAAC,aAAa;AACrB,cAAM,OAAO,SAAS;AACtB,cAAM,OAAO,KAAK,QAAQ;AAC1B,cAAM,MAAMC,gBAAAA,UAAU,IAAI,KAAK,KAAK,OAAOA,gBAAAA,UAAU;AAErD,YAAI,SAAS,KAAK;AAChB,kBAAQ,IAAI;AAAA,QACtB,WAAmB,SAAS,KAAK;AAEvB,0BAAiB;AACjB,iBAAO,sBAAsB;AAAA,QACvC,WAAmB,SAAS,KAAK;AACvBC,uBAAAA,MAAM,GAAG;AACT,iBAAO,KAAK;AAAA,QACtB,OAAe;AACLA,uBAAAA,MAAM,GAAG;AACT,iBAAO,IAAI;AAAA,QACZ;AAAA,MACF;AAAA,MACD,MAAM,CAAC,UAAU;AACf,YAAI,SAAS,MAAM,UAAU;AAI7B,YAAI,WAAW,uCAAuC;AACpD,mBAAS;AAAA,QACV;AAUD,YAAI,WAAW,mBAAmB,WAAW,gBAAgB;AAC3D,mBAAS;AAAA,QACV,WAAU,OAAO,SAAS,SAAS,GAAG;AACrC,mBAAS;AAAA,QACV,WAAU,OAAO,SAAS,iCAAiC,GAAG;AAC7D,mBAAS,SAAS,OAAO,OAAO,OAAO,SAAS,CAAC,IAAI;AAAA,QACtD;AAEDA,qBAAAA,MAAM,MAAM;AACZ,eAAO,KAAK;AAAA,MACb;AAAA,IACP,CAAK;AAAA,EACL,CAAG;AACH;AAGA,SAAS,kBAAkB;AAEzB,EAAO,YAAa,KAAK,UAAQ;AAC/B,SAAK,YAAa;AAAA,EACtB,CAAG;AAIDF,gBAAAA,MAAI,SAAS;AAAA,IACX,KAAK;AAAA,EACT,CAAG;AAcH;;;"}