/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-f8d81299 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}
.login-content.data-v-f8d81299 {
  padding: 40rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}
.logo-section.data-v-f8d81299 {
  text-align: center;
  margin-bottom: 60rpx;
  margin-top: 80rpx;
  padding-top: 60rpx;
}
.logo-section .logo.data-v-f8d81299 {
  width: 140rpx;
  height: 140rpx;
  border-radius: 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}
.logo-section .app-name.data-v-f8d81299 {
  display: block;
  font-size: 52rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
}
.logo-section .subtitle.data-v-f8d81299 {
  display: block;
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}
.form-section.data-v-f8d81299 {
  flex: 1;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 40rpx 40rpx 0 0;
  padding: 80rpx 50rpx 60rpx;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.15);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  margin-top: auto;
}
.input-group.data-v-f8d81299 {
  margin-bottom: 50rpx;
}
.input-group.data-v-f8d81299:last-child {
  margin-bottom: 0;
}
.input-wrapper.data-v-f8d81299 {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
  border: 2rpx solid #e8ecf4;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.input-wrapper.data-v-f8d81299:focus-within {
  border-color: #667eea;
  box-shadow: 0 4rpx 25rpx rgba(102, 126, 234, 0.2);
  transform: translateY(-2rpx);
}
.input-wrapper.captcha-wrapper.data-v-f8d81299 {
  padding-right: 20rpx;
}
.input-icon.data-v-f8d81299 {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 36rpx;
}
.form-input.data-v-f8d81299 {
  flex: 1;
  height: 100rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #333333;
  background: transparent;
  border: none;
  font-weight: 500;
}
.form-input.data-v-f8d81299::-webkit-input-placeholder {
  color: #a8b2c8;
  font-weight: 400;
}
.form-input.data-v-f8d81299::placeholder {
  color: #a8b2c8;
  font-weight: 400;
}
.captcha-image.data-v-f8d81299 {
  width: 140rpx;
  height: 80rpx;
  margin-left: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e8ecf4;
  cursor: pointer;
  transition: all 0.3s ease;
}
.captcha-image.data-v-f8d81299:hover {
  border-color: #667eea;
}
.captcha-image .captcha-img.data-v-f8d81299 {
  width: 100%;
  height: 100%;
}
.captcha-image .refresh-text.data-v-f8d81299 {
  font-size: 22rpx;
  color: #667eea;
  font-weight: 500;
}
.error-text.data-v-f8d81299 {
  display: block;
  color: #ff4757;
  font-size: 26rpx;
  margin-top: 15rpx;
  margin-left: 100rpx;
  font-weight: 500;
}
.button-group.data-v-f8d81299 {
  margin: 80rpx 0 50rpx;
}
.login-btn.data-v-f8d81299 {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20rpx;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}
.login-btn.data-v-f8d81299:not([disabled]):active {
  transform: translateY(2rpx);
  box-shadow: 0 12rpx 35rpx rgba(102, 126, 234, 0.4);
}
.login-btn[disabled].data-v-f8d81299 {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
.register-link.data-v-f8d81299 {
  text-align: center;
  margin-bottom: 40rpx;
}
.register-link .link-text.data-v-f8d81299 {
  color: #8892b0;
  font-size: 30rpx;
  font-weight: 400;
}
.register-link .link-button.data-v-f8d81299 {
  color: #667eea;
  font-size: 30rpx;
  font-weight: bold;
  margin-left: 10rpx;
  text-decoration: underline;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.login-content.data-v-f8d81299 {
    padding: 30rpx;
}
.logo-section.data-v-f8d81299 {
    margin-top: 40rpx;
    padding-top: 40rpx;
}
.form-section.data-v-f8d81299 {
    padding: 60rpx 40rpx 50rpx;
}
.logo.data-v-f8d81299 {
    width: 120rpx;
    height: 120rpx;
}
.app-name.data-v-f8d81299 {
    font-size: 46rpx;
}
}
/* 平台特定样式 */
.login-content.data-v-f8d81299 {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 图标字体优化 */
.icon-user.data-v-f8d81299::before {
  content: "👤";
  font-size: 36rpx;
}
.icon-password.data-v-f8d81299::before {
  content: "🔒";
  font-size: 36rpx;
}
.icon-code.data-v-f8d81299::before {
  content: "🔢";
  font-size: 36rpx;
}