<view class="addLink data-v-380f9cc6"><my-header wx:if="{{a}}" class="data-v-380f9cc6" u-i="380f9cc6-0" bind:__l="__l" u-p="{{b}}"/><view class="addBox data-v-380f9cc6"><view class="checkCardType data-v-380f9cc6"><view class="label data-v-380f9cc6">证件类型</view><view class="checkItem-itm data-v-380f9cc6"><view wx:for="{{c}}" wx:for-item="item" wx:key="c" class="checkItem data-v-380f9cc6" bindtap="{{item.d}}"><view class="{{['checkShow', 'data-v-380f9cc6', item.a && 'isCheck']}}"></view><text class="checkName data-v-380f9cc6">{{item.b}}</text></view></view></view><view class="addInfo data-v-380f9cc6"><view class="infoItem data-v-380f9cc6"><view class="label data-v-380f9cc6">姓名</view><input class="uni-input data-v-380f9cc6" placeholder="与证件名称一致" placeholder-style="font-family: 'PingFang SC'" value="{{d}}" bindinput="{{e}}"/></view><view class="infoItem data-v-380f9cc6"><view class="label data-v-380f9cc6">证件号码</view><input class="uni-input data-v-380f9cc6" placeholder="请输入联系人证件号码" placeholder-style="font-family: 'PingFang SC'" value="{{f}}" bindinput="{{g}}"/></view><view class="infoItem data-v-380f9cc6"><view class="label data-v-380f9cc6">手机号码</view><input class="uni-input data-v-380f9cc6" placeholder="请输入联系人手机号码" placeholder-style="font-family: 'PingFang SC'" value="{{h}}" bindinput="{{i}}"/></view><view wx:if="{{j}}" class="infoItem data-v-380f9cc6"><view class="label data-v-380f9cc6">年龄</view><input class="uni-input data-v-380f9cc6" placeholder="请输入联系人年龄" placeholder-style="font-family: 'PingFang SC'" type="number" value="{{k}}" bindinput="{{l}}"/></view></view><view class="user_agree data-v-380f9cc6"><view class="checkItem data-v-380f9cc6" bindtap="{{o}}"><view class="{{['checkShow', 'data-v-380f9cc6', m && 'isCheck']}}"></view><text class="checkName data-v-380f9cc6">同意</text><text class="user_agreement_btn data-v-380f9cc6" catchtap="{{n}}">《用户服务协议和隐私政策》</text></view></view></view><view class="sureChoose data-v-380f9cc6"><view class="upBtn data-v-380f9cc6" bindtap="{{p}}">提交</view></view><view wx:if="{{q}}" class="mask data-v-380f9cc6"><view class="maskContent data-v-380f9cc6"><view class="noticeTitle data-v-380f9cc6">用户服务协议和隐私政策</view><view wx:for="{{r}}" wx:for-item="notice" wx:key="g" class="noticeView data-v-380f9cc6"><view wx:if="{{notice.a}}" class="title data-v-380f9cc6">{{notice.b}}</view><view wx:for="{{notice.c}}" wx:for-item="text" wx:key="b" class="text data-v-380f9cc6">{{text.a}}</view><view wx:for="{{notice.d}}" wx:for-item="item" wx:key="b" class="item data-v-380f9cc6">{{item.a}}</view><view wx:if="{{notice.e}}" class="foot data-v-380f9cc6">{{notice.f}}</view></view><view class="agreeBtn data-v-380f9cc6" bindtap="{{s}}">已阅读</view></view></view></view>