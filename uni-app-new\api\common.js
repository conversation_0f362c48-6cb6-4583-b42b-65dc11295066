import { request } from '../utils/request.js'

// 获取系统配置
export function getSystemConfig() {
  return request({
    url: '/apitp/system/config',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 获取公告信息
export function getAnnouncements(params) {
  return request({
    url: '/apitp/announcement/list',
    method: 'GET',
    params
  })
}

// 获取公告详情
export function getAnnouncementDetail(announcementId) {
  return request({
    url: `/apitp/announcement/detail/${announcementId}`,
    method: 'GET'
  })
}

// 获取轮播图
export function getBanners() {
  return request({
    url: '/apitp/banner/list',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 获取常见问题
export function getFAQ() {
  return request({
    url: '/apitp/faq/list',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 获取联系方式
export function getContactInfo() {
  return request({
    url: '/apitp/contact/info',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 发送短信验证码
export function sendSmsCode(phone, type) {
  return request({
    url: '/apitp/sms/send',
    method: 'POST',
    data: { phone, type },
    headers: {
      isToken: false
    }
  })
}

// 验证短信验证码
export function verifySmsCode(phone, code, type) {
  return request({
    url: '/apitp/sms/verify',
    method: 'POST',
    data: { phone, code, type },
    headers: {
      isToken: false
    }
  })
}

// 文件上传
export function uploadFile(file, type) {
  return request({
    url: '/apitp/upload',
    method: 'POST',
    data: { file, type }
  })
}

// 获取字典数据
export function getDictData(dictType) {
  return request({
    url: `/apitp/dict/data/${dictType}`,
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 获取地区数据
export function getRegionData(parentId = 0) {
  return request({
    url: '/apitp/region/list',
    method: 'GET',
    params: { parentId },
    headers: {
      isToken: false
    }
  })
}

// 意见反馈
export function submitFeedback(feedbackData) {
  return request({
    url: '/apitp/feedback/submit',
    method: 'POST',
    data: feedbackData
  })
}

// 获取版本信息
export function getVersionInfo() {
  return request({
    url: '/apitp/version/info',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 检查更新
export function checkUpdate(currentVersion) {
  return request({
    url: '/apitp/version/check',
    method: 'GET',
    params: { version: currentVersion },
    headers: {
      isToken: false
    }
  })
}

// 获取隐私政策
export function getPrivacyPolicy() {
  return request({
    url: '/apitp/policy/privacy',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}

// 获取用户协议
export function getUserAgreement() {
  return request({
    url: '/apitp/policy/agreement',
    method: 'GET',
    headers: {
      isToken: false
    }
  })
}