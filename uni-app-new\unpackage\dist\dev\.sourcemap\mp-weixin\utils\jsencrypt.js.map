{"version": 3, "file": "jsencrypt.js", "sources": ["utils/jsencrypt.js"], "sourcesContent": ["/**\r\n * RSA加密解密工具\r\n * 适配uni-app环境\r\n */\r\n\r\n// 公钥\r\nconst PUBLIC_KEY = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='\r\n\r\n// 私钥（生产环境中应该从安全的地方获取）\r\nconst PRIVATE_KEY = '**********'\r\n\r\n/**\r\n * RSA加密\r\n * @param {string} text 要加密的文本\r\n * @returns {string} 加密后的文本\r\n */\r\nexport function encrypt(text) {\r\n  try {\r\n    // 在uni-app环境中，我们使用简化的加密实现\r\n    // 实际项目中应该使用专门的RSA加密库\r\n\r\n    // 这里返回base64编码作为简化实现\r\n    // 生产环境中应该使用真正的RSA加密\r\n    return uni.base64Encode(text)\r\n  } catch (error) {\r\n    console.error('加密失败:', error)\r\n    return text\r\n  }\r\n}\r\n\r\n/**\r\n * RSA解密\r\n * @param {string} encryptedText 要解密的文本\r\n * @returns {string} 解密后的文本\r\n */\r\nexport function decrypt(encryptedText) {\r\n  try {\r\n    // 在uni-app环境中，我们使用简化的解密实现\r\n    // 实际项目中应该使用专门的RSA解密库\r\n\r\n    // 这里返回base64解码作为简化实现\r\n    // 生产环境中应该使用真正的RSA解密\r\n    return uni.base64Decode(encryptedText)\r\n  } catch (error) {\r\n    console.error('解密失败:', error)\r\n    return encryptedText\r\n  }\r\n}\r\n\r\n// 默认导出\r\nexport default {\r\n  encrypt,\r\n  decrypt\r\n}"], "names": ["uni"], "mappings": ";;AAgBO,SAAS,QAAQ,MAAM;AAC5B,MAAI;AAMF,WAAOA,cAAG,MAAC,aAAa,IAAI;AAAA,EAC7B,SAAQ,OAAO;AACdA,kBAAAA,iDAAc,SAAS,KAAK;AAC5B,WAAO;AAAA,EACR;AACH;;"}