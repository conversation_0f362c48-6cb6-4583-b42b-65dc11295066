<?php
namespace addons\redisadmin\library;

use think\Env;

class RedisService
{
    protected $redis;

    public function __construct()
    {
        $host    = Env::get('redis.host', get_addon_config('redisadmin')['redis_host']);
        $port    = Env::get('redis.port', get_addon_config('redisadmin')['redis_port']);
        $auth    = Env::get('redis.auth', get_addon_config('redisadmin')['redis_auth']);
        $select  = Env::get('redis.select', get_addon_config('redisadmin')['redis_db']);
        $timeout = Env::get('redis.timeout', 1);

        $this->redis = new \Redis();
        $this->redis->connect($host, $port, $timeout);

        if ($auth) {
            $this->redis->auth($auth);
        }

        $this->redis->select($select);
    }

    public function getInstance()
    {
        return $this->redis;
    }

    public function keys($pattern = '*')
    {
        return $this->redis->keys($pattern);
    }

    public function get($key)
    {
        return $this->redis->get($key);
    }

    public function set($key, $value, $ttl = 0)
    {
        if ($ttl > 0) {
            return $this->redis->setex($key, $ttl, $value);
        }
        return $this->redis->set($key, $value);
    }

    public function del($key)
    {
        return $this->redis->del($key);
    }
}
