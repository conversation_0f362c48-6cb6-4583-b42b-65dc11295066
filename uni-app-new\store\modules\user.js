import { storage } from '../../utils/storage.js'
import { constant } from '../../utils/constant.js'
import { login, getInfo, logout } from '../../api/login.js'
import { getToken, setToken, removeToken } from '../../utils/auth.js'

const state = () => ({
  token: getToken(),
  tenantId: storage.get(constant.tenantId),
  rememberMe: storage.get(constant.rememberMe),
  username: storage.get(constant.username),
  password: storage.get(constant.password),
  avatar: storage.get(constant.avatar),
  roles: storage.get(constant.roles),
  permissions: storage.get(constant.permissions),
  // 新增状态
  openid: '', // 微信openid
  userInfo: null, // 完整用户信息
  loginTime: null // 登录时间
})

const
mutations = {
  SET_TOKEN(state, token) {
    state.token = token
    if (token) {
      setToken(token)
      state.loginTime = new Date().getTime()
    }
  },
  
  SET_TENANTID(state, tenantId) {
    state.tenantId = tenantId
    if (tenantId === '' || tenantId === null) {
      storage.remove(constant.tenantId)
    } else {
      storage.set(constant.tenantId, tenantId)
    }
  },
  
  SET_REMEMBERME(state, rememberMe) {
    state.rememberMe = rememberMe
    if (rememberMe === '' || rememberMe === null || rememberMe === false) {
      storage.remove(constant.rememberMe)
    } else {
      storage.set(constant.rememberMe, rememberMe)
    }
  },
  
  SET_USERNAME(state, username) {
    state.username = username
    if (username === '' || username === null) {
      storage.remove(constant.username)
    } else {
      storage.set(constant.username, username)
    }
  },
  
  SET_PASSWORD(state, password) {
    state.password = password
    if (password === '' || password === null) {
      storage.remove(constant.password)
    } else {
      storage.set(constant.password, password)
    }
  },
  
  SET_AVATAR(state, avatar) {
    state.avatar = avatar
    if (avatar === '' || avatar === null) {
      storage.remove(constant.avatar)
    } else {
      storage.set(constant.avatar, avatar)
    }
  },
  
  SET_ROLES(state, roles) {
    state.roles = roles
    if (roles === '' || roles === null || (Array.isArray(roles) && roles.length === 0)) {
      storage.remove(constant.roles)
    } else {
      storage.set(constant.roles, roles)
    }
  },
  
  SET_PERMISSIONS(state, permissions) {
    state.permissions = permissions
    if (permissions === '' || permissions === null || (Array.isArray(permissions) && permissions.length === 0)) {
      storage.remove(constant.permissions)
    } else {
      storage.set(constant.permissions, permissions)
    }
  },
  
  // 新增mutations
  SET_OPENID(state, openid) {
    state.openid = openid
  },
  
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo
  },
  
  CLEAR_USER_DATA(state) {
    state.token = ''
    state.tenantId = ''
    state.rememberMe = false
    state.username = ''
    state.password = ''
    state.avatar = ''
    state.roles = []
    state.permissions = []
    state.openid = ''
    state.userInfo = null
    state.loginTime = null
  }
}

const
actions = {
  // 登录action
  async Login({ commit }, loginForm) {
    const { tenantId, username, password, code, uuid, rememberMe } = loginForm
    const trimmedUsername = username.trim()
    
    try {
      // 处理记住密码
      if (rememberMe) {
        commit('SET_TENANTID', tenantId)
        commit('SET_REMEMBERME', rememberMe)
        commit('SET_USERNAME', trimmedUsername)
        commit('SET_PASSWORD', password)
      } else {
        commit('SET_TENANTID', '')
        commit('SET_REMEMBERME', false)
        commit('SET_USERNAME', '')
        commit('SET_PASSWORD', '')
      }
      
      // 调用登录接口
      const response = await login(tenantId, trimmedUsername, password, code)
      const { access_token } = response.data
      
      // 设置token
      commit('SET_TOKEN', access_token)
      
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  },
  
  // 获取用户信息
  async GetInfo({ commit, state }) {
    try {
      const response = await getInfo()
      const { user, roles, permissions } = response.data
      
      // 处理头像
      const avatar = (user && user.avatarUrl && user.avatarUrl !== null) 
        ? user.avatarUrl 
        : '@/static/images/profile.jpg'
      
      // 处理用户名
      const username = (user && user.userName && user.userName !== null) 
        ? user.userName 
        : ''
      
      // 设置角色和权限
      if (roles && roles.length > 0) {
        commit('SET_ROLES', roles)
        commit('SET_PERMISSIONS', permissions)
      } else {
        commit('SET_ROLES', ['ROLE_DEFAULT'])
      }
      
      // 设置用户信息
      commit('SET_USERNAME', username)
      commit('SET_AVATAR', avatar)
      commit('SET_USER_INFO', user)
      
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },
  
  // 登出action
  async LogOut({ commit, state }) {
    try {
      const response = await logout()
      
      // 清除所有用户数据
      commit('CLEAR_USER_DATA')
      
      // 清除token和存储
      removeToken()
      storage.clean()
      
      return response
    } catch (error) {
      console.error('登出失败:', error)
      // 即使接口失败也要清除本地数据
      commit('CLEAR_USER_DATA')
      removeToken()
      storage.clean()
      throw error
    }
  },
  
  // 微信登录action
  async WxLogin({ commit }, { code, openid }) {
    try {
      // 这里需要根据实际的微信登录接口调整
      const response = await login('', '', '', code)
      const { access_token } = response.data
      
      commit('SET_TOKEN', access_token)
      commit('SET_OPENID', openid)
      
      return response
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    }
  },
  
  // 刷新token
  async RefreshToken({ commit, state }) {
    try {
      // 这里需要根据实际的刷新token接口调整
      // const response = await refreshToken()
      // commit('SET_TOKEN', response.data.access_token)
      // return response
      
      // 暂时返回当前token
      return { data: { access_token: state.token } }
    } catch (error) {
      console.error('刷新token失败:', error)
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}