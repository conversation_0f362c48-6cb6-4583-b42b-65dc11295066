{"version": 3, "file": "index.js", "sources": ["pages_app/user/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHVzZXJcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"user\">\r\n    <!-- 自定义头部 -->\r\n    <my-header\r\n      v-if=\"!config.iSzgm\"\r\n      title=\"个人中心\"\r\n      :isBack=\"false\"\r\n      :isShowHome=\"false\"\r\n      background=\"transparent\"\r\n    />\r\n\r\n    <view class=\"user_box\">\r\n      <!-- 用户信息 -->\r\n      <view class=\"user_info\">\r\n        <view class=\"user_info_avatar\">\r\n          <image\r\n            :src=\"userInfo.avatar || '/static/img/user/default-avatar.png'\"\r\n            mode=\"aspectFill\"\r\n          />\r\n        </view>\r\n        <view class=\"user_info_text\">\r\n          <view class=\"user_info_name\">{{ userInfo.nickName || '未设置昵称' }}</view>\r\n          <view class=\"user_info_phone\">\r\n            <image src=\"/static/img/user/phone.png\" mode=\"aspectFit\" />\r\n            <text>{{ userInfo.phonenumber || '未绑定手机' }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"user_info_out\" @tap=\"logout\">\r\n          退出登录\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 联系人管理 -->\r\n      <view class=\"contacts_btn\" @tap=\"goToContacts\">\r\n        <image src=\"/static/img/user/contacts.png\" mode=\"aspectFit\" class=\"contacts_image\" />\r\n        <text>联系人管理</text>\r\n        <image src=\"/static/img/user/back.png\" mode=\"aspectFit\" class=\"forward_icon\" />\r\n      </view>\r\n\r\n      <!-- 常用功能 -->\r\n      <view class=\"my_order\">\r\n        <view class=\"my_order_title\">常用功能</view>\r\n        <view class=\"my_order_btns\">\r\n          <view\r\n            v-for=\"(item, index) in functionList\"\r\n            :key=\"index\"\r\n            class=\"my_btn\"\r\n            @tap=\"handleFunctionClick(item)\"\r\n          >\r\n            <view class=\"btn_icon_box\">\r\n              <image :src=\"item.icon\" mode=\"aspectFit\" />\r\n              <view v-if=\"item.showBadge\" :class=\"['badge', item.badgeClass]\">\r\n                {{ item.badgeText }}\r\n              </view>\r\n            </view>\r\n            <text>{{ item.name }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 我的预约 -->\r\n      <view class=\"my_order\">\r\n        <view class=\"my_order_title\">我的预约</view>\r\n        <view class=\"my_order_btns\">\r\n          <view\r\n            v-for=\"(item, index) in bookingList\"\r\n            :key=\"index\"\r\n            class=\"my_btn\"\r\n            @tap=\"handleBookingClick(item)\"\r\n          >\r\n            <view class=\"btn_icon_box\">\r\n              <image :src=\"item.icon\" mode=\"aspectFit\" />\r\n              <view v-if=\"item.showBadge\" :class=\"['badge', item.badgeClass]\">\r\n                {{ item.badgeText }}\r\n              </view>\r\n            </view>\r\n            <text>{{ item.name }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'UserIndex',\r\n  data() {\r\n    return {\r\n      config: {\r\n        iSzgm: false // 根据实际配置设置\r\n      },\r\n      userInfo: {\r\n        avatar: '',\r\n        nickName: '',\r\n        sex: '',\r\n        phonenumber: ''\r\n      },\r\n      allFunctionList: [\r\n        {\r\n          icon: '/static/img/user/app_getbalance.png',\r\n          name: '管理员码',\r\n          path: '/pages_app/user/oneqrcode',\r\n          key: 'code',\r\n          showBadge: false,\r\n          badgeText: '',\r\n          badgeClass: '',\r\n          platforms: ['all'] // 所有平台都显示\r\n        },\r\n        {\r\n          icon: '/static/img/user/app_getbalance.png',\r\n          name: '订阅通知',\r\n          path: 'noticeSubcribe',\r\n          key: 'notice',\r\n          showBadge: false,\r\n          badgeText: '',\r\n          badgeClass: '',\r\n          platforms: ['mp-weixin'] // 仅微信小程序显示\r\n        }\r\n      ],\r\n      bookingList: [\r\n        {\r\n          icon: '/static/img/user/venue.png',\r\n          name: '参观预约',\r\n          path: '/pages_app/user/venuescheme',\r\n          key: 'venue',\r\n          showBadge: false,\r\n          badgeText: '',\r\n          badgeClass: 'badge-blue'\r\n        },\r\n        {\r\n          icon: '/static/img/user/film.png',\r\n          name: '观影预约',\r\n          path: '/pages_app/user/filmscheme',\r\n          key: 'film',\r\n          showBadge: false,\r\n          badgeText: '',\r\n          badgeClass: 'badge-orange'\r\n        },\r\n        {\r\n          icon: '/static/img/user/course.png',\r\n          name: '课程预约',\r\n          path: '/pages_app/user/curriculumscheme',\r\n          key: 'course',\r\n          showBadge: false,\r\n          badgeText: '',\r\n          badgeClass: 'badge-purple'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 根据当前平台过滤功能列表\r\n    functionList() {\r\n      return this.allFunctionList.filter(item => {\r\n        if (item.platforms.includes('all')) {\r\n          return true\r\n        }\r\n        // #ifdef MP-WEIXIN\r\n        return item.platforms.includes('mp-weixin')\r\n        // #endif\r\n        // #ifndef MP-WEIXIN\r\n        return !item.platforms.includes('mp-weixin')\r\n        // #endif\r\n      })\r\n    }\r\n  },\r\n\r\n  onShow() {\r\n    this.getUserInfo()\r\n    this.updateBadges()\r\n  },\r\n\r\n  methods: {\r\n    // 获取用户信息\r\n    async getUserInfo() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/common/getUserInfo',\r\n          method: 'get'\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          this.userInfo = res.data.data || {}\r\n        }\r\n      } catch (error) {\r\n        console.error('获取用户信息失败:', error)\r\n      }\r\n    },\r\n\r\n    // 更新徽章数量\r\n    async updateBadges() {\r\n      try {\r\n        // 这里可以添加获取各种预约数量的逻辑\r\n        // 例如获取待使用的预约数量等\r\n\r\n        // 示例：获取待处理的预约数量\r\n        // const venueCount = await this.getVenueBookingCount()\r\n        // const filmCount = await this.getFilmBookingCount()\r\n        // const courseCount = await this.getCourseBookingCount()\r\n\r\n        // 更新徽章显示\r\n        // this.updateBookingBadge('venue', venueCount)\r\n        // this.updateBookingBadge('film', filmCount)\r\n        // this.updateBookingBadge('course', courseCount)\r\n      } catch (error) {\r\n        console.error('更新徽章失败:', error)\r\n      }\r\n    },\r\n\r\n    // 更新预约徽章\r\n    updateBookingBadge(key, count) {\r\n      const item = this.bookingList.find(item => item.key === key)\r\n      if (item && count > 0) {\r\n        item.showBadge = true\r\n        item.badgeText = count > 99 ? '99+' : count.toString()\r\n      } else if (item) {\r\n        item.showBadge = false\r\n      }\r\n    },\r\n\r\n    // 处理功能点击\r\n    handleFunctionClick(item) {\r\n      if (item.path === 'noticeSubcribe') {\r\n        // 处理订阅通知\r\n        this.handleNoticeSubscribe()\r\n      } else if (item.path) {\r\n        uni.navigateTo({\r\n          url: item.path\r\n        })\r\n      }\r\n    },\r\n\r\n    // 处理订阅通知\r\n    handleNoticeSubscribe() {\r\n      // 微信小程序订阅消息模板ID\r\n      const tmplIds = [\r\n        'sWn1mSByjsKEuiD-QOg48VlKvbjhcp_XfZpUmMJjt5g',\r\n        '4FDnApuDczeYIFSYDNGBw9FWwZG3Fr6J6Sq8PqWE6j0'\r\n      ]\r\n\r\n      // #ifdef MP-WEIXIN\r\n      uni.requestSubscribeMessage({\r\n        tmplIds: tmplIds,\r\n        success: (res) => {\r\n          if (res[tmplIds[0]] === 'accept' || res[tmplIds[1]] === 'accept') {\r\n            uni.showToast({\r\n              title: '订阅成功',\r\n              icon: 'success',\r\n              duration: 2000\r\n            })\r\n          }\r\n        },\r\n        fail: () => {\r\n          uni.showToast({\r\n            title: '订阅失败',\r\n            icon: 'error',\r\n            duration: 2000\r\n          })\r\n        }\r\n      })\r\n      // #endif\r\n\r\n      // #ifndef MP-WEIXIN\r\n      uni.showToast({\r\n        title: '该功能仅在微信小程序中可用',\r\n        icon: 'none',\r\n        duration: 2000\r\n      })\r\n      // #endif\r\n    },\r\n\r\n    // 处理预约点击\r\n    handleBookingClick(item) {\r\n      if (item.path) {\r\n        uni.navigateTo({\r\n          url: item.path\r\n        })\r\n      }\r\n    },\r\n\r\n    // 跳转到联系人管理\r\n    goToContacts() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/contacts/index'\r\n      })\r\n    },\r\n\r\n    // 退出登录\r\n    logout() {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要退出登录吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.performLogout()\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 执行退出登录\r\n    async performLogout() {\r\n      try {\r\n        uni.showLoading({\r\n          title: '退出中...'\r\n        })\r\n\r\n        // 调用退出登录接口\r\n        await this.$myRequest({\r\n          url: '/auth/logout',\r\n          method: 'post'\r\n        })\r\n\r\n        // 清除本地存储\r\n        uni.removeStorageSync('token')\r\n        uni.removeStorageSync('userInfo')\r\n\r\n        uni.hideLoading()\r\n\r\n        // 跳转到登录页\r\n        uni.reLaunch({\r\n          url: '/pages/login/index'\r\n        })\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        console.error('退出登录失败:', error)\r\n        uni.showToast({\r\n          title: '退出失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.user {\r\n  background: url(data:image/png;base64,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) no-repeat top #f3f4f6;\r\n  background-size: 100% auto;\r\n  font-family: PingFang SC;\r\n  height: auto;\r\n  min-height: 100vh;\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n.user {\r\n\r\n  .user_box {\r\n    box-sizing: border-box;\r\n    height: 100%;\r\n    padding: 20rpx 28rpx 0;\r\n\r\n    // 用户信息\r\n    .user_info {\r\n      align-items: center;\r\n      color: #fff;\r\n      display: flex;\r\n      padding-left: 26rpx;\r\n      position: relative;\r\n\r\n      .user_info_avatar {\r\n        height: 120rpx;\r\n        width: 120rpx;\r\n\r\n        image {\r\n          border: 5rpx solid #fff;\r\n          border-radius: 50%;\r\n          height: 100%;\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .user_info_text {\r\n        display: flex;\r\n        flex-direction: column;\r\n        height: 120rpx;\r\n        justify-content: space-around;\r\n        margin-left: 30rpx;\r\n\r\n        .user_info_name {\r\n          font-size: 15px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        .user_info_phone {\r\n          align-items: center;\r\n          background-color: #fff;\r\n          border-radius: 38rpx;\r\n          display: flex;\r\n          height: 44rpx;\r\n          justify-content: center;\r\n          width: 220rpx;\r\n\r\n          image {\r\n            height: 20rpx;\r\n            width: 12rpx;\r\n          }\r\n\r\n          text {\r\n            color: #888;\r\n            font-family: PingFang SC;\r\n            font-size: 24rpx;\r\n            margin-left: 10rpx;\r\n          }\r\n        }\r\n      }\r\n\r\n      .user_info_out {\r\n        background: #07c160;\r\n        border-radius: 8rpx;\r\n        bottom: 0;\r\n        height: 50rpx;\r\n        line-height: 50rpx;\r\n        position: absolute;\r\n        right: 0;\r\n        text-align: center;\r\n        width: 160rpx;\r\n      }\r\n    }\r\n\r\n    // 联系人管理\r\n    .contacts_btn {\r\n      align-items: center;\r\n      background-color: #fff;\r\n      border-radius: 10rpx;\r\n      display: flex;\r\n      height: 86rpx;\r\n      margin-top: 38rpx;\r\n      position: relative;\r\n      width: 100%;\r\n\r\n      .contacts_image {\r\n        height: 44rpx;\r\n        margin-left: 28rpx;\r\n        width: 44rpx;\r\n      }\r\n\r\n      text {\r\n        color: #000;\r\n        font-family: PingFang SC;\r\n        font-size: 27rpx;\r\n        font-weight: 600;\r\n        margin-left: 26rpx;\r\n      }\r\n\r\n      .forward_icon {\r\n        height: 24rpx;\r\n        position: absolute;\r\n        right: 32rpx;\r\n        width: 12rpx;\r\n      }\r\n    }\r\n\r\n    // 功能区域和预约区域\r\n    .my_order {\r\n      background-color: #fff;\r\n      border-radius: 10rpx;\r\n      box-sizing: border-box;\r\n      height: 250rpx;\r\n      margin-top: 20rpx;\r\n      padding: 20rpx;\r\n      width: 100%;\r\n\r\n      .my_order_title {\r\n        color: #000;\r\n        font-family: PingFang SC;\r\n        font-size: 34rpx;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .my_order_btns {\r\n        display: flex;\r\n        height: 120rpx;\r\n        justify-content: space-between;\r\n        margin-top: 28rpx;\r\n        padding: 0 20rpx;\r\n\r\n        .my_btn {\r\n          position: relative;\r\n\r\n          .btn_icon_box {\r\n            align-items: center;\r\n            border-radius: 50%;\r\n            display: flex;\r\n            height: 76rpx;\r\n            justify-content: center;\r\n            margin: 0 auto 5rpx;\r\n            position: relative;\r\n            width: 76rpx;\r\n\r\n            image {\r\n              height: 100%;\r\n              width: 100%;\r\n            }\r\n\r\n            .badge {\r\n              background: #d51d1d;\r\n              border-radius: 50%;\r\n              box-sizing: border-box;\r\n              color: #fff;\r\n              font-size: 27rpx;\r\n              left: 58rpx;\r\n              padding: 0 12rpx;\r\n              position: absolute;\r\n              top: -4rpx;\r\n\r\n              &.overflowOneLength {\r\n                border-radius: 19rpx;\r\n              }\r\n            }\r\n          }\r\n\r\n          text {\r\n            color: #000;\r\n            font-family: PingFang SC;\r\n            font-size: 26rpx;\r\n            font-weight: 600;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.user_box {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/user/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "item"], "mappings": ";;;AAqFA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,QACN,OAAO;AAAA;AAAA,MACR;AAAA,MACD,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,KAAK;AAAA,QACL,aAAa;AAAA,MACd;AAAA,MACD,iBAAiB;AAAA,QACf;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW,CAAC,KAAK;AAAA;AAAA,QAClB;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,UACZ,WAAW,CAAC,WAAW;AAAA;AAAA,QACzB;AAAA,MACD;AAAA,MACD,aAAa;AAAA,QACX;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,QACb;AAAA,QACD;AAAA,UACE,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,KAAK;AAAA,UACL,WAAW;AAAA,UACX,WAAW;AAAA,UACX,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,eAAe;AACb,aAAO,KAAK,gBAAgB,OAAO,UAAQ;AACzC,YAAI,KAAK,UAAU,SAAS,KAAK,GAAG;AAClC,iBAAO;AAAA,QACT;AAEA,eAAO,KAAK,UAAU,SAAS,WAAW;AAAA,OAK3C;AAAA,IACH;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,YAAY;AACjB,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,WAAW,IAAI,KAAK,QAAQ,CAAC;AAAA,QACpC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,eAAe;AAAA,IAiBpB;AAAA;AAAA,IAGD,mBAAmB,KAAK,OAAO;AAC7B,YAAM,OAAO,KAAK,YAAY,KAAK,CAAAC,UAAQA,MAAK,QAAQ,GAAG;AAC3D,UAAI,QAAQ,QAAQ,GAAG;AACrB,aAAK,YAAY;AACjB,aAAK,YAAY,QAAQ,KAAK,QAAQ,MAAM,SAAS;AAAA,MACrD,WAAS,MAAM;AACf,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,MAAM;AACxB,UAAI,KAAK,SAAS,kBAAkB;AAElC,aAAK,sBAAsB;AAAA,MAC7B,WAAW,KAAK,MAAM;AACpBD,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,KAAK;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,wBAAwB;AAEtB,YAAM,UAAU;AAAA,QACd;AAAA,QACA;AAAA,MACF;AAGAA,oBAAAA,MAAI,wBAAwB;AAAA,QAC1B;AAAA,QACA,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,QAAQ,CAAC,CAAC,MAAM,YAAY,IAAI,QAAQ,CAAC,CAAC,MAAM,UAAU;AAChEA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AAAA,UACH;AAAA,QACD;AAAA,QACD,MAAM,MAAM;AACVA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAAA,QACH;AAAA,OACD;AAAA,IAUF;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACvB,UAAI,KAAK,MAAM;AACbA,sBAAAA,MAAI,WAAW;AAAA,UACb,KAAK,KAAK;AAAA,SACX;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACbA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,SACR;AAGD,cAAM,KAAK,WAAW;AAAA,UACpB,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAGDA,sBAAG,MAAC,kBAAkB,OAAO;AAC7BA,sBAAG,MAAC,kBAAkB,UAAU;AAEhCA,sBAAAA,MAAI,YAAY;AAGhBA,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA,SACN;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,mCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9UA,GAAG,WAAW,eAAe;"}