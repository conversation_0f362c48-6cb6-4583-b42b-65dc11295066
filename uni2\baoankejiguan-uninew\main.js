import Vue from 'vue'
import App from './App'
import {
  myRequest
} from './api/api.js'


// import './common/css/base.css'
import './common/css/icon.css'

Vue.prototype.$myRequest = myRequest
// Vue.prototype.$appID = "wxb83b88f7fbf3c4cb" // 我的
Vue.prototype.$appID = "wx7fdbf0566b7e1707" // 宝安科技馆
// 宝安科技馆定位
Vue.prototype.$baoanLocation = {
  la: 22.55866135902317,
  lo: 113.91141057014467
}
// 公司
// Vue.prototype.$baoanLocation = {
//  la: 22.57462,
//  lo: 113.93959
// }
Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
  ...App
})
app.$mount()
