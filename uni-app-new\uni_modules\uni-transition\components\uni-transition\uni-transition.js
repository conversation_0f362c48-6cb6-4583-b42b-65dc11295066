var t = require("../../../../@babel/runtime/helpers/toConsumableArray"),
  i = require("../../../../@babel/runtime/helpers/typeof"),
  n = require("../../../../@babel/runtime/helpers/objectSpread2"),
  a = require("./createAnimation.js"),
  o = require("../../../../common/vendor.js"),
  e = {
    name: "uniTransition",
    emits: ["click", "change"],
    props: {
      show: {
        type: Boolean,
        default: !1
      },
      modeClass: {
        type: [Array, String],
        default: function() {
          return "fade"
        }
      },
      duration: {
        type: Number,
        default: 300
      },
      styles: {
        type: Object,
        default: function() {
          return {}
        }
      },
      customClass: {
        type: String,
        default: ""
      },
      onceRender: {
        type: Boolean,
        default: !1
      }
    },
    data: function() {
      return {
        isShow: !1,
        transform: "",
        opacity: 1,
        animationData: {},
        durationTime: 300,
        config: {}
      }
    },
    watch: {
      show: {
        handler: function(t) {
          t ? this.open() : this.isShow && this.close()
        },
        immediate: !0
      }
    },
    computed: {
      stylesObject: function() {
        var t = n(n({}, this.styles), {}, {
            "transition-duration": this.duration / 1e3 + "s"
          }),
          i = "";
        for (var a in t) {
          i += this.toLine(a) + ":" + t[a] + ";"
        }
        return i
      },
      transformStyles: function() {
        return "transform:" + this.transform + ";opacity:" + this.opacity + ";" + this.stylesObject
      }
    },
    created: function() {
      this.config = {
        duration: this.duration,
        timingFunction: "ease",
        transformOrigin: "50% 50%",
        delay: 0
      }, this.durationTime = this.duration
    },
    methods: {
      init: function() {
        var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        t.duration && (this.durationTime = t.duration), this.animation = a.createAnimation(Object.assign(this.config, t), this)
      },
      onClick: function() {
        this.$emit("click", {
          detail: this.isShow
        })
      },
      step: function(n) {
        var a = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        if (this.animation) {
          for (var o in n) try {
            var e;
            "object" == i(n[o]) ? (e = this.animation)[o].apply(e, t(n[o])) : this.animation[o](n[o])
          } catch (t) {
            console.error("方法 ".concat(o, " 不存在"))
          }
          return this.animation.step(a), this
        }
      },
      run: function(t) {
        this.animation && this.animation.run(t)
      },
      open: function() {
        var t = this;
        clearTimeout(this.timer), this.transform = "", this.isShow = !0;
        var n = this.styleInit(!1),
          o = n.opacity,
          e = n.transform;
        i(o) < "u" && (this.opacity = o), this.transform = e, this.$nextTick((function() {
          t.timer = setTimeout((function() {
            t.animation = a.createAnimation(t.config, t), t.tranfromInit(!1).step(), t.animation.run(), t.$emit("change", {
              detail: t.isShow
            })
          }), 20)
        }))
      },
      close: function(t) {
        var i = this;
        this.animation && this.tranfromInit(!0).step().run((function() {
          i.isShow = !1, i.animationData = null, i.animation = null;
          var t = i.styleInit(!1),
            n = t.opacity,
            a = t.transform;
          i.opacity = n || 1, i.transform = a, i.$emit("change", {
            detail: i.isShow
          })
        }))
      },
      styleInit: function(t) {
        var i = this,
          n = {
            transform: ""
          },
          a = function(t, a) {
            "fade" === a ? n.opacity = i.animationType(t)[a] : n.transform += i.animationType(t)[a] + " "
          };
        return "string" == typeof this.modeClass ? a(t, this.modeClass) : this.modeClass.forEach((function(i) {
          a(t, i)
        })), n
      },
      tranfromInit: function(t) {
        var i = this,
          n = function(t, n) {
            var a = null;
            "fade" === n ? a = t ? 0 : 1 : (a = t ? "-100%" : "0", "zoom-in" === n && (a = t ? .8 : 1), "zoom-out" === n && (a = t ? 1.2 : 1), "slide-right" === n && (a = t ? "100%" : "0"), "slide-bottom" === n && (a = t ? "100%" : "0")), i.animation[i.animationMode()[n]](a)
          };
        return "string" == typeof this.modeClass ? n(t, this.modeClass) : this.modeClass.forEach((function(i) {
          n(t, i)
        })), this.animation
      },
      animationType: function(t) {
        return {
          fade: t ? 1 : 0,
          "slide-top": "translateY(".concat(t ? "0" : "-100%", ")"),
          "slide-right": "translateX(".concat(t ? "0" : "100%", ")"),
          "slide-bottom": "translateY(".concat(t ? "0" : "100%", ")"),
          "slide-left": "translateX(".concat(t ? "0" : "-100%", ")"),
          "zoom-in": "scaleX(".concat(t ? 1 : .8, ") scaleY(").concat(t ? 1 : .8, ")"),
          "zoom-out": "scaleX(".concat(t ? 1 : 1.2, ") scaleY(").concat(t ? 1 : 1.2, ")")
        }
      },
      animationMode: function() {
        return {
          fade: "opacity",
          "slide-top": "translateY",
          "slide-right": "translateX",
          "slide-bottom": "translateY",
          "slide-left": "translateX",
          "zoom-in": "scale",
          "zoom-out": "scale"
        }
      },
      toLine: function(t) {
        return t.replace(/([A-Z])/g, "-$1").toLowerCase()
      }
    }
  };
var s = o._export_sfc(e, [
  ["render", function(t, i, n, a, e, s) {
    return {
      a: e.isShow,
      b: e.animationData,
      c: o.n(n.customClass),
      d: o.s(s.transformStyles),
      e: o.o((function() {
        return s.onClick && s.onClick.apply(s, arguments)
      }))
    }
  }]
]);
wx.createComponent(s);