/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.film-cancel.data-v-99fb144e {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.film-cancel .content.data-v-99fb144e {
  padding: 20rpx;
  padding-top: 120rpx;
}
.film-cancel .content .film-info.data-v-99fb144e {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.film-cancel .content .film-info .info-header.data-v-99fb144e {
  margin-bottom: 20rpx;
}
.film-cancel .content .film-info .info-header .venue-name.data-v-99fb144e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.film-cancel .content .film-info .film-details.data-v-99fb144e {
  display: flex;
}
.film-cancel .content .film-info .film-details .film-poster.data-v-99fb144e {
  width: 120rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.film-cancel .content .film-info .film-details .film-poster .poster-image.data-v-99fb144e {
  width: 100%;
  height: 100%;
}
.film-cancel .content .film-info .film-details .film-meta.data-v-99fb144e {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.film-cancel .content .film-info .film-details .film-meta .film-name.data-v-99fb144e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.film-cancel .content .film-info .film-details .film-meta .film-type.data-v-99fb144e,
.film-cancel .content .film-info .film-details .film-meta .film-time.data-v-99fb144e,
.film-cancel .content .film-info .film-details .film-meta .film-date.data-v-99fb144e,
.film-cancel .content .film-info .film-details .film-meta .film-count.data-v-99fb144e {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
  line-height: 1.3;
}
.film-cancel .content .contacts-section.data-v-99fb144e {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.film-cancel .content .contacts-section .section-title.data-v-99fb144e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.film-cancel .content .contacts-section .contacts-list .contact-item.data-v-99fb144e {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.film-cancel .content .contacts-section .contacts-list .contact-item.data-v-99fb144e:last-child {
  border-bottom: none;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .contact-info.data-v-99fb144e {
  flex: 1;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-name.data-v-99fb144e {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-phone.data-v-99fb144e {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-id.data-v-99fb144e {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .checkbox.data-v-99fb144e {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .checkbox.checked.data-v-99fb144e {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}
.film-cancel .content .contacts-section .contacts-list .contact-item .checkbox.checked .check-icon.data-v-99fb144e {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}
.film-cancel .content .action-section .cancel-btn.data-v-99fb144e {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.film-cancel .content .action-section .cancel-btn.data-v-99fb144e::after {
  border: none;
}
.film-cancel .content .action-section .cancel-btn.data-v-99fb144e:disabled {
  background: #ccc;
  color: #999;
}

/* 平台特定样式 */
.content.data-v-99fb144e {
  padding-bottom: env(safe-area-inset-bottom);
}