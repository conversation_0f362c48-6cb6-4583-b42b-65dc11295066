"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const store_index = require("./store/index.js");
require("./permission.js");
const utils_request = require("./utils/request.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages_app/login/index.js";
  "./pages_app/register/index.js";
  "./pages_app/vieworder/index.js";
  "./pages_app/vieworder/filmdes.js";
  "./pages_app/contacts/index.js";
  "./pages_app/contacts/addcontact.js";
  "./pages_app/user/index.js";
  "./pages_app/user/filmscheme.js";
  "./pages_app/contactmanager/index.js";
  "./pages_app/schemesuccess/filmsuccess.js";
  "./pages_app/schemesuccess/filmcancel.js";
  "./pages_app/entervenue/index.js";
  "./pages_app/schemesuccess/venuesuccess.js";
  "./pages_app/schemesuccess/venuecancel.js";
  "./pages_app/curriculum/index.js";
  "./pages_app/curriculum/choosecurriculum.js";
  "./pages_app/schemesuccess/curriculumsuccess.js";
  "./pages_app/schemesuccess/curriculumcancel.js";
  "./pages_app/user/venuescheme.js";
  "./pages_app/user/curriculumscheme.js";
  "./pages_app/user/oneqrcode.js";
}
const _sfc_main = {
  globalData: {
    // 基础配置
    baseUrl: "https://bakjgyyxt.baoan.gov.cn",
    iSzgm: false,
    clientId: "be7052a7e4f802c20df10a8d131adb12",
    publicKey: "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==",
    privateKey: "**********",
    // 应用信息
    appInfo: {
      name: "baoanquestacon-app",
      version: "2.0.0",
      logo: "/static/favicon.ico"
    },
    // 微信小程序配置
    appId: "wx7fdbf0566b7e1707",
    productionTip: false,
    // 宝安位置信息
    baoanLocation: {
      la: 22.55866135902317,
      lo: 113.91141057014467
    },
    // 系统信息
    systemInfo: null,
    statusBarHeight: 0,
    customBarHeight: 0
  },
  onLaunch: function(options) {
    // common_vendor.index.__f__("log", "at App.vue:38", "App Launch", options);
    // this.getSystemInfo();
    // this.addMemoryWarningListener();
    // this.initApp();
    // if (options && options.query) {
    //   common_vendor.index.__f__("log", "at App.vue:54", "启动参数:", options.query);
    // }
  },
  onShow: function(options) {
    // common_vendor.index.__f__("log", "at App.vue:60", "App Show", options);
    // this.handleAppShow(options);
  },
  onHide: function() {
    // common_vendor.index.__f__("log", "at App.vue:67", "App Hide");
    // this.handleAppHide();
  },
  onError: function(error) {
    // common_vendor.index.__f__("error", "at App.vue:74", "App Error:", error);
    // this.handleGlobalError(error);
  },
  methods: {
    // 获取系统信息
    getSystemInfo() {
      try {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        this.globalData.systemInfo = systemInfo;
        this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0;
        const menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
        this.globalData.customBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - systemInfo.statusBarHeight;
        common_vendor.index.__f__("log", "at App.vue:98", "系统信息获取成功:", systemInfo);
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:100", "获取系统信息失败:", error);
      }
    },
    // 加载自定义字体
    loadCustomFonts() {
      common_vendor.index.loadFontFace({
        global: true,
        family: "PingFang SC",
        source: 'url("https://wesalt-ai-digial-dev.oss-cn-shenzhen.aliyuncs.com/font/PINGFANG%20REGULAR.TTF")',
        success: () => {
          common_vendor.index.__f__("log", "at App.vue:111", "PingFang SC 字体加载成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at App.vue:114", "PingFang SC 字体加载失败:", err);
        }
      });
    },
    // 添加内存警告监听
    addMemoryWarningListener() {
      common_vendor.index.onMemoryWarning(() => {
        common_vendor.index.__f__("log", "at App.vue:122", "内存不足，进行清理");
        this.clearCache();
      });
    },
    // 初始化应用
    initApp() {
      common_vendor.index.__f__("log", "at App.vue:129", "应用初始化开始");
      this.checkForUpdate();
      this.initGlobalData();
      this.initPlugins();
      common_vendor.index.__f__("log", "at App.vue:142", "应用初始化完成");
    },
    // 检查小程序更新
    checkForUpdate() {
      const updateManager = common_vendor.index.getUpdateManager();
      updateManager.onCheckForUpdate((res) => {
        common_vendor.index.__f__("log", "at App.vue:151", "检查更新结果:", res.hasUpdate);
      });
      updateManager.onUpdateReady(() => {
        common_vendor.index.showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用？",
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      updateManager.onUpdateFailed(() => {
        common_vendor.index.__f__("error", "at App.vue:167", "新版本下载失败");
      });
    },
    // 初始化全局数据
    initGlobalData() {
      common_vendor.index.__f__("log", "at App.vue:175", "全局数据初始化完成");
    },
    // 初始化插件
    initPlugins() {
      common_vendor.index.__f__("log", "at App.vue:181", "插件初始化完成");
    },
    // 处理应用显示
    handleAppShow(options) {
      if (options && options.scene) {
        common_vendor.index.__f__("log", "at App.vue:188", "应用场景值:", options.scene);
      }
    },
    // 处理应用隐藏
    handleAppHide() {
    },
    // 全局错误处理
    handleGlobalError(error) {
      common_vendor.index.__f__("error", "at App.vue:201", "全局错误:", error);
    },
    // 清理缓存数据
    clearCache() {
      try {
        common_vendor.index.__f__("log", "at App.vue:211", "开始清理缓存");
        common_vendor.index.__f__("log", "at App.vue:216", "缓存清理完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:218", "缓存清理失败:", error);
      }
    }
  }
};
const MyHeader = () => "./components/my-header/my-header.js";
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(store_index.store);
  app.component("my-header", MyHeader);
  app.config.globalProperties.$myRequest = utils_request.request;
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
