{"version": 3, "file": "choosecurriculum.js", "sources": ["pages_app/curriculum/choosecurriculum.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGN1cnJpY3VsdW1cY2hvb3NlY3VycmljdWx1bS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"choose-curriculum\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      title=\"课程详情\" \r\n      :isBack=\"true\" \r\n      :isShowHome=\"true\"\r\n      background=\"transparent\"\r\n      color=\"#ffffff\"\r\n    />\r\n    \r\n    <view class=\"curriculum-container\">\r\n      <!-- 课程封面 -->\r\n      <view class=\"curriculum-header\">\r\n        <image \r\n          class=\"curriculum-bg\" \r\n          mode=\"aspectFill\" \r\n          src=\"/static/img/curriculum/curriculum_bg.png\"\r\n        />\r\n        <view class=\"curriculum-info\">\r\n          <image \r\n            v-if=\"courseInfo.courseCover\" \r\n            class=\"course-cover\" \r\n            mode=\"aspectFill\" \r\n            :src=\"getImages(courseInfo.courseCover)\"\r\n          />\r\n          <view class=\"course-details\">\r\n            <text class=\"course-name\">{{ courseInfo.courseName || '课程加载中...' }}</text>\r\n            <view class=\"course-meta\">\r\n              <text class=\"meta-item\">适龄：{{ courseInfo.courseAgeProp || '--' }}</text>\r\n              <text class=\"meta-item\">时间：{{ courseInfo.courseStartTime || '--' }} - {{ courseInfo.courseEndTime || '--' }}</text>\r\n              <text class=\"meta-item\">地点：{{ courseInfo.courseAddress || '--' }}</text>\r\n              <text class=\"meta-item\">剩余名额：{{ courseInfo.inventoryVotes || 0 }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 课程详情 -->\r\n      <view class=\"curriculum-content\">\r\n        <view class=\"content-section\">\r\n          <view class=\"section-title\">\r\n            <image class=\"title-icon\" src=\"/static/img/curriculum/curriculum_title_inner.png\" mode=\"aspectFit\" />\r\n            <text class=\"title-text\">课程介绍</text>\r\n          </view>\r\n          <view class=\"section-content\">\r\n            <rich-text :nodes=\"courseInfo.courseIntroduce || '暂无课程介绍'\"></rich-text>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"content-section\">\r\n          <view class=\"section-title\">\r\n            <image class=\"title-icon\" src=\"/static/img/curriculum/curriculum_title_inner.png\" mode=\"aspectFit\" />\r\n            <text class=\"title-text\">预约须知</text>\r\n          </view>\r\n          <view class=\"section-content notice-content\">\r\n            <view class=\"notice-item\">\r\n              <text class=\"notice-title\">一、报名方式</text>\r\n              <view class=\"notice-list\">\r\n                <text class=\"notice-text\">1、本次公益培训课程仅接受微信报名，名额有限，先到先得，额满为止</text>\r\n                <text class=\"notice-text\">2、课程咨询热线：0755-27880235</text>\r\n                <text class=\"notice-text\">3、公益课开始上课后自动关闭报名入口</text>\r\n                <text class=\"notice-text\">4、学员可通过个人中心-课程预约-查看凭证，管理查询个人预约信息</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"notice-item\">\r\n              <text class=\"notice-title\">二、温馨提示</text>\r\n              <view class=\"notice-list\">\r\n                <text class=\"notice-text\">1、学员必须以本人真实信息报名，如现场确认时发现报名信息不符合则取消资格</text>\r\n                <text class=\"notice-text\">2、课程期间，请学员严格遵守上课时间，为保证教学质量迟到超过10分钟则不能进入教室学习</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 联系人选择 -->\r\n      <view class=\"contacts-section\">\r\n        <view class=\"section-title\">\r\n          <image class=\"title-icon\" src=\"/static/img/curriculum/curriculum_title_inner.png\" mode=\"aspectFit\" />\r\n          <text class=\"title-text\">选择联系人</text>\r\n        </view>\r\n        \r\n        <view v-if=\"contactsList.length > 0\" class=\"contacts-list\">\r\n          <view \r\n            v-for=\"(contact, index) in contactsList\" \r\n            :key=\"contact.linkId\"\r\n            class=\"contact-item\"\r\n            @tap=\"selectContact(contact, index)\"\r\n          >\r\n            <view class=\"contact-info\">\r\n              <text class=\"contact-name\">{{ contact.linkmanName }}</text>\r\n              <text class=\"contact-id\">{{ hideIdCard(contact.linkmanCertificate) }}</text>\r\n            </view>\r\n            <view class=\"contact-select\">\r\n              <view :class=\"['select-circle', { 'selected': selectedContacts.includes(index) }]\"></view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view v-else class=\"no-contacts\">\r\n          <text class=\"no-contacts-text\">暂无联系人，请先添加联系人</text>\r\n          <button class=\"add-contact-btn\" @tap=\"goToAddContact\">添加联系人</button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部操作栏 -->\r\n    <view class=\"bottom-bar\">\r\n      <button \r\n        class=\"submit-btn\" \r\n        :disabled=\"!canSubmit\" \r\n        @tap=\"submitReservation\"\r\n      >\r\n        {{ courseInfo.inventoryVotes > 0 ? '立即预约' : '名额已满' }}\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getImages, myRequest } from '../../api/api.js'\r\nimport config from '../../config.js'\r\nimport Utils from '../../utils/index.js'\r\n\r\nexport default {\r\n  name: 'ChooseCurriculum',\r\n  components: {\r\n    MyHeader: () => import('../../components/my-header/my-header.vue')\r\n  },\r\n  data() {\r\n    return {\r\n      courseId: null,\r\n      courseInfo: {},\r\n      contactsList: [],\r\n      selectedContacts: [],\r\n      isSubmitting: false\r\n    }\r\n  },\r\n  computed: {\r\n    canSubmit() {\r\n      return this.courseInfo.inventoryVotes > 0 && \r\n             this.selectedContacts.length > 0 && \r\n             !this.isSubmitting\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    if (options.id) {\r\n      this.courseId = options.id\r\n      this.getCourseInfo()\r\n      this.getContactsList()\r\n    } else {\r\n      uni.showToast({\r\n        title: '参数错误',\r\n        icon: 'error'\r\n      })\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    }\r\n  },\r\n  methods: {\r\n    getImages,\r\n    \r\n    // 获取课程信息\r\n    async getCourseInfo() {\r\n      try {\r\n        const response = await myRequest({\r\n          url: '/web/session/getSessionById',\r\n          method: 'get',\r\n          data: {\r\n            id: this.courseId\r\n          }\r\n        })\r\n        \r\n        if (response.data.code === 200) {\r\n          const data = response.data.data\r\n          data.courseEndTime = Utils.changeTime(data.courseEndTime)\r\n          data.courseStartTime = Utils.changeTime(data.courseStartTime)\r\n          this.courseInfo = data\r\n        } else {\r\n          throw new Error(response.data.msg || '获取课程信息失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取课程信息失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '获取课程信息失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 获取联系人列表\r\n    async getContactsList() {\r\n      try {\r\n        const response = await myRequest({\r\n          url: '/web/linkman/list',\r\n          method: 'get'\r\n        })\r\n        \r\n        if (response.data.code === 200) {\r\n          this.contactsList = response.data.rows || []\r\n        } else {\r\n          throw new Error(response.data.msg || '获取联系人列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取联系人列表失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '获取联系人列表失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 选择联系人\r\n    selectContact(contact, index) {\r\n      const position = this.selectedContacts.indexOf(index)\r\n      if (position > -1) {\r\n        this.selectedContacts.splice(position, 1)\r\n      } else {\r\n        this.selectedContacts.push(index)\r\n      }\r\n    },\r\n    \r\n    // 前往添加联系人\r\n    goToAddContact() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/contacts/addcontact'\r\n      })\r\n    },\r\n    \r\n    // 提交预约\r\n    async submitReservation() {\r\n      if (!this.canSubmit) return\r\n      \r\n      this.isSubmitting = true\r\n      \r\n      try {\r\n        uni.showLoading({\r\n          title: '提交中...',\r\n          mask: true\r\n        })\r\n        \r\n        const selectedPeople = this.selectedContacts.map(index => {\r\n          return this.contactsList[index].linkId\r\n        })\r\n        \r\n        const response = await myRequest({\r\n          url: '/web/session/subscribe',\r\n          method: 'post',\r\n          data: {\r\n            sessionId: this.courseId,\r\n            linkmanIds: selectedPeople.join(',')\r\n          }\r\n        })\r\n        \r\n        uni.hideLoading()\r\n        \r\n        if (response.data.code === 200) {\r\n          const batchNumber = response.data.data\r\n          uni.redirectTo({\r\n            url: `/pages_app/schemesuccess/curriculumsuccess?batchNumber=${batchNumber}`\r\n          })\r\n        } else {\r\n          throw new Error(response.data.msg || '预约失败')\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        console.error('预约失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '预约失败',\r\n          icon: 'error'\r\n        })\r\n      } finally {\r\n        this.isSubmitting = false\r\n      }\r\n    },\r\n    \r\n    // 隐藏身份证号中间部分\r\n    hideIdCard(idCard) {\r\n      if (!idCard || idCard.length < 8) return idCard\r\n      \r\n      return idCard.replace(idCard.substring(4, 15), '*******')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.choose-curriculum {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  padding-bottom: 120rpx; // 为底部操作栏留出空间\r\n}\r\n\r\n.curriculum-container {\r\n  padding: 20rpx 30rpx;\r\n}\r\n\r\n.curriculum-header {\r\n  position: relative;\r\n  height: 360rpx;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n  margin-bottom: 30rpx;\r\n  \r\n  .curriculum-bg {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 1;\r\n  }\r\n  \r\n  .curriculum-info {\r\n    position: relative;\r\n    z-index: 2;\r\n    display: flex;\r\n    padding: 40rpx;\r\n    height: 100%;\r\n    box-sizing: border-box;\r\n    align-items: center;\r\n    \r\n    .course-cover {\r\n      width: 180rpx;\r\n      height: 180rpx;\r\n      border-radius: 12rpx;\r\n      margin-right: 30rpx;\r\n      background-color: rgba(255, 255, 255, 0.2);\r\n    }\r\n    \r\n    .course-details {\r\n      flex: 1;\r\n      color: #ffffff;\r\n      \r\n      .course-name {\r\n        font-size: 36rpx;\r\n        font-weight: 600;\r\n        margin-bottom: 20rpx;\r\n        display: block;\r\n      }\r\n      \r\n      .course-meta {\r\n        .meta-item {\r\n          font-size: 26rpx;\r\n          display: block;\r\n          margin-bottom: 10rpx;\r\n          opacity: 0.9;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.curriculum-content {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  \r\n  .content-section {\r\n    margin-bottom: 40rpx;\r\n    \r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n    \r\n    .section-title {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n      \r\n      .title-icon {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n        margin-right: 10rpx;\r\n      }\r\n      \r\n      .title-text {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333333;\r\n      }\r\n    }\r\n    \r\n    .section-content {\r\n      font-size: 28rpx;\r\n      color: #666666;\r\n      line-height: 1.6;\r\n    }\r\n    \r\n    .notice-content {\r\n      .notice-item {\r\n        margin-bottom: 30rpx;\r\n        \r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n        \r\n        .notice-title {\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #333333;\r\n          margin-bottom: 10rpx;\r\n          display: block;\r\n        }\r\n        \r\n        .notice-list {\r\n          .notice-text {\r\n            font-size: 26rpx;\r\n            color: #666666;\r\n            line-height: 1.6;\r\n            display: block;\r\n            margin-bottom: 8rpx;\r\n            \r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.contacts-section {\r\n  background-color: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  \r\n  .section-title {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .title-icon {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      margin-right: 10rpx;\r\n    }\r\n    \r\n    .title-text {\r\n      font-size: 32rpx;\r\n      font-weight: 600;\r\n      color: #333333;\r\n    }\r\n  }\r\n  \r\n  .contacts-list {\r\n    .contact-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 20rpx 0;\r\n      border-bottom: 1rpx solid #f0f0f0;\r\n      \r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n      \r\n      .contact-info {\r\n        .contact-name {\r\n          font-size: 28rpx;\r\n          color: #333333;\r\n          font-weight: 500;\r\n          margin-bottom: 6rpx;\r\n          display: block;\r\n        }\r\n        \r\n        .contact-id {\r\n          font-size: 24rpx;\r\n          color: #999999;\r\n          display: block;\r\n        }\r\n      }\r\n      \r\n      .contact-select {\r\n        .select-circle {\r\n          width: 40rpx;\r\n          height: 40rpx;\r\n          border-radius: 50%;\r\n          border: 2rpx solid #dddddd;\r\n          position: relative;\r\n          \r\n          &.selected {\r\n            border-color: #1976d2;\r\n            background-color: #1976d2;\r\n            \r\n            &:after {\r\n              content: '';\r\n              position: absolute;\r\n              top: 50%;\r\n              left: 50%;\r\n              transform: translate(-50%, -50%);\r\n              width: 20rpx;\r\n              height: 12rpx;\r\n              border-left: 4rpx solid #ffffff;\r\n              border-bottom: 4rpx solid #ffffff;\r\n              transform: translate(-50%, -60%) rotate(-45deg);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  .no-contacts {\r\n    padding: 40rpx 0;\r\n    text-align: center;\r\n    \r\n    .no-contacts-text {\r\n      font-size: 28rpx;\r\n      color: #999999;\r\n      margin-bottom: 20rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .add-contact-btn {\r\n      display: inline-block;\r\n      background-color: #1976d2;\r\n      color: #ffffff;\r\n      font-size: 28rpx;\r\n      padding: 16rpx 40rpx;\r\n      border-radius: 40rpx;\r\n      border: none;\r\n      \r\n      &::after {\r\n        border: none;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background-color: #ffffff;\r\n  padding: 20rpx 30rpx;\r\n  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n  z-index: 100;\r\n  \r\n  .submit-btn {\r\n    width: 100%;\r\n    height: 88rpx;\r\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n    color: #ffffff;\r\n    font-size: 32rpx;\r\n    font-weight: 500;\r\n    border-radius: 44rpx;\r\n    border: none;\r\n    \r\n    &::after {\r\n      border: none;\r\n    }\r\n    \r\n    &:disabled {\r\n      opacity: 0.6;\r\n    }\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/curriculum/choosecurriculum.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "getImages", "myRequest", "Utils"], "mappings": ";;;;;;AA6HA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV,UAAU,MAAa;AAAA,EACxB;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,UAAU;AAAA,MACV,YAAY,CAAE;AAAA,MACd,cAAc,CAAE;AAAA,MAChB,kBAAkB,CAAE;AAAA,MACpB,cAAc;AAAA,IAChB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,YAAY;AACV,aAAO,KAAK,WAAW,iBAAiB,KACjC,KAAK,iBAAiB,SAAS,KAC/B,CAAC,KAAK;AAAA,IACf;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,QAAI,QAAQ,IAAI;AACd,WAAK,WAAW,QAAQ;AACxB,WAAK,cAAc;AACnB,WAAK,gBAAgB;AAAA,WAChB;AACLA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,OACP;AACD,iBAAW,MAAM;AACfA,sBAAAA,MAAI,aAAa;AAAA,MAClB,GAAE,IAAI;AAAA,IACT;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,WAAAC,QAAS;AAAA;AAAA,IAGT,MAAM,gBAAgB;AACpB,UAAI;AACF,cAAM,WAAW,MAAMC,kBAAU;AAAA,UAC/B,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,IAAI,KAAK;AAAA,UACX;AAAA,SACD;AAED,YAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,gBAAM,OAAO,SAAS,KAAK;AAC3B,eAAK,gBAAgBC,YAAAA,MAAM,WAAW,KAAK,aAAa;AACxD,eAAK,kBAAkBA,YAAAA,MAAM,WAAW,KAAK,eAAe;AAC5D,eAAK,aAAa;AAAA,eACb;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,OAAO,UAAU;AAAA,QACjD;AAAA,MACA,SAAO,OAAO;AACdH,sBAAAA,MAAc,MAAA,SAAA,oDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,WAAW,MAAME,kBAAU;AAAA,UAC/B,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAED,YAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,eAAK,eAAe,SAAS,KAAK,QAAQ,CAAC;AAAA,eACtC;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,OAAO,WAAW;AAAA,QAClD;AAAA,MACA,SAAO,OAAO;AACdF,sBAAAA,MAAA,MAAA,SAAA,oDAAc,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,SAAS,OAAO;AAC5B,YAAM,WAAW,KAAK,iBAAiB,QAAQ,KAAK;AACpD,UAAI,WAAW,IAAI;AACjB,aAAK,iBAAiB,OAAO,UAAU,CAAC;AAAA,aACnC;AACL,aAAK,iBAAiB,KAAK,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI,CAAC,KAAK;AAAW;AAErB,WAAK,eAAe;AAEpB,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAED,cAAM,iBAAiB,KAAK,iBAAiB,IAAI,WAAS;AACxD,iBAAO,KAAK,aAAa,KAAK,EAAE;AAAA,SACjC;AAED,cAAM,WAAW,MAAME,kBAAU;AAAA,UAC/B,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,WAAW,KAAK;AAAA,YAChB,YAAY,eAAe,KAAK,GAAG;AAAA,UACrC;AAAA,SACD;AAEDF,sBAAAA,MAAI,YAAY;AAEhB,YAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,gBAAM,cAAc,SAAS,KAAK;AAClCA,wBAAAA,MAAI,WAAW;AAAA,YACb,KAAK,0DAA0D,WAAW;AAAA,WAC3E;AAAA,eACI;AACL,gBAAM,IAAI,MAAM,SAAS,KAAK,OAAO,MAAM;AAAA,QAC7C;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,oDAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,QAAQ;AACjB,UAAI,CAAC,UAAU,OAAO,SAAS;AAAG,eAAO;AAEzC,aAAO,OAAO,QAAQ,OAAO,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,IAC1D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5RA,GAAG,WAAW,eAAe;"}