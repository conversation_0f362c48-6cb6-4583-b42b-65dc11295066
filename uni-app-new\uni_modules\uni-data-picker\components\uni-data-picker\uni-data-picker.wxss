.uni-data-tree {
    flex: 1;
    font-size: 14px;
    position: relative
}

.error-text {
    color: #dd524d
}

.input-value {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    font-size: 14px;
    height: 35px;
    overflow: hidden;
    padding: 0 5px 0 10px
}

.input-value-border {
    border: 1px solid #e5e5e5;
    border-radius: 5px
}

.selected-area {
    display: flex;
    flex: 1;
    flex-direction: row;
    overflow: hidden
}

.load-more {
    margin-right: auto
}

.selected-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap
}

.selected-item {
    flex-direction: row;
    white-space: nowrap
}

.text-color {
    color: #333
}

.placeholder {
    color: gray;
    font-size: 12px
}

.input-split-line {
    opacity: .5
}

.arrow-area {
    display: flex;
    justify-content: center;
    margin-bottom: 5px;
    margin-left: auto;
    position: relative;
    transform: rotate(-45deg);
    transform-origin: center;
    width: 20px
}

.input-arrow {
    border-bottom: 1px solid #999;
    border-left: 1px solid #999;
    height: 7px;
    width: 7px
}

.uni-data-tree-cover {
    background-color: rgba(0,0,0,.4);
    top: 0;
    z-index: 100
}

.uni-data-tree-cover,.uni-data-tree-dialog {
    bottom: 0;
    display: flex;
    flex-direction: column;
    left: 0;
    position: fixed;
    right: 0
}

.uni-data-tree-dialog {
    background-color: #fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    overflow: hidden;
    top: 20%;
    z-index: 102
}

.dialog-caption {
    display: flex;
    flex-direction: row;
    position: relative
}

.title-area {
    align-items: center;
    display: flex;
    margin: auto;
    padding: 0 10px
}

.dialog-title {
    line-height: 44px
}

.dialog-close {
    align-items: center;
    bottom: 0;
    display: flex;
    flex-direction: row;
    padding: 0 15px;
    position: absolute;
    right: 0;
    top: 0
}

.dialog-close-plus {
    background-color: #666;
    border-radius: 2px;
    height: 2px;
    transform: rotate(45deg);
    width: 16px
}

.dialog-close-rotate {
    position: absolute;
    transform: rotate(-45deg)
}

.picker-view {
    flex: 1;
    overflow: hidden
}

.icon-clear {
    align-items: center;
    display: flex
}

.uni-popper__arrow,.uni-popper__arrow:after {
    border: 6px solid transparent;
    display: block;
    height: 0;
    position: absolute;
    width: 0
}

.uni-popper__arrow {
    border-bottom-color: #ebeef5;
    border-top-width: 0;
    filter: drop-shadow(0 2px 12px rgba(0,0,0,.03));
    left: 10%;
    margin-right: 3px;
    top: -6px
}

.uni-popper__arrow:after {
    border-bottom-color: #fff;
    border-top-width: 0;
    content: " ";
    margin-left: -6px;
    top: 1px
}
