var e = require("../../../../common/vendor.js"),
  t = require("./util.js"),
  o = new Set,
  r = null;
e.index.onNeedPrivacyAuthorization && e.index.onNeedPrivacyAuthorization((function(e) {
  "function" == typeof r && r(e)
}));
var n = {
  name: "wsWxPrivacy",
  emits: ["disagree", "agree"],
  props: {
    title: {
      type: String,
      default: "用户隐私保护提示"
    },
    desc: {
      type: String,
      default: "感谢您使用本应用，您使用本应用的服务之前请仔细阅读并同意"
    },
    protocol: {
      type: String,
      default: "《用户隐私保护指引》"
    },
    enableAutoProtocol: {
      type: Boolean,
      default: !1
    },
    subDesc: {
      type: String,
      default: "。当您点击同意并继续使用产品服务时，即表示你已理解并同意该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用相应服务。"
    },
    disagreeEnabled: {
      type: Boolean,
      default: !0
    },
    disagreePromptText: {
      type: String,
      default: "请先仔细阅读并同意隐私协议"
    },
    disagreeText: {
      type: String,
      default: "不同意"
    },
    agreeText: {
      type: String,
      default: "同意并继续"
    },
    bgColor: {
      type: String,
      default: ""
    },
    themeColor: {
      type: String,
      default: ""
    }
  },
  data: function() {
    return {
      privacyContractName: ""
    }
  },
  computed: {
    rootStyle: function() {
      return this.bgColor ? "background:".concat(this.bgColor) : ""
    },
    protocolStyle: function() {
      return this.themeColor ? "color:".concat(this.themeColor) : ""
    },
    agreeStyle: function() {
      return this.themeColor ? "background:".concat(this.themeColor) : ""
    }
  },
  created: function() {
    var n = this;
    r = function(e) {
      var r = t.getContext(),
        n = t.getComponent(r, "#privacy-popup");
      if (n) {
        var a = t.getComponent(n, "#privacy");
        a && a.open && a.open()
      }
      o.add(e)
    }, this.enableAutoProtocol && e.index.getPrivacySetting && e.index.getPrivacySetting({
      success: function(e) {
        e.privacyContractName && (n.privacyContractName = e.privacyContractName)
      },
      fail: function() {},
      complete: function() {}
    })
  },
  methods: {
    openPrivacyContract: function() {
      e.wx$1.openPrivacyContract({
        success: function(e) {
          console.log("openPrivacyContract success")
        },
        fail: function(e) {
          console.error("openPrivacyContract fail", e)
        }
      })
    },
    handleDisagree: function() {
      this.disagreeEnabled ? (this.$refs.privacyPopup.close(), o.forEach((function(e) {
        e({
          event: "disagree"
        })
      })), o.clear(), this.$emit("disagree")) : e.index.showToast({
        icon: "none",
        title: this.disagreePromptText
      })
    },
    handleAgree: function() {
      this.$refs.privacyPopup.close(), o.forEach((function(e) {
        e({
          event: "agree",
          buttonId: "agree-btn"
        })
      })), o.clear(), this.$emit("agree")
    }
  }
};
Array || e.resolveComponent("uni-popup")();
Math;
var a = e._export_sfc(n, [
  ["render", function(t, o, r, n, a, i) {
    return {
      a: e.t(r.title),
      b: e.t(r.desc),
      c: e.t(a.privacyContractName || r.protocol),
      d: e.s(i.protocolStyle),
      e: e.o((function() {
        return i.openPrivacyContract && i.openPrivacyContract.apply(i, arguments)
      })),
      f: e.t(r.subDesc),
      g: e.t(r.agreeText),
      h: e.s(i.agreeStyle),
      i: e.o((function() {
        return i.handleAgree && i.handleAgree.apply(i, arguments)
      })),
      j: e.t(r.disagreeText),
      k: e.o((function() {
        return i.handleDisagree && i.handleDisagree.apply(i, arguments)
      })),
      l: e.s(i.rootStyle),
      m: e.sr("privacyPopup", "5705b407-0"),
      n: e.p({
        id: "privacy",
        type: "center",
        maskClick: !1
      })
    }
  }],
  ["__scopeId", "data-v-5705b407"]
]);
wx.createComponent(a);