<?php

namespace app\admin\model;

use think\Model;


class UserBlack extends Model
{
    // 表名
    protected $table = 'film_black_list';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [];


    /**
     * 定义一对多关联：获取该黑名单用户的记录列表
     */
    public function filmBlackRecordList()
    {
        return $this->hasMany(FilmBlackRecord::class, 'user_id', 'user_id')
            // ->where('del_flag', '0')
            ->order('create_time', 'desc');
    }
}
