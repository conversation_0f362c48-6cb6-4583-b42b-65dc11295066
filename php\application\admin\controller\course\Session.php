<?php

namespace app\admin\controller\course;

use app\common\controller\Backend;

/**
 * 课程场次管理
 *
 * @icon fa fa-circle-o
 */
class Session extends Backend
{

    /**
     * Session模型对象
     * @var \app\admin\model\course\Session
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\course\Session;
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 按日期查询课程场次
     * URL示例: /admin/session/list?queryDate=2025-07-20
     */
    public function list()
    {
        $queryDate = $this->request->get("queryDate");

        if (!$queryDate) {
            return json(['code' => 0, 'msg' => '缺少参数 queryDate']);
        }
        // 强制标准化为 YYYY-MM-DD 格式
        $queryDate = date('Y-m-d', strtotime($queryDate));
        // ① 解析日期（只保留 yyyy-MM-dd）
        $inputDate = strtotime(date('Y-m-d', strtotime($queryDate)));
        $today = strtotime(date('Y-m-d'));

        // ② 如果是过去的日期，返回空数组
        if ($inputDate < $today) {
            return json(['code' => 0, 'msg' => '不能查询过去的时间', 'data' => []]);
        }


        // ③ 查询是否闭馆（holidays 表中 is_close = 0 表示闭馆）
        $holidays_model = new \app\common\model\Holidays;
        $isClose = $holidays_model
            ->whereRaw("DATE_FORMAT(holidays_date, '%Y-%m-%d') = :date", ['date' => $queryDate])
            ->where('is_close', 0)
            ->find();

        if ($isClose) {
            return json(['code' => 0, 'msg' => '闭馆日', 'data' => []]);
        }


        // 查询数据库
        $list = $this->model
            ->whereRaw("DATE_FORMAT(course_start_time, '%Y-%m-%d') = :date", ['date' => $queryDate])
            ->where('del_flag', 0)
            ->order('course_start_time asc')
            ->select();

        // 格式化输出
        $rows = [];
        foreach ($list as $item) {
            $rows[] = [
                'id'              => $item['id'],
                'courseCover'     => $item['course_cover'],
                'courseName'      => $item['course_name'],
                'courseAgeProp'   => $item['course_age_prop'],
                'coursePoll'      => $item['course_poll'],
                'courseState'     => $item['course_state'], // 是否开放预约
                'courseStartTime' => $item['course_start_time'],
                'courseEndTime'   => $item['course_end_time'],
                'inventoryVotes'  => $item['inventory_votes'],
            ];
        }

        return json([
            'code'  => 200,
            'msg'   => '查询成功',
            'total' => count($rows),
            'data'  => $rows,
        ]);
    }


    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");

            if ($params) {
                // 添加额外字段
                $params['del_flag']     = 0; // 逻辑有效
                $params['course_state'] = 0; // 启用预约
                $params['inventory_votes'] =  $params['course_poll'];

                // 拼接日期和时间（前端只提交了时分）
                $queryDate = $this->request->get("selectedDate");
                if ($queryDate) {
                    $params['course_start_time'] = $queryDate . ' ' . $params['course_start_time'] . ':00';
                    $params['course_end_time']   = $queryDate . ' ' . $params['course_end_time'] . ':00';
                    if (!$this->validateTimeParams($params['course_start_time'], $params['course_end_time'])) {
                        $this->error('时间参数非法');
                    }
                }


                $id = $this->model->save($params);
                $this->success("添加成功");
            }

            $this->error("提交数据无效");
        }


        // 渲染表单视图
        $selectedDate = $this->request->get("selectedDate");
        $this->assign('selectedDate', $selectedDate);
        return $this->view->fetch();
    }





    //
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error("记录未找到");
        }




        if ($this->request->isPost()) {


            if ($row['inventory_votes'] !=  $row['course_poll']) {
                $this->error("课程已有人预约,无法修改");
            }

            $params = $this->request->post("row/a");
            if ($params) {
                // 拼接时间字段（和添加一样）
                $queryDate = date('Y-m-d', strtotime($row['course_start_time']));
                $params['course_start_time'] = $queryDate . ' ' . $params['course_start_time'] . ':00';
                $params['course_end_time']   = $queryDate . ' ' . $params['course_end_time'] . ':00';
                if (!$this->validateTimeParams($params['course_start_time'], $params['course_end_time'])) {
                    $this->error('时间参数非法');
                }

                $params['inventory_votes'] =  $params['course_poll'];

                $row->save($params);
                $this->success("修改成功");
            }

            $this->error("提交数据无效");
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    public function del($ids = "")
    {
        $ids = $ids ?: $this->request->post("ids");
        if (!$ids) {
            $this->error("参数错误");
        }

        $row = $this->model->get($ids);
        if (!$row) {
            $this->error("记录不存在");
        }

        $row->del_flag = 2;
        $row->save();

        $this->success("删除成功");
    }
    public function set_state()
    {
        $id = $this->request->post('id');
        $state = $this->request->post('state');

        if (!$id || !in_array($state, ['0', '1'])) {
            $this->error('参数错误');
        }

        $row = $this->model->get($id);
        if (!$row) {
            $this->error('课程未找到');
        }


        $row->course_state = $state;
        $row->save();
        $msg = $state === '1' ? '开启预约成功' : '停止预约成功';
        $this->success($msg);
    }

    public function import()
    {


        if ($this->request->isPost()) {
            $params = $this->request->post("rows/a");

            if ($params) {

                // 拼接日期和时间（前端只提交了时分）
                $queryDate = $this->request->get("selectedDate");


                $rows = [];
                foreach ($params as $row) {


                    $row['course_start_time'] = $queryDate . ' ' . $row['course_start_time'] . ':00';
                    $row['course_end_time']   = $queryDate . ' ' . $row['course_end_time'] . ':00';

                    if (!$this->validateTimeParams($row['course_start_time'], $row['course_end_time'])) {
                        $this->error('时间参数非法');
                    }

                    $rows[] = [
                        'course_name'      => $row['course_name'] ?? '',
                        'course_introduce'      => $row['course_introduce'] ?? '',
                        'course_age_prop'  => $row['course_age_prop'] ?? '',
                        'course_address'   => $row['course_address'] ?? '',

                        'course_poll'      => $row['course_poll'] ?? 0,
                        'inventory_votes'      => $row['course_poll'] ?? 0,

                        'course_start_time' =>   $row['course_start_time'],
                        'course_end_time'  =>    $row['course_end_time'],
                        'course_state'     => 0,
                        'del_flag'         => 0,
                    ];
                }
                // print_r($rows);die;
                $this->model->saveAll($rows);
                $this->success("成功导入 " . count($rows) . " 条数据");
            }

            $this->error("提交数据无效");
        }


        // 渲染表单视图
        $selectedDate = $this->request->get("selectedDate");
        $this->assign('selectedDate', $selectedDate);
        return $this->view->fetch();
    }




    /**
     * 获取某天课程统计数据
     * POST: /admin/statistics/course/getCourse
     * 参数: { "date": "2025-07-27" }
     */
    public function getStats()
    {
        $dateStr = $this->request->get("date");

        if (!$dateStr) {
            return json(['code' => 0, 'msg' => '缺少参数: date']);
        }

        $date = date("Ymd", strtotime($dateStr));

        $list =  \app\admin\model\course\Session::alias('c')
            ->field("
                c.id,
                c.course_name as courseName,
                c.course_start_time as courseStartTime,
                c.course_end_time as courseEndTime,
                c.course_poll as coursePoll,
                c.inventory_votes as inventoryVotes,
                (
                    select count(*) 
                    from course_subscribe 
                    where course_session_id = c.id and del_flag = 0
                ) as register,
                (
                    select count(*) 
                    from course_subscribe 
                    where course_session_id = c.id and del_flag = 0 and sign_state = 1
                ) as hadSign
            ")
            ->where("c.del_flag", 0)
            ->whereRaw("DATE_FORMAT(c.course_start_time, '%Y%m%d') = ?", [$date])
            ->order("c.course_start_time asc")
            ->select();

        return json(['code' => 1, 'msg' => 'success', 'data' => $list]);
    }




    /**
     * 快速验证课程起止时间合法性（简化版）
     *
     * @param string $start 格式："2025-07-21 10:00:00"
     * @param string $end   格式："2025-07-21 12:00:00"
     * @return bool
     */
    protected function validateTimeParams($start, $end)
    {
        // 转换为时间戳
        $startTime = strtotime($start);
        $endTime   = strtotime($end);

        // 任意一方无效，或转换失败
        if (!$startTime || !$endTime) {
            $this->error('时间格式无法解析');
            return false;
        }

        // 必须为同一天（不跨天）
        if (substr($start, 0, 10) !== substr($end, 0, 10)) {
            $this->error('课程时间必须在同一天内，禁止跨天');
            return false;
        }

        // 必须为开始 < 结束
        if ($startTime >= $endTime) {
            $this->error('课程开始时间不能早于当前时间');
            return false;
        }

        return true;
    }
}
