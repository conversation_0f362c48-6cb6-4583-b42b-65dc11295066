"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "CurriculumScheme",
  data() {
    return {
      curriculumList: [],
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      statusTextList: ["去签到", "已过期(未检票)", "已取消", "去检票", "已完成", "已过期(未签到)", "场次取消"],
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      // 安全区域
      safeAreaBottom: 0
    };
  },
  onLoad() {
    var _a;
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.safeAreaBottom = ((_a = systemInfo.safeAreaInsets) == null ? void 0 : _a.bottom) || 0;
  },
  onShow() {
    this.resetData();
    this.getCurriculumList();
  },
  methods: {
    // 重置数据
    resetData() {
      this.curriculumList = [];
      this.pageNum = 1;
      this.total = 0;
      this.hasMore = true;
    },
    // 获取课程记录列表
    async getCurriculumList() {
      if (this.loading || !this.hasMore)
        return;
      this.loading = true;
      try {
        const res = await this.$myRequest({
          url: "/web/session/showMySign",
          method: "get",
          data: {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        });
        if (res.code === 200) {
          const dataList = res.data.data.rows || [];
          this.total = res.data.data.total || 0;
          const processedList = dataList.map((item) => {
            const startTime = item.courseStartTime;
            const date = startTime.slice(0, 10).replace(/-/g, ".") + "  " + this.weekList[new Date(startTime).getDay()];
            return {
              ...item,
              date,
              courseStartTime: this.formatTime(item.courseStartTime),
              courseEndTime: this.formatTime(item.courseEndTime)
            };
          });
          if (this.pageNum === 1) {
            this.curriculumList = processedList;
          } else {
            this.curriculumList.push(...processedList);
          }
          this.hasMore = this.curriculumList.length < this.total;
        } else {
          throw new Error(res.msg || "获取记录失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/curriculumscheme.vue:187", "获取课程记录失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取记录失败",
          icon: "error"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.pageNum++;
      this.getCurriculumList();
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 获取状态文本
    getStatusText(state) {
      if (state >= 0 && state < this.statusTextList.length) {
        return this.statusTextList[state];
      }
      return "未知状态";
    },
    // 获取状态样式类
    getStatusClass(state) {
      const statusMap = {
        0: "status-pending",
        // 去签到
        1: "status-expired",
        // 已过期(未检票)
        2: "status-cancelled",
        // 已取消
        3: "status-checkin",
        // 去检票
        4: "status-completed",
        // 已完成
        5: "status-expired",
        // 已过期(未签到)
        6: "status-cancelled"
        // 场次取消
      };
      return statusMap[state] || "status-unknown";
    },
    // 是否显示操作按钮
    showActionButtons(state) {
      return state === 1 || state === 4 || this.canCancel(state);
    },
    // 是否可以取消
    canCancel(state) {
      return state === 1 || state === 4;
    },
    // 是否可以查看二维码
    canViewQRCode(state) {
      return state === 1 || state === 4;
    },
    // 查看详情
    viewDetails(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/curriculumsuccess?id=${item.sessionId}`
      });
    },
    // 取消预约
    cancelBooking(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/curriculumcancel?sessionId=${item.sessionId}&batchNumber=${item.batchNumber}`
      });
    },
    // 查看二维码
    viewQRCode(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/curriculumsuccess?id=${item.sessionId}`
      });
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "课程记录",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333"
    }),
    b: $data.curriculumList.length === 0 && !$data.loading
  }, $data.curriculumList.length === 0 && !$data.loading ? {
    c: common_assets._imports_0$2
  } : {}, {
    d: common_vendor.f($data.curriculumList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getStatusText(item.subscribeState)),
        b: common_vendor.n($options.getStatusClass(item.subscribeState)),
        c: common_vendor.t(item.courseName),
        d: common_vendor.t(item.date),
        e: common_vendor.t(item.courseStartTime),
        f: common_vendor.t(item.courseEndTime),
        g: item.courseTeacher
      }, item.courseTeacher ? {
        h: common_vendor.t(item.courseTeacher)
      } : {}, {
        i: item.courseLocation
      }, item.courseLocation ? {
        j: common_vendor.t(item.courseLocation)
      } : {}, {
        k: common_vendor.t(item.subscribeType),
        l: $options.showActionButtons(item.subscribeState)
      }, $options.showActionButtons(item.subscribeState) ? common_vendor.e({
        m: $options.canCancel(item.subscribeState)
      }, $options.canCancel(item.subscribeState) ? {
        n: common_vendor.o(($event) => $options.cancelBooking(item), item.sessionId || index)
      } : {}, {
        o: $options.canViewQRCode(item.subscribeState)
      }, $options.canViewQRCode(item.subscribeState) ? {
        p: common_vendor.o(($event) => $options.viewQRCode(item), item.sessionId || index)
      } : {}) : {}, {
        q: item.sessionId || index,
        r: common_vendor.o(($event) => $options.viewDetails(item), item.sessionId || index)
      });
    }),
    e: $data.loading
  }, $data.loading ? {} : {}, {
    f: !$data.hasMore && $data.curriculumList.length > 0
  }, !$data.hasMore && $data.curriculumList.length > 0 ? {} : {}, {
    g: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    h: $data.safeAreaBottom + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3ca1b3b8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/user/curriculumscheme.js.map
