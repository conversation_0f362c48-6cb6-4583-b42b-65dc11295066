<script>
	import Vue from 'vue'
	export default {
		onLaunch: function() {
			// uni.loadFontFace({
			// 	global: true,
			// 	family: "PingFang SC",
			// 	source: 'url("https://wesalt-ai-digial-dev.oss-cn-shenzhen.aliyuncs.com/font/PINGFANG%20REGULAR.TTF")',
			// 	success() {
			// 		console.log('PingFang SC load success')
			// 	}
			// });

			// uni.loadFontFace({
			// 	global: true,
			// 	family: "YouSheBiaoTiHei",
			// 	source: 'url("https://wesalt-ai-digial-dev.oss-cn-shenzhen.aliyuncs.com/font/YouSheBiaoTiHei.TTF")',
			// 	success() {
			// 		console.log('YouSheBiaoTiHei load success')
			// 	}
			// });
		},
		onShow: function() {
			// console.log('App Show')
		},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style>
	.uni-input {
		font-family: 'PingFang SC' !important;
	}
</style>
