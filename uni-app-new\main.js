import App from './App'
import store from './store'
import { request } from './utils/request'

// 导入全局组件
import MyHeader from './components/my-header/my-header.vue'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 全局配置
Vue.config.productionTip = false

// 注册全局组件
Vue.component('my-header', MyHeader)

// 挂载全局方法
Vue.prototype.$store = store
Vue.prototype.$myRequest = request

App.mpType = 'app'
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 使用store
  app.use(store)

  // 注册全局组件
  app.component('my-header', MyHeader)

  // 全局属性
  app.config.globalProperties.$myRequest = request

  return {
    app
  }
}
// #endif