"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "FilmCancel",
  data() {
    return {
      filmInfo: {},
      contactsList: [],
      filmTypeList: ["球幕电影", "4D电影"],
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      // 参数
      filmSessionId: null,
      batchNumber: null,
      vote: null,
      // 状态
      isCancelling: false
    };
  },
  computed: {
    // 是否有选中的联系人
    hasSelectedContacts() {
      return this.contactsList.some((contact) => contact.linkCheck);
    }
  },
  onLoad(options) {
    this.filmSessionId = options.filmSessionId;
    this.batchNumber = options.batchNumber;
    this.vote = options.vote;
    this.getFilmInfo();
    this.getContactsList();
  },
  methods: {
    // 获取电影信息
    async getFilmInfo() {
      try {
        const res = await this.$myRequest({
          url: "/web/fileSession/personalCenterFilmByFilmSessionId",
          method: "get",
          data: {
            filmSessionId: this.filmSessionId
          }
        });
        if (res.code === 200) {
          this.filmInfo = {
            ...res.data.data,
            filmStartTime: this.formatTime(res.data.data.filmStartTime),
            filmEndTime: this.formatTime(res.data.data.filmEndTime),
            orderNum: res.data.data.filmPoll - res.data.data.inventoryVotes
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/filmcancel.vue:130", "获取电影信息失败:", error);
        common_vendor.index.showToast({
          title: "获取信息失败",
          icon: "error"
        });
      }
    },
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: "/web/fileSession/getFilmSubscribePeoples",
          method: "get",
          data: {
            batchNumber: this.batchNumber
          }
        });
        if (res.code === 200) {
          this.contactsList = (res.data.data || []).map((contact) => ({
            ...contact,
            linkCheck: false
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/filmcancel.vue:156", "获取联系人失败:", error);
        common_vendor.index.showToast({
          title: "获取联系人失败",
          icon: "error"
        });
      }
    },
    // 切换联系人选择状态
    toggleContact(index) {
      this.contactsList[index].linkCheck = !this.contactsList[index].linkCheck;
    },
    // 确认取消预约
    confirmCancel() {
      if (!this.hasSelectedContacts) {
        common_vendor.index.showToast({
          title: "请选择要取消的联系人",
          icon: "error"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认取消",
        content: "确定要取消选中联系人的预约吗？",
        success: (res) => {
          if (res.confirm) {
            this.performCancel();
          }
        }
      });
    },
    // 执行取消操作
    async performCancel() {
      if (this.isCancelling)
        return;
      this.isCancelling = true;
      try {
        const selectedIds = this.contactsList.filter((contact) => contact.linkCheck).map((contact) => contact.linkId);
        const res = await this.$myRequest({
          url: "/web/fileSession/cancelFilmSession",
          method: "get",
          data: {
            vote: this.vote,
            filmSessionId: this.filmSessionId,
            batchNumber: this.batchNumber,
            peopleIds: selectedIds.join(",")
          }
        });
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "取消预约成功",
            icon: "success",
            duration: 2e3
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages_app/user/filmscheme"
            });
          }, 2e3);
        } else {
          throw new Error(res.msg || "取消失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/filmcancel.vue:228", "取消预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      } finally {
        this.isCancelling = false;
      }
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 获取星期
    getWeekDay(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr.replace(/-/g, "/"));
        return this.weekList[date.getDay()];
      } catch (error) {
        return "";
      }
    },
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8)
        return idCard;
      return idCard.replace(idCard.substring(4, 15), "*******");
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      title: "取消预约",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333"
    }),
    b: $data.filmInfo.filmCover,
    c: common_vendor.t($data.filmInfo.filmName),
    d: common_vendor.t($data.filmTypeList[$data.filmInfo.filmType - 1] || "未知"),
    e: common_vendor.t($data.filmInfo.filmStartTime),
    f: common_vendor.t($data.filmInfo.filmEndTime),
    g: common_vendor.t($data.filmInfo.filmArrangedDate),
    h: common_vendor.t($options.getWeekDay($data.filmInfo.filmArrangedDate)),
    i: common_vendor.t($data.filmInfo.orderNum),
    j: common_vendor.f($data.contactsList, (contact, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(contact.linkmanName),
        b: common_vendor.t(contact.linkmanPhone),
        c: common_vendor.t($options.hideIdCard(contact.linkmanCertificate)),
        d: contact.linkCheck
      }, contact.linkCheck ? {} : {}, {
        e: common_vendor.n({
          checked: contact.linkCheck
        }),
        f: contact.linkId,
        g: common_vendor.o(($event) => $options.toggleContact(index), contact.linkId)
      });
    }),
    k: common_vendor.t($data.isCancelling ? "取消中..." : "确认取消预约"),
    l: common_vendor.o((...args) => $options.confirmCancel && $options.confirmCancel(...args)),
    m: !$options.hasSelectedContacts || $data.isCancelling
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-99fb144e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/schemesuccess/filmcancel.js.map
