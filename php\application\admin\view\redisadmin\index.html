<div class="panel panel-default">
    <div class="panel-heading">
        <strong>Redis 键列表</strong>
    </div>
    <div class="panel-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover table-striped">
                <thead class="thead-light">
                    <tr>
                        <th style="width:35%">Key</th>
                        <th style="width:10%">类型</th>
                        <th style="width:10%">TTL</th>
                        <th style="width:20%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="list" id="item"}
                    <tr>
                        <td class="text-break">{$item.key}</td>
                        <td><span class="label label-info">{$item.type}</span></td>
                        <td>{$item.ttl}</td>
                        <td>
                            <a href="{:url('redisadmin/view', ['key' => $item.key])}" class="btn btn-xs btn-primary">查看</a>
                            <!-- <a href="{:url('redisadmin/edit', ['key' => $item.key])}" class="btn btn-xs btn-warning">编辑</a> -->
                            <!-- <button type="button" class="btn btn-xs btn-danger" onclick="delKey('{$item.key}')">删除</button> -->
                        </td>
                    </tr>
                    {/volist}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function delKey(key){
    if(confirm("确认删除 " + key + "?")){
        $.post("{:url('redisadmin/redis/delete')}", {key: key}, function(res){
            alert(res.msg);
            location.reload();
        });
    }
}
</script>
