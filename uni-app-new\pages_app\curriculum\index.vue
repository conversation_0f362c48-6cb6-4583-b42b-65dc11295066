<template>
  <view class="curriculum">
    <!-- 自定义头部 -->
    <my-header 
      v-if="!config.iSzgm"
      :is-back="true"
      :is-show-home="true"
      title="宝安科技馆"
      background="transparent"
      menu-class="df"
    />
    
    <!-- 课程预约主体内容 -->
    <view class="curriculum_order_box">
      <!-- 日历选择区域 -->
      <view class="calendar_content">
        <view class="calendar">
          <uni-calendar
            :date="timeSection.start"
            :insert="true"
            :lunar="false"
            :show-month="false"
            :start-date="timeSection.start"
            :end-date="timeSection.end"
            :selected="selected"
            @change="getSessionDetail"
          />
        </view>
      </view>
      
      <!-- 课程列表区域 -->
      <view class="curriculum_list_content">
        <image 
          class="curriculum_title" 
          mode="aspectFill" 
          src="/static/img/curriculum/curriculum_title.png"
        />
        
        <!-- 课程列表 -->
        <view v-if="curriculumList.length > 0" class="curriculum_list">
          <view 
            v-for="item in curriculumList" 
            :key="item.id"
            :class="['curriculum_item', { 'disNone': !item.isShow }]"
            @tap="chooseCourse(item.id)"
          >
            <view class="itemLeft">
              <image 
                class="cover" 
                mode="aspectFill" 
                :src="getImages(item.courseCover)"
              />
              <view class="curriculumInfo">
                <text class="curriculumName">{{ item.courseName }}</text>
                <text class="curriculumType">适龄：{{ item.courseAgeProp }}</text>
                <text class="curriculumTime">时间：{{ item.courseStartTime }}-{{ item.courseEndTime }}</text>
                <text class="curriculumPlace">地点：{{ item.courseAddress }}</text>
              </view>
            </view>
            <view class="itemRight">
              <view class="ticketType" style="display:none">
                <text class="n_text">倒计时</text>
                <text class="t_text">25:31</text>
              </view>
              <view 
                :class="['order_button', { 'gray': item.inventoryVotes == 0 }]"
              >
                剩余:{{ item.inventoryVotes }}
              </view>
            </view>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view v-else class="curriculum_tip">
          暂无课程数据
        </view>
      </view>
    </view>
    
    <!-- 报名须知弹窗 -->
    <view v-if="isShowMask" class="mask">
      <view class="maskContent">
        <view class="noticeTitle">报名须知</view>
        <view class="noticeView">
          <view 
            v-for="(notice, index) in noticeList" 
            :key="index"
            class="noticeItem"
          >
            <view class="itemTitle">{{ notice.title }}</view>
            <view 
              v-for="(item, itemIndex) in notice.view" 
              :key="itemIndex"
              class="itemContent"
            >
              {{ item }}
            </view>
          </view>
        </view>
        <view 
          :class="['agreeBtn', { 'gray': isShowGray }]"
          @tap="agree"
        >
          {{ readText }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getImages, myRequest } from '../../api/api.js'
import config from '../../config.js'
import Utils from '../../utils/index.js'

export default {
  name: 'CurriculumIndex',
  components: {
    MyHeader: () => import('../../components/my-header/my-header.vue')
  },
  data() {
    return {
      config,
      userInfo: {
        avatar: null,
        nickName: null,
        sex: null,
        phonenumber: null
      },
      isShowMask: true,
      noticeList: [
        {
          title: "一、报名方式",
          view: [
            "1、本次公益培训课程仅接受微信报名，名额有限，先到先得，额满为止",
            "2、课程咨询热线：0755-27880235。",
            "3、公益课开始上课后自动关闭报名入口。",
            "4、学员可通过个人中心-课程预约-查看凭证，管理查询个人预约信息。"
          ]
        },
        {
          title: "二、温馨提示",
          view: [
            "1、学员必须以本人真实信息报名，如现场确认时发现报名信息不符合则取消资格",
            "2、课程期间，请学员严格遵守上课时间，为保证教学质量迟到超过10分钟则不能进入教室学习。"
          ]
        }
      ],
      selected: [],
      times: null,
      readText: "确定已阅读（5s）",
      readTime: 5,
      timeSection: {
        start: "",
        end: ""
      },
      curriculumList: [],
      isShowGray: false,
      fulldate: null
    }
  },
  computed: {
    // 兼容原有的config计算属性
    configComputed() {
      return config
    }
  },
  created() {
    this.getTimeSection()
    this.showMask()
  },
  onShow() {
    const token = uni.getStorageSync('token')
    if (token) {
      this.getDayInfo()
    } else {
      this.showLoginModal()
    }
  },
  methods: {
    getImages,
    
    // 获取时间区间
    getTimeSection() {
      const now = new Date()
      const start = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate()
      const endDate = new Date(now.getTime() + 6 * 24 * 60 * 60 * 1000) // 6天后
      const end = endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate()
      
      this.timeSection.start = start
      this.timeSection.end = end
    },
    
    // 同意须知
    agree() {
      if (this.readTime === 1) {
        setTimeout(() => {
          this.isShowMask = false
        }, 300)
      }
    },
    
    // 显示须知弹窗
    showMask() {
      // #ifdef MP-WEIXIN
      const accountInfo = uni.getAccountInfoSync()
      this.isShowMask = accountInfo.miniProgram.envVersion !== 'develop'
      // #endif
      
      // #ifndef MP-WEIXIN
      this.isShowMask = true
      // #endif
      
      this.isShowGray = true
      this.readTime = 5
      this.times = setInterval(() => {
        if (this.readTime === 1) {
          this.readText = "确定已阅读"
          this.isShowGray = false
          clearInterval(this.times)
        } else {
          this.readTime--
          this.readText = `确定已阅读（${this.readTime}s）`
        }
      }, 1000)
    },
    
    // 选择课程
    chooseCourse(courseId) {
      uni.navigateTo({
        url: `/pages_app/curriculum/choosecurriculum?id=${courseId}`
      })
    },
    
    // 显示登录弹窗
    showLoginModal() {
      uni.showModal({
        title: "温馨提示",
        content: "授权微信登录后才能正常使用小程序功能",
        success: (res) => {
          if (res.cancel) {
            uni.showToast({
              title: "您拒绝了请求，不能正常使用小程序",
              icon: "error",
              duration: 2000
            })
          } else {
            this.performLogin()
          }
        }
      })
    },
    
    // 执行登录
    performLogin() {
      // #ifdef MP-WEIXIN
      uni.login({
        provider: "weixin",
        success: (res) => {
          const requestData = {
            data: { code: res.code },
            url: `/wx/user/${config.appId}/login`,
            method: "get"
          }
          
          myRequest(requestData).then((response) => {
            uni.hideLoading()
            uni.showToast({ title: "登录成功" })
            
            const user = response.data.data.user
            uni.setStorageSync("token", response.data.data.token)
            uni.setStorageSync("userInfo", user)
            
            this.userInfo = user || {
              avatar: null,
              nickName: null,
              sex: null,
              phonenumber: null
            }
            
            this.getDayInfo()
          }).catch((error) => {
            console.log("登录异常:", error)
            uni.showToast({
              title: "登录异常:" + error.errMsg,
              icon: "error",
              duration: 5000
            })
          })
        }
      })
      // #endif
      
      // #ifndef MP-WEIXIN
      // 非微信小程序平台的登录处理
      uni.navigateTo({
        url: '/pages_app/login/index'
      })
      // #endif
    },
    
    // 获取场馆开放信息
    async getDayInfo() {
      try {
        const response = await myRequest({
          url: "/auth/venue/getVenueInfo"
        })
        
        if (response.data.code === 200) {
          const data = response.data.data
          data.forEach(item => {
            const date = Utils.changeTime(item.day, true)
            if (item.isClose === "0") {
              this.selected.push({
                date: date,
                info: "闭馆"
              })
            }
          })
          
          // 找到第一个开放日
          for (let i = 0; i < data.length; i++) {
            if (data[i].isClose === "1") {
              this.timeSection.start = Utils.changeTime(data[i].day, true)
              break
            }
          }
          
          if (this.timeSection.start) {
            this.getSessionDetail({
              fulldate: this.fulldate || this.timeSection.start
            })
          }
        }
      } catch (error) {
        console.error('获取场馆信息失败:', error)
      }
    },
    
    // 获取课程详情
    getSessionDetail(event) {
      myRequest({
        url: "/web/session/getSessionDetail",
        method: "post",
        data: {
          date: event.fulldate
        }
      }).then((response) => {
        if (response.data.code === 200) {
          this.fulldate = event.fulldate
          const data = response.data.data
          const now = new Date()
          
          data.forEach(item => {
            item.isShow = new Date(item.courseStartTime) > now
            item.courseEndTime = Utils.changeTime(item.courseEndTime)
            item.courseStartTime = Utils.changeTime(item.courseStartTime)
          })
          
          this.curriculumList = data.filter(item => item.isShow)
        }
      }).catch((error) => {
        console.error('获取课程详情失败:', error)
        uni.showToast({
          title: '获取课程信息失败',
          icon: 'error'
        })
      })
    }
  }
}
</script>
<style sco
ped>
.curriculum {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.curriculum_order_box {
  padding-top: 20rpx;
}

.calendar_content {
  background-color: #ffffff;
  margin: 0 30rpx 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.calendar {
  padding: 20rpx;
}

.curriculum_list_content {
  background-color: #ffffff;
  margin: 0 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}

.curriculum_title {
  width: 100%;
  height: 80rpx;
  margin-bottom: 30rpx;
}

.curriculum_list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.curriculum_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}

.curriculum_item:active {
  background-color: #e9ecef;
}

.curriculum_item.disNone {
  display: none;
}

.itemLeft {
  display: flex;
  flex: 1;
  align-items: center;
}

.cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 30rpx;
  object-fit: cover;
}

.curriculumInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8rpx;
}

.curriculumName {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}

.curriculumType,
.curriculumTime,
.curriculumPlace {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.3;
}

.itemRight {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}

.ticketType {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.n_text {
  font-size: 22rpx;
  color: #999999;
}

.t_text {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 600;
}

.order_button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.order_button.gray {
  background: #cccccc;
  color: #999999;
}

.curriculum_tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  padding: 100rpx 0;
}

/* 弹窗样式 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.maskContent {
  background-color: #ffffff;
  width: 600rpx;
  max-height: 80vh;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}

.noticeTitle {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.noticeView {
  flex: 1;
  max-height: 500rpx;
  overflow-y: auto;
  margin-bottom: 30rpx;
}

.noticeItem {
  margin-bottom: 30rpx;
}

.itemTitle {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 15rpx;
}

.itemContent {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}

.agreeBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 25rpx;
  border-radius: 15rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.agreeBtn.gray {
  background: #cccccc;
  color: #999999;
}

.agreeBtn:active {
  transform: scale(0.98);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .curriculum_item {
    padding: 25rpx;
  }
  
  .cover {
    width: 100rpx;
    height: 100rpx;
    margin-right: 25rpx;
  }
  
  .curriculumName {
    font-size: 30rpx;
  }
  
  .curriculumType,
  .curriculumTime,
  .curriculumPlace {
    font-size: 24rpx;
  }
}
</style>