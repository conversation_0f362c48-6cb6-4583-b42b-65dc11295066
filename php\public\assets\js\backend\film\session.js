define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'moment',
    '/assets/libs/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js'
], function ($, undefined, Backend, Table, Form, moment) {

    var Controller = {
        index: function () {
            Controller.api.bindevent();
            Controller.api.initWeekPicker('#c-week_start');
            Controller.api.initWeekPicker('#c-week_dend');    
            //添加影片排期
            $('.card-add').on('click', function () {
                const selectedDate = $(this).closest('.panel-body').attr('data-date'); // 获取当天日期
                Fast.api.open(
                    'film/base/index?select=1&date=' + selectedDate, // select=1 用于告诉页面是“选择模式”
                    '选择影片添加排期',
                    {
                        area: ['90%', '90%'],
                        callback: function (data) {
                     
                            let year = $('#c-week_start').data('year'); // ISO 年份
                            let week = $('#c-week_start').data('week');     // ISO 
                            console.log('选中的影片:', data);
                            console.log('刷新周',year,week);
                            Controller.api.loadScheduleByWeek(year, week);
                            
                        }
                    }
                );
            });
              
            //删除
            $(document).on('click', '.btn-delete', function () {
                const id = $(this).data('id');
            
                Layer.confirm('确定要删除该排期吗？', {icon: 3, title: '提示'}, function (index) {
                    Fast.api.ajax({
                        url: 'film/session/del/ids/' + id,
                        method: 'POST'
                    }, function (res) {
                        Layer.msg('删除成功');
            
                        // 删除卡片元素
                        $('.film-card[data-id="' + id + '"]').remove();
            

            
                        Layer.close(index);
                        return false;
                    });
                });
            });
            // 白名单+ 库存
            $(document).on('click', '.btn-whitelist', function () {
                const id = $(this).data('id');
                Controller.api.openWhitelistDialog(id);
            });
            //常用方案拷贝
            $('#btn-copy-template').on('click', function () {
                // 只有启用状态才允许点击
                if ($(this).prop('disabled')) return;
            
                // 取出周一日期对应的时间 + 年 + 周
                let sourceStart = $('#c-week_start').data('start');
                let sourceYear = $('#c-week_start').data('year');
                let sourceWeek = $('#c-week_start').data('week');
            
                let targetStart = $('#c-week_dend').data('start');
                let targetYear = $('#c-week_dend').data('year');
                let targetWeek = $('#c-week_dend').data('week');
            
                if (!sourceStart || !targetStart) {
                    Layer.msg('请先选择两个完整的周时间');
                    return;
                }
            
                // 弹出确认提示
                Layer.confirm(`确定将【${sourceStart} 第${sourceWeek}周】的排片复制到【${targetStart} 第${targetWeek}周】吗？`, {
                    icon: 3,
                    title: '确认拷贝排片方案'
                }, function (index) {
                    Layer.close(index);
            
                    //  发起请求
                    Fast.api.ajax({
                        url: 'film/session/sessionCopy',
                        method: 'POST',
                        data: {
                            source_year: sourceYear,
                            source_week: sourceWeek,
                            target_year: targetYear,
                            target_week: targetWeek
                        }
                    }, function () {
                    // console.log(res);
                    

                        // Layer.close(index);
               
                        // Layer.msg('排片拷贝失败：' + err.msg);
                        // return false;
                    });
                });
            });
            
            
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            initWeekPicker: function (selector) {
                let lastPickedWeek = null;  // 记录上一次选中的 year-week
                $(selector).datetimepicker({
                    format: 'YYYY-MM-DD',
                    locale: 'zh-cn',
                    dayViewHeaderFormat: 'YYYY年 MMMM',
                    showTodayButton: true,
                    showClear: false,
                    showClose: true
                }).on("dp.change", function (e) {
                    let selected = moment(e.date);
                    let startOfWeek = selected.clone().startOf('isoWeek'); // 周一
                    let week = selected.isoWeek();
                    let year = selected.isoWeekYear();
                    let formatted = `${startOfWeek.format('YYYY-MM-DD')} 第${week}周`;

                    $(this).val(formatted);
                    // 可选：存入隐藏域或 data 属性
                    $(this).data('start', startOfWeek.format('YYYY-MM-DD')); // ISO 年月日
                    $(this).data('year', year); // ISO 年份
                    $(this).data('week', week);     // ISO 周数

                      // 只有当选择了与上次不同的 week，才请求 排期数据                
                    if(selector == '#c-week_start'){            
                        const currentWeek = `${year}-${week}`;
                        console.log(currentWeek,lastPickedWeek);                  
                        if (currentWeek == lastPickedWeek) {
                            return; // 未变化，或者首次点击没选日期，直接返回
                        }
                        console.log('切换周',year,week);
                        lastPickedWeek = currentWeek;
                        Controller.api.loadScheduleByWeek(year, week);    
                    }
                    
           
                         //  检查两个时间字段，并比较顺序，
                        let weekStart = $('#c-week_start').data('start');
                        let weekEnd = $('#c-week_dend').data('start');

                        let mStart = moment(weekStart, 'YYYY-MM-DD');
                        let mEnd = moment(weekEnd, 'YYYY-MM-DD');
                        

                        if (mEnd.isAfter(mStart)) {
                                // 目标时间在排片时间之后 
                            $('#btn-copy-template').prop('disabled', false); // 启用
                        } else {
                            $('#btn-copy-template').prop('disabled', true);  // 禁用
                        }
                
                            

                });

                // 只有“排片日期：”才有  默认值：今天所在周 ， 
                if(selector == '#c-week_start'){   
                    let now = moment();
                    let start = now.clone().startOf('isoWeek');
                    let weekStr = `${start.format('YYYY-MM-DD')} 第${now.isoWeek()}周`;
                    let year = now.isoWeekYear();
                    let week = now.isoWeek();
                    $(selector).val(weekStr)
                    $(selector).data('start', start.format('YYYY-MM-DD')); // ISO 年月日
                    $(selector).data('year', year); // ISO 年份
                    $(selector).data('week', week);     // ISO 

             
                    lastPickedWeek = `${year}-${week}`;
                    console.log('初始化周',year,week);
                    Controller.api.loadScheduleByWeek(year, week);

                }

            },            
            loadScheduleByWeek: function (year, week) {
                // 计算该周的起始日期
                let startOfWeek = moment().year(year).isoWeek(week).startOf('isoWeek');
            
                // 清空旧数据
                $(".schedule-day").each(function (i) {
                    const dayOffset = parseInt($(this).data('day')) - 1;
                    const currentDate = startOfWeek.clone().add(dayOffset, 'days');
                    const dateStr = currentDate.format('YYYY-MM-DD');
                    const label = currentDate.format('YYYY-MM-DD');
            
                    // 设置日期 label 和 data-date
                    $(this).find('.date-label').text(label);
                    $(this).find('.panel-body').attr('data-date', dateStr);
                    $(this).find('.schedule-cards').empty();
                });
            
                // 第二步：请求接口加载数据
                Fast.api.ajax({
                    url: 'film/session/schedule',
                    data: { year: year, week: week },
                }, function (res) {                 
                    const data = res || {};
                    // 遍历返回数据，插入卡片到对应列
                    Object.keys(data).forEach(function (dateStr) {
                        const list = data[dateStr];
                        const $targetCol = $('.schedule-day .panel-body[data-date="' + dateStr + '"]').find('.schedule-cards');

                        list.forEach(function (item) {
                            const cardHtml = `
                                <div class="card film-card" data-id="${item.id}" style="border:1px solid #eee;margin-bottom:10px;padding:10px;">
                                    <div class="row">
                                        <div class="col-xs-4">
                                            <img src="${item.film.film_cover || ''}" style="width:75px;border-radius:4px;" />
                                        </div>
                                        <div class="col-xs-8">
                                            <div><strong>${item.film.film_name || '未命名影片'}</strong></div>
                                            <div>类型：${item.film.film_type_text}</div>
                                            <div>时间：${moment(item.film_start_time).format('HH:mm')} ~ ${moment(item.film_end_time).format('HH:mm')}</div>

                                        </div>
                                    </div>
                                    <div class="row2" style="font-size:14px;margin: 8px 0;">
                                        剩余预约票数：<span class="text-success" style="font-weight:bold;">${item.inventory_votes} 张</span>　
                                        总票数：<span class="text-warning" style="font-weight:bold;">${item.film_poll} 张</span>
                                    </div>
                                    <div class="btn-group btn-group-sm" style="width:100%;">
                                        <button class="btn btn-primary btn-whitelist" data-id="${item.id}" style="width:48%;">操作</button>
                                        <button class="btn btn-danger btn-delete" data-id="${item.id}" style="width:48%;margin-left:4%;">删除</button>
                                    </div>
                                </div>`;
                            $targetCol.append(cardHtml);
                        });
                    });
                    return false;
                });
            },
            openWhitelistDialog:function(sessionId) {
                // 取原卡片 DOM（用于展示）
                var $card = $('.film-card[data-id="' + sessionId + '"]');
                var $rowInfo_tmp = $card.find('.row').clone(); // 只复制影片信息部分（.row）   
                var $rowInfo_tmp2 = $card.find('.row2').clone(); // 只复制影片信息部分（.row）
                
                $rowInfo = $('<div class="card film-card" style="border:1px solid #eee;margin-bottom:10px;padding:10px;"></div>');
                $rowInfo.append($rowInfo_tmp);
                $rowInfo.append($rowInfo_tmp2);

                // 构造弹窗 HTML
                var dialogHtml = $('<div class="whitelist-popup" style="margin: 15px 15px;"></div>');
                dialogHtml.append($rowInfo);
            
                // 添加操作区域（row2）

                var $actionHtml = $(`
                    <div class="row2" style="margin-top:15px;">                   
                        <div>
                            <div style="margin-bottom:10px;">
                                <label>添加白名单：</label>
                                <div style="font-size: 14px;text-align: center;margin-bottom: 22px;line-height: 30px;background-color: #f1f1f1;">ps:添加白名单，系统将自动占用该场次名额</div>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="whitelist-phone" placeholder="请输入手机号">
                                    <span class="input-group-btn">
                                        <button class="btn btn-default btn-count" data-count="1">1张</button>
                                        <button class="btn btn-default btn-count" data-count="2">2张</button>
                                        <button class="btn btn-default btn-count" data-count="3">3张</button>
                                    </span>
                                </div>
                                <div class="text-center" style="margin-top:10px;">
                                    <button class="btn btn-primary" id="btn-add-whitelist">添加</button>
                                </div>
                            </div>
                            <hr style="margin:15px 0; border-color:#eee;">
                            <label>库存操作：</label>
                            <div class="btn-group" data-toggle="buttons">
                                <label class="btn btn-default active"><input type="radio" name="stock-op" value="none" checked> 无改变</label>
                                <label class="btn btn-default"><input type="radio" name="stock-op" value="inc"> 增加</label>
                                <label class="btn btn-default"><input type="radio" name="stock-op" value="dec"> 减少</label>
                            </div>
                            <input type="number" class="form-control" id="stock-value" placeholder="请输入修改数量" style="margin-top:10px;">
                            <div class="text-center" style="margin-top:10px;">
                                <button class="btn btn-primary" id="btn-submit-update">操作</button>
                            </div>
                        </div>
                    </div>
                `);
                dialogHtml.append($actionHtml);
            
                // 打开弹窗
                Layer.open({
                    type: 1,
                    area: ['550px', 'auto'],
                    title: '修改影片排期',
                    content: dialogHtml.prop('outerHTML'),
                    success: function (layero, index) {
                        // 切换张数按钮
                        $(layero).on('click', '.btn-count', function () {
                            $(layero).find('.btn-count').removeClass('btn-primary').addClass('btn-default');
                            $(this).addClass('btn-primary').removeClass('btn-default');
                        });
            
                        // 添加白名单
                        $(layero).on('click', '#btn-add-whitelist', function () {
                            const phone = $(layero).find('#whitelist-phone').val();
                            const count = $(layero).find('.btn-count.btn-primary').data('count');
            
                            if (!/^1[3-9]\d{9}$/.test(phone)) {
                                Layer.msg('请输入正确手机号');
                                return;
                            }
            
                            Fast.api.ajax({
                                url: 'film/session/add_whitelist',
                                method: 'POST',
                                data: {
                                    session_id: sessionId,
                                    phone: phone,
                                    count: count
                                }
                            }, function () {
                                // Layer.msg('添加成功');
                                Layer.close(index);
                                let year = $('#c-week_start').data('year'); // ISO 年份
                                let week = $('#c-week_start').data('week');     // ISO 
                                console.log('刷新周',year,week);
                                Controller.api.loadScheduleByWeek(year, week);
                            });
                        });
            
                        // 提交库存修改
                        $(layero).on('click', '#btn-submit-update', function () {
                            const op = $(layero).find('input[name=stock-op]:checked').val();
                            const value = parseInt($(layero).find('#stock-value').val() || 0);
            
                            if (op == 'none') {
                                Layer.msg('请选择增加、减少操作');
                                return;
                            }
                            if (!value) {
                                Layer.msg('请输入修改数量');
                                return;
                            }
            
                            Fast.api.ajax({
                                url: 'film/session/update_inventory',
                                method: 'POST',
                                data: {
                                    session_id: sessionId,
                                    op: op,
                                    value: value
                                }
                            }, function () {
                                // Layer.msg('库存修改成功');
                                Layer.close(index);
                                let year = $('#c-week_start').data('year'); // ISO 年份
                                let week = $('#c-week_start').data('week');     // ISO 
                                console.log('刷新周',year,week);
                                Controller.api.loadScheduleByWeek(year, week);
                            });
                        });
                    }
                });
            }

            
            

        }
    };

    return Controller;
});
