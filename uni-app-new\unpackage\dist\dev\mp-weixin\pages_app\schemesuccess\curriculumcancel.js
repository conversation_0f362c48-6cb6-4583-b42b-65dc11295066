"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "CurriculumCancel",
  data() {
    return {
      courseInfo: {},
      contactsList: [],
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      // 参数
      sessionId: null,
      batchNumber: null,
      // 状态
      isCancelling: false
    };
  },
  computed: {
    // 是否有选中的联系人
    hasSelectedContacts() {
      return this.contactsList.some((contact) => contact.linkCheck);
    }
  },
  onLoad(options) {
    this.sessionId = options.sessionId;
    this.batchNumber = options.batchNumber;
    this.getCourseInfo();
    this.getContactsList();
  },
  methods: {
    // 获取课程信息
    async getCourseInfo() {
      try {
        const res = await this.$myRequest({
          url: "/web/session/showVoucher",
          method: "get",
          data: {
            courseId: this.sessionId
          }
        });
        if (res.code === 200) {
          const data = res.data.data;
          const startTime = data.courseStartTime;
          const date = startTime.slice(0, 10).replace(/-/g, ".") + "  " + this.weekList[new Date(startTime).getDay()];
          this.courseInfo = {
            ...data,
            date,
            courseStartTime: this.formatTime(data.courseStartTime),
            courseEndTime: this.formatTime(data.courseEndTime)
          };
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/curriculumcancel.vue:138", "获取课程信息失败:", error);
        common_vendor.index.showToast({
          title: "获取信息失败",
          icon: "error"
        });
      }
    },
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: "/web/session/getCourseSubscribePeoples",
          method: "get",
          data: {
            batchNumber: this.batchNumber
          }
        });
        if (res.code === 200) {
          this.contactsList = (res.data.data || []).map((contact) => ({
            ...contact,
            linkCheck: false
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/curriculumcancel.vue:164", "获取联系人失败:", error);
        common_vendor.index.showToast({
          title: "获取联系人失败",
          icon: "error"
        });
      }
    },
    // 切换联系人选择状态
    toggleContact(index) {
      this.contactsList[index].linkCheck = !this.contactsList[index].linkCheck;
    },
    // 确认取消预约
    confirmCancel() {
      if (!this.hasSelectedContacts) {
        common_vendor.index.showToast({
          title: "请选择要取消的联系人",
          icon: "error"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认取消",
        content: "确定要取消选中联系人的预约吗？",
        success: (res) => {
          if (res.confirm) {
            this.performCancel();
          }
        }
      });
    },
    // 执行取消操作
    async performCancel() {
      if (this.isCancelling)
        return;
      this.isCancelling = true;
      try {
        const selectedIds = this.contactsList.filter((contact) => contact.linkCheck).map((contact) => contact.linkId);
        const res = await this.$myRequest({
          url: "/web/session/cancelCourseSession",
          method: "get",
          data: {
            sessionId: this.sessionId,
            batchNumber: this.batchNumber,
            peopleIds: selectedIds.join(",")
          }
        });
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "取消预约成功",
            icon: "success",
            duration: 2e3
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages_app/user/curriculumscheme"
            });
          }, 2e3);
        } else {
          throw new Error(res.msg || "取消失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/curriculumcancel.vue:235", "取消预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "取消失败",
          icon: "error"
        });
      } finally {
        this.isCancelling = false;
      }
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8)
        return idCard;
      return idCard.replace(idCard.substring(4, 15), "*******");
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "取消预约",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333"
    }),
    b: $data.courseInfo.courseCover,
    c: common_vendor.t($data.courseInfo.courseName),
    d: $data.courseInfo.courseTeacher
  }, $data.courseInfo.courseTeacher ? {
    e: common_vendor.t($data.courseInfo.courseTeacher)
  } : {}, {
    f: $data.courseInfo.courseLocation
  }, $data.courseInfo.courseLocation ? {
    g: common_vendor.t($data.courseInfo.courseLocation)
  } : {}, {
    h: common_vendor.t($data.courseInfo.courseStartTime),
    i: common_vendor.t($data.courseInfo.courseEndTime),
    j: common_vendor.t($data.courseInfo.date),
    k: common_vendor.f($data.contactsList, (contact, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(contact.linkmanName),
        b: common_vendor.t(contact.linkmanPhone),
        c: common_vendor.t($options.hideIdCard(contact.linkmanCertificate)),
        d: contact.linkCheck
      }, contact.linkCheck ? {} : {}, {
        e: common_vendor.n({
          checked: contact.linkCheck
        }),
        f: contact.linkId,
        g: common_vendor.o(($event) => $options.toggleContact(index), contact.linkId)
      });
    }),
    l: common_vendor.t($data.isCancelling ? "取消中..." : "确认取消预约"),
    m: common_vendor.o((...args) => $options.confirmCancel && $options.confirmCancel(...args)),
    n: !$options.hasSelectedContacts || $data.isCancelling
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0a9c3b4c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/schemesuccess/curriculumcancel.js.map
