<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use think\Cache;

/**
 * 示例接口
 */
class Announcement extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['getInfo','getEntranceInfo'];
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['test2'];

    /**
     * Announcement模型对象
     * @var \app\common\model\Announcement
     */
    protected $model = null;

    /**
     * EntranceAnnouncement模型对象
     * @var \app\common\model\EntranceAnnouncement
     */
    protected $entranceModel = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Announcement;
        $this->entranceModel = new \app\common\model\EntranceAnnouncement;
    }

    public function getInfo()
    {
        $info = Cache::store('redis')->get('announcement');
        if (!$info) {
            $row = $this->model->get(1)->toArray();
            if (!$row) {
                return $this->error('公告未找到');
            }
            //这个接口，前端是展示msg的数据。
            $this->success($row['content']);
        }
        $this->success($info);
/*
{
	"msg": "温馨提示：电影放映前10分排队亮码入场，电影放映前3分钟失去观影优先权，只要有剩余座位，现场观众允许排队入场。电影开映后严禁入内！！",
	"code": 200
}
*/

        $this->success('返回成功', $this->request->param());
    }

    /**
     * 无需登录的接口
     *
     */
    public function test1()
    {
        $this->success('返回成功', ['action' => 'test1']);
    }

    /**
     * 需要登录的接口
     *
     */
    public function test2()
    {
        $this->success('返回成功', ['action' => 'test2']);
    }

    /**
     * 需要登录且需要验证有相应组的权限
     *
     */
    public function test3()
    {
        $this->success('返回成功', ['action' => 'test3']);
    }

    /**
     * 获取入场公告信息
     */
    public function getEntranceInfo()
    {
        $row = $this->entranceModel->where('id', 1)->find();
        if (!$row) {
            $this->error('公告未找到');
        }
        
        $data = [
            'id' => $row['id'],
            'entranceObject' => $row['entrance_object'],
            'entranceRegister' => $row['entrance_register'], 
            'entranceAudit' => $row['entrance_audit'],
            'beiyongOne' => $row['beiyong_one'],
            'beiyongTwo' => $row['beiyong_two'],
            'beiyongThree' => $row['beiyong_three'],
            'beiyongFour' => $row['beiyong_four'],
            'beiyongFive' => $row['beiyong_five']
        ];
        
        $this->success('获取成功', $data);
    }

}

