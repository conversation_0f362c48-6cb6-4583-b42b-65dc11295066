<?php

namespace app\admin\controller\report;

use app\common\controller\Backend;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\Db;

/**
 * 影片场次管理
 *
 * @icon fa fa-circle-o
 */
class Reportfilm extends Backend
{

    /**
     * Reportfilm模型对象
     * @var \app\admin\model\report\Reportfilm
     */
    protected $model = null;
    protected $film_subscribe_model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\film\Session;
        $this->film_subscribe_model = new \app\common\model\film\Subscribe;
        // 控制器中手动加载语言文件

        $lang = include APP_PATH . 'admin/lang/zh-cn/film/base.php';
        $this->assignconfig('film_base_lang', $lang);
    }

    public function getFilmReport()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            // 获取日期范围参数（通过 filter）
            $filter = json_decode($this->request->get("filter", '{}'), true);
            $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
            if ($dateRange && strpos($dateRange, ' - ') !== false) {
                [$startDate, $endDate] = explode(' - ', $dateRange);
                $startDate = trim($startDate);
                $endDate = trim($endDate);
            } else {
                $startDate = $endDate = null;
            }

            // 构建基本 SQL
            $query  =   $this->model
                ->alias('s')
                ->join(['film' => 'f'], 'f.id = s.film_id')
                ->join(['holidays' => 'h'], "DATE_FORMAT(h.holidays_date, '%y%m%d') = DATE_FORMAT(s.film_start_time, '%y%m%d')", 'LEFT');

            if ($startDate && $endDate) {
                $query->whereRaw("DATE_FORMAT(s.film_start_time, '%y%m%d') >= DATE_FORMAT(:startDate, '%y%m%d')", ['startDate' => $startDate])
                    ->whereRaw("DATE_FORMAT(s.film_end_time, '%y%m%d') <= DATE_FORMAT(:endDate, '%y%m%d')", ['endDate' => $endDate]);
            } elseif ($startDate) {
                $query->whereRaw("DATE_FORMAT(s.film_start_time, '%y%m%d') >= DATE_FORMAT(:now, '%y%m%d')", ['now' => date('Y-m-d')]);
            } elseif ($endDate) {
                $query->whereRaw("DATE_FORMAT(s.film_end_time, '%y%m%d')  <= DATE_FORMAT(:now, '%y%m%d')", ['now' => date('Y-m-d')]);
            }



            // 查询列表
            $list = $query
                ->field('s.id,s.film_start_time,s.film_end_time,s.film_poll,s.inventory_votes,
                         f.film_name,f.film_type, h.is_close,s.del_flag')
                ->order($sort, $order)
                // ->select(false);print_r($list);die;
                ->paginate($limit);

            $rows = $list->items();
            $total = $list->total();


            // 查询每个场次的签到人数

            $sessionIds = array_column($rows, 'id');
            $signListsubQuery =  $this->film_subscribe_model
                ->field('film_seeion_id, COUNT(*) as signed_nums')
                ->where('del_flag', 0)
                ->where('subscribe_state', '4')
                ->whereIn('film_seeion_id', $sessionIds)
                ->group('film_seeion_id')
                ->buildSql();

            $signList = Db::table($signListsubQuery . ' a')->column('signed_nums', 'film_seeion_id');




            foreach ($rows as &$row) {
                $row['signed_nums'] = isset($signList[$row['id']]) ? $signList[$row['id']] : 0;
            }

            return json(['total' => $total, 'rows' => $rows]);
        }

        return $this->view->fetch();
    }

    /**
     * 获取影片场次订阅详情
     */
    public function show($ids = null)
    {
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }

        if (!$ids) {
            $this->error("缺少ID");
        }

        // 使用 FastAdmin 的 buildparams 自动构造分页、排序、筛选条件
        [$where_buildparams, $sort, $order, $offset, $limit] = $this->buildparams();


        // 判断闭馆状态、删除状态
        $isClose =  $this->model
            ->alias('f')
            ->join(['holidays' => 'h'], "DATE_FORMAT(h.holidays_date,'%y%m%d') = DATE_FORMAT(f.film_start_time,'%y%m%d')", 'LEFT')
            ->where('f.id', $ids)
            ->value('h.is_close');

        $isDel =  $this->model
            ->where('id', $ids)
            ->value('del_flag');

        $isClose = strval($isClose); // 确保是字符串
        $isDel = strval($isDel);

        // 构建查询
        $where = [];
        $where['s.film_seeion_id'] = ['=', $ids];


        // 动态判断状态逻辑
        if ($isClose === '0' && $isDel === '0') {

            $where['s.del_flag']        = ['=', 2];
            $where['s.is_close']        = ['=', 1];
            $where['s.subscribe_state'] = ['<>', 3];
        } elseif ($isClose === '1' && $isDel === '2') {
            $where['s.del_flag']        = ['=', 2];
            $where['s.is_del']          = ['=', 1];
            $where['s.subscribe_state'] = ['<>', 3];
        } elseif ($isClose === '0' && $isDel === '2') {
            $where['s.del_flag']        = ['=', 2];
            $where['s.is_del']          = ['=', 1];
            $where['s.is_close']        = ['=', 1];
            $where['s.subscribe_state'] = ['<>', 3];
        } elseif ($isClose === '1' && $isDel === '0') {
            $where['s.del_flag']        = ['=', 0];
            $where['s.subscribe_state'] = ['<>', 3];
        }

        // 查询总数


        // 查询列表
        $list = $this->film_subscribe_model
            ->alias('s')
            ->join(['user_linkman'=>'l'], 'l.id = s.user_linkman_id')
            ->field([
                's.id',
                's.subscribe_type as subscribeType',
                's.is_ticket as isTicket',
                's.subscribe_state as subscribeState',
                'l.linkman_name',
                'l.linkman_phone',
                'l.linkman_age as linkmanAge',
                'l.linkman_certificate as linkmanCertificate'
            ])

            ->where($where_buildparams)
            ->where($where)
            ->order($sort, $order)
            // ->select(false);        print_r($list);        die;
            ->paginate($limit);


        // 使用 each  脱敏处理
        $list->each(function ($row) {
            $row['linkman_name'] = desensitize_name($row['linkman_name']);
            $row['linkman_phone'] = desensitize_phone($row['linkman_phone']);
            $row['linkmanCertificate'] = desensitize_certificate($row['linkmanCertificate']);

            return $row;
        });

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    public function export()
    {
        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }

        // 查询原始数据
        $query =   $this->film_subscribe_model
            ->alias('sb')
            ->join(['film_session' => 'se'], 'sb.film_seeion_id = se.id')
            ->join(['user_linkman' => 'l'], 'l.id = sb.user_linkman_id', 'LEFT')
            ->join(['film' => 'f'], 'se.film_id = f.id', 'LEFT')
            ->where('sb.subscribe_state', '<>', 3);

        if ($startDate) {
            $query->whereRaw("DATE_FORMAT(se.film_start_time,'%y%m%d') >= ?", [date('ymd', strtotime($startDate))]);
        }
        if ($endDate) {
            $query->whereRaw("DATE_FORMAT(se.film_end_time,'%y%m%d') <= ?", [date('ymd', strtotime($endDate))]);
        }

        $rows = $query->field([
            'sb.film_seeion_id as sessionId',
            'sb.user_id as userId',
            "DATE_FORMAT(se.film_start_time,'%Y-%m-%d') as filmStartTime",
            "IF(f.film_type = 1,'球幕电影','4D电影') as filmType",
            "f.film_name as filmName",
            "CONCAT(DATE_FORMAT(se.film_start_time,'%H:%i:%s'),'-',DATE_FORMAT(se.film_end_time,'%H:%i:%s')) as filmEndTime",
            "se.film_poll as filmPoll",
            'sb.subscribe_type as num',
            'sb.subscribe_state as subscribeState',
            "IF(sb.is_ticket=0,'未检票','已检票') as isTicket",
            'l.linkman_name',
            'l.linkman_phone',
            'l.linkman_age',
            'l.linkman_certificate as linkmanCertificate',
            'sb.is_close as isClose',
            'sb.is_del as isDel',
            'sb.del_flag as delFlag',
            // "(SELECT COUNT(*) FROM film_black_record fbr WHERE fbr.user_id = sb.user_id) as breakRuleCount"
        ])
            ->select();
        // ->select(false);    print_r($rows);die;

        // 分组整理：sessionId => userId => 联系人
        $grouped = [];

        foreach ($rows as $row) {

            $sid = $row['sessionId'];
            $uid = $row['userId'];

            // 保留用于条件判断的字段
            $isClose = $row['isClose'];
            $isDel = $row['isDel'];
            $delFlag = $row['delFlag'];

            // 按 Java 逻辑判断当前记录是否保留
            $keep = false;
            if ($isClose == '1' && $isDel == '0') {
                $keep = $row['isClose'] === '1';
            } elseif ($isDel == '1' && $isClose == '0') {
                $keep = $row['isDel'] === '1';
            } elseif ($isClose == '0' && $isDel == '0') {
                $keep = $row['delFlag'] === '0';
            }
            if (!$keep) continue;


            $grouped[$sid][$uid][] = $row;
        }


        // 组装导出数据
        $export = [];
        foreach ($grouped as $users) {
            foreach ($users as $contactList) {
                $base = $contactList[0];







                // 场馆状态处理
                $base['isClose'] = $base['isClose'] === '1' ? '管理员闭馆' : '正常';

                // 影片状态处理
                $base['isDel'] = $base['isDel'] === '1' ? '管理员删除影片' : '正常';

                // 签到状态处理
                switch ($base['subscribeState']) {
                    case '2':
                    case '4':
                    case '5':
                        $base['subscribeState'] = '已签到';
                        break;
                    case '6':
                        $base['subscribeState'] = '未签到,票已回流';
                        break;
                    case '7':
                        $base['subscribeState'] = '场次人数不满被取消';
                        break;
                    default:
                        $base['subscribeState'] = '未签到';
                }

                $record = [
                    'filmStartTime' => $base['filmStartTime'],
                    'filmType' => $base['filmType'],
                    'filmName' => $base['filmName'],
                    'filmEndTime' => $base['filmEndTime'],
                    'filmPoll' => $base['filmPoll'],
                    'num' => $base['num'],
                    'subscribeState' => $base['subscribeState'],
                    // 'breakRuleCount' => $base['breakRuleCount'],
                    'isClose' => $base['isClose'],
                    'isDel' => $base['isDel'],
                ];

                for ($i = 0; $i < min(5, count($contactList)); $i++) {
                    $row = $contactList[$i];
                    // 脱敏
                    $record["linkmanName" . ($i + 1)] = desensitize_name($row['linkman_name']);
                    $record["linkmanPhone" . ($i + 1)] = desensitize_phone($row['linkman_phone']);
                    $record["linkmanCertificate" . ($i + 1)] = desensitize_certificate($row['linkmanCertificate']);
                    $record["linkmanAge" . ($i + 1)] = $row['linkman_age'];
                    $record["isTicket" . ($i + 1)] = $row['isTicket'];
                }

                $export[] = $record;
            }
        }

        // 显式定义字段顺序 + 中文表头
        $columns = [
            'filmStartTime'      => '预约日期',
            'isClose'            => '场馆状态',
            'isDel'              => '影片状态',
            'filmType'           => '电影类型',
            'filmName'           => '电影名称',
            'filmEndTime'        => '时间',
            'filmPoll'           => '可预约量',
            'num'                => '预约人数',
            'linkmanName1'       => '预约人员名称一',
            'linkmanPhone1'      => '手机号一',
            'linkmanAge1'        => '年龄一',
            'linkmanCertificate1' => '身份证一',
            'linkmanName2'       => '预约人员名称二',
            'linkmanPhone2'      => '手机号二',
            'linkmanAge2'        => '年龄二',
            'linkmanCertificate2' => '身份证二',
            'linkmanName3'       => '预约人员名称三',
            'linkmanPhone3'      => '手机号三',
            'linkmanAge3'        => '年龄三',
            'linkmanCertificate3' => '身份证三',
            'linkmanName4'       => '预约人员名称四',
            'linkmanPhone4'      => '手机号四',
            'linkmanAge4'        => '年龄四',
            'linkmanCertificate4' => '身份证四',
            'linkmanName5'       => '预约人员名称五',
            'linkmanPhone5'      => '手机号五',
            'linkmanAge5'        => '年龄五',
            'linkmanCertificate5' => '身份证五',
            'subscribeState'     => '是否已签到',
            'isTicket1'          => '票码一',
            'isTicket2'          => '票码二',
            'isTicket3'          => '票码三',
            'isTicket4'          => '票码四',
            'isTicket5'          => '票码五',
            // 'breakRuleCount'     => '状态（违规次数）',
        ];

        //  按字段顺序导出数据
        $orderedData = [];
        foreach ($export as $row) {
            $newRow = [];
            foreach ($columns as $field => $title) {
                $newRow[] = isset($row[$field]) ? $row[$field] : '';
            }
            $orderedData[] = $newRow;
        }

        // 写入 Excel（中文表头 + 正确顺序）
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray(array_values($columns), null, 'A1'); // 中文表头
        $sheet->fromArray($orderedData, null, 'A2');           // 数据行

        $filename = '影片导出_' . date('YmdHis') . '.xlsx';
        ob_clean(); // 清空缓冲区
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment; filename=\"$filename\"");
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        $writer->save("php://output");
        exit;
    }
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
}
