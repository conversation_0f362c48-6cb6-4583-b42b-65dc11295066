<template>
  <view class="film-cancel">
    <!-- 自定义头部 -->
    <my-header 
      title="取消预约" 
      :isBack="true" 
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
    />
    
    <view class="content">
      <!-- 电影信息 -->
      <view class="film-info">
        <view class="info-header">
          <text class="venue-name">宝安科技馆 观影预约</text>
        </view>
        
        <view class="film-details">
          <view class="film-poster">
            <image 
              :src="filmInfo.filmCover" 
              mode="aspectFill"
              class="poster-image"
            />
          </view>
          <view class="film-meta">
            <text class="film-name">{{ filmInfo.filmName }}</text>
            <text class="film-type">类型：{{ filmTypeList[filmInfo.filmType - 1] || '未知' }}</text>
            <text class="film-time">时间：{{ filmInfo.filmStartTime }} - {{ filmInfo.filmEndTime }}</text>
            <text class="film-date">日期：{{ filmInfo.filmArrangedDate }} {{ getWeekDay(filmInfo.filmArrangedDate) }}</text>
            <text class="film-count">预约人数：{{ filmInfo.orderNum }}人</text>
          </view>
        </view>
      </view>
      
      <!-- 联系人选择 -->
      <view class="contacts-section">
        <view class="section-title">选择要取消的联系人</view>
        <view class="contacts-list">
          <view 
            v-for="(contact, index) in contactsList" 
            :key="contact.linkId"
            class="contact-item"
            @tap="toggleContact(index)"
          >
            <view class="contact-info">
              <text class="contact-name">{{ contact.linkmanName }}</text>
              <text class="contact-phone">{{ contact.linkmanPhone }}</text>
              <text class="contact-id">{{ hideIdCard(contact.linkmanCertificate) }}</text>
            </view>
            <view :class="['checkbox', { checked: contact.linkCheck }]">
              <text v-if="contact.linkCheck" class="check-icon">✓</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 取消按钮 -->
      <view class="action-section">
        <button 
          class="cancel-btn"
          @tap="confirmCancel"
          :disabled="!hasSelectedContacts || isCancelling"
        >
          {{ isCancelling ? '取消中...' : '确认取消预约' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FilmCancel',
  data() {
    return {
      filmInfo: {},
      contactsList: [],
      filmTypeList: ['球幕电影', '4D电影'],
      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      
      // 参数
      filmSessionId: null,
      batchNumber: null,
      vote: null,
      
      // 状态
      isCancelling: false
    }
  },
  
  computed: {
    // 是否有选中的联系人
    hasSelectedContacts() {
      return this.contactsList.some(contact => contact.linkCheck)
    }
  },
  
  onLoad(options) {
    this.filmSessionId = options.filmSessionId
    this.batchNumber = options.batchNumber
    this.vote = options.vote
    
    this.getFilmInfo()
    this.getContactsList()
  },
  
  methods: {
    // 获取电影信息
    async getFilmInfo() {
      try {
        const res = await this.$myRequest({
          url: '/web/fileSession/personalCenterFilmByFilmSessionId',
          method: 'get',
          data: {
            filmSessionId: this.filmSessionId
          }
        })
        
        if (res.code === 200) {
          this.filmInfo = {
            ...res.data.data,
            filmStartTime: this.formatTime(res.data.data.filmStartTime),
            filmEndTime: this.formatTime(res.data.data.filmEndTime),
            orderNum: res.data.data.filmPoll - res.data.data.inventoryVotes
          }
        }
      } catch (error) {
        console.error('获取电影信息失败:', error)
        uni.showToast({
          title: '获取信息失败',
          icon: 'error'
        })
      }
    },
    
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: '/web/fileSession/getFilmSubscribePeoples',
          method: 'get',
          data: {
            batchNumber: this.batchNumber
          }
        })
        
        if (res.code === 200) {
          this.contactsList = (res.data.data || []).map(contact => ({
            ...contact,
            linkCheck: false
          }))
        }
      } catch (error) {
        console.error('获取联系人失败:', error)
        uni.showToast({
          title: '获取联系人失败',
          icon: 'error'
        })
      }
    },
    
    // 切换联系人选择状态
    toggleContact(index) {
      this.contactsList[index].linkCheck = !this.contactsList[index].linkCheck
    },
    
    // 确认取消预约
    confirmCancel() {
      if (!this.hasSelectedContacts) {
        uni.showToast({
          title: '请选择要取消的联系人',
          icon: 'error'
        })
        return
      }
      
      uni.showModal({
        title: '确认取消',
        content: '确定要取消选中联系人的预约吗？',
        success: (res) => {
          if (res.confirm) {
            this.performCancel()
          }
        }
      })
    },
    
    // 执行取消操作
    async performCancel() {
      if (this.isCancelling) return
      
      this.isCancelling = true
      
      try {
        const selectedIds = this.contactsList
          .filter(contact => contact.linkCheck)
          .map(contact => contact.linkId)
        
        const res = await this.$myRequest({
          url: '/web/fileSession/cancelFilmSession',
          method: 'get',
          data: {
            vote: this.vote,
            filmSessionId: this.filmSessionId,
            batchNumber: this.batchNumber,
            peopleIds: selectedIds.join(',')
          }
        })
        
        if (res.code === 200) {
          uni.showToast({
            title: '取消预约成功',
            icon: 'success',
            duration: 2000
          })
          
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages_app/user/filmscheme'
            })
          }, 2000)
        } else {
          throw new Error(res.msg || '取消失败')
        }
      } catch (error) {
        console.error('取消预约失败:', error)
        uni.showToast({
          title: error.message || '取消失败',
          icon: 'error'
        })
      } finally {
        this.isCancelling = false
      }
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      
      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },
    
    // 获取星期
    getWeekDay(dateStr) {
      if (!dateStr) return ''
      
      try {
        const date = new Date(dateStr.replace(/-/g, '/'))
        return this.weekList[date.getDay()]
      } catch (error) {
        return ''
      }
    },
    
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      
      return idCard.replace(idCard.substring(4, 15), '*******')
    }
  }
}
</script>

<style lang="scss" scoped>
.film-cancel {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding: 20rpx;
    padding-top: 120rpx; // 为头部留出空间

    // 电影信息
    .film-info {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .info-header {
        margin-bottom: 20rpx;

        .venue-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .film-details {
        display: flex;

        .film-poster {
          width: 120rpx;
          height: 160rpx;
          margin-right: 20rpx;
          border-radius: 12rpx;
          overflow: hidden;

          .poster-image {
            width: 100%;
            height: 100%;
          }
        }

        .film-meta {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .film-name {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
            line-height: 1.4;
          }

          .film-type,
          .film-time,
          .film-date,
          .film-count {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 6rpx;
            line-height: 1.3;
          }
        }
      }
    }

    // 联系人选择
    .contacts-section {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 30rpx;
      }

      .contacts-list {
        .contact-item {
          display: flex;
          align-items: center;
          padding: 20rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .contact-info {
            flex: 1;

            .contact-name {
              display: block;
              font-size: 30rpx;
              font-weight: 500;
              color: #333;
              margin-bottom: 8rpx;
            }

            .contact-phone {
              display: block;
              font-size: 26rpx;
              color: #666;
              margin-bottom: 6rpx;
            }

            .contact-id {
              display: block;
              font-size: 24rpx;
              color: #999;
            }
          }

          .checkbox {
            width: 48rpx;
            height: 48rpx;
            border: 2rpx solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &.checked {
              background: linear-gradient(135deg, #667eea, #764ba2);
              border-color: #667eea;

              .check-icon {
                color: white;
                font-size: 28rpx;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    // 操作区域
    .action-section {
      .cancel-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;

        &::after {
          border: none;
        }

        &:disabled {
          background: #ccc;
          color: #999;
        }
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.film-cancel {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
