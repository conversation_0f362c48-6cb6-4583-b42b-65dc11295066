{"version": 3, "file": "filmdes.js", "sources": ["pages_app/vieworder/filmdes.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHZpZXdvcmRlclxmaWxtZGVzLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"filmDes\">\r\n    <view class=\"filmInfoContent\">\r\n      <!-- 自定义头部 -->\r\n      <my-header \r\n        title=\"观影登记\" \r\n        :isBack=\"true\" \r\n        :isShowHome=\"true\"\r\n        background=\"transparent\"\r\n        color=\"#ffffff\"\r\n      />\r\n      \r\n      <view class=\"content\">\r\n        <view class=\"titlebar\"></view>\r\n        <view :class=\"['main', isChooseContact ? 'isChooseContact' : '']\">\r\n          <view class=\"show_fixed\">\r\n            <!-- 电影信息卡片 -->\r\n            <view class=\"filmNew\">\r\n              <view class=\"filmItem\">\r\n                <view class=\"itemLeft\">\r\n                  <image \r\n                    :src=\"film.filmCover\" \r\n                    mode=\"aspectFill\" \r\n                    class=\"cover\"\r\n                  />\r\n                  <view class=\"fileInfo\">\r\n                    <text class=\"filmName\">{{ film.filmName }}</text>\r\n                    <text class=\"filmType\">类型：{{ filmTypeList[film.filmType - 1] }}</text>\r\n                    <text class=\"filmTime\">时间：{{ film.filmStartTime }}-{{ film.filmEndTime }}</text>\r\n                    <text class=\"filmDate\">\r\n                      <text>日期：{{ film.filmArrangedDate }}</text>\r\n                      <text style=\"margin-left: 5px\">{{ weekList[new Date(film.filmArrangedDate).getDay()] }}</text>\r\n                    </text>\r\n                  </view>\r\n                </view>\r\n                <view class=\"itemRight\">\r\n                  <!-- 倒计时 -->\r\n                  <view \r\n                    :class=\"['ticketType', isShowCountDown ? '' : 'hidden']\"\r\n                  >\r\n                    <text class=\"n_text\">倒计时</text>\r\n                    <text class=\"t_text\">{{ countDown }}</text>\r\n                  </view>\r\n                  <!-- 剩余票数 -->\r\n                  <view \r\n                    :class=\"[\r\n                      'order_button', \r\n                      isShowCountDown ? 'green' : '', \r\n                      film.inventoryVotes == 0 ? 'gray' : ''\r\n                    ]\"\r\n                  >\r\n                    剩余:{{ film.inventoryVotes }}\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              <!-- 电影介绍 -->\r\n              <view class=\"filmText\">{{ film.fileIntroduce }}</view>\r\n            </view>\r\n            \r\n            <!-- 分割线 -->\r\n            <view class=\"line\">\r\n              <view \r\n                class=\"line_item\" \r\n                v-for=\"(item, index) in 20\" \r\n                :key=\"index\"\r\n              ></view>\r\n            </view>\r\n            \r\n            <!-- 预约人数选择 -->\r\n            <view class=\"orderNum\">\r\n              <view class=\"orderTitle\">预约人数</view>\r\n              <view class=\"chooseNum\">\r\n                <view \r\n                  v-for=\"(item, index) in checkItem\" \r\n                  :key=\"index\"\r\n                  :class=\"['checkItem', checkIndex === index ? 'isCheck' : '']\"\r\n                  @tap=\"checkNum(item.value)\"\r\n                >\r\n                  {{ item.label }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n            \r\n            <!-- 分割线 -->\r\n            <view class=\"line lineshow\">\r\n              <view \r\n                class=\"line_item\" \r\n                v-for=\"(item, index) in 20\" \r\n                :key=\"index\"\r\n              ></view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 联系人列表 -->\r\n          <view class=\"contact\">\r\n            <view class=\"contactTitle\">人员名单</view>\r\n            <view class=\"contactList\">\r\n              <view \r\n                v-for=\"(link, index) in linkList\" \r\n                :key=\"link.id\"\r\n                class=\"contactItem\"\r\n              >\r\n                <view class=\"left\">\r\n                  <view class=\"peopleName\">{{ link.linkmanName }}</view>\r\n                  <view class=\"peopleCard\">证件号 {{ hideCardNum(index) }}</view>\r\n                  <view class=\"peopleMablie\">\r\n                    <text>手机号码 {{ link.linkmanPhone }}</text>\r\n                    <text>年龄 {{ link.linkmanAge }}</text>\r\n                  </view>\r\n                </view>\r\n                <view class=\"right\">\r\n                  <view class=\"checkBtn isCheck\"></view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <view class=\"show_foot\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部提交按钮 -->\r\n    <view class=\"filmInfofont\" :style=\"{ display: isChooseContact ? 'block' : 'none' }\">\r\n      <view class=\"submitBtn\" @tap=\"submitOrder\">提交</view>\r\n    </view>\r\n    \r\n    <!-- 黑名单弹窗 -->\r\n    <view class=\"mask\" v-show=\"isShowMaskBlack\">\r\n      <view class=\"blackDefault\">\r\n        <view class=\"blackStateIcon\"></view>\r\n        <view class=\"blackStateT\">\r\n          <text>{{ blackTips }}</text>\r\n          <view></view>\r\n          <text v-if=\"blackTipsDate\">\r\n            解封剩余<text class=\"blackDate\">{{ blackTipsDate }}</text>\r\n          </text>\r\n        </view>\r\n        <view class=\"blackBtn\" @tap=\"closeBlackMask\">返回</view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 定位弹窗 -->\r\n    <view class=\"mask\" v-show=\"isShowMaskLocation\">\r\n      <view class=\"blackDefault\">\r\n        <view class=\"locationStateIcon\"></view>\r\n        <view class=\"blackStateT\">\r\n          <text>您的定位较远</text>\r\n          <view></view>\r\n          <text>\r\n            请移步至宝安科技馆进行<text class=\"blackDate\">现场签到</text>\r\n          </text>\r\n        </view>\r\n        <view class=\"blackBtn\" @tap=\"closeLocationMask\">返回</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getFilmBySessionId, submitFilmOrder, checkLocation } from '@/api/reservation.js'\r\nimport { formatTime } from '@/utils/common.js'\r\n\r\nexport default {\r\n  name: 'FilmDetails',\r\n  data() {\r\n    return {\r\n      // 是否选择了联系人\r\n      isChooseContact: true,\r\n      \r\n      // 预约人数选项\r\n      checkItem: [\r\n        { label: '1人', value: '1' },\r\n        { label: '2人', value: '2' },\r\n        { label: '3人', value: '3' },\r\n        { label: '4人', value: '4' },\r\n        { label: '5人', value: '5' }\r\n      ],\r\n      checkIndex: 0,\r\n      \r\n      // 联系人列表\r\n      linkList: [],\r\n      \r\n      // 电影信息\r\n      film: {},\r\n      filmSessionId: null,\r\n      \r\n      // 电影类型\r\n      filmTypeList: ['球幕电影', '4D电影'],\r\n      \r\n      // 星期列表\r\n      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\r\n      \r\n      // 弹窗控制\r\n      isShowMaskBlack: false,\r\n      blackTips: null,\r\n      blackTipsDate: null,\r\n      \r\n      // 倒计时\r\n      countDown: null,\r\n      isShowCountDown: false,\r\n      timer: null,\r\n      \r\n      // 定位弹窗\r\n      isShowMaskLocation: false,\r\n      \r\n      // 预约相关\r\n      batchnumber: null,\r\n      subscribeSuccess: false,\r\n      reservationsSuccess: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 隐藏身份证号中间部分\r\n    hideCardNum() {\r\n      return (index) => {\r\n        if (!this.linkList[index] || !this.linkList[index].linkmanCertificate) {\r\n          return ''\r\n        }\r\n        const num = this.linkList[index].linkmanCertificate\r\n        return num.replace(num.substring(4, 15), '*******')\r\n      }\r\n    }\r\n  },\r\n  \r\n  onLoad(option) {\r\n    this.filmSessionId = option.filmSessionId\r\n  },\r\n  \r\n  onShow() {\r\n    this.getFilm()\r\n    \r\n    // 获取选择的联系人\r\n    if (uni.getStorageSync('gyyy_link')) {\r\n      this.isChooseContact = true\r\n      this.linkList = JSON.parse(uni.getStorageSync('gyyy_link'))\r\n      this.checkIndex = this.linkList.length - 1\r\n    } else {\r\n      this.isChooseContact = false\r\n    }\r\n  },\r\n  \r\n  onHide() {\r\n    uni.removeStorageSync('gyyy_link')\r\n    clearInterval(this.timer)\r\n  },\r\n  \r\n  onUnload() {\r\n    clearInterval(this.timer)\r\n  },\r\n  \r\n  methods: {\r\n    // 选择预约人数\r\n    checkNum(num) {\r\n      this.checkIndex = parseInt(num) - 1\r\n      uni.navigateTo({\r\n        url: `/pages_app/contacts/index?num=${num}&type=gyyy_link`\r\n      })\r\n    },\r\n\r\n    // 获取电影信息\r\n    async getFilm() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/fileSession/personalCenterFilmByFilmSessionId',\r\n          method: 'get',\r\n          data: {\r\n            filmSessionId: this.filmSessionId\r\n          }\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          this.film = {\r\n            ...res.data.data,\r\n            filmStartTime: formatTime(res.data.data.filmStartTime.replace(/-/g, '/'), false),\r\n            filmEndTime: formatTime(res.data.data.filmEndTime.replace(/-/g, '/'), false)\r\n          }\r\n          this.getCountDown()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取电影信息失败:', error)\r\n        uni.showToast({\r\n          title: '获取电影信息失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 获取倒计时\r\n    getCountDown() {\r\n      clearInterval(this.timer)\r\n\r\n      const signEndTime = new Date(\r\n        this.film.filmArrangedDate.replace(/-/g, '/') + ' ' + this.film.filmStartTime\r\n      ).getTime()\r\n      const filmArrangeDateTime = new Date(this.film.filmArrangedDate.replace(/-/g, '/')).getTime()\r\n      let nowTime = new Date().getTime()\r\n      const time = signEndTime - nowTime + filmArrangeDateTime\r\n\r\n      // 判断是否显示倒计时（在开场前30分钟内显示）\r\n      this.isShowCountDown = time > filmArrangeDateTime && time < (1800000 + filmArrangeDateTime)\r\n\r\n      this.timer = setInterval(() => {\r\n        nowTime = new Date().getTime()\r\n\r\n        // 如果已过预约时间，停止倒计时并提示\r\n        if (signEndTime <= nowTime) {\r\n          clearInterval(this.timer)\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '影片预约时间已过，返回上一页',\r\n            showCancel: false,\r\n            success: (res) => {\r\n              if (res.confirm) {\r\n                uni.navigateBack()\r\n              }\r\n            }\r\n          })\r\n          return\r\n        }\r\n\r\n        this.isShowCountDown = time > filmArrangeDateTime && time < (1800000 + filmArrangeDateTime)\r\n        const remainTime = signEndTime - nowTime\r\n        this.countDown = this.formatCountDown(remainTime)\r\n      }, 1000)\r\n    },\r\n\r\n    // 格式化倒计时显示\r\n    formatCountDown(time) {\r\n      if (time <= 0) return '00:00'\r\n\r\n      const minutes = Math.floor(time / 60000)\r\n      const seconds = Math.floor((time % 60000) / 1000)\r\n\r\n      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n    },\r\n\r\n    // 提交预约订单\r\n    async submitOrder() {\r\n      if (!this.linkList || this.linkList.length === 0) {\r\n        uni.showToast({\r\n          title: '请选择联系人',\r\n          icon: 'error'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.film.inventoryVotes <= 0) {\r\n        uni.showToast({\r\n          title: '该场次已满',\r\n          icon: 'error'\r\n        })\r\n        return\r\n      }\r\n\r\n      try {\r\n        uni.showLoading({\r\n          title: '提交中...'\r\n        })\r\n\r\n        const res = await this.$myRequest({\r\n          url: '/web/fileSession/filmSubscribe',\r\n          method: 'post',\r\n          data: {\r\n            filmSessionId: this.filmSessionId,\r\n            linkmanIds: this.linkList.map(item => item.id).join(','),\r\n            subscribeNum: this.linkList.length\r\n          }\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          uni.hideLoading()\r\n          uni.showToast({\r\n            title: '预约成功'\r\n          })\r\n\r\n          // 跳转到成功页面\r\n          setTimeout(() => {\r\n            uni.redirectTo({\r\n              url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${res.data.data.batchNumber}&filmSessionId=${this.filmSessionId}`\r\n            })\r\n          }, 1500)\r\n        } else {\r\n          throw new Error(res.msg || '预约失败')\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        console.error('提交预约失败:', error)\r\n\r\n        // 处理特殊错误情况\r\n        if (error.message && error.message.includes('黑名单')) {\r\n          this.showBlackMask(error.message)\r\n        } else {\r\n          uni.showToast({\r\n            title: error.message || '预约失败',\r\n            icon: 'error'\r\n          })\r\n        }\r\n      }\r\n    },\r\n\r\n    // 显示黑名单弹窗\r\n    showBlackMask(message) {\r\n      this.blackTips = message\r\n      this.isShowMaskBlack = true\r\n    },\r\n\r\n    // 关闭黑名单弹窗\r\n    closeBlackMask() {\r\n      this.isShowMaskBlack = false\r\n      uni.navigateBack()\r\n    },\r\n\r\n    // 关闭定位弹窗\r\n    closeLocationMask() {\r\n      this.isShowMaskLocation = false\r\n      uni.navigateBack()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.filmDes {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  font-family: 'PingFang SC', sans-serif;\r\n  min-height: 100vh;\r\n  overflow: auto;\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.filmInfoContent {\r\n  min-height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.content {\r\n  width: 100%;\r\n  padding-top: 120rpx;\r\n}\r\n\r\n.titlebar {\r\n  background: url('data:image/png;base64,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') no-repeat top;\r\n  background-size: cover;\r\n  height: 96rpx;\r\n  margin: 81rpx auto 0;\r\n  width: calc(100% - 30rpx);\r\n}\r\n\r\n.main {\r\n  height: 729rpx;\r\n  margin: -50rpx auto 150rpx;\r\n  position: relative;\r\n  width: 654rpx;\r\n\r\n  &.isChooseContact {\r\n    height: auto;\r\n    min-height: 729rpx;\r\n  }\r\n}\r\n\r\n.show_fixed {\r\n  background: url('data:image/png;base64,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') no-repeat 50%;\r\n  background-size: 100% 100%;\r\n  height: 100%;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n  padding: 40rpx;\r\n}\r\n\r\n.filmNew {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.filmItem {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.itemLeft {\r\n  display: flex;\r\n  flex: 1;\r\n}\r\n\r\n.cover {\r\n  width: 160rpx;\r\n  height: 220rpx;\r\n  border-radius: 12rpx;\r\n  margin-right: 30rpx;\r\n}\r\n\r\n.fileInfo {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .filmName {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333333;\r\n    margin-bottom: 15rpx;\r\n    line-height: 1.4;\r\n  }\r\n\r\n  .filmType,\r\n  .filmTime,\r\n  .filmDate {\r\n    font-size: 28rpx;\r\n    color: #666666;\r\n    margin-bottom: 10rpx;\r\n    line-height: 1.3;\r\n  }\r\n}\r\n\r\n.itemRight {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.ticketType {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 8rpx;\r\n  padding: 10rpx 15rpx;\r\n  margin-bottom: 15rpx;\r\n  text-align: center;\r\n\r\n  &.hidden {\r\n    display: none;\r\n  }\r\n\r\n  .n_text {\r\n    display: block;\r\n    font-size: 24rpx;\r\n    color: #999999;\r\n    margin-bottom: 5rpx;\r\n  }\r\n\r\n  .t_text {\r\n    display: block;\r\n    font-size: 28rpx;\r\n    color: #ff4757;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.order_button {\r\n  background: #ff6b6b;\r\n  color: #ffffff;\r\n  padding: 15rpx 25rpx;\r\n  border-radius: 25rpx;\r\n  font-size: 26rpx;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  min-width: 120rpx;\r\n\r\n  &.green {\r\n    background: #2ed573;\r\n  }\r\n\r\n  &.gray {\r\n    background: #95a5a6;\r\n  }\r\n}\r\n\r\n.filmText {\r\n  font-size: 28rpx;\r\n  color: #555555;\r\n  line-height: 1.6;\r\n  margin-bottom: 30rpx;\r\n  padding: 20rpx;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border-radius: 12rpx;\r\n}\r\n\r\n.line {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin: 30rpx 0;\r\n\r\n  .line_item {\r\n    width: 8rpx;\r\n    height: 8rpx;\r\n    background: rgba(255, 255, 255, 0.3);\r\n    border-radius: 50%;\r\n  }\r\n\r\n  &.lineshow {\r\n    margin: 40rpx 0;\r\n  }\r\n}\r\n\r\n.orderNum {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.orderTitle {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n  margin-bottom: 25rpx;\r\n  text-align: center;\r\n}\r\n\r\n.chooseNum {\r\n  display: flex;\r\n  justify-content: space-around;\r\n  flex-wrap: wrap;\r\n  gap: 20rpx;\r\n}\r\n\r\n.checkItem {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 2rpx solid rgba(255, 255, 255, 0.3);\r\n  color: #666666;\r\n  padding: 20rpx 30rpx;\r\n  border-radius: 25rpx;\r\n  font-size: 28rpx;\r\n  text-align: center;\r\n  min-width: 100rpx;\r\n  transition: all 0.3s ease;\r\n\r\n  &.isCheck {\r\n    background: #2ed573;\r\n    border-color: #2ed573;\r\n    color: #ffffff;\r\n    transform: scale(1.05);\r\n  }\r\n}\r\n\r\n.contact {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.contactTitle {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n  margin-bottom: 25rpx;\r\n  text-align: center;\r\n}\r\n\r\n.contactList {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border-radius: 12rpx;\r\n  padding: 20rpx;\r\n}\r\n\r\n.contactItem {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 25rpx 20rpx;\r\n  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.left {\r\n  flex: 1;\r\n}\r\n\r\n.peopleName {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.peopleCard,\r\n.peopleMablie {\r\n  font-size: 26rpx;\r\n  color: #666666;\r\n  margin-bottom: 8rpx;\r\n\r\n  text {\r\n    margin-right: 20rpx;\r\n  }\r\n}\r\n\r\n.right {\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.checkBtn {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 2rpx solid #ddd;\r\n  border-radius: 50%;\r\n  position: relative;\r\n\r\n  &.isCheck {\r\n    background: #2ed573;\r\n    border-color: #2ed573;\r\n\r\n    &::after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 12rpx;\r\n      top: 6rpx;\r\n      width: 12rpx;\r\n      height: 20rpx;\r\n      border: 3rpx solid #ffffff;\r\n      border-top: none;\r\n      border-left: none;\r\n      transform: rotate(45deg);\r\n    }\r\n  }\r\n}\r\n\r\n.show_foot {\r\n  height: 120rpx;\r\n}\r\n\r\n.filmInfofont {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.95);\r\n  padding: 20rpx 40rpx;\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 100;\r\n}\r\n\r\n.submitBtn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  padding: 25rpx;\r\n  border-radius: 25rpx;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: translateY(2rpx);\r\n    box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);\r\n  }\r\n}\r\n\r\n.mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.blackDefault {\r\n  background: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 60rpx 40rpx;\r\n  margin: 0 40rpx;\r\n  text-align: center;\r\n  max-width: 600rpx;\r\n  width: 100%;\r\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.blackStateIcon,\r\n.locationStateIcon {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  margin: 0 auto 30rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 60rpx;\r\n}\r\n\r\n.blackStateIcon {\r\n  background: #ff4757;\r\n\r\n  &::before {\r\n    content: '⚠️';\r\n  }\r\n}\r\n\r\n.locationStateIcon {\r\n  background: #ffa502;\r\n\r\n  &::before {\r\n    content: '📍';\r\n  }\r\n}\r\n\r\n.blackStateT {\r\n  margin-bottom: 40rpx;\r\n\r\n  text {\r\n    display: block;\r\n    font-size: 32rpx;\r\n    color: #333333;\r\n    line-height: 1.5;\r\n    margin-bottom: 15rpx;\r\n  }\r\n}\r\n\r\n.blackDate {\r\n  color: #ff4757;\r\n  font-weight: bold;\r\n}\r\n\r\n.blackBtn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  padding: 20rpx 60rpx;\r\n  border-radius: 25rpx;\r\n  font-size: 30rpx;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: translateY(2rpx);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* 响应式适配 */\r\n@media screen and (max-width: 750rpx) {\r\n  .main {\r\n    width: calc(100% - 40rpx);\r\n    margin-left: 20rpx;\r\n    margin-right: 20rpx;\r\n  }\r\n\r\n  .show_fixed {\r\n    padding: 30rpx;\r\n  }\r\n\r\n  .filmItem {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n  }\r\n\r\n  .itemLeft {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .cover {\r\n    margin-right: 0;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .chooseNum {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.filmDes {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.filmInfofont {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>\r\n", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/vieworder/filmdes.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "formatTime"], "mappings": ";;;;AAmKA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,iBAAiB;AAAA;AAAA,MAGjB,WAAW;AAAA,QACT,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAK;AAAA,QAC3B,EAAE,OAAO,MAAM,OAAO,IAAI;AAAA,MAC3B;AAAA,MACD,YAAY;AAAA;AAAA,MAGZ,UAAU,CAAE;AAAA;AAAA,MAGZ,MAAM,CAAE;AAAA,MACR,eAAe;AAAA;AAAA,MAGf,cAAc,CAAC,QAAQ,MAAM;AAAA;AAAA,MAG7B,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA;AAAA,MAGnD,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,eAAe;AAAA;AAAA,MAGf,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,OAAO;AAAA;AAAA,MAGP,oBAAoB;AAAA;AAAA,MAGpB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,IACvB;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,cAAc;AACZ,aAAO,CAAC,UAAU;AAChB,YAAI,CAAC,KAAK,SAAS,KAAK,KAAK,CAAC,KAAK,SAAS,KAAK,EAAE,oBAAoB;AACrE,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,KAAK,SAAS,KAAK,EAAE;AACjC,eAAO,IAAI,QAAQ,IAAI,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,MACpD;AAAA,IACF;AAAA,EACD;AAAA,EAED,OAAO,QAAQ;AACb,SAAK,gBAAgB,OAAO;AAAA,EAC7B;AAAA,EAED,SAAS;AACP,SAAK,QAAQ;AAGb,QAAIA,cAAG,MAAC,eAAe,WAAW,GAAG;AACnC,WAAK,kBAAkB;AACvB,WAAK,WAAW,KAAK,MAAMA,cAAAA,MAAI,eAAe,WAAW,CAAC;AAC1D,WAAK,aAAa,KAAK,SAAS,SAAS;AAAA,WACpC;AACL,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACD;AAAA,EAED,SAAS;AACPA,kBAAG,MAAC,kBAAkB,WAAW;AACjC,kBAAc,KAAK,KAAK;AAAA,EACzB;AAAA,EAED,WAAW;AACT,kBAAc,KAAK,KAAK;AAAA,EACzB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,SAAS,KAAK;AACZ,WAAK,aAAa,SAAS,GAAG,IAAI;AAClCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,iCAAiC,GAAG;AAAA,OAC1C;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,UAAU;AACd,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,eAAe,KAAK;AAAA,UACtB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,OAAO;AAAA,YACV,GAAG,IAAI,KAAK;AAAA,YACZ,eAAeC,aAAU,WAAC,IAAI,KAAK,KAAK,cAAc,QAAQ,MAAM,GAAG,GAAG,KAAK;AAAA,YAC/E,aAAaA,aAAU,WAAC,IAAI,KAAK,KAAK,YAAY,QAAQ,MAAM,GAAG,GAAG,KAAK;AAAA,UAC7E;AACA,eAAK,aAAa;AAAA,QACpB;AAAA,MACA,SAAO,OAAO;AACdD,sBAAAA,MAAc,MAAA,SAAA,0CAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACb,oBAAc,KAAK,KAAK;AAExB,YAAM,eAAc,oBAAI;AAAA,QACtB,KAAK,KAAK,iBAAiB,QAAQ,MAAM,GAAG,IAAI,MAAM,KAAK,KAAK;AAAA,MACjE,GAAC,QAAQ;AACV,YAAM,sBAAsB,IAAI,KAAK,KAAK,KAAK,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAAE,QAAQ;AAC5F,UAAI,WAAU,oBAAI,KAAM,GAAC,QAAQ;AACjC,YAAM,OAAO,cAAc,UAAU;AAGrC,WAAK,kBAAkB,OAAO,uBAAuB,OAAQ,OAAU;AAEvE,WAAK,QAAQ,YAAY,MAAM;AAC7B,mBAAU,oBAAI,KAAM,GAAC,QAAQ;AAG7B,YAAI,eAAe,SAAS;AAC1B,wBAAc,KAAK,KAAK;AACxBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,SAAS,CAAC,QAAQ;AAChB,kBAAI,IAAI,SAAS;AACfA,8BAAAA,MAAI,aAAa;AAAA,cACnB;AAAA,YACF;AAAA,WACD;AACD;AAAA,QACF;AAEA,aAAK,kBAAkB,OAAO,uBAAuB,OAAQ,OAAU;AACvE,cAAM,aAAa,cAAc;AACjC,aAAK,YAAY,KAAK,gBAAgB,UAAU;AAAA,MACjD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,gBAAgB,MAAM;AACpB,UAAI,QAAQ;AAAG,eAAO;AAEtB,YAAM,UAAU,KAAK,MAAM,OAAO,GAAK;AACvC,YAAM,UAAU,KAAK,MAAO,OAAO,MAAS,GAAI;AAEhD,aAAO,GAAG,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,IACrF;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW,GAAG;AAChDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,KAAK,kBAAkB,GAAG;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,SACR;AAED,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,eAAe,KAAK;AAAA,YACpB,YAAY,KAAK,SAAS,IAAI,UAAQ,KAAK,EAAE,EAAE,KAAK,GAAG;AAAA,YACvD,cAAc,KAAK,SAAS;AAAA,UAC9B;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpBA,wBAAAA,MAAI,YAAY;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,WACR;AAGD,qBAAW,MAAM;AACfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,oDAAoD,IAAI,KAAK,KAAK,WAAW,kBAAkB,KAAK,aAAa;AAAA,aACvH;AAAA,UACF,GAAE,IAAI;AAAA,eACF;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,QACnC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAA,MAAA,SAAA,0CAAc,WAAW,KAAK;AAG9B,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK,GAAG;AAClD,eAAK,cAAc,MAAM,OAAO;AAAA,eAC3B;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,SAAS;AACrB,WAAK,YAAY;AACjB,WAAK,kBAAkB;AAAA,IACxB;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,kBAAkB;AACvBA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,qBAAqB;AAC1BA,oBAAAA,MAAI,aAAa;AAAA,IACnB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnaA,GAAG,WAAW,eAAe;"}