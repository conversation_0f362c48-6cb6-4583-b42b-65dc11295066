<view class="uni-calendar data-v-c3423b62">
    <view bindtap="{{c}}" class="{{['uni-calendar__mask','data-v-c3423b62',b&&'uni-calendar--mask-show']}}" wx:if="{{a}}"></view>
    <view class="{{['uni-calendar__content','data-v-c3423b62',z&&'uni-calendar--fixed',A&&'uni-calendar--ani-show']}}" wx:if="{{d}}">
        <view class="uni-calendar__header uni-calendar--fixed-top data-v-c3423b62" wx:if="{{e}}">
            <view bindtap="{{g}}" class="uni-calendar__header-btn-box data-v-c3423b62">
                <text class="uni-calendar__header-text uni-calendar--fixed-width data-v-c3423b62">{{f}}</text>
            </view>
            <view bindtap="{{i}}" class="uni-calendar__header-btn-box data-v-c3423b62">
                <text class="uni-calendar__header-text uni-calendar--fixed-width data-v-c3423b62">{{h}}</text>
            </view>
        </view>
        <view class="uni-calendar__header data-v-c3423b62">
            <view catchtap="{{j}}" class="uni-calendar__header-btn-box data-v-c3423b62">
                <view class="uni-calendar__header-btn uni-calendar--left data-v-c3423b62"></view>
            </view>
            <picker bindchange="{{m}}" class="data-v-c3423b62" fields="month" mode="date" value="{{l}}">
                <text class="uni-calendar__header-text data-v-c3423b62">{{k}}</text>
            </picker>
            <view catchtap="{{n}}" class="uni-calendar__header-btn-box data-v-c3423b62">
                <view class="uni-calendar__header-btn uni-calendar--right data-v-c3423b62"></view>
            </view>
        </view>
        <view class="uni-calendar__box data-v-c3423b62">
            <view class="uni-calendar__box-bg data-v-c3423b62" wx:if="{{o}}">
                <text class="uni-calendar__box-bg-text data-v-c3423b62">{{p}}</text>
            </view>
            <view class="uni-calendar__weeks data-v-c3423b62">
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{q}}</text>
                </view>
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{r}}</text>
                </view>
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{s}}</text>
                </view>
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{t}}</text>
                </view>
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{v}}</text>
                </view>
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{w}}</text>
                </view>
                <view class="uni-calendar__weeks-day data-v-c3423b62">
                    <text class="uni-calendar__weeks-day-text data-v-c3423b62">{{x}}</text>
                </view>
            </view>
            <view class="uni-calendar__weeks data-v-c3423b62" wx:for="{{y}}" wx:key="b">
                <view class="uni-calendar__weeks-item data-v-c3423b62" wx:for="{{item.a}}" wx:for-item="weeks" wx:key="d">
                    <calendar-item bind:__l="__l" bindchange="{{weeks.a}}" class="uni-calendar-item--hook data-v-c3423b62" uI="{{weeks.b}}" uP="{{weeks.c}}" wx:if="{{weeks.c}}"></calendar-item>
                </view>
            </view>
        </view>
    </view>
</view>
