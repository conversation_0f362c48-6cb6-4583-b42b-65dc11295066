{"version": 3, "file": "auth.js", "sources": ["utils/auth.js"], "sourcesContent": ["const TOKEN_KEY = 'token'\r\nconst REQUEST_CODE_KEY = 'requestCode'\r\n\r\n// 获取token\r\nexport function getToken() {\r\n  try {\r\n    return uni.getStorageSync(TOKEN_KEY)\r\n  } catch (error) {\r\n    console.error('获取token失败:', error)\r\n    return ''\r\n  }\r\n}\r\n\r\n// 设置token\r\nexport function setToken(token) {\r\n  try {\r\n    return uni.setStorageSync(TOKEN_KEY, token)\r\n  } catch (error) {\r\n    console.error('设置token失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\n// 移除token\r\nexport function removeToken() {\r\n  try {\r\n    uni.removeStorageSync(REQUEST_CODE_KEY)\r\n    uni.removeStorageSync(TOKEN_KEY)\r\n    return true\r\n  } catch (error) {\r\n    console.error('移除token失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\n// 检查token是否存在\r\nexport function hasToken() {\r\n  const token = getToken()\r\n  return token && token.length > 0\r\n}\r\n\r\n// 清除所有认证相关数据\r\nexport function clearAuth() {\r\n  try {\r\n    removeToken()\r\n    // 清除其他认证相关的存储数据\r\n    uni.removeStorageSync('userInfo')\r\n    uni.removeStorageSync('openid')\r\n    return true\r\n  } catch (error) {\r\n    console.error('清除认证数据失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\n// 平台兼容性处理\r\nexport function getOpenId() {\r\n  try {\r\n    // #ifdef MP-WEIXIN\r\n    return uni.getStorageSync('openid')\r\n    // #endif\r\n    \r\n    // #ifndef MP-WEIXIN\r\n    return ''\r\n    // #endif\r\n  } catch (error) {\r\n    console.error('获取openid失败:', error)\r\n    return ''\r\n  }\r\n}\r\n\r\nexport function setOpenId(openid) {\r\n  try {\r\n    // #ifdef MP-WEIXIN\r\n    return uni.setStorageSync('openid', openid)\r\n    // #endif\r\n    \r\n    // #ifndef MP-WEIXIN\r\n    return true\r\n    // #endif\r\n  } catch (error) {\r\n    console.error('设置openid失败:', error)\r\n    return false\r\n  }\r\n}"], "names": ["uni"], "mappings": ";;;AAAA,MAAM,YAAY;AAClB,MAAM,mBAAmB;AAGlB,SAAS,WAAW;AACzB,MAAI;AACF,WAAOA,cAAG,MAAC,eAAe,SAAS;AAAA,EACpC,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,sBAAA,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAGO,SAAS,SAAS,OAAO;AAC9B,MAAI;AACF,WAAOA,oBAAI,eAAe,WAAW,KAAK;AAAA,EAC3C,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uBAAA,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAGO,SAAS,cAAc;AAC5B,MAAI;AACFA,kBAAG,MAAC,kBAAkB,gBAAgB;AACtCA,kBAAG,MAAC,kBAAkB,SAAS;AAC/B,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uBAAA,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;AAGO,SAAS,WAAW;AACzB,QAAM,QAAQ,SAAU;AACxB,SAAO,SAAS,MAAM,SAAS;AACjC;AAGO,SAAS,YAAY;AAC1B,MAAI;AACF,gBAAa;AAEbA,kBAAG,MAAC,kBAAkB,UAAU;AAChCA,kBAAG,MAAC,kBAAkB,QAAQ;AAC9B,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,uBAAc,aAAa,KAAK;AAChC,WAAO;AAAA,EACR;AACH;AAGO,SAAS,YAAY;AAC1B,MAAI;AAEF,WAAOA,cAAG,MAAC,eAAe,QAAQ;AAAA,EAMnC,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uBAAA,eAAe,KAAK;AAClC,WAAO;AAAA,EACR;AACH;AAEO,SAAS,UAAU,QAAQ;AAChC,MAAI;AAEF,WAAOA,oBAAI,eAAe,UAAU,MAAM;AAAA,EAM3C,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,uBAAA,eAAe,KAAK;AAClC,WAAO;AAAA,EACR;AACH;;;;;;;;"}