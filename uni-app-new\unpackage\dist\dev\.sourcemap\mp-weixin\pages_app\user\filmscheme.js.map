{"version": 3, "file": "filmscheme.js", "sources": ["pages_app/user/filmscheme.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHVzZXJcZmlsbXNjaGVtZS52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"film-scheme\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      title=\"观影记录\" \r\n      :isBack=\"true\" \r\n      :isShowHome=\"true\"\r\n      background=\"#ffffff\"\r\n      color=\"#333333\"\r\n    />\r\n    \r\n    <!-- 记录列表 -->\r\n    <scroll-view \r\n      class=\"scheme-list\" \r\n      scroll-y \r\n      @scrolltolower=\"loadMore\"\r\n      :style=\"{ paddingBottom: safeAreaBottom + 'px' }\"\r\n    >\r\n      <view class=\"list-container\">\r\n        <!-- 空状态 -->\r\n        <view v-if=\"filmList.length === 0 && !loading\" class=\"empty-state\">\r\n          <image class=\"empty-icon\" src=\"/static/img/common/empty.png\" mode=\"aspectFit\" />\r\n          <text class=\"empty-text\">暂无观影记录</text>\r\n        </view>\r\n        \r\n        <!-- 记录项 -->\r\n        <view \r\n          v-for=\"(item, index) in filmList\" \r\n          :key=\"item.filmSessionId || index\"\r\n          class=\"scheme-item\"\r\n        >\r\n          <!-- 标题 -->\r\n          <view class=\"item-header\">\r\n            <text class=\"venue-name\">宝安科技馆 观影预约</text>\r\n            <view :class=\"['status-badge', getStatusClass(item.subscribeState)]\">\r\n              {{ getStatusText(item.subscribeState) }}\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 电影信息 -->\r\n          <view class=\"film-info\">\r\n            <view class=\"film-poster\">\r\n              <image \r\n                :src=\"item.filmCover\" \r\n                mode=\"aspectFill\" \r\n                class=\"poster-image\"\r\n                @error=\"handleImageError\"\r\n              />\r\n            </view>\r\n            <view class=\"film-details\">\r\n              <text class=\"film-name\">{{ item.filmName }}</text>\r\n              <text class=\"film-type\">类型：{{ filmTypeList[item.filmType - 1] || '未知' }}</text>\r\n              <text class=\"film-time\">时间：{{ item.filmStartTime }} - {{ item.filmEndTime }}</text>\r\n              <text class=\"film-date\">\r\n                日期：{{ item.filmArrangedDate }} {{ getWeekDay(item.filmArrangedDate) }}\r\n              </text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 预约信息 -->\r\n          <view class=\"booking-info\">\r\n            <text class=\"ticket-count\">预约票数：{{ item.subscribeType }}张</text>\r\n          </view>\r\n          \r\n          <!-- 操作按钮 -->\r\n          <view class=\"action-buttons\" v-if=\"showActionButtons(item.subscribeState)\">\r\n            <button \r\n              v-if=\"canCancel(item.subscribeState)\"\r\n              class=\"cancel-btn\"\r\n              @tap=\"cancelBooking(item)\"\r\n            >\r\n              取消预约\r\n            </button>\r\n            <button \r\n              v-if=\"canViewQRCode(item.subscribeState)\"\r\n              class=\"qrcode-btn\"\r\n              @tap=\"viewQRCode(item)\"\r\n            >\r\n              查看凭证\r\n            </button>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 加载更多 -->\r\n        <view v-if=\"loading\" class=\"loading-more\">\r\n          <text class=\"loading-text\">加载中...</text>\r\n        </view>\r\n        \r\n        <view v-if=\"!hasMore && filmList.length > 0\" class=\"no-more\">\r\n          <text class=\"no-more-text\">没有更多记录了</text>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'FilmScheme',\r\n  data() {\r\n    return {\r\n      filmList: [],\r\n      filmTypeList: ['球幕电影', '4D电影'],\r\n      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\r\n      statusTextList: ['去签到', '已过期(未检票)', '已取消', '去检票', '已完成', '已过期(未签到)', '场次取消'],\r\n      \r\n      // 分页参数\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      loading: false,\r\n      hasMore: true,\r\n      \r\n      // 安全区域\r\n      safeAreaBottom: 0\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    // 获取安全区域信息\r\n    const systemInfo = uni.getSystemInfoSync()\r\n    this.safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0\r\n  },\r\n  \r\n  onShow() {\r\n    this.resetData()\r\n    this.getFilmList()\r\n  },\r\n  \r\n  methods: {\r\n    // 重置数据\r\n    resetData() {\r\n      this.filmList = []\r\n      this.pageNum = 1\r\n      this.total = 0\r\n      this.hasMore = true\r\n    },\r\n    \r\n    // 获取观影记录列表\r\n    async getFilmList() {\r\n      if (this.loading || !this.hasMore) return\r\n      \r\n      this.loading = true\r\n      \r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/fileSession/personalCenterFilm',\r\n          method: 'get',\r\n          data: {\r\n            pageNum: this.pageNum,\r\n            pageSize: this.pageSize\r\n          }\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          const dataList = res.data.data.rows || []\r\n          this.total = res.data.data.total || 0\r\n          \r\n          // 处理数据\r\n          const processedList = dataList.map(item => ({\r\n            ...item,\r\n            filmStartTime: this.formatTime(item.filmStartTime),\r\n            filmEndTime: this.formatTime(item.filmEndTime)\r\n          }))\r\n          \r\n          if (this.pageNum === 1) {\r\n            this.filmList = processedList\r\n          } else {\r\n            this.filmList.push(...processedList)\r\n          }\r\n          \r\n          // 检查是否还有更多数据\r\n          this.hasMore = this.filmList.length < this.total\r\n        } else {\r\n          throw new Error(res.msg || '获取记录失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取观影记录失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '获取记录失败',\r\n          icon: 'error'\r\n        })\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    // 加载更多\r\n    loadMore() {\r\n      if (!this.hasMore || this.loading) return\r\n\r\n      this.pageNum++\r\n      this.getFilmList()\r\n    },\r\n\r\n    // 格式化时间\r\n    formatTime(timeStr) {\r\n      if (!timeStr) return ''\r\n\r\n      try {\r\n        const date = new Date(timeStr.replace(/-/g, '/'))\r\n        const hours = date.getHours().toString().padStart(2, '0')\r\n        const minutes = date.getMinutes().toString().padStart(2, '0')\r\n        return `${hours}:${minutes}`\r\n      } catch (error) {\r\n        return timeStr\r\n      }\r\n    },\r\n\r\n    // 获取星期\r\n    getWeekDay(dateStr) {\r\n      if (!dateStr) return ''\r\n\r\n      try {\r\n        const date = new Date(dateStr.replace(/-/g, '/'))\r\n        return this.weekList[date.getDay()]\r\n      } catch (error) {\r\n        return ''\r\n      }\r\n    },\r\n\r\n    // 获取状态文本\r\n    getStatusText(state) {\r\n      if (state >= 0 && state < this.statusTextList.length) {\r\n        return this.statusTextList[state]\r\n      }\r\n      return '未知状态'\r\n    },\r\n\r\n    // 获取状态样式类\r\n    getStatusClass(state) {\r\n      const statusMap = {\r\n        0: 'status-pending',    // 去签到\r\n        1: 'status-expired',    // 已过期(未检票)\r\n        2: 'status-cancelled',  // 已取消\r\n        3: 'status-checkin',    // 去检票\r\n        4: 'status-completed',  // 已完成\r\n        5: 'status-expired',    // 已过期(未签到)\r\n        6: 'status-cancelled'   // 场次取消\r\n      }\r\n      return statusMap[state] || 'status-unknown'\r\n    },\r\n\r\n    // 是否显示操作按钮\r\n    showActionButtons(state) {\r\n      return state === 1 || state === 4 || this.canCancel(state)\r\n    },\r\n\r\n    // 是否可以取消\r\n    canCancel(state) {\r\n      return state === 1 || state === 4\r\n    },\r\n\r\n    // 是否可以查看二维码\r\n    canViewQRCode(state) {\r\n      return state === 1 || state === 4\r\n    },\r\n\r\n    // 取消预约\r\n    cancelBooking(item) {\r\n      uni.navigateTo({\r\n        url: `/pages_app/schemesuccess/filmcancel?vote=${item.subscribeType}&batchNumber=${item.batchNumber}&filmSessionId=${item.filmSessionId}`\r\n      })\r\n    },\r\n\r\n    // 查看二维码\r\n    viewQRCode(item) {\r\n      uni.navigateTo({\r\n        url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${item.batchNumber}&filmSessionId=${item.filmSessionId}`\r\n      })\r\n    },\r\n\r\n    // 图片加载失败处理\r\n    handleImageError(e) {\r\n      console.warn('图片加载失败:', e)\r\n      // 可以设置默认图片\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.film-scheme {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\r\n  .scheme-list {\r\n    height: calc(100vh - 88rpx);\r\n\r\n    .list-container {\r\n      padding: 20rpx;\r\n    }\r\n  }\r\n\r\n  // 空状态\r\n  .empty-state {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 120rpx 40rpx;\r\n\r\n    .empty-icon {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 30rpx;\r\n      opacity: 0.6;\r\n    }\r\n\r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: rgba(255, 255, 255, 0.7);\r\n    }\r\n  }\r\n\r\n  // 记录项\r\n  .scheme-item {\r\n    background: rgba(255, 255, 255, 0.95);\r\n    border-radius: 20rpx;\r\n    margin-bottom: 30rpx;\r\n    padding: 30rpx;\r\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n    .item-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n\r\n      .venue-name {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333;\r\n      }\r\n\r\n      .status-badge {\r\n        padding: 8rpx 16rpx;\r\n        border-radius: 20rpx;\r\n        font-size: 24rpx;\r\n        font-weight: 500;\r\n\r\n        &.status-pending {\r\n          background: #e3f2fd;\r\n          color: #1976d2;\r\n        }\r\n\r\n        &.status-expired {\r\n          background: #fce4ec;\r\n          color: #c2185b;\r\n        }\r\n\r\n        &.status-cancelled {\r\n          background: #f3e5f5;\r\n          color: #7b1fa2;\r\n        }\r\n\r\n        &.status-checkin {\r\n          background: #e8f5e8;\r\n          color: #388e3c;\r\n        }\r\n\r\n        &.status-completed {\r\n          background: #e8f5e8;\r\n          color: #388e3c;\r\n        }\r\n\r\n        &.status-unknown {\r\n          background: #f5f5f5;\r\n          color: #666;\r\n        }\r\n      }\r\n    }\r\n\r\n    .film-info {\r\n      display: flex;\r\n      margin-bottom: 20rpx;\r\n\r\n      .film-poster {\r\n        width: 120rpx;\r\n        height: 160rpx;\r\n        margin-right: 20rpx;\r\n        border-radius: 12rpx;\r\n        overflow: hidden;\r\n\r\n        .poster-image {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n\r\n      .film-details {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n\r\n        .film-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n          margin-bottom: 8rpx;\r\n          line-height: 1.4;\r\n        }\r\n\r\n        .film-type,\r\n        .film-time,\r\n        .film-date {\r\n          font-size: 26rpx;\r\n          color: #666;\r\n          margin-bottom: 6rpx;\r\n          line-height: 1.3;\r\n        }\r\n      }\r\n    }\r\n\r\n    .booking-info {\r\n      margin-bottom: 20rpx;\r\n\r\n      .ticket-count {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n\r\n    .action-buttons {\r\n      display: flex;\r\n      gap: 20rpx;\r\n\r\n      .cancel-btn,\r\n      .qrcode-btn {\r\n        flex: 1;\r\n        height: 80rpx;\r\n        border-radius: 40rpx;\r\n        font-size: 28rpx;\r\n        font-weight: 500;\r\n        border: none;\r\n\r\n        &::after {\r\n          border: none;\r\n        }\r\n      }\r\n\r\n      .cancel-btn {\r\n        background: linear-gradient(135deg, #ff6b6b, #ee5a52);\r\n        color: white;\r\n      }\r\n\r\n      .qrcode-btn {\r\n        background: linear-gradient(135deg, #4ecdc4, #44a08d);\r\n        color: white;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 加载状态\r\n  .loading-more {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 40rpx;\r\n\r\n    .loading-text {\r\n      font-size: 28rpx;\r\n      color: rgba(255, 255, 255, 0.7);\r\n    }\r\n  }\r\n\r\n  .no-more {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 40rpx;\r\n\r\n    .no-more-text {\r\n      font-size: 26rpx;\r\n      color: rgba(255, 255, 255, 0.5);\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.film-scheme {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.scheme-list {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>\r\n", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/user/filmscheme.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AAiGA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,cAAc,CAAC,QAAQ,MAAM;AAAA,MAC7B,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,MACnD,gBAAgB,CAAC,OAAO,YAAY,OAAO,OAAO,OAAO,YAAY,MAAM;AAAA;AAAA,MAG3E,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA;AAAA,MAGT,gBAAgB;AAAA,IAClB;AAAA,EACD;AAAA,EAED,SAAS;;AAEP,UAAM,aAAaA,cAAG,MAAC,kBAAkB;AACzC,SAAK,mBAAiB,gBAAW,mBAAX,mBAA2B,WAAU;AAAA,EAC5D;AAAA,EAED,SAAS;AACP,SAAK,UAAU;AACf,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,YAAY;AACV,WAAK,WAAW,CAAC;AACjB,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,KAAK,WAAW,CAAC,KAAK;AAAS;AAEnC,WAAK,UAAU;AAEf,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,SAAS,KAAK;AAAA,YACd,UAAU,KAAK;AAAA,UACjB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,WAAW,IAAI,KAAK,KAAK,QAAQ,CAAC;AACxC,eAAK,QAAQ,IAAI,KAAK,KAAK,SAAS;AAGpC,gBAAM,gBAAgB,SAAS,IAAI,WAAS;AAAA,YAC1C,GAAG;AAAA,YACH,eAAe,KAAK,WAAW,KAAK,aAAa;AAAA,YACjD,aAAa,KAAK,WAAW,KAAK,WAAW;AAAA,UAC/C,EAAE;AAEF,cAAI,KAAK,YAAY,GAAG;AACtB,iBAAK,WAAW;AAAA,iBACX;AACL,iBAAK,SAAS,KAAK,GAAG,aAAa;AAAA,UACrC;AAGA,eAAK,UAAU,KAAK,SAAS,SAAS,KAAK;AAAA,eACtC;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,QAAQ;AAAA,QACrC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,UAAU;AAAA,MACjB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AACT,UAAI,CAAC,KAAK,WAAW,KAAK;AAAS;AAEnC,WAAK;AACL,WAAK,YAAY;AAAA,IAClB;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC1B,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,eAAO,KAAK,SAAS,KAAK,OAAM,CAAE;AAAA,MAClC,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,UAAI,SAAS,KAAK,QAAQ,KAAK,eAAe,QAAQ;AACpD,eAAO,KAAK,eAAe,KAAK;AAAA,MAClC;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,YAAM,YAAY;AAAA,QAChB,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,QACH,GAAG;AAAA;AAAA,MACL;AACA,aAAO,UAAU,KAAK,KAAK;AAAA,IAC5B;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACvB,aAAO,UAAU,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK;AAAA,IAC1D;AAAA;AAAA,IAGD,UAAU,OAAO;AACf,aAAO,UAAU,KAAK,UAAU;AAAA,IACjC;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,aAAO,UAAU,KAAK,UAAU;AAAA,IACjC;AAAA;AAAA,IAGD,cAAc,MAAM;AAClBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4CAA4C,KAAK,aAAa,gBAAgB,KAAK,WAAW,kBAAkB,KAAK,aAAa;AAAA,OACxI;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,MAAM;AACfA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,oDAAoD,KAAK,WAAW,kBAAkB,KAAK,aAAa;AAAA,OAC9G;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClBA,oBAAAA,MAAA,MAAA,QAAA,wCAAa,WAAW,CAAC;AAAA,IAE3B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrRA,GAAG,WAAW,eAAe;"}