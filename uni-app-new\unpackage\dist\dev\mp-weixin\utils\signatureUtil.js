"use strict";
function simpleMD5(str) {
  let hash = 0;
  if (str.length === 0)
    return hash.toString();
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16).padStart(8, "0");
}
function serializeData(data) {
  if (typeof data === "string") {
    try {
      data = JSON.parse(data);
    } catch (error) {
      return "";
    }
  }
  if (!data || typeof data !== "object") {
    return "";
  }
  let result = "";
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      result += key + "=" + data[key] + "&";
    }
  }
  return result;
}
function signatureGenerate({ data, url, headers }) {
  const timestamp = (/* @__PURE__ */ new Date()).getTime();
  const dataStr = serializeData(data);
  const signStr = "&timestamp=" + timestamp + "&url=" + url + dataStr;
  return {
    signature: simpleMD5(signStr).toUpperCase(),
    timestamp
  };
}
exports.signatureGenerate = signatureGenerate;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/signatureUtil.js.map
