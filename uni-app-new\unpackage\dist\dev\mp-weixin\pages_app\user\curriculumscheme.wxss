/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.curriculum-scheme.data-v-3ca1b3b8 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.curriculum-scheme .scheme-list.data-v-3ca1b3b8 {
  height: calc(100vh - 88rpx);
}
.curriculum-scheme .scheme-list .list-container.data-v-3ca1b3b8 {
  padding: 20rpx;
}
.curriculum-scheme .empty-state.data-v-3ca1b3b8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}
.curriculum-scheme .empty-state .empty-icon.data-v-3ca1b3b8 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.curriculum-scheme .empty-state .empty-text.data-v-3ca1b3b8 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}
.curriculum-scheme .scheme-item.data-v-3ca1b3b8 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.curriculum-scheme .scheme-item .item-header.data-v-3ca1b3b8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.curriculum-scheme .scheme-item .item-header .venue-name.data-v-3ca1b3b8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.curriculum-scheme .scheme-item .item-header .status-badge.data-v-3ca1b3b8 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.curriculum-scheme .scheme-item .item-header .status-badge.status-pending.data-v-3ca1b3b8 {
  background: #e3f2fd;
  color: #1976d2;
}
.curriculum-scheme .scheme-item .item-header .status-badge.status-expired.data-v-3ca1b3b8 {
  background: #fce4ec;
  color: #c2185b;
}
.curriculum-scheme .scheme-item .item-header .status-badge.status-cancelled.data-v-3ca1b3b8 {
  background: #f3e5f5;
  color: #7b1fa2;
}
.curriculum-scheme .scheme-item .item-header .status-badge.status-checkin.data-v-3ca1b3b8 {
  background: #e8f5e8;
  color: #388e3c;
}
.curriculum-scheme .scheme-item .item-header .status-badge.status-completed.data-v-3ca1b3b8 {
  background: #e8f5e8;
  color: #388e3c;
}
.curriculum-scheme .scheme-item .item-header .status-badge.status-unknown.data-v-3ca1b3b8 {
  background: #f5f5f5;
  color: #666;
}
.curriculum-scheme .scheme-item .course-info.data-v-3ca1b3b8 {
  margin-bottom: 20rpx;
}
.curriculum-scheme .scheme-item .course-info .course-title.data-v-3ca1b3b8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}
.curriculum-scheme .scheme-item .course-info .course-details .detail-row.data-v-3ca1b3b8 {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}
.curriculum-scheme .scheme-item .course-info .course-details .detail-row.data-v-3ca1b3b8:last-child {
  margin-bottom: 0;
}
.curriculum-scheme .scheme-item .course-info .course-details .detail-row .label.data-v-3ca1b3b8 {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}
.curriculum-scheme .scheme-item .course-info .course-details .detail-row .value.data-v-3ca1b3b8 {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.3;
}
.curriculum-scheme .scheme-item .action-buttons.data-v-3ca1b3b8 {
  display: flex;
  gap: 20rpx;
}
.curriculum-scheme .scheme-item .action-buttons .cancel-btn.data-v-3ca1b3b8,
.curriculum-scheme .scheme-item .action-buttons .qrcode-btn.data-v-3ca1b3b8 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}
.curriculum-scheme .scheme-item .action-buttons .cancel-btn.data-v-3ca1b3b8::after,
.curriculum-scheme .scheme-item .action-buttons .qrcode-btn.data-v-3ca1b3b8::after {
  border: none;
}
.curriculum-scheme .scheme-item .action-buttons .cancel-btn.data-v-3ca1b3b8 {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}
.curriculum-scheme .scheme-item .action-buttons .qrcode-btn.data-v-3ca1b3b8 {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}
.curriculum-scheme .loading-more.data-v-3ca1b3b8 {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}
.curriculum-scheme .loading-more .loading-text.data-v-3ca1b3b8 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}
.curriculum-scheme .no-more.data-v-3ca1b3b8 {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}
.curriculum-scheme .no-more .no-more-text.data-v-3ca1b3b8 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 平台特定样式 */
.scheme-list.data-v-3ca1b3b8 {
  padding-bottom: env(safe-area-inset-bottom);
}