import { constant } from './constant.js'

const STORAGE_KEY = 'storage_data'
const allowedKeys = [
  constant.avatar,
  constant.username,
  constant.password,
  constant.roles,
  constant.tenantId,
  constant.rememberMe,
  constant.permissions
]

// 本地存储 - 关键适配点：uni.setStorageSync 替代 wx.setStorageSync
export const storage = {
  set(key, value) {
    if (allowedKeys.indexOf(key) !== -1) {
      try {
        let storageData = uni.getStorageSync(STORAGE_KEY) || {}
        storageData[key] = value
        uni.setStorageSync(STORAGE_KEY, storageData)
        return true
      } catch (error) {
        console.error('存储数据失败:', error)
        return false
      }
    }
    return false
  },

  get(key) {
    try {
      const storageData = uni.getStorageSync(STORAGE_KEY) || {}
      return storageData[key] || ''
    } catch (error) {
      console.error('读取存储数据失败:', error)
      return ''
    }
  },

  remove(key) {
    try {
      let storageData = uni.getStorageSync(STORAGE_KEY) || {}
      delete storageData[key]
      uni.setStorageSync(STORAGE_KEY, storageData)
      return true
    } catch (error) {
      console.error('删除存储数据失败:', error)
      return false
    }
  },

  clean() {
    try {
      uni.removeStorageSync(STORAGE_KEY)
      return true
    } catch (error) {
      console.error('清空存储数据失败:', error)
      return false
    }
  },

  // 检查key是否被允许
  isAllowedKey(key) {
    return allowedKeys.indexOf(key) !== -1
  },

  // 获取所有存储的数据
  getAll() {
    try {
      return uni.getStorageSync(STORAGE_KEY) || {}
    } catch (error) {
      console.error('获取所有存储数据失败:', error)
      return {}
    }
  }
}

// 通用存储方法 - 跨平台兼容
export function setStorage(key, value) {
  try {
    // 平台兼容性处理
    // #ifdef H5
    // H5平台可能有存储限制，需要检查
    if (typeof value === 'object') {
      const jsonStr = JSON.stringify(value)
      if (jsonStr.length > 5 * 1024 * 1024) { // 5MB限制
        console.warn('存储数据过大，可能在某些平台失败')
      }
    }
    // #endif
    
    uni.setStorageSync(key, value)
    return true
  } catch (error) {
    console.error('存储失败:', error)
    
    // #ifdef H5
    // H5平台存储失败时的降级处理
    if (error.name === 'QuotaExceededError') {
      console.error('存储空间不足，尝试清理旧数据')
      // 可以在这里实现清理逻辑
    }
    // #endif
    
    return false
  }
}

export function getStorage(key) {
  try {
    const value = uni.getStorageSync(key)
    
    // #ifdef APP-PLUS
    // App平台可能需要特殊处理
    if (value === undefined || value === null) {
      return null
    }
    // #endif
    
    return value
  } catch (error) {
    console.error('读取存储失败:', error)
    return null
  }
}

export function removeStorage(key) {
  try {
    uni.removeStorageSync(key)
    return true
  } catch (error) {
    console.error('删除存储失败:', error)
    return false
  }
}

export function clearStorage() {
  try {
    uni.clearStorageSync()
    return true
  } catch (error) {
    console.error('清空存储失败:', error)
    return false
  }
}

// 异步存储方法（推荐在不阻塞UI的场景使用）
export function setStorageAsync(key, value) {
  return new Promise((resolve, reject) => {
    uni.setStorage({
      key,
      data: value,
      success: () => resolve(true),
      fail: (error) => {
        console.error('异步存储失败:', error)
        reject(error)
      }
    })
  })
}

export function getStorageAsync(key) {
  return new Promise((resolve, reject) => {
    uni.getStorage({
      key,
      success: (res) => resolve(res.data),
      fail: (error) => {
        console.error('异步读取存储失败:', error)
        resolve(null)
      }
    })
  })
}

// 获取存储信息
export function getStorageInfo() {
  return new Promise((resolve, reject) => {
    uni.getStorageInfo({
      success: (res) => resolve(res),
      fail: (error) => {
        console.error('获取存储信息失败:', error)
        reject(error)
      }
    })
  })
}