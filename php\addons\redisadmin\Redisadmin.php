<?php
namespace addons\redisadmin;

use think\Addons;
use app\common\library\Menu;

class Redisadmin extends Addons
{
    public function install()
    {
        $menu = [
            [
                'name'    => 'redisadmin',
                'title'   => 'Redis管理',
                'icon'    => 'fa fa-database',
                'ismenu'  => 1,
                'weigh'   => 1,
                'remark'  => 'Redis 数据管理',
                'sublist' => [
                    [
                        'name'    => 'redisadmin/manage',
                        'title'   => '键值操作',
                        'icon'    => 'fa fa-key',
                        'ismenu'  => 1,
                        'weigh'   => 3,
                        'sublist' => [
                            ['name' => 'redisadmin/manage/index', 'title' => '查看'],
                            ['name' => 'redisadmin/manage/edit', 'title' => '编辑'],
                            ['name' => 'redisadmin/manage/delete', 'title' => '删除'],
                            ['name' => 'redisadmin/manage/ttl', 'title' => '设置TTL'],
                            ['name' => 'redisadmin/manage/multi', 'title' => '批量操作'],
                        ]
                    ],
                    [
                        'name'    => 'redisadmin/stat',
                        'title'   => '统计分析',
                        'icon'    => 'fa fa-bar-chart',
                        'ismenu'  => 1,
                        'weigh'   => 2,
                        'sublist' => [
                            ['name' => 'redisadmin/stat/index', 'title' => '概览'],
                            ['name' => 'redisadmin/stat/memory', 'title' => '内存'],
                        ]
                    ],
                    [
                        'name'    => 'redisadmin/monitor',
                        'title'   => '实时监控',
                        'icon'    => 'fa fa-eye',
                        'ismenu'  => 1,
                        'weigh'   => 1,
                        'sublist' => [
                            ['name' => 'redisadmin/monitor/index', 'title' => '查看'],
                        ]
                    ]
                ]
            ]
        ];
        Menu::create($menu);
        return true;
    }

    public function uninstall()
    {
        Menu::delete('redisadmin');
        return true;
    }

    public function enable()
    {
        Menu::enable('redisadmin');
        return true;
    }

    public function disable()
    {
        Menu::disable('redisadmin');
        return true;
    }
}
