这是一套空的框架， 基于VUE2

1. 源代码样式作为基础。
2. 对比放编译的代码，查缺补漏。

## pages
### pages/index/index
1. 多了wsWxPrivacy
2. 去除了授权登录的内容



## subPackages": [
    {
      "root": "pages_app/",
      "pages": [
        "login/index",
        "register/index",
        "vieworder/index",
        "vieworder/filmdes",
        "contacts/index",
        "contacts/addcontact",
        "user/index",
        "user/filmscheme",
        "contactmanager/index",
        "schemesuccess/filmsuccess",
        "schemesuccess/filmcancel",
        "entervenue/index",
        "schemesuccess/venuesuccess",
        "schemesuccess/venuecancel",
        "curriculum/index",
        "curriculum/choosecurriculum",
        "schemesuccess/curriculumsuccess",
        "schemesuccess/curriculumcancel",
        "user/venuescheme",
        "user/curriculumscheme",
        "user/oneqrcode"
      ]