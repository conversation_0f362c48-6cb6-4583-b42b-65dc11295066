<view class="{{[c,'segmented-control']}}" style="{{'border-color:'+d}}">
    <view bindtap="{{item.j}}" class="{{[b,item.d,item.e,item.f,'segmented-control__item']}}" style="{{'background-color:'+item.h+';'+'border-color:'+item.i}}" wx:for="{{a}}" wx:key="g">
        <view>
            <text class="{{['segmented-control__text',item.c]}}" style="{{'color:'+item.b}}">{{item.a}}</text>
        </view>
    </view>
</view>
