"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "PrivacyPopup",
  props: {
    id: {
      type: String,
      default: "privacy-popup"
    }
  },
  data() {
    return {
      isShow: false
    };
  },
  mounted() {
    this.checkPrivacyStatus();
  },
  watch: {
    // 监听父组件传入的显示状态
    "$parent.showPrivacy"(newVal) {
      if (newVal) {
        this.show();
      } else {
        this.hide();
      }
    }
  },
  methods: {
    checkPrivacyStatus() {
      try {
        const privacyAgreed = common_vendor.index.getStorageSync("privacy_agreed");
        if (!privacyAgreed) {
          this.$nextTick(() => {
            this.show();
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/privacy-popup/privacy-popup.vue:64", "检查隐私状态失败:", error);
        this.show();
      }
    },
    show() {
      this.isShow = true;
      this.$refs.popup.open();
    },
    hide() {
      this.isShow = false;
      this.$refs.popup.close();
    },
    handleAgree() {
      try {
        common_vendor.index.setStorageSync("privacy_agreed", true);
        common_vendor.index.setStorageSync("privacy_agreed_time", Date.now());
        this.hide();
        this.$emit("agree");
        common_vendor.index.$emit && common_vendor.index.$emit("privacyAgree", true);
        common_vendor.index.showToast({
          title: "感谢您的信任",
          icon: "success",
          duration: 1500
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at components/privacy-popup/privacy-popup.vue:97", "保存隐私政策同意状态失败:", error);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "none"
        });
      }
    },
    handleReject() {
      this.hide();
      this.$emit("reject");
      common_vendor.index.$emit("privacyReject", false);
      common_vendor.index.showModal({
        title: "提示",
        content: "拒绝隐私政策将无法使用相关功能，您可以稍后在设置中重新选择。",
        showCancel: false,
        confirmText: "知道了"
      });
    }
  }
};
if (!Array) {
  const _component_uni_popup = common_vendor.resolveComponent("uni-popup");
  _component_uni_popup();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.handleReject && $options.handleReject(...args)),
    b: common_vendor.o((...args) => $options.handleAgree && $options.handleAgree(...args)),
    c: common_vendor.sr("popup", "af3fbef1-0"),
    d: common_vendor.p({
      type: "center",
      ["mask-click"]: false
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-af3fbef1"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/privacy-popup/privacy-popup.js.map
