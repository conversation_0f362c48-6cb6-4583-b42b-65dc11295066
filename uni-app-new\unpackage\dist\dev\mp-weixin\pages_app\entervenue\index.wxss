/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.entervenue.data-v-62d6126f {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.entervenue .content.data-v-62d6126f {
  padding-top: 120rpx;
}
.entervenue .content .venue_content .venue_top.data-v-62d6126f {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.entervenue .content .venue_content .venue_top .calendar.data-v-62d6126f {
  padding: 20rpx;
}
.entervenue .content .venue_content .venue_bottom.data-v-62d6126f {
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.entervenue .content .venue_content .venue_bottom.isChooseVenue.data-v-62d6126f {
  margin-bottom: 120rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box.data-v-62d6126f {
  padding: 30rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .title.data-v-62d6126f {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .venue_list.data-v-62d6126f {
  margin-bottom: 30rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .venue_list .venue_picker.data-v-62d6126f {
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  overflow: hidden;
  transition: border-color 0.3s ease;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .venue_list .venue_picker.data-v-62d6126f:focus-within {
  border-color: #667eea;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .line.data-v-62d6126f {
  display: flex;
  justify-content: center;
  margin: 40rpx 0;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .line .line_item.data-v-62d6126f {
  width: 8rpx;
  height: 8rpx;
  background: #ddd;
  border-radius: 50%;
  margin: 0 4rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .line .line_item.data-v-62d6126f:nth-child(odd) {
  background: #667eea;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .chooseNum.data-v-62d6126f {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .chooseNum .checkItem.data-v-62d6126f {
  flex: 1;
  min-width: 120rpx;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .chooseNum .checkItem.isCheck.data-v-62d6126f {
  border-color: #667eea;
  background: #667eea;
  color: white;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList.data-v-62d6126f {
  margin-bottom: 30rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem.data-v-62d6126f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem.data-v-62d6126f:last-child {
  border-bottom: none;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .left.data-v-62d6126f {
  flex: 1;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .left .name.data-v-62d6126f {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .left .phone.data-v-62d6126f {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .left .idcard.data-v-62d6126f {
  font-size: 24rpx;
  color: #999;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .right .checkbox.data-v-62d6126f {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .right .checkbox.checked.data-v-62d6126f {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .contactList .contactItem .right .checkbox.checked .check-icon.data-v-62d6126f {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .addContact.data-v-62d6126f {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  color: #666;
  font-size: 28rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .addContact.data-v-62d6126f:active {
  background: #f8f8f8;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .isChoose .addContact .add-icon.data-v-62d6126f {
  font-size: 36rpx;
  margin-right: 10rpx;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .submit_btn .submit.data-v-62d6126f {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .submit_btn .submit.data-v-62d6126f::after {
  border: none;
}
.entervenue .content .venue_content .venue_bottom .show_fixed_box .submit_btn .submit.data-v-62d6126f:disabled {
  background: #ccc;
  color: #999;
}

/* 平台特定样式 */
.content.data-v-62d6126f {
  padding-bottom: env(safe-area-inset-bottom);
}