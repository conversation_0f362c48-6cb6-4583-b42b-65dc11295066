{"version": 3, "file": "index.js", "sources": ["pages_app/vieworder/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHZpZXdvcmRlclxpbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"vieworder\">\r\n    <view class=\"content\">\r\n      <my-header\r\n        :isBack=\"true\"\r\n        :isShowHome=\"true\"\r\n        title=\"观影预约\"\r\n        background=\"transparent\"\r\n      />\r\n\r\n      <view class=\"movie_content\">\r\n        <view class=\"movie_list\">\r\n          <view\r\n            class=\"movie_item\"\r\n            v-for=\"(item, index) in movieList\"\r\n            :key=\"item.id\"\r\n            @click=\"goToMovieDetail(item)\"\r\n          >\r\n            <view class=\"movie_poster\">\r\n              <image\r\n                :src=\"item.movieCover\"\r\n                mode=\"aspectFill\"\r\n                class=\"poster_image\"\r\n              />\r\n            </view>\r\n            <view class=\"movie_info\">\r\n              <view class=\"movie_title\">{{ item.movieName }}</view>\r\n              <view class=\"movie_type\">{{ item.movieType }}</view>\r\n              <view class=\"movie_duration\">时长：{{ item.movieDuration }}分钟</view>\r\n              <view class=\"movie_rating\" v-if=\"item.movieRating\">\r\n                评分：{{ item.movieRating }}\r\n              </view>\r\n              <view class=\"movie_desc\">{{ item.movieDesc }}</view>\r\n            </view>\r\n            <view class=\"movie_action\">\r\n              <view class=\"movie_status\">{{ getMovieStatus(item) }}</view>\r\n              <button\r\n                class=\"reserve_btn\"\r\n                @click.stop=\"reserveMovie(item)\"\r\n                :disabled=\"!canReserve(item)\"\r\n              >\r\n                {{ getReserveText(item) }}\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"empty_state\" v-if=\"movieList.length === 0 && !isLoading\">\r\n          <view class=\"empty_icon\">🎬</view>\r\n          <view class=\"empty_text\">暂无电影信息</view>\r\n        </view>\r\n\r\n        <view class=\"loading_state\" v-if=\"isLoading\">\r\n          <view class=\"loading_icon\">⏳</view>\r\n          <view class=\"loading_text\">加载中...</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport MyHeader from '@/components/my-header/my-header.vue'\r\n\r\nexport default {\r\n  name: 'ViewOrder',\r\n  components: {\r\n    MyHeader\r\n  },\r\n  data() {\r\n    return {\r\n      movieList: [],\r\n      isLoading: false\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.getMovieList()\r\n  },\r\n\r\n  onShow() {\r\n    this.getMovieList()\r\n  },\r\n\r\n  onPullDownRefresh() {\r\n    this.getMovieList().finally(() => {\r\n      uni.stopPullDownRefresh()\r\n    })\r\n  },\r\n\r\n  methods: {\r\n    // 获取电影列表\r\n    async getMovieList() {\r\n      try {\r\n        this.isLoading = true\r\n\r\n        const res = await this.$myRequest({\r\n          url: '/web/movie/getMovieList',\r\n          method: 'get'\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          this.movieList = res.data.data || []\r\n        } else {\r\n          throw new Error(res.msg || '获取电影列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取电影列表失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '获取电影列表失败',\r\n          icon: 'error'\r\n        })\r\n      } finally {\r\n        this.isLoading = false\r\n      }\r\n    },\r\n\r\n    // 获取电影状态\r\n    getMovieStatus(movie) {\r\n      const now = new Date()\r\n      const startTime = new Date(movie.movieStartTime)\r\n      const endTime = new Date(movie.movieEndTime)\r\n\r\n      if (now < startTime) {\r\n        return '即将上映'\r\n      } else if (now >= startTime && now <= endTime) {\r\n        return '正在上映'\r\n      } else {\r\n        return '已下映'\r\n      }\r\n    },\r\n\r\n    // 是否可以预约\r\n    canReserve(movie) {\r\n      const now = new Date()\r\n      const startTime = new Date(movie.movieStartTime)\r\n      const endTime = new Date(movie.movieEndTime)\r\n\r\n      return now >= startTime && now <= endTime && movie.inventoryVotes > 0\r\n    },\r\n\r\n    // 获取预约按钮文字\r\n    getReserveText(movie) {\r\n      if (!this.canReserve(movie)) {\r\n        return '不可预约'\r\n      }\r\n      return '立即预约'\r\n    },\r\n\r\n    // 跳转到电影详情\r\n    goToMovieDetail(movie) {\r\n      uni.navigateTo({\r\n        url: `/pages_app/vieworder/filmdes?movieId=${movie.id}`\r\n      })\r\n    },\r\n\r\n    // 预约电影\r\n    reserveMovie(movie) {\r\n      if (!this.canReserve(movie)) {\r\n        uni.showToast({\r\n          title: '该电影暂不可预约',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.goToMovieDetail(movie)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.vieworder {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\r\n  .content {\r\n    padding-top: 120rpx; // 为头部留出空间\r\n\r\n    .movie_content {\r\n      padding: 20rpx;\r\n\r\n      .movie_list {\r\n        .movie_item {\r\n          background: rgba(255, 255, 255, 0.95);\r\n          border-radius: 20rpx;\r\n          margin-bottom: 30rpx;\r\n          padding: 30rpx;\r\n          box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n          display: flex;\r\n          transition: all 0.3s ease;\r\n\r\n          &:active {\r\n            transform: scale(0.98);\r\n          }\r\n\r\n          .movie_poster {\r\n            width: 120rpx;\r\n            height: 160rpx;\r\n            margin-right: 20rpx;\r\n            border-radius: 12rpx;\r\n            overflow: hidden;\r\n\r\n            .poster_image {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n\r\n          .movie_info {\r\n            flex: 1;\r\n            margin-right: 20rpx;\r\n\r\n            .movie_title {\r\n              font-size: 32rpx;\r\n              font-weight: 600;\r\n              color: #333;\r\n              margin-bottom: 8rpx;\r\n              line-height: 1.4;\r\n            }\r\n\r\n            .movie_type {\r\n              font-size: 26rpx;\r\n              color: #666;\r\n              margin-bottom: 6rpx;\r\n            }\r\n\r\n            .movie_duration,\r\n            .movie_rating {\r\n              font-size: 24rpx;\r\n              color: #999;\r\n              margin-bottom: 4rpx;\r\n            }\r\n\r\n            .movie_desc {\r\n              font-size: 24rpx;\r\n              color: #666;\r\n              line-height: 1.4;\r\n              margin-top: 8rpx;\r\n              display: -webkit-box;\r\n              -webkit-line-clamp: 2;\r\n              -webkit-box-orient: vertical;\r\n              overflow: hidden;\r\n            }\r\n          }\r\n\r\n          .movie_action {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120rpx;\r\n\r\n            .movie_status {\r\n              font-size: 24rpx;\r\n              color: #666;\r\n              margin-bottom: 20rpx;\r\n              text-align: center;\r\n            }\r\n\r\n            .reserve_btn {\r\n              width: 120rpx;\r\n              height: 60rpx;\r\n              background: linear-gradient(135deg, #667eea, #764ba2);\r\n              color: white;\r\n              border-radius: 30rpx;\r\n              font-size: 24rpx;\r\n              border: none;\r\n\r\n              &::after {\r\n                border: none;\r\n              }\r\n\r\n              &:disabled {\r\n                background: #ccc;\r\n                color: #999;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .empty_state,\r\n      .loading_state {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 100rpx 40rpx;\r\n\r\n        .empty_icon,\r\n        .loading_icon {\r\n          font-size: 80rpx;\r\n          margin-bottom: 20rpx;\r\n        }\r\n\r\n        .empty_text,\r\n        .loading_text {\r\n          font-size: 28rpx;\r\n          color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.vieworder {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/vieworder/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AA8DA,MAAK,WAAY,MAAW;AAE5B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,WAAW,CAAE;AAAA,MACb,WAAW;AAAA,IACb;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,SAAS;AACP,SAAK,aAAa;AAAA,EACnB;AAAA,EAED,oBAAoB;AAClB,SAAK,eAAe,QAAQ,MAAM;AAChCA,oBAAAA,MAAI,oBAAoB;AAAA,KACzB;AAAA,EACF;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,eAAe;AACnB,UAAI;AACF,aAAK,YAAY;AAEjB,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,YAAY,IAAI,KAAK,QAAQ,CAAC;AAAA,eAC9B;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,UAAU;AAAA,QACvC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,wCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,YAAY;AAAA,MACnB;AAAA,IACD;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,YAAY,IAAI,KAAK,MAAM,cAAc;AAC/C,YAAM,UAAU,IAAI,KAAK,MAAM,YAAY;AAE3C,UAAI,MAAM,WAAW;AACnB,eAAO;AAAA,MACT,WAAW,OAAO,aAAa,OAAO,SAAS;AAC7C,eAAO;AAAA,aACF;AACL,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,OAAO;AAChB,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,YAAY,IAAI,KAAK,MAAM,cAAc;AAC/C,YAAM,UAAU,IAAI,KAAK,MAAM,YAAY;AAE3C,aAAO,OAAO,aAAa,OAAO,WAAW,MAAM,iBAAiB;AAAA,IACrE;AAAA;AAAA,IAGD,eAAe,OAAO;AACpB,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3B,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,wCAAwC,MAAM,EAAE;AAAA,OACtD;AAAA,IACF;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,gBAAgB,KAAK;AAAA,IAC5B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxKA,GAAG,WAAW,eAAe;"}