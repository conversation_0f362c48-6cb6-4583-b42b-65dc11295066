<template>
  <view class="film-scheme">
    <!-- 自定义头部 -->
    <my-header 
      title="观影记录" 
      :isBack="true" 
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
    />
    
    <!-- 记录列表 -->
    <scroll-view 
      class="scheme-list" 
      scroll-y 
      @scrolltolower="loadMore"
      :style="{ paddingBottom: safeAreaBottom + 'px' }"
    >
      <view class="list-container">
        <!-- 空状态 -->
        <view v-if="filmList.length === 0 && !loading" class="empty-state">
          <image class="empty-icon" src="/static/img/common/empty.png" mode="aspectFit" />
          <text class="empty-text">暂无观影记录</text>
        </view>
        
        <!-- 记录项 -->
        <view 
          v-for="(item, index) in filmList" 
          :key="item.filmSessionId || index"
          class="scheme-item"
        >
          <!-- 标题 -->
          <view class="item-header">
            <text class="venue-name">宝安科技馆 观影预约</text>
            <view :class="['status-badge', getStatusClass(item.subscribeState)]">
              {{ getStatusText(item.subscribeState) }}
            </view>
          </view>
          
          <!-- 电影信息 -->
          <view class="film-info">
            <view class="film-poster">
              <image 
                :src="item.filmCover" 
                mode="aspectFill" 
                class="poster-image"
                @error="handleImageError"
              />
            </view>
            <view class="film-details">
              <text class="film-name">{{ item.filmName }}</text>
              <text class="film-type">类型：{{ filmTypeList[item.filmType - 1] || '未知' }}</text>
              <text class="film-time">时间：{{ item.filmStartTime }} - {{ item.filmEndTime }}</text>
              <text class="film-date">
                日期：{{ item.filmArrangedDate }} {{ getWeekDay(item.filmArrangedDate) }}
              </text>
            </view>
          </view>
          
          <!-- 预约信息 -->
          <view class="booking-info">
            <text class="ticket-count">预约票数：{{ item.subscribeType }}张</text>
          </view>
          
          <!-- 操作按钮 -->
          <view class="action-buttons" v-if="showActionButtons(item.subscribeState)">
            <button 
              v-if="canCancel(item.subscribeState)"
              class="cancel-btn"
              @tap="cancelBooking(item)"
            >
              取消预约
            </button>
            <button 
              v-if="canViewQRCode(item.subscribeState)"
              class="qrcode-btn"
              @tap="viewQRCode(item)"
            >
              查看凭证
            </button>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="loading" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
        
        <view v-if="!hasMore && filmList.length > 0" class="no-more">
          <text class="no-more-text">没有更多记录了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'FilmScheme',
  data() {
    return {
      filmList: [],
      filmTypeList: ['球幕电影', '4D电影'],
      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      statusTextList: ['去签到', '已过期(未检票)', '已取消', '去检票', '已完成', '已过期(未签到)', '场次取消'],
      
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      
      // 安全区域
      safeAreaBottom: 0
    }
  },
  
  onLoad() {
    // 获取安全区域信息
    const systemInfo = uni.getSystemInfoSync()
    this.safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0
  },
  
  onShow() {
    this.resetData()
    this.getFilmList()
  },
  
  methods: {
    // 重置数据
    resetData() {
      this.filmList = []
      this.pageNum = 1
      this.total = 0
      this.hasMore = true
    },
    
    // 获取观影记录列表
    async getFilmList() {
      if (this.loading || !this.hasMore) return
      
      this.loading = true
      
      try {
        const res = await this.$myRequest({
          url: '/web/fileSession/personalCenterFilm',
          method: 'get',
          data: {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        })
        
        if (res.code === 200) {
          const dataList = res.data.data.rows || []
          this.total = res.data.data.total || 0
          
          // 处理数据
          const processedList = dataList.map(item => ({
            ...item,
            filmStartTime: this.formatTime(item.filmStartTime),
            filmEndTime: this.formatTime(item.filmEndTime)
          }))
          
          if (this.pageNum === 1) {
            this.filmList = processedList
          } else {
            this.filmList.push(...processedList)
          }
          
          // 检查是否还有更多数据
          this.hasMore = this.filmList.length < this.total
        } else {
          throw new Error(res.msg || '获取记录失败')
        }
      } catch (error) {
        console.error('获取观影记录失败:', error)
        uni.showToast({
          title: error.message || '获取记录失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return

      this.pageNum++
      this.getFilmList()
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''

      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },

    // 获取星期
    getWeekDay(dateStr) {
      if (!dateStr) return ''

      try {
        const date = new Date(dateStr.replace(/-/g, '/'))
        return this.weekList[date.getDay()]
      } catch (error) {
        return ''
      }
    },

    // 获取状态文本
    getStatusText(state) {
      if (state >= 0 && state < this.statusTextList.length) {
        return this.statusTextList[state]
      }
      return '未知状态'
    },

    // 获取状态样式类
    getStatusClass(state) {
      const statusMap = {
        0: 'status-pending',    // 去签到
        1: 'status-expired',    // 已过期(未检票)
        2: 'status-cancelled',  // 已取消
        3: 'status-checkin',    // 去检票
        4: 'status-completed',  // 已完成
        5: 'status-expired',    // 已过期(未签到)
        6: 'status-cancelled'   // 场次取消
      }
      return statusMap[state] || 'status-unknown'
    },

    // 是否显示操作按钮
    showActionButtons(state) {
      return state === 1 || state === 4 || this.canCancel(state)
    },

    // 是否可以取消
    canCancel(state) {
      return state === 1 || state === 4
    },

    // 是否可以查看二维码
    canViewQRCode(state) {
      return state === 1 || state === 4
    },

    // 取消预约
    cancelBooking(item) {
      uni.navigateTo({
        url: `/pages_app/schemesuccess/filmcancel?vote=${item.subscribeType}&batchNumber=${item.batchNumber}&filmSessionId=${item.filmSessionId}`
      })
    },

    // 查看二维码
    viewQRCode(item) {
      uni.navigateTo({
        url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${item.batchNumber}&filmSessionId=${item.filmSessionId}`
      })
    },

    // 图片加载失败处理
    handleImageError(e) {
      console.warn('图片加载失败:', e)
      // 可以设置默认图片
    }
  }
}
</script>

<style lang="scss" scoped>
.film-scheme {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .scheme-list {
    height: calc(100vh - 88rpx);

    .list-container {
      padding: 20rpx;
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 30rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  // 记录项
  .scheme-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 30rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .venue-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-pending {
          background: #e3f2fd;
          color: #1976d2;
        }

        &.status-expired {
          background: #fce4ec;
          color: #c2185b;
        }

        &.status-cancelled {
          background: #f3e5f5;
          color: #7b1fa2;
        }

        &.status-checkin {
          background: #e8f5e8;
          color: #388e3c;
        }

        &.status-completed {
          background: #e8f5e8;
          color: #388e3c;
        }

        &.status-unknown {
          background: #f5f5f5;
          color: #666;
        }
      }
    }

    .film-info {
      display: flex;
      margin-bottom: 20rpx;

      .film-poster {
        width: 120rpx;
        height: 160rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        overflow: hidden;

        .poster-image {
          width: 100%;
          height: 100%;
        }
      }

      .film-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .film-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
          line-height: 1.4;
        }

        .film-type,
        .film-time,
        .film-date {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 6rpx;
          line-height: 1.3;
        }
      }
    }

    .booking-info {
      margin-bottom: 20rpx;

      .ticket-count {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
      }
    }

    .action-buttons {
      display: flex;
      gap: 20rpx;

      .cancel-btn,
      .qrcode-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
        border: none;

        &::after {
          border: none;
        }
      }

      .cancel-btn {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
      }

      .qrcode-btn {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
      }
    }
  }

  // 加载状态
  .loading-more {
    display: flex;
    justify-content: center;
    padding: 40rpx;

    .loading-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .no-more {
    display: flex;
    justify-content: center;
    padding: 40rpx;

    .no-more-text {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.film-scheme {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.scheme-list {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
