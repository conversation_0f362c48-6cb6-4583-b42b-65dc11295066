{"version": 3, "file": "crypto.js", "sources": ["utils/crypto.js"], "sourcesContent": ["/**\r\n * 加密工具类\r\n * 适配uni-app环境\r\n */\r\n\r\n/**\r\n * Base64编码\r\n * @param {string} text 要编码的文本\r\n * @returns {string} Base64编码后的文本\r\n */\r\nexport function encryptBase64(text) {\r\n  try {\r\n    return uni.base64Encode(text)\r\n  } catch (error) {\r\n    console.error('Base64编码失败:', error)\r\n    return text\r\n  }\r\n}\r\n\r\n/**\r\n * AES加密\r\n * @param {string} text 要加密的文本\r\n * @param {string} key 加密密钥\r\n * @returns {string} 加密后的文本\r\n */\r\nexport function encryptWithAes(text, key) {\r\n  try {\r\n    // 在uni-app环境中，我们使用简化的AES加密实现\r\n    // 实际项目中应该使用专门的AES加密库\r\n\r\n    // 这里返回base64编码作为简化实现\r\n    // 生产环境中应该使用真正的AES加密\r\n    return uni.base64Encode(text + key)\r\n  } catch (error) {\r\n    console.error('AES加密失败:', error)\r\n    return text\r\n  }\r\n}\r\n\r\n/**\r\n * 生成AES密钥\r\n * @returns {string} 生成的AES密钥\r\n */\r\nexport function generateAesKey() {\r\n  try {\r\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\r\n    let result = ''\r\n\r\n    for (let i = 0; i < 32; i++) {\r\n      result += chars.charAt(Math.floor(Math.random() * chars.length))\r\n    }\r\n\r\n    return result\r\n  } catch (error) {\r\n    console.error('生成AES密钥失败:', error)\r\n    return 'defaultkey12345678901234567890'\r\n  }\r\n}\r\n\r\n// 默认导出\r\nexport default {\r\n  encryptBase64,\r\n  encryptWithAes,\r\n  generateAesKey\r\n}"], "names": ["uni"], "mappings": ";;AAUO,SAAS,cAAc,MAAM;AAClC,MAAI;AACF,WAAOA,cAAG,MAAC,aAAa,IAAI;AAAA,EAC7B,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,yBAAA,eAAe,KAAK;AAClC,WAAO;AAAA,EACR;AACH;AAQO,SAAS,eAAe,MAAM,KAAK;AACxC,MAAI;AAMF,WAAOA,oBAAI,aAAa,OAAO,GAAG;AAAA,EACnC,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,SAAA,yBAAc,YAAY,KAAK;AAC/B,WAAO;AAAA,EACR;AACH;AAMO,SAAS,iBAAiB;AAC/B,MAAI;AACF,UAAM,QAAQ;AACd,QAAI,SAAS;AAEb,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,MAAM,MAAM,CAAC;AAAA,IAChE;AAED,WAAO;AAAA,EACR,SAAQ,OAAO;AACdA,kBAAAA,MAAc,MAAA,SAAA,yBAAA,cAAc,KAAK;AACjC,WAAO;AAAA,EACR;AACH;;;;"}