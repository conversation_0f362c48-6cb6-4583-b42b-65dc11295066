.uni-load-more {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 40px;
    justify-content: center
}

.uni-load-more__text {
    font-size: 14px;
    margin-left: 8px
}

.uni-load-more__img {
    height: 24px;
    width: 24px
}

.uni-load-more__img--nvue {
    color: #666
}

.uni-load-more__img--android,.uni-load-more__img--ios {
    height: 24px;
    transform: rotate(0);
    width: 24px
}

.uni-load-more__img--android {
    animation: loading-ios 1s linear 0s infinite
}

.uni-load-more__img--ios-H5 {
    animation: loading-ios-H5 1s step-end 0s infinite;
    position: relative
}

.uni-load-more__img--ios-H5 wx-image {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

@keyframes loading-ios-H5 {
    0% {
        transform: rotate(0)
    }

    8% {
        transform: rotate(30deg)
    }

    16% {
        transform: rotate(60deg)
    }

    24% {
        transform: rotate(90deg)
    }

    32% {
        transform: rotate(120deg)
    }

    40% {
        transform: rotate(150deg)
    }

    48% {
        transform: rotate(180deg)
    }

    56% {
        transform: rotate(210deg)
    }

    64% {
        transform: rotate(240deg)
    }

    73% {
        transform: rotate(270deg)
    }

    82% {
        transform: rotate(300deg)
    }

    91% {
        transform: rotate(330deg)
    }

    to {
        transform: rotate(1turn)
    }
}

.uni-load-more__img--android-MP {
    animation: loading-ios 1s ease 0s infinite;
    height: 24px;
    position: relative;
    transform: rotate(0);
    width: 24px
}

.uni-load-more__img--android-MP .uni-load-more__img-icon {
    border: 2px solid transparent;
    border-radius: 50%;
    border-top-color: #777;
    box-sizing: border-box;
    height: 100%;
    position: absolute;
    transform-origin: center;
    width: 100%
}

.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(1) {
    animation: loading-android-MP-1 1s linear 0s infinite
}

.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(2) {
    animation: loading-android-MP-2 1s linear 0s infinite
}

.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(3) {
    animation: loading-android-MP-3 1s linear 0s infinite
}

@keyframes loading-android {
    0% {
        transform: rotate(0)
    }

    to {
        transform: rotate(1turn)
    }
}

@keyframes loading-android-MP-1 {
    0% {
        transform: rotate(0)
    }

    50% {
        transform: rotate(90deg)
    }

    to {
        transform: rotate(1turn)
    }
}

@keyframes loading-android-MP-2 {
    0% {
        transform: rotate(0)
    }

    50% {
        transform: rotate(180deg)
    }

    to {
        transform: rotate(1turn)
    }
}

@keyframes loading-android-MP-3 {
    0% {
        transform: rotate(0)
    }

    50% {
        transform: rotate(270deg)
    }

    to {
        transform: rotate(1turn)
    }
}