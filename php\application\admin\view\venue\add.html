<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Venue_poll')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-venue_poll" data-rule="required" class="form-control" name="row[venue_poll]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Venue_start_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-venue_start_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[venue_start_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Venue_end_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-venue_end_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[venue_end_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Venue_arranged_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-venue_arranged_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[venue_arranged_date]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_open')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_open" data-rule="required" class="form-control" name="row[is_open]" type="text" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Week')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-week" data-rule="required" class="form-control" name="row[week]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Inventory_votes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-inventory_votes" data-rule="required" class="form-control" name="row[inventory_votes]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_by')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_by" data-rule="required" class="form-control" name="row[create_by]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Update_by')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-update_by" data-rule="required" class="form-control" name="row[update_by]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Update_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-update_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[update_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" data-rule="required" class="form-control" name="row[remark]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Del_flag')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-del_flag" data-rule="required" class="form-control" name="row[del_flag]" type="text" value="0">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
