"use strict";
const common_vendor = require("../common/vendor.js");
const config = require("../config.js");
const utils_auth = require("./auth.js");
const utils_errorCode = require("./errorCode.js");
const utils_common = require("./common.js");
const utils_crypto = require("./crypto.js");
const utils_jsencrypt = require("./jsencrypt.js");
const baseUrl = config.config.baseUrl;
config.config.iSzgm;
const appId = config.config.appId;
function request(options) {
  return new Promise((resolve, reject) => {
    options.header = options.header || {};
    const isToken = !(options.headers || {}).isToken === false;
    const isEncrypt = (options.headers || {}).isEncrypt === true;
    if (!options.url.includes(`/wx/user/${appId}/login`) && utils_auth.getToken() && isToken) {
      options.header.Authorization = "wx " + utils_auth.getToken();
    }
    if (options.params) {
      let paramStr = options.url + "?" + utils_common.tansParams(options.params);
      options.url = paramStr.slice(0, -1);
    }
    if (isEncrypt && (options.method === "POST" || options.method === "PUT")) {
      const aesKey = utils_crypto.generateAesKey();
      options.header["encrypt-key"] = utils_jsencrypt.encrypt(utils_crypto.encryptBase64(aesKey));
      options.data = typeof options.data === "object" ? utils_crypto.encryptWithAes(JSON.stringify(options.data), aesKey) : utils_crypto.encryptWithAes(options.data, aesKey);
    }
    const requestConfig = {
      method: options.method || "GET",
      timeout: options.timeout || 1e4,
      url: baseUrl + options.url,
      data: options.data,
      header: options.header,
      dataType: "json"
    };
    if (options.enableHttp2) {
      requestConfig.enableHttp2 = true;
    }
    common_vendor.index.request({
      ...requestConfig,
      success: (response) => {
        const data = response.data;
        const code = data.code || 200;
        const msg = utils_errorCode.errorCode[code] || data.msg || utils_errorCode.errorCode.default;
        if (code === 200) {
          resolve(data);
        } else if (code === 401) {
          handleAuthError();
          reject("无效的会话，或者会话已过期，请重新登录。");
        } else if (code === 500) {
          utils_common.toast(msg);
          reject("500");
        } else {
          utils_common.toast(msg);
          reject(code);
        }
      },
      fail: (error) => {
        let errMsg = error.errMsg || "请求失败";
        if (errMsg === "request:fail url not in domain list") {
          errMsg = "请求域名不在白名单中，请联系管理员";
        }
        if (errMsg === "Network Error" || errMsg === "request:fail") {
          errMsg = "后端接口连接异常";
        } else if (errMsg.includes("timeout")) {
          errMsg = "系统接口请求超时";
        } else if (errMsg.includes("Request failed with status code")) {
          errMsg = "系统接口" + errMsg.substr(errMsg.length - 3) + "异常";
        }
        utils_common.toast(errMsg);
        reject(error);
      }
    });
  });
}
function handleAuthError() {
  "./auth.js".then((auth) => {
    auth.removeToken();
  });
  common_vendor.index.reLaunch({
    url: "/pages_app/login/index"
  });
}
exports.baseUrl = baseUrl;
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
