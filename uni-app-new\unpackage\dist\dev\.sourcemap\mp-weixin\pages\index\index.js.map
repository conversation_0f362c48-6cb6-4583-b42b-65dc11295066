{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"content\">\r\n    <my-header\r\n      v-if=\"showHeader\"\r\n      :isBack=\"false\"\r\n      :isShowHome=\"false\"\r\n      title=\"宝安科技馆\"\r\n      color=\"#fff\"\r\n      background=\"transparent\"\r\n    ></my-header>\r\n    <view class=\"title\">线上智能服务平台</view>\r\n    <view class=\"btn_list\">\r\n      <button\r\n        v-for=\"(item, index) in jumpList\"\r\n        :key=\"index\"\r\n        type=\"default\"\r\n        :style=\"{\r\n          background: `url(${item.bg}) no-repeat center center`,\r\n          backgroundSize: '100% 100%'\r\n        }\"\r\n        @tap=\"jumpTo(item.path, item.id)\"\r\n      >\r\n        <view class=\"nav_name\">{{ item.name }}</view>\r\n        <view class=\"icon\">\r\n          <image class=\"set_icon\" mode=\"aspectFill\" :src=\"item.icon\"></image>\r\n        </view>\r\n      </button>\r\n    </view>\r\n    <uni-notice-bar\r\n      v-if=\"notice\"\r\n      class=\"scrollText\"\r\n      :scrollable=\"true\"\r\n      :speed=\"41.8\"\r\n      :single=\"true\"\r\n      background-color=\"#ffffff\"\r\n      :text=\"notice\"\r\n    ></uni-notice-bar>\r\n    <privacy-popup\r\n      v-if=\"showPrivacy\"\r\n      id=\"privacy-popup\"\r\n      @agree=\"onPrivacyAgree\"\r\n      @reject=\"onPrivacyReject\"\r\n    ></privacy-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { myRequest } from '@/api/api.js'\r\nimport config from '@/config.js'\r\nimport MyHeader from '@/components/my-header/my-header.vue'\r\nimport PrivacyPopup from '@/components/privacy-popup/privacy-popup.vue'\r\n\r\nexport default {\r\n  components: {\r\n    MyHeader,\r\n    PrivacyPopup\r\n  },\r\n  data() {\r\n    return {\r\n      iSzgm: config.iSzgm,\r\n      appId: \"\",\r\n      initCode: \"\",\r\n      jumpList: [\r\n        {\r\n          bg: \"/static/img/home/<USER>\",\r\n          icon: \"/static/img/home/<USER>\",\r\n          path: \"/pages_app/entervenue/index\",\r\n          name: \"参观预约\",\r\n          id: 0\r\n        },\r\n        {\r\n          bg: \"/static/img/home/<USER>\",\r\n          icon: \"/static/img/home/<USER>\",\r\n          path: \"/pages_app/vieworder/index\",\r\n          name: \"观影预约\",\r\n          id: 1\r\n        },\r\n        {\r\n          bg: \"/static/img/home/<USER>\",\r\n          icon: \"/static/img/home/<USER>\",\r\n          path: \"/pages_app/curriculum/index\",\r\n          name: \"课程预约\",\r\n          id: 2\r\n        },\r\n        {\r\n          bg: \"/static/img/home/<USER>\",\r\n          icon: \"/static/img/home/<USER>\",\r\n          path: \"/pages_app/user/index\",\r\n          name: \"个人中心\",\r\n          id: 3\r\n        }\r\n      ],\r\n      notice: \"\",\r\n      path: null,\r\n      privacyAllow: false,\r\n      showHeader: true,\r\n      showPrivacy: false,\r\n      isNavigating: false // 防止重复导航\r\n    }\r\n  },\r\n  onShow() {\r\n    this.getNotice()\r\n  },\r\n  onLoad() {\r\n    this.initPrivacyCheck()\r\n  },\r\n  onReady() {\r\n    // 页面渲染完成后检查隐私政策状态\r\n    this.checkPrivacyDisplay()\r\n  },\r\n  onUnload() {\r\n    // 页面卸载时清理资源\r\n    this.notice = ''\r\n    this.path = null\r\n  },\r\n  onHide() {\r\n    // 页面隐藏时清理定时器等资源\r\n    // 如果有定时器，在这里清理\r\n  },\r\n  methods: {\r\n    initPrivacyCheck() {\r\n      // 检查隐私政策同意状态\r\n      const privacyAgreed = uni.getStorageSync('privacy_agreed')\r\n      if (privacyAgreed) {\r\n        this.privacyAllow = true\r\n        this.showPrivacy = false\r\n      }\r\n\r\n      // 平台兼容的隐私授权检查\r\n      // #ifdef MP-WEIXIN\r\n      try {\r\n        uni.requirePrivacyAuthorize({\r\n          success: () => {\r\n            this.privacyAllow = true\r\n            console.log('微信隐私授权成功')\r\n          },\r\n          fail: (error) => {\r\n            console.log('微信隐私授权失败:', error)\r\n            // 显示自定义隐私政策弹窗\r\n            this.showPrivacy = true\r\n          },\r\n          complete: () => {}\r\n        })\r\n      } catch (error) {\r\n        console.log('requirePrivacyAuthorize API不可用:', error)\r\n        // 降级处理，显示自定义隐私政策弹窗\r\n        if (!privacyAgreed) {\r\n          this.showPrivacy = true\r\n        }\r\n      }\r\n      // #endif\r\n\r\n      // #ifndef MP-WEIXIN\r\n      // 非微信小程序平台的处理\r\n      if (privacyAgreed) {\r\n        this.privacyAllow = true\r\n        this.showPrivacy = false\r\n      } else {\r\n        // 显示自定义隐私政策弹窗\r\n        this.showPrivacy = true\r\n      }\r\n      // #endif\r\n    },\r\n    async jumpTo(path, id) {\r\n      // 防抖处理，避免重复点击\r\n      if (this.isNavigating) {\r\n        return\r\n      }\r\n\r\n      if (!this.privacyAllow) {\r\n        uni.showToast({\r\n          title: \"请先同意隐私政策\",\r\n          icon: \"none\",\r\n          duration: 3000\r\n        })\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.isNavigating = true\r\n        this.path = path\r\n\r\n        await uni.navigateTo({\r\n          url: this.path\r\n        })\r\n      } catch (error) {\r\n        console.error('页面跳转失败:', error)\r\n        uni.showToast({\r\n          title: \"页面跳转失败\",\r\n          icon: \"none\",\r\n          duration: 2000\r\n        })\r\n      } finally {\r\n        // 延迟重置导航状态，防止快速连续点击\r\n        setTimeout(() => {\r\n          this.isNavigating = false\r\n        }, 500)\r\n      }\r\n    },\r\n    getNotice() {\r\n      myRequest({\r\n        url: \"/apitp/announcement/getInfo\",\r\n        noLoadingFlag: true // 不显示加载提示，避免影响用户体验\r\n      }).then((res) => {\r\n        if (res.data && res.data.code === 200) {\r\n          this.notice = res.data.msg || res.data.data\r\n        }\r\n      }).catch((error) => {\r\n        console.error('获取公告信息失败:', error)\r\n        // 静默处理错误，不影响页面正常显示\r\n      })\r\n    },\r\n    onPrivacyAgree() {\r\n      this.privacyAllow = true\r\n      this.showPrivacy = false\r\n      console.log('用户同意隐私政策')\r\n\r\n      // 保存用户同意状态\r\n      uni.setStorageSync('privacy_agreed', true)\r\n    },\r\n    onPrivacyReject() {\r\n      this.privacyAllow = false\r\n      this.showPrivacy = false\r\n      console.log('用户拒绝隐私政策')\r\n\r\n      // 显示提示信息\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '需要同意隐私政策才能使用应用功能',\r\n        showCancel: false,\r\n        confirmText: '我知道了'\r\n      })\r\n    },\r\n    checkPrivacyDisplay() {\r\n      // 检查是否需要显示隐私政策弹窗\r\n      const privacyAgreed = uni.getStorageSync('privacy_agreed')\r\n      if (!privacyAgreed) {\r\n        this.showPrivacy = true\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  height: 100vh;\r\n  background-color: #f8f8f8;\r\n  background: url(/static/img/home/<USER>\n  background-size: contain;\r\n  color: #fff;\r\n  overflow: scroll;\r\n\r\n  .title {\r\n    margin-top: 54rpx;\r\n    margin-bottom: 62rpx;\r\n    font-size: 58rpx;\r\n    font-family: 'YouSheBiaoTiHei', 'PingFang SC', sans-serif;\r\n    text-align: center;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .btn_list {\r\n    width: 100%;\r\n    height: auto;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: space-around;\r\n    margin-top: 158rpx;\r\n\r\n    button {\r\n      width: 692rpx;\r\n      height: 192rpx;\r\n      margin: 0 auto 76rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      box-sizing: border-box;\r\n      padding: 0 174rpx 0 158rpx;\r\n      font-weight: 600;\r\n      border-radius: 20rpx;\r\n\r\n      &::after {\r\n        border: none;\r\n      }\r\n\r\n      .nav_name {\r\n        font-size: 42rpx;\r\n        font-family: 'PingFang SC', sans-serif;\r\n        color: #FFFFFF;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .icon {\r\n        width: 160rpx;\r\n        height: 160rpx;\r\n        background-color: #fff;\r\n        border-radius: 50%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .set_icon {\r\n          width: 115rpx;\r\n          height: 105rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n\r\n  .scrollText {\r\n    width: 727rpx;\r\n    display: block;\r\n    margin: 262rpx auto 138rpx auto;\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.content {\r\n  background-attachment: scroll;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["config", "uni", "myRequest"], "mappings": ";;;;AAiDA,MAAK,WAAY,MAAW;AAC5B,MAAO,eAAc,MAAW;AAEhC,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,OAAOA,OAAM,OAAC;AAAA,MACd,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,QACR;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,IAAI;AAAA,QACL;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,IAAI;AAAA,QACL;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,IAAI;AAAA,QACL;AAAA,QACD;AAAA,UACE,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,IAAI;AAAA,QACN;AAAA,MACD;AAAA,MACD,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,cAAc;AAAA,MACd,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,cAAc;AAAA;AAAA,IAChB;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,UAAU;AAAA,EAChB;AAAA,EACD,SAAS;AACP,SAAK,iBAAiB;AAAA,EACvB;AAAA,EACD,UAAU;AAER,SAAK,oBAAoB;AAAA,EAC1B;AAAA,EACD,WAAW;AAET,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EACb;AAAA,EACD,SAAS;AAAA,EAGR;AAAA,EACD,SAAS;AAAA,IACP,mBAAmB;AAEjB,YAAM,gBAAgBC,cAAAA,MAAI,eAAe,gBAAgB;AACzD,UAAI,eAAe;AACjB,aAAK,eAAe;AACpB,aAAK,cAAc;AAAA,MACrB;AAIA,UAAI;AACFA,sBAAAA,MAAI,wBAAwB;AAAA,UAC1B,SAAS,MAAM;AACb,iBAAK,eAAe;AACpBA,0BAAAA,mDAAY,UAAU;AAAA,UACvB;AAAA,UACD,MAAM,CAAC,UAAU;AACfA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,KAAK;AAE9B,iBAAK,cAAc;AAAA,UACpB;AAAA,UACD,UAAU,MAAM;AAAA,UAAC;AAAA,SAClB;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,mCAAmC,KAAK;AAEpD,YAAI,CAAC,eAAe;AAClB,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IAaD;AAAA,IACD,MAAM,OAAO,MAAM,IAAI;AAErB,UAAI,KAAK,cAAc;AACrB;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,cAAc;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD;AAAA,MACF;AAEA,UAAI;AACF,aAAK,eAAe;AACpB,aAAK,OAAO;AAEZ,cAAMA,cAAAA,MAAI,WAAW;AAAA,UACnB,KAAK,KAAK;AAAA,SACX;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACH,UAAU;AAER,mBAAW,MAAM;AACf,eAAK,eAAe;AAAA,QACrB,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA,IACD,YAAY;AACVC,wBAAU;AAAA,QACR,KAAK;AAAA,QACL,eAAe;AAAA;AAAA,MACjB,CAAC,EAAE,KAAK,CAAC,QAAQ;AACf,YAAI,IAAI,QAAQ,IAAI,KAAK,SAAS,KAAK;AACrC,eAAK,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK;AAAA,QACzC;AAAA,MACF,CAAC,EAAE,MAAM,CAAC,UAAU;AAClBD,sBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAAA,OAEjC;AAAA,IACF;AAAA,IACD,iBAAiB;AACf,WAAK,eAAe;AACpB,WAAK,cAAc;AACnBA,oBAAAA,mDAAY,UAAU;AAGtBA,0BAAI,eAAe,kBAAkB,IAAI;AAAA,IAC1C;AAAA,IACD,kBAAkB;AAChB,WAAK,eAAe;AACpB,WAAK,cAAc;AACnBA,oBAAAA,mDAAY,UAAU;AAGtBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,OACd;AAAA,IACF;AAAA,IACD,sBAAsB;AAEpB,YAAM,gBAAgBA,cAAAA,MAAI,eAAe,gBAAgB;AACzD,UAAI,CAAC,eAAe;AAClB,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChPA,GAAG,WAAW,eAAe;"}