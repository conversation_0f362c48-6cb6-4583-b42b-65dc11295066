<template>
  <view class="contactContent">
    <view class="content">
      <!-- 头部组件 -->
      <my-header 
        v-if="!config.iSzgm"
        :menuClass="'bor'"
        :isFixed="true"
        :isBack="true"
        :isShowHome="true"
        :title="'联系人管理'"
        :color="'#000'"
      />
      
      <view class="contactBox">
        <!-- 添加联系人按钮 -->
        <view class="addContact" @tap="addContact">
          <text class="add">+</text> 添加联系人
        </view>
        
        <!-- 联系人列表 -->
        <view class="contactList">
          <view 
            v-for="(item, index) in linkList" 
            :key="item.id"
            class="contactItem"
            :class="{ isMove: item.isMove }"
            @touchstart="touchstart"
            @touchmove="touchmove($event, index)"
            :data-index="index"
          >
            <view class="left">
              <view class="peopleName">{{ item.linkmanName }}</view>
              <view class="peopleCard">证件号 {{ hideCardNum(index) }}</view>
              <view class="peopleMablie">
                <text>手机号码 {{ item.linkmanPhone }}</text>
                <text>年龄 {{ item.linkmanAge }}</text>
              </view>
            </view>
            <view class="handlePlace">
              <view class="edit" @tap="editItem(item.id)">编辑</view>
              <view class="del" @tap="deleteItem(item.id)">删除</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { myRequest } from '../../api/api.js'
import config from '../../config.js'

export default {
  name: 'ContactManager',
  data() {
    return {
      maxNum: 0,
      linkList: [],
      checkAllNum: 0,
      startX: '',
      startY: ''
    }
  },
  computed: {
    config() {
      return config
    }
  },
  onShow() {
    this.getLinkList()
  },
  onLoad(options) {
    this.maxNum = options.num || 0
  },
  methods: {
    // 获取联系人列表
    getLinkList() {
      myRequest({
        url: '/auth/linkman/list'
      }).then(res => {
        if (res.data && res.data.rows) {
          this.linkList = res.data.rows.map(item => ({
            ...item,
            isMove: false
          }))
        }
      }).catch(err => {
        console.error('获取联系人列表失败:', err)
        uni.showToast({
          title: '获取联系人列表失败',
          icon: 'error'
        })
      })
    },
    
    // 隐藏身份证号中间部分
    hideCardNum(index) {
      const certificate = this.linkList[index].linkmanCertificate
      const length = certificate.length
      
      if (length < 4) return certificate
      
      if (length > 4 && length !== 18) {
        let result = certificate.substring(0, 2)
        for (let i = 0; i < length; i++) {
          if (i >= 2 && i < length - 2) {
            result += '*'
          }
        }
        result += certificate.substring(length - 2, length)
        return result
      }
      
      return certificate.replace(certificate.substring(4, 15), '*******')
    },
    
    // 添加联系人
    addContact() {
      uni.navigateTo({
        url: '/pages_app/contacts/addcontact'
      })
    },
    
    // 删除联系人
    deleteItem(id) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个联系人吗？',
        success: (res) => {
          if (res.confirm) {
            myRequest({
              url: `/auth/linkman/${id}`,
              method: 'DELETE'
            }).then(res => {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.getLinkList()
            }).catch(err => {
              console.error('删除联系人失败:', err)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            })
          }
        }
      })
    },
    
    // 编辑联系人
    editItem(id) {
      uni.navigateTo({
        url: `/pages_app/contacts/addcontact?id=${id}`
      })
    },
    
    // 触摸开始
    touchstart(e) {
      this.linkList.forEach(item => {
        if (item.isMove) {
          item.isMove = false
        }
      })
      this.startX = e.changedTouches[0].clientX
      this.startY = e.changedTouches[0].clientY
    },
    
    // 触摸移动
    touchmove(e, index) {
      const startX = this.startX
      const startY = this.startY
      const touchX = e.changedTouches[0].clientX
      const touchY = e.changedTouches[0].clientY
      const angle = this.angle({ x: startX, y: startY }, { x: touchX, y: touchY })
      
      if (Math.abs(angle) <= 30 && startX - touchX >= 30) {
        this.linkList[index].isMove = true
      }
    },
    
    // 计算角度
    angle(start, end) {
      const x = end.x - start.x
      const y = end.y - start.y
      return Math.atan(y / x) * 360 / Math.PI
    }
  }
}
</script>

<style scoped>
.contactContent {
  background: #f3f4f6;
  font-family: PingFang SC;
  height: 100vh;
  overflow: scroll;
  width: 100%;
}

.content {
  height: auto;
  width: 100%;
}

.contactBox {
  background: #f3f4f6;
  box-sizing: border-box;
  height: auto;
  padding: 29rpx 28rpx;
  width: 100%;
}

.addContact {
  background-color: #fff;
  border-radius: 10rpx;
  color: #5cb7ff;
  font-size: 35rpx;
  font-weight: 600;
  height: 87rpx;
  line-height: 87rpx;
  margin-bottom: 28rpx;
  text-align: center;
  width: 100%;
}

.add {
  font-size: 42rpx;
  margin-right: 10rpx;
}

.contactList {
  height: auto;
  overflow: hidden;
  width: 100%;
}

.contactItem {
  background-color: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  height: 158rpx;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 18rpx 28rpx;
  position: relative;
  width: 100%;
}

.contactItem.isMove .left {
  transform: translate(-40%);
}

.contactItem.isMove .handlePlace {
  width: 40%;
}

.left {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  transition: all 0.3s;
  width: auto;
}

.peopleName {
  color: #000;
  font-size: 29rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.peopleCard,
.peopleMablie {
  color: #888;
  font-size: 23rpx;
}

.peopleMablie text:first-child {
  display: inline-block;
  margin-right: 96rpx;
}

.handlePlace {
  color: #fff;
  display: flex;
  font-size: 28rpx;
  height: 100%;
  line-height: 158rpx;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  transition: all 0.3s;
  width: 0;
  z-index: 99;
}

.edit {
  background-color: #5cb7ff;
  height: 100%;
  width: 50%;
}

.del {
  background-color: #fc5531;
  height: 100%;
  width: 50%;
}
</style>