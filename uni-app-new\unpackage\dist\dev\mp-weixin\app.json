{"pages": ["pages/index/index"], "subPackages": [{"root": "pages_app", "pages": ["login/index", "register/index", "vieworder/index", "vieworder/filmdes", "contacts/index", "contacts/addcontact", "user/index", "user/filmscheme", "contactmanager/index", "schemesuccess/filmsuccess", "schemesuccess/filmcancel", "entervenue/index", "schemesuccess/venuesuccess", "schemesuccess/venuecancel", "curriculum/index", "curriculum/choosecurriculum", "schemesuccess/curriculumsuccess", "schemesuccess/curriculumcancel", "user/venuescheme", "user/curriculumscheme", "user/oneqrcode"]}], "window": {"navigationBarTextStyle": "white", "navigationBarTitleText": "宝安科技馆预约服务平台", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8", "navigationStyle": "custom", "disableScroll": true, "backgroundTextStyle": "dark", "enablePullDownRefresh": false, "onReachBottomDistance": 50}, "preloadRule": {"pages/index/index": {"network": "all", "packages": ["pages_app"]}}, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "__usePrivacyCheck__": true, "lazyCodeLoading": "requiredComponents", "renderer": "webview", "navigateToMiniProgramAppIdList": [], "networkTimeout": {"request": 60000, "downloadFile": 60000}, "usingComponents": {"my-header": "/components/my-header/my-header"}}