define(['jquery', 'bootstrap', 'backend', 'addtabs',  'echarts', 'echarts-theme', 'template',
    'moment', 'moment/locale/zh-cn', 
    '/assets/libs/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js'
], function ($, undefined, Backend, Datatable,  Echarts, undefined, Template, moment) {

    var Controller = {
        index: function () {
            function parseVenueData(list) {
                let data = {
                    register: 0,
                    noRegister: 0,
                    sign: 0,
                    morningRegister: 0,
                    morningSign: 0,
                    afternoonRegister: 0,
                    afternoonSign: 0
                };
        
                list.forEach(function (item) {
                    let poll = parseInt(item.venuePoll || 0);
                    let reg = parseInt(item.register || 0);
                    let sign = parseInt(item.hadSign || 0);
                    let flag = item.flag;
        
                    data.register += reg;
                    data.noRegister += poll - reg;
                    data.sign += sign;
        
                    if (flag == '0') {
                        data.morningRegister = reg;
                        data.morningSign = sign;
                    } else if (flag == '1') {
                        data.afternoonRegister = reg;
                        data.afternoonSign = sign;
                    }
                });
        
                return data;
            }
        
            function renderCharts(data) {
                // 饼图1
                var chart1 = Echarts.init(document.getElementById('venue-chart1'));
                chart1.setOption({
                    // tooltip: { trigger: 'none' },
                    tooltip: { trigger: 'item' }, // 支持 hover 展示详细值
                    legend: { orient: 'vertical', left: 'center', bottom: '3%' },
                    color: [
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#FFCA5F' }, { offset: 1, color: '#FFB33C' } ] },
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#1F3CFF' }, { offset: 1, color: '#80C7FF' } ] }
                    ],
                    series: [{
                        type: 'pie',
                        radius: '50%',
                        data: [
                            { value: data.noRegister, name: '入馆未预约量：' + data.noRegister },
                            { value: data.register, name: '入馆已预约量：' + data.register }
                        ]
                    }]
                });
        
                // 上午场
                var chart2 = Echarts.init(document.getElementById('venue-chart2'));
                chart2.setOption({
                    tooltip: { trigger: 'none' },
                    legend: { bottom: '0', left: 'center' },
                    color: [
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#1F3CFF' }, { offset: 1, color: '#80C7FF' } ] },
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#FFCA5F' }, { offset: 1, color: '#FFB33C' } ] }
                    ],
                    series: [{
                        type: 'pie',
                        radius: ['45%', '70%'],
                        data: [
                            { value: data.morningRegister, name: '已预约量: ' + data.morningRegister },
                            { value: data.morningSign, name: '已签到量: ' + data.morningSign }
                        ],
                        label: { show: false },
                        labelLine: { show: false }
                    }]
                });
        
                // 下午场
                var chart3 = Echarts.init(document.getElementById('venue-chart3'));
                chart3.setOption({
                    legend: { bottom: '0', left: 'center' },
                    color: [
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#1F3CFF' }, { offset: 1, color: '#80C7FF' } ] },
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#FFCA5F' }, { offset: 1, color: '#FFB33C' } ] }
                    ],
                    series: [{
                        type: 'pie',
                        radius: ['45%', '70%'],
                        data: [
                            { value: data.afternoonRegister, name: '已预约量: ' + data.afternoonRegister },
                            { value: data.afternoonSign, name: '已签到量: ' + data.afternoonSign }
                        ],
                        label: { show: false },
                        labelLine: { show: false }
                    }]
                });
        
                $('#totalRegister').text(data.register + '人');
                $('#totalSign').text(data.sign + '人');
            }
        
            function fetchData() {
                // 模拟请求（改成你自己的后端接口）
                $.ajax({
                    url: '/admin/venue/getStats?date=' + $('#venue-date').val(),
                    // url: '/admin/venue/getstats?date=2025-6-2',
                    type: 'GET',
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 1 && Array.isArray(res.data)) {
                            var parsed = parseVenueData(res.data);
                            renderCharts(parsed);
                        } else {
                            Toastr.error(res.msg || '数据加载失败');
                        }
                    }
                });
            }
        
            // 默认加载
            $('#venue-date').val(new Date().toISOString().split('T')[0]);
            fetchData();
            Controller.api.bindDatepicker('#venue-date', function () {
                fetchData();
            });



            function fetchFilmData() {
        
                $.ajax({
                    url: '/admin/film/Base/getStats?date=' + $('#film-date').val(),
                    // url: '/admin/film/Base/getStats?date=2025-6-2',
                    type: 'GET',
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 1 && Array.isArray(res.data)) {
                            renderFilmCharts(res.data);
                        } else {
                            Toastr.error(res.msg || '加载失败');
                        }
                    }
                });
            }

            function renderFilmCharts(list) {
                let filmNameList = [], registerList = [], hadTicketList = [], timeList = [], cancelList = [];
                let filmType0Count = 0, filmType1Count = 0, filmType0HadTicket = 0, filmType1HadTicket = 0;
        
                list.sort((a, b) => new Date(a.filmStartTime) - new Date(b.filmStartTime));
        
                list.forEach(item => {
                    let name = item.filmName;
                    let reg = item.filmPoll - item.inventoryVotes;
                    let had = item.hadTicket;
                    let timeStr = formatHM(item.filmStartTime) + "-" + formatHM(item.filmEndTime);
        
                    filmNameList.push(name);
                    registerList.push(reg);
                    hadTicketList.push(had);
                    timeList.push(timeStr);
                    cancelList.push(item.filmState === "02");
        
                    if (item.filmType == 1) {
                        filmType0Count++;
                        filmType0HadTicket += had;
                    } else if (item.filmType == 2) {
                        filmType1Count++;
                        filmType1HadTicket += had;
                    }
                });
        
                let chart = Echarts.init(document.getElementById('film-charts'));
                chart.setOption({
                    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                    color: [
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 1, color: '#1F3CFF' }, { offset: 0, color: '#80C7FF' } ] },
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#FFCA5F' }, { offset: 1, color: '#FFB33C' } ] }
                    ],
                    xAxis: {
                        type: 'category',
                        data: filmNameList,
                        axisLabel: {
                            interval: 0,
                            formatter: function (val) {
                                return val.length > 6 ? val.slice(0, 6) + "\n" + val.slice(6) : val;
                                // return val.split('').join('\n'); //竖排
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '人数',
                        minInterval: 1
                    },
                    barGap: '0',
                    series: [
                        { name: '已抢票', type: 'bar', data: registerList },
                        { name: '已核销', type: 'bar', data: hadTicketList }
                    ]
                });
        
                // 渲染影片列表
                let html = '';
                list.forEach((item, i) => {
                    html += `
                        <div style="margin-top:10px; border-bottom:1px dashed #ccc; padding-bottom:8px;">
                            <div style="font-weight:bold;">
                                ${item.filmName} （${formatHM(item.filmStartTime)}-${formatHM(item.filmEndTime)}）
                                ${cancelList[i] ? '<span style="color:red;">【已取消】</span>' : ''}
                            </div>
                            <div style="font-size:13px;margin-top:4px;">
                                <span style="color:#1F3CFF;">已抢票：${registerList[i]}</span>　
                                <span style="color:#FFB33C;">已核销：${hadTicketList[i]}</span>
                            </div>
                        </div>
                    `;
                });
                $('#film-list').html(html);
        
                $('#filmType0Count').text(filmType0Count + '场');
                $('#filmType0HadTicket').text(filmType0HadTicket + '人');
                $('#filmType1Count').text(filmType1Count + '场');
                $('#filmType1HadTicket').text(filmType1HadTicket + '人');
            }
        
            function formatHM(dateStr) {
                let d = new Date(dateStr);
                let h = d.getHours().toString().padStart(2, '0');
                let m = d.getMinutes().toString().padStart(2, '0');
                return h + ':' + m;
            }
        
            // 初始化日期与事件绑定
            $('#film-date').val(new Date().toISOString().split('T')[0]);
            fetchFilmData();
            Controller.api.bindDatepicker('#film-date', function () {
                fetchFilmData();
            });





            function fetchCourseData() {
                var date = $('#course-date').val();
                $.ajax({
                    url: '/admin/course/Session/getStats?date=' + date,
                    type: 'GET',
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 1 && Array.isArray(res.data)) {
                            renderCourseCharts(res.data);
                        } else {
                            Toastr.error(res.msg || '加载失败');
                        }
                    }
                });
            }
        
            function renderCourseCharts(list) {
                let nameList = [], registerList = [], signList = [], timeList = [];
                let totalSign = 0;
        
                list.sort((a, b) => new Date(a.courseStartTime) - new Date(b.courseStartTime));
        
                list.forEach(item => {
                    nameList.push(item.courseName);
                    registerList.push(item.register);
                    signList.push(item.hadSign);
                    timeList.push(formatHM(item.courseStartTime) + "-" + formatHM(item.courseEndTime));
                    totalSign += item.hadSign;
                });
        
                let chart = Echarts.init(document.getElementById('course-charts'));
                chart.setOption({
                    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                    color: [
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 1, color: '#1F3CFF' }, { offset: 0, color: '#80C7FF' } ] },
                        { type: 'linear', x: 0, y: 0, x2: 1, y2: 1, colorStops: [
                            { offset: 0, color: '#FFCA5F' }, { offset: 1, color: '#FFB33C' } ] }
                    ],
                    grid: {
                        left: '0%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    yAxis: {
                        type: 'category',
                        data: nameList,
                        axisLabel: {
                            interval: 0,
                            fontSize: 12,
                            formatter: function (value) {
                                return value.length > 6 ? value.slice(0, 6) + "\n" + value.slice(6) : value;
                            }
                        }
                    },
                    xAxis: {
                        type: 'value',
                        minInterval: 1
                    },
                    barGap: 0,
                    series: [
                        { name: '已抢票', type: 'bar', data: registerList },
                        { name: '已签到', type: 'bar', data: signList }
                    ]
                });
        
                // 渲染课程列表
                let html = '';
                list.forEach((item, i) => {
                    html += `
                        <div style="margin-top:10px; border-bottom:1px dashed #ccc; padding-bottom:8px;">
                            <div style="font-weight:bold;">
                                ${item.courseName} （${formatHM(item.courseStartTime)}-${formatHM(item.courseEndTime)}）
                            </div>
                            <div style="font-size:13px;margin-top:4px;">
                                <span style="color:#1F3CFF;">已抢票：${registerList[i]}</span>　
                                <span style="color:#FFB33C;">已签到：${signList[i]}</span>
                            </div>
                        </div>
                    `;
                });
                $('#course-list').html(html);
        
                $('#courseCount').text(list.length + '场');
                $('#hadSignCount').text(totalSign + '人');
            }
        
            function formatHM(dateStr) {
                let d = new Date(dateStr);
                let h = d.getHours().toString().padStart(2, '0');
                let m = d.getMinutes().toString().padStart(2, '0');
                return h + ':' + m;
            }
        
            $('#course-date').val(new Date().toISOString().split('T')[0]);
            fetchCourseData();
            Controller.api.bindDatepicker('#course-date', function () {
                fetchCourseData();
            });


        },
        api: {
            bindDatepicker: function (selector, onChangeCallback) {
                $(selector).datetimepicker({
                    format: 'YYYY-MM-DD',
                    locale: 'zh-cn',
                    showTodayButton: true,
                    showClose: true,
                    icons: {
                        time: 'fa fa-clock-o',
                        date: 'fa fa-calendar',
                        up: 'fa fa-chevron-up',
                        down: 'fa fa-chevron-down',
                        previous: 'fa fa-chevron-left',
                        next: 'fa fa-chevron-right',
                        today: 'fa fa-crosshairs',
                        clear: 'fa fa-trash',
                        close: 'fa fa-times'
                    }
                }).on('dp.change', function (e) {
                    if (typeof onChangeCallback === 'function') {
                        onChangeCallback(e);
                    }
                });
                var group1 = $(selector).closest('.input-group.date');
       
    
                group1.datetimepicker({
                    format: 'YYYY-MM-DD',
                    locale: 'zh-cn',

                 
               
                    dayViewHeaderFormat: 'YYYY年 MMMM',
                    showTodayButton: true,
                    showClear: false,
                

                  
                    showClose: true,
                    icons: {
                        time: 'fa fa-clock-o',
                        date: 'fa fa-calendar',
                        up: 'fa fa-chevron-up',
                        down: 'fa fa-chevron-down',
                        previous: 'fa fa-chevron-left',
                        next: 'fa fa-chevron-right',
                        today: 'fa fa-crosshairs',
                        clear: 'fa fa-trash',
                        close: 'fa fa-times'
                    }
                }).on('dp.change', function (e) {
                    if (typeof onChangeCallback === 'function') {
                        onChangeCallback(e);
                    }
                });
    
                // 默认值设为今天
                $(selector).val(new Date().toISOString().split('T')[0]);
            }
        }
    };

    return Controller;
});
