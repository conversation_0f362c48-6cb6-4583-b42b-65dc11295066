{"version": 3, "file": "addcontact.js", "sources": ["pages_app/contacts/addcontact.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGNvbnRhY3RzXGFkZGNvbnRhY3QudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"addLink\">\r\n    <!-- 头部组件 -->\r\n    <my-header \r\n      v-if=\"!config.iSzgm\"\r\n      :isBack=\"true\"\r\n      :isShowHome=\"false\"\r\n      :title=\"linkmanID ? '编辑人员' : '新增人员'\"\r\n      :color=\"'#000'\"\r\n    />\r\n    \r\n    <view class=\"addBox\">\r\n      <!-- 证件类型选择 -->\r\n      <view class=\"checkCardType\">\r\n        <view class=\"label\">证件类型</view>\r\n        <view class=\"checkItem-itm\">\r\n          <view \r\n            v-for=\"(item, index) in cardTypeList\" \r\n            :key=\"index\"\r\n            class=\"checkItem\"\r\n            @tap=\"checkCardType(item.value)\"\r\n          >\r\n            <view class=\"checkShow\" :class=\"{ isCheck: index === form.linkmanCertificateType }\"></view>\r\n            <text class=\"checkName\">{{ item.label }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 联系人信息 -->\r\n      <view class=\"addInfo\">\r\n        <view class=\"infoItem\">\r\n          <view class=\"label\">姓名</view>\r\n          <input \r\n            class=\"uni-input\"\r\n            v-model=\"form.linkmanName\"\r\n            placeholder=\"与证件名称一致\"\r\n            placeholder-style=\"font-family: 'PingFang SC'\"\r\n          />\r\n        </view>\r\n        <view class=\"infoItem\">\r\n          <view class=\"label\">证件号码</view>\r\n          <input \r\n            class=\"uni-input\"\r\n            v-model=\"form.linkmanCertificate\"\r\n            placeholder=\"请输入联系人证件号码\"\r\n            placeholder-style=\"font-family: 'PingFang SC'\"\r\n          />\r\n        </view>\r\n        <view class=\"infoItem\">\r\n          <view class=\"label\">手机号码</view>\r\n          <input \r\n            class=\"uni-input\"\r\n            v-model=\"form.linkmanPhone\"\r\n            placeholder=\"请输入联系人手机号码\"\r\n            placeholder-style=\"font-family: 'PingFang SC'\"\r\n          />\r\n        </view>\r\n        <view class=\"infoItem\" v-if=\"form.linkmanCertificateType === 1 || form.linkmanCertificateType === 3\">\r\n          <view class=\"label\">年龄</view>\r\n          <input \r\n            class=\"uni-input\"\r\n            v-model=\"form.linkmanAge\"\r\n            placeholder=\"请输入联系人年龄\"\r\n            placeholder-style=\"font-family: 'PingFang SC'\"\r\n            type=\"number\"\r\n          />\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 用户协议 -->\r\n      <view class=\"user_agree\">\r\n        <view class=\"checkItem\" @tap=\"changeAgree\">\r\n          <view class=\"checkShow\" :class=\"{ isCheck: agree }\"></view>\r\n          <text class=\"checkName\">同意</text>\r\n          <text class=\"user_agreement_btn\" @tap.stop=\"showUserAgreement(true)\">《用户服务协议和隐私政策》</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 提交按钮 -->\r\n    <view class=\"sureChoose\">\r\n      <view class=\"upBtn\" @tap=\"submit\">提交</view>\r\n    </view>\r\n    \r\n    <!-- 用户协议弹窗 -->\r\n    <view class=\"mask\" v-if=\"isShowMask\">\r\n      <view class=\"maskContent\">\r\n        <view class=\"noticeTitle\">用户服务协议和隐私政策</view>\r\n        <view class=\"noticeView\" v-for=\"(notice, index) in noticeList\" :key=\"index\">\r\n          <view class=\"title\" v-if=\"notice.title\">{{ notice.title }}</view>\r\n          <view class=\"text\" v-for=\"(text, textIndex) in notice.textList\" :key=\"textIndex\">{{ text }}</view>\r\n          <view class=\"item\" v-for=\"(item, itemIndex) in notice.ulList\" :key=\"itemIndex\">{{ item }}</view>\r\n          <view class=\"foot\" v-if=\"notice.footText\">{{ notice.footText }}</view>\r\n        </view>\r\n        <view class=\"agreeBtn\" @tap=\"showUserAgreement(false)\">已阅读</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { myRequest } from '../../api/api.js'\r\nimport config from '../../config.js'\r\n\r\nexport default {\r\n  name: 'AddContact',\r\n  data() {\r\n    return {\r\n      form: {\r\n        linkmanAge: '',\r\n        linkmanCertificate: '',\r\n        linkmanCertificateType: 0,\r\n        linkmanName: '',\r\n        linkmanPhone: ''\r\n      },\r\n      cardTypeList: [\r\n        { label: '身份证', value: 0 },\r\n        { label: '港澳台居民通行证/居住证', value: 1 },\r\n        { label: '临时身份证', value: 2 },\r\n        { label: '护照', value: 3 }\r\n      ],\r\n      linkmanID: null,\r\n      agree: false,\r\n      isShowMask: false,\r\n      noticeList: [\r\n        {\r\n          textList: ['我们非常重视对您的个人隐私保护，有时候我们需要某些信息才能为您提供您请求的服务，本隐私声明解释了这些情况下的数据收集和使用情况。']\r\n        },\r\n        {\r\n          title: '关于您的个人信息',\r\n          textList: [\r\n            '我们严格保护您个人信息的安全。我们使用各种安全技术和程序来保护您的个人信息不被未经授权的访问、使用或泄漏。',\r\n            '我们会在法律要求或符合我们的相关服务条款、软件许可使用协议约定的情况下透露您的个人信息，或者有充分理由相信必须这样做才能：'\r\n          ],\r\n          ulList: [\r\n            '满足法律或行政法规的明文规定，或者符合我们APP/小程序适用的法律程序；',\r\n            '符合我们相关服务条款、软件许可使用协议的约定；',\r\n            '在紧急情况下保护服务的用户或大众的个人安全。'\r\n          ],\r\n          footText: '我们不会未经您的允许将这些信息与第三方共享，本声明已经列出的上述情况除外。'\r\n        },\r\n        {\r\n          title: '关于免责说明',\r\n          textList: ['就下列相关事宜的发生，我们不承担任何法律责任：'],\r\n          ulList: [\r\n            '由于您将用户密码告知他人或与他人共享注册帐户，由此导致的任何个人信息的泄漏，或其他非因我们原因导致的个人信息的泄漏；',\r\n            '我们根据法律规定或政府相关政策要求提供您的个人信息；',\r\n            '任何由于黑客攻击、电脑病毒侵入或政府管制而造成的暂时性网站关闭；',\r\n            '因不可抗力导致的任何后果；',\r\n            '我们在各服务条款及声明中列明的使用方式或免责情形。'\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    config() {\r\n      return config\r\n    }\r\n  },\r\n  onLoad(options) {\r\n    this.linkmanID = options.id || null\r\n    \r\n    if (this.linkmanID) {\r\n      this.getContactDetail()\r\n    }\r\n  },\r\n  methods: {\r\n    // 获取联系人详情\r\n    getContactDetail() {\r\n      myRequest({\r\n        url: `/auth/linkman/${this.linkmanID}`,\r\n        method: 'GET'\r\n      }).then(res => {\r\n        if (res.data && res.data.data) {\r\n          this.form = res.data.data\r\n        }\r\n      }).catch(err => {\r\n        console.error('获取联系人详情失败:', err)\r\n        uni.showToast({\r\n          title: '获取联系人详情失败',\r\n          icon: 'error'\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 选择证件类型\r\n    checkCardType(value) {\r\n      this.form.linkmanCertificateType = value\r\n    },\r\n    \r\n    // 提交表单\r\n    submit() {\r\n      // 验证姓名\r\n      if (!this.form.linkmanName || this.form.linkmanName.trim() === '') {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请输入姓名',\r\n          showCancel: false\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 验证证件号码\r\n      if (!this.form.linkmanCertificate || this.form.linkmanCertificate.trim() === '') {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请输入证件号码',\r\n          showCancel: false\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 验证手机号码\r\n      if (!this.form.linkmanPhone || this.form.linkmanPhone.trim() === '') {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请输入手机号码',\r\n          showCancel: false\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 验证手机号码格式\r\n      if (!/^1[3-9]\\d{9}$/.test(this.form.linkmanPhone)) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请输入正确的手机号码',\r\n          showCancel: false\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 验证姓名格式（护照除外）\r\n      if (this.form.linkmanCertificateType !== 3 && \r\n          !/^[a-zA-Z\\u4E00-\\u9FA5\\uf900-\\ufa2d·\\s]{2,20}$/.test(this.form.linkmanName)) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请输入正确的姓名',\r\n          showCancel: false\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 验证年龄（港澳台居民通行证/居住证或护照需要输入年龄）\r\n      if ((this.form.linkmanCertificateType === 1 || this.form.linkmanCertificateType === 3)) {\r\n        if (!this.form.linkmanAge || !/^\\d{1,2}$/.test(this.form.linkmanAge)) {\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '请输入年龄',\r\n            showCancel: false\r\n          })\r\n          return\r\n        }\r\n        \r\n        const age = parseInt(this.form.linkmanAge)\r\n        if (age < 1 || age > 120) {\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: '请输入正确的年龄（1-120岁）',\r\n            showCancel: false\r\n          })\r\n          return\r\n        }\r\n      }\r\n      \r\n      // 验证协议同意\r\n      if (!this.agree) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '请详细阅读并勾选下方《用户服务协议和隐私政策》',\r\n          showCancel: false\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 提交数据\r\n      const url = this.linkmanID ? '/auth/linkman/edit' : '/auth/linkman/add'\r\n      const method = this.linkmanID ? 'PUT' : 'POST'\r\n      \r\n      myRequest({\r\n        url,\r\n        method,\r\n        data: this.form\r\n      }).then(res => {\r\n        uni.showToast({\r\n          title: this.linkmanID ? '修改成功' : '添加成功',\r\n          icon: 'success',\r\n          duration: 2000,\r\n          success: () => {\r\n            setTimeout(() => {\r\n              uni.navigateBack()\r\n            }, 2000)\r\n          }\r\n        })\r\n      }).catch(err => {\r\n        console.error('提交失败:', err)\r\n        uni.showToast({\r\n          title: '提交失败',\r\n          icon: 'error'\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 切换协议同意状态\r\n    changeAgree() {\r\n      this.agree = !this.agree\r\n    },\r\n    \r\n    // 显示/隐藏用户协议\r\n    showUserAgreement(show) {\r\n      this.isShowMask = show\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.addLink {\r\n  background-color: #f3f4f6;\r\n  font-family: PingFang SC;\r\n  height: auto;\r\n  min-height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.addBox {\r\n  box-sizing: border-box;\r\n  height: auto;\r\n  padding: 29rpx;\r\n  width: 100%;\r\n}\r\n\r\n.label {\r\n  color: #888;\r\n  font-size: 27rpx;\r\n  font-weight: 600;\r\n  margin-right: 29rpx;\r\n  min-width: 110rpx;\r\n}\r\n\r\n.checkCardType {\r\n  align-items: center;\r\n  background-color: #fff;\r\n  border-radius: 15rpx;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  margin-bottom: 29rpx;\r\n  padding: 0 15rpx 0 29rpx;\r\n  width: 100%;\r\n}\r\n\r\n.checkItem-itm {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.checkItem {\r\n  align-items: center;\r\n  display: flex;\r\n  margin-left: 20rpx;\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.checkShow {\r\n  border: 2rpx solid #888;\r\n  border-radius: 50%;\r\n  font-size: 25rpx;\r\n  font-weight: 700;\r\n  height: 29rpx;\r\n  line-height: 29rpx;\r\n  margin-right: 10rpx;\r\n  text-align: center;\r\n  width: 29rpx;\r\n}\r\n\r\n.checkShow.isCheck {\r\n  background: #ffba38;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.checkShow.isCheck::before {\r\n  content: \"✓\";\r\n  display: inline-block;\r\n  color: #fff;\r\n}\r\n\r\n.checkName {\r\n  color: #000;\r\n  font-size: 27rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.addInfo {\r\n  background-color: #fff;\r\n  border-radius: 15rpx;\r\n  box-sizing: border-box;\r\n  padding: 0 29rpx;\r\n  width: 100%;\r\n}\r\n\r\n.infoItem {\r\n  align-items: center;\r\n  border-bottom: 1rpx solid rgba(136, 136, 136, 0.5);\r\n  display: flex;\r\n  height: 92rpx;\r\n  width: 100%;\r\n}\r\n\r\n.infoItem:last-of-type {\r\n  border-bottom: 0;\r\n}\r\n\r\n.uni-input {\r\n  color: #000;\r\n  font-family: PingFang SC !important;\r\n  font-size: 27rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.user_agree {\r\n  box-sizing: border-box;\r\n  height: 150rpx;\r\n  padding: 20rpx 29rpx;\r\n  width: 100%;\r\n}\r\n\r\n.user_agree .checkItem {\r\n  align-items: center;\r\n  display: flex;\r\n}\r\n\r\n.user_agree .checkShow {\r\n  border: 2rpx solid #888;\r\n  border-radius: 50%;\r\n  font-size: 25rpx;\r\n  font-weight: 700;\r\n  height: 29rpx;\r\n  line-height: 29rpx;\r\n  margin-right: 25rpx;\r\n  text-align: center;\r\n  width: 29rpx;\r\n}\r\n\r\n.user_agree .checkShow.isCheck {\r\n  background: #ffba38;\r\n  border: 2rpx solid transparent;\r\n}\r\n\r\n.user_agree .checkShow.isCheck::before {\r\n  content: \"✓\";\r\n  display: inline-block;\r\n  color: #fff;\r\n}\r\n\r\n.user_agree .checkName {\r\n  color: #000;\r\n  font-size: 27rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.user_agreement_btn {\r\n  color: #5cb7ff;\r\n  font-size: 27rpx;\r\n  font-weight: 600;\r\n}\r\n\r\n.sureChoose {\r\n  align-items: center;\r\n  bottom: 0;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 150rpx;\r\n  justify-content: space-evenly;\r\n  left: 0;\r\n  padding: 0 29rpx;\r\n  position: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.upBtn {\r\n  background: #5cb7ff;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);\r\n  color: #fff;\r\n  font-size: 35rpx;\r\n  height: 77rpx;\r\n  line-height: 77rpx;\r\n  text-align: center;\r\n  width: 654rpx;\r\n}\r\n\r\n.mask {\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  bottom: 0;\r\n  height: auto;\r\n  overflow: auto;\r\n  padding: 237rpx 0;\r\n  position: fixed;\r\n  top: 0;\r\n  width: 100%;\r\n  z-index: 999;\r\n}\r\n\r\n.maskContent {\r\n  background: #fff;\r\n  border: 2rpx solid #707070;\r\n  border-radius: 19rpx;\r\n  box-sizing: border-box;\r\n  font-family: PingFang SC;\r\n  height: auto;\r\n  margin: 0 auto;\r\n  padding: 58rpx 58rpx 48rpx;\r\n  width: 654rpx;\r\n}\r\n\r\n.noticeTitle {\r\n  color: #1b1b1b;\r\n  font-size: 35rpx;\r\n  font-weight: 700;\r\n  margin-bottom: 30rpx;\r\n  text-align: center;\r\n}\r\n\r\n.noticeView {\r\n  height: auto;\r\n  line-height: 50rpx;\r\n  text-align: justify;\r\n  width: 100%;\r\n}\r\n\r\n.title {\r\n  font-size: 29rpx;\r\n  font-weight: 700;\r\n}\r\n\r\n.text {\r\n  font-size: 27rpx;\r\n  margin: 30rpx 0;\r\n}\r\n\r\n.item {\r\n  font-size: 27rpx;\r\n  line-height: 40rpx;\r\n  margin-bottom: 20rpx;\r\n  padding-left: 40rpx;\r\n  position: relative;\r\n}\r\n\r\n.item::before {\r\n  background-color: #000;\r\n  border-radius: 50%;\r\n  content: \"\";\r\n  height: 10rpx;\r\n  left: 0.5em;\r\n  position: absolute;\r\n  top: 0.5em;\r\n  width: 10rpx;\r\n}\r\n\r\n.foot {\r\n  font-size: 27rpx;\r\n  margin: 40rpx 0;\r\n}\r\n\r\n.agreeBtn {\r\n  background: #5cb7ff;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);\r\n  color: #fff;\r\n  font-size: 35rpx;\r\n  height: 77rpx;\r\n  line-height: 77rpx;\r\n  margin: 0 auto;\r\n  text-align: center;\r\n  width: 381rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/contacts/addcontact.vue'\nwx.createPage(MiniProgramPage)"], "names": ["config", "myRequest", "uni"], "mappings": ";;;;AAwGA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,aAAa;AAAA,QACb,cAAc;AAAA,MACf;AAAA,MACD,cAAc;AAAA,QACZ,EAAE,OAAO,OAAO,OAAO,EAAG;AAAA,QAC1B,EAAE,OAAO,gBAAgB,OAAO,EAAG;AAAA,QACnC,EAAE,OAAO,SAAS,OAAO,EAAG;AAAA,QAC5B,EAAE,OAAO,MAAM,OAAO,EAAE;AAAA,MACzB;AAAA,MACD,WAAW;AAAA,MACX,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,YAAY;AAAA,QACV;AAAA,UACE,UAAU,CAAC,kEAAkE;AAAA,QAC9E;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,UAAU;AAAA,YACR;AAAA,YACA;AAAA,UACD;AAAA,UACD,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAAA,UACD,UAAU;AAAA,QACX;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,UAAU,CAAC,yBAAyB;AAAA,UACpC,QAAQ;AAAA,YACN;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,SAAS;AACP,aAAOA,OAAK;AAAA,IACd;AAAA,EACD;AAAA,EACD,OAAO,SAAS;AACd,SAAK,YAAY,QAAQ,MAAM;AAE/B,QAAI,KAAK,WAAW;AAClB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,mBAAmB;AACjBC,wBAAU;AAAA,QACR,KAAK,iBAAiB,KAAK,SAAS;AAAA,QACpC,QAAQ;AAAA,OACT,EAAE,KAAK,SAAO;AACb,YAAI,IAAI,QAAQ,IAAI,KAAK,MAAM;AAC7B,eAAK,OAAO,IAAI,KAAK;AAAA,QACvB;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,4CAAA,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,WAAK,KAAK,yBAAyB;AAAA,IACpC;AAAA;AAAA,IAGD,SAAS;AAEP,UAAI,CAAC,KAAK,KAAK,eAAe,KAAK,KAAK,YAAY,KAAO,MAAI,IAAI;AACjEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,SACb;AACD;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,KAAK,sBAAsB,KAAK,KAAK,mBAAmB,WAAW,IAAI;AAC/EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,SACb;AACD;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,KAAK,gBAAgB,KAAK,KAAK,aAAa,KAAK,MAAM,IAAI;AACnEA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,SACb;AACD;AAAA,MACF;AAGA,UAAI,CAAC,gBAAgB,KAAK,KAAK,KAAK,YAAY,GAAG;AACjDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,SACb;AACD;AAAA,MACF;AAGA,UAAI,KAAK,KAAK,2BAA2B,KACrC,CAAC,gDAAgD,KAAK,KAAK,KAAK,WAAW,GAAG;AAChFA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,SACb;AACD;AAAA,MACF;AAGA,UAAK,KAAK,KAAK,2BAA2B,KAAK,KAAK,KAAK,2BAA2B,GAAI;AACtF,YAAI,CAAC,KAAK,KAAK,cAAc,CAAC,YAAY,KAAK,KAAK,KAAK,UAAU,GAAG;AACpEA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,WACb;AACD;AAAA,QACF;AAEA,cAAM,MAAM,SAAS,KAAK,KAAK,UAAU;AACzC,YAAI,MAAM,KAAK,MAAM,KAAK;AACxBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,WACb;AACD;AAAA,QACF;AAAA,MACF;AAGA,UAAI,CAAC,KAAK,OAAO;AACfA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,SACb;AACD;AAAA,MACF;AAGA,YAAM,MAAM,KAAK,YAAY,uBAAuB;AACpD,YAAM,SAAS,KAAK,YAAY,QAAQ;AAExCD,wBAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA,MAAM,KAAK;AAAA,OACZ,EAAE,KAAK,SAAO;AACbC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,KAAK,YAAY,SAAS;AAAA,UACjC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM;AACb,uBAAW,MAAM;AACfA,4BAAAA,MAAI,aAAa;AAAA,YAClB,GAAE,GAAI;AAAA,UACT;AAAA,SACD;AAAA,MACH,CAAC,EAAE,MAAM,SAAO;AACdA,sBAAAA,iEAAc,SAAS,GAAG;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,cAAc;AACZ,WAAK,QAAQ,CAAC,KAAK;AAAA,IACpB;AAAA;AAAA,IAGD,kBAAkB,MAAM;AACtB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzTA,GAAG,WAAW,eAAe;"}