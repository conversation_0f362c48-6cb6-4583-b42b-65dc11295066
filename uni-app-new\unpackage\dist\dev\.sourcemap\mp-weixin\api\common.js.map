{"version": 3, "file": "common.js", "sources": ["api/common.js"], "sourcesContent": ["import { request } from '../utils/request.js'\r\n\r\n// 获取系统配置\r\nexport function getSystemConfig() {\r\n  return request({\r\n    url: '/apitp/system/config',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取公告信息\r\nexport function getAnnouncements(params) {\r\n  return request({\r\n    url: '/apitp/announcement/list',\r\n    method: 'GET',\r\n    params\r\n  })\r\n}\r\n\r\n// 获取公告详情\r\nexport function getAnnouncementDetail(announcementId) {\r\n  return request({\r\n    url: `/apitp/announcement/detail/${announcementId}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 获取轮播图\r\nexport function getBanners() {\r\n  return request({\r\n    url: '/apitp/banner/list',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取常见问题\r\nexport function getFAQ() {\r\n  return request({\r\n    url: '/apitp/faq/list',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取联系方式\r\nexport function getContactInfo() {\r\n  return request({\r\n    url: '/apitp/contact/info',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 发送短信验证码\r\nexport function sendSmsCode(phone, type) {\r\n  return request({\r\n    url: '/apitp/sms/send',\r\n    method: 'POST',\r\n    data: { phone, type },\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 验证短信验证码\r\nexport function verifySmsCode(phone, code, type) {\r\n  return request({\r\n    url: '/apitp/sms/verify',\r\n    method: 'POST',\r\n    data: { phone, code, type },\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 文件上传\r\nexport function uploadFile(file, type) {\r\n  return request({\r\n    url: '/apitp/upload',\r\n    method: 'POST',\r\n    data: { file, type }\r\n  })\r\n}\r\n\r\n// 获取字典数据\r\nexport function getDictData(dictType) {\r\n  return request({\r\n    url: `/apitp/dict/data/${dictType}`,\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取地区数据\r\nexport function getRegionData(parentId = 0) {\r\n  return request({\r\n    url: '/apitp/region/list',\r\n    method: 'GET',\r\n    params: { parentId },\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 意见反馈\r\nexport function submitFeedback(feedbackData) {\r\n  return request({\r\n    url: '/apitp/feedback/submit',\r\n    method: 'POST',\r\n    data: feedbackData\r\n  })\r\n}\r\n\r\n// 获取版本信息\r\nexport function getVersionInfo() {\r\n  return request({\r\n    url: '/apitp/version/info',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 检查更新\r\nexport function checkUpdate(currentVersion) {\r\n  return request({\r\n    url: '/apitp/version/check',\r\n    method: 'GET',\r\n    params: { version: currentVersion },\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取隐私政策\r\nexport function getPrivacyPolicy() {\r\n  return request({\r\n    url: '/apitp/policy/privacy',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}\r\n\r\n// 获取用户协议\r\nexport function getUserAgreement() {\r\n  return request({\r\n    url: '/apitp/policy/agreement',\r\n    method: 'GET',\r\n    headers: {\r\n      isToken: false\r\n    }\r\n  })\r\n}"], "names": ["request"], "mappings": ";;AAgEO,SAAS,YAAY,OAAO,MAAM;AACvC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,MAAM,EAAE,OAAO,KAAM;AAAA,IACrB,SAAS;AAAA,MACP,SAAS;AAAA,IACV;AAAA,EACL,CAAG;AACH;AAGO,SAAS,cAAc,OAAO,MAAM,MAAM;AAC/C,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,MAAM,EAAE,OAAO,MAAM,KAAM;AAAA,IAC3B,SAAS;AAAA,MACP,SAAS;AAAA,IACV;AAAA,EACL,CAAG;AACH;AAmEO,SAAS,mBAAmB;AACjC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,SAAS;AAAA,IACV;AAAA,EACL,CAAG;AACH;AAGO,SAAS,mBAAmB;AACjC,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,SAAS;AAAA,MACP,SAAS;AAAA,IACV;AAAA,EACL,CAAG;AACH;;;;;"}