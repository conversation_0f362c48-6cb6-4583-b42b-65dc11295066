define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'moment'], function ($, undefined, Backend, Table, Form, moment) {
  var calendar = null;                // FullCalendar 实例
  let selectedDate = new Date(); // 默认当前日期
  var currentStatus = null;          // 当前选中日期的开闭馆状态 ('1' = 开馆, '0' = 闭馆)




  var Controller = {
    index: function () {
      var calendarEl = document.getElementById('holiday-calendar');

      $(function () {
        var calendar = new FullCalendar.Calendar(calendarEl, {
          locale: 'zh-cn',
          initialView: 'dayGridMonth',
          height: 'auto',
          headerToolbar: {
            left: 'prev today next',
            center: 'title',
            right: ''
          },
          selectable: true,
          customButtons: {

            today: {
              text: '今天',
              click: function () {
                selectedDate = new Date(); // 今天
                calendar.today();
                highlightSelectedDate();
              }
            },
            prev: {
              text: '上个月',
              click: function () {
                calendar.prev();

                setTimeout(() => {
                  const firstOfMonth = calendar.view.currentStart;
                  selectedDate = firstOfMonth;
                  highlightSelectedDate();
                });
              }
            },
            next: {
              text: '下个月',
              click: function () {
                calendar.next();

                setTimeout(() => {
                  const firstOfMonth = calendar.view.currentStart;
                  console.log('firstOfMonth', firstOfMonth)
                  selectedDate = firstOfMonth;
                  highlightSelectedDate();
                });
              }
            },


            openButton: {
              text: '开馆',
              click: function () {
                if (!selectedDate) return Toastr.warning("请先选择一个日期");
                const dateStr = formatDateToYMD(selectedDate);
                currentStatus = getDateStatusFromEvent(dateStr); // // 自定义函数，从事件中判断当天状态


                if (currentStatus === '1') {
                  Toastr.info("已开馆，无需操作");
                } else {
                  $.post('functioncontrol/holidays/update', {
                    date: dateStr,
                    is_close: '1'
                  }, function (res) {
                    res.code === 1 ? Toastr.success("开馆成功") : Toastr.error(res.msg || "操作失败");
                    calendar.refetchEvents();
                  }, 'json');
                }
                highlightSelectedDate();
              }
            },
            closeButton: {
              text: '闭馆',
              click: function () {
                if (!selectedDate) return Toastr.warning("请先选择一个日期");
                const dateStr = formatDateToYMD(selectedDate);
                currentStatus = getDateStatusFromEvent(dateStr); // // 自定义函数，从事件中判断当天状态


                if (currentStatus === '0') {
                  Toastr.info("已闭馆，无需操作");
                } else {
                  $('#closeModal').modal('show'); // 打开弹窗
                }
                highlightSelectedDate();
              }
            }
          },
          headerToolbar: {
            left: 'prev today next',
            center: 'title',
            right: 'openButton closeButton'
          },
          events: function (info, successCallback, failureCallback) {
            // 加载指定月份的数据
            const middle = new Date(info.start.getTime());
            middle.setDate(middle.getDate() + 15);

            const year = middle.getFullYear();
            const month = String(middle.getMonth() + 1).padStart(2, '0');
            const ym = `${year}-${month}`;

            $.get('functioncontrol/holidays/getlist', {
              month: ym
            }, function (res) {
              if (res.code === 1) {
                successCallback(res.data);
              } else {
                failureCallback(res.msg);
              }
            });
          },
          eventContent: function (arg) {
            const isClose = arg.event.extendedProps.is_close === '0'; // 0 = 闭馆

            const el = document.createElement('div');
            el.innerHTML = `<span class="${isClose ? 'calendar-close' : 'calendar-open'}">
                    ${isClose ? '闭馆日' : '开馆日'}
                  </span>`;

            return { domNodes: [el] };
          },
          dateClick: function (info) {

            //这一段是处理，点击了 非本月的日子，自动跳到对应月
            const clickedDate = info.date;
            const calendar = info.view.calendar;
            const currentMonth = calendar.getDate().getMonth(); // 当前中心月（0-11）
            const clickedMonth = clickedDate.getMonth();

            if (clickedMonth < currentMonth) {
              calendar.prev(); // 上个月
            } else if (clickedMonth > currentMonth) {
              calendar.next(); // 下个月
            } else {
              // 本月日，正常处理选中
              $('.fc-daygrid-day').removeClass('selected-date');
              $('.fc-daygrid-day[data-date="' + info.dateStr + '"]').addClass('selected-date');
            }

            selectedDate = info.date;
            highlightSelectedDate();


          },
          // 每次视图变更都会触发
          datesSet: function (info) {


            highlightSelectedDate(); // 高亮打钩

          },
        });

        calendar.render();
        setTimeout(() => {
          $('.fc-prev-button').text('上个月');
          $('.fc-next-button').text('下个月');
          $('.fc-closeButton-button').css({
            'background-color': 'red',
            'border-color': 'red',
            'color': 'white'
          });
          $('.fc-openButton-button').css({
            'background-color': '#1890ff',
            'border-color': '#1890ff',
            'color': 'white'
          });
        }, 100);

        // 提交闭馆信息并更新状态
        $('#confirm-close').on('click', function () {
          const title = $('#notice-title').val();
          const content = $('#notice-content').val();

          const dateStr = formatDateToYMD(selectedDate);

          Layer.confirm(`确定关闭日期为：${dateStr} 的场馆？`, {
            icon: 0,
            title: '提示'
          }, function (index) {
            // ✅ 用户点击确认后的操作
            $.post('functioncontrol/holidays/close', {
              date: dateStr,
              is_close: '0',
              title: title,
              content: content
            }, function (res) {
              if (res.code === 1) {
                Toastr.success('闭馆成功');
                $('#closeModal').modal('hide');
                Layer.close(index);
                // 可选：刷新日历
                calendar.refetchEvents();
              } else {
                Toastr.error(res.msg || '操作失败');
              }
            }, 'json');
          });


        });
        // ======= 辅助函数放这里 =======
        // 自定义函数，从事件中判断当天状态 （开馆 闭馆)
        function getDateStatusFromEvent(dateStr) {
          const events = calendar.getEvents();
          const event = events.find(ev => ev.startStr === dateStr);
          return event ? event.extendedProps.is_close : null;
        }
        //、创建高亮函数
        function highlightSelectedDate() {
          $('.fc-daygrid-day').removeClass('fc-selected');
          const dateObj = new Date(selectedDate); // 转为标准 Date 对象
          const dateStr = formatDateToYMD(dateObj);

          console.log('高亮函数：');
          console.log('[highl] selectedDate:', selectedDate);
          console.log('[highl] formatted:', dateStr);


          $(`.fc-daygrid-day[data-date="${dateStr}"]`).addClass('fc-selected');

          $('.fc-prev-button').text('上个月');
          $('.fc-next-button').text('下个月');
        }
        //、创建高亮函数2
        function formatDateToYMD(date) {
          const y = date.getFullYear();
          const m = String(date.getMonth() + 1).padStart(2, '0');
          const d = String(date.getDate()).padStart(2, '0');
          return `${y}-${m}-${d}`;
        }


      });


    }
  };
  return Controller;
});
