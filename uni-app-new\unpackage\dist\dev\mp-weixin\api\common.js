"use strict";
const utils_request = require("../utils/request.js");
function sendSmsCode(phone, type) {
  return utils_request.request({
    url: "/apitp/sms/send",
    method: "POST",
    data: { phone, type },
    headers: {
      isToken: false
    }
  });
}
function verifySmsCode(phone, code, type) {
  return utils_request.request({
    url: "/apitp/sms/verify",
    method: "POST",
    data: { phone, code, type },
    headers: {
      isToken: false
    }
  });
}
function getPrivacyPolicy() {
  return utils_request.request({
    url: "/apitp/policy/privacy",
    method: "GET",
    headers: {
      isToken: false
    }
  });
}
function getUserAgreement() {
  return utils_request.request({
    url: "/apitp/policy/agreement",
    method: "GET",
    headers: {
      isToken: false
    }
  });
}
exports.getPrivacyPolicy = getPrivacyPolicy;
exports.getUserAgreement = getUserAgreement;
exports.sendSmsCode = sendSmsCode;
exports.verifySmsCode = verifySmsCode;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/common.js.map
