import { request } from '../utils/request.js'

// 获取验证码图片
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'GET',
    timeout: 20000
  })
}

// 获取用户信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'GET'
  })
}

// 用户登录
export function login(tenantId, username, password, code, uuid) {
  const data = {
    tenantId,
    username,
    password,
    code,
    uuid
  }
  
  return request({
    url: '/login',
    headers: {
      isToken: false,
      isEncrypt: false
    },
    method: 'POST',
    data
  })
}

// 微信登录
export function wxLogin(appId, code, openid) {
  const data = {
    code,
    openid
  }
  
  return request({
    url: `/wx/user/${appId}/login`,
    headers: {
      isToken: false,
      isEncrypt: false
    },
    method: 'POST',
    data
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/logout',
    method: 'POST'
  })
}

// 用户注册
export function register(userData) {
  return request({
    url: '/register',
    headers: {
      isToken: false,
      isEncrypt: false
    },
    method: 'POST',
    data: userData
  })
}

// 手机号注册
export function registerWithPhone(userData) {
  return request({
    url: '/wx/user/register',
    headers: {
      isToken: false,
      isEncrypt: false
    },
    method: 'POST',
    data: userData
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/refresh',
    method: 'POST'
  })
}

// 修改密码
export function changePassword(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  
  return request({
    url: '/user/changePassword',
    method: 'PUT',
    data
  })
}

// 忘记密码
export function forgotPassword(phone, code, newPassword) {
  const data = {
    phone,
    code,
    newPassword
  }
  
  return request({
    url: '/user/forgotPassword',
    headers: {
      isToken: false
    },
    method: 'POST',
    data
  })
}