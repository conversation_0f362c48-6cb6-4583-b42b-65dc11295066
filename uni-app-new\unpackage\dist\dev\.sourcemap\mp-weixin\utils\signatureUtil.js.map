{"version": 3, "file": "signatureUtil.js", "sources": ["utils/signatureUtil.js"], "sourcesContent": ["/**\r\n * 签名工具\r\n * 适配uni-app环境\r\n */\r\n\r\n/**\r\n * 简单的MD5实现（用于签名）\r\n * 在实际项目中应该使用专门的MD5库\r\n */\r\nfunction simpleMD5(str) {\r\n  // 这是一个简化的MD5实现，仅用于演示\r\n  // 实际项目中应该使用真正的MD5库\r\n  let hash = 0\r\n  if (str.length === 0) return hash.toString()\r\n\r\n  for (let i = 0; i < str.length; i++) {\r\n    const char = str.charCodeAt(i)\r\n    hash = ((hash << 5) - hash) + char\r\n    hash = hash & hash // 转换为32位整数\r\n  }\r\n\r\n  return Math.abs(hash).toString(16).padStart(8, '0')\r\n}\r\n\r\n/**\r\n * 序列化数据为查询字符串\r\n * @param {Object|string} data 数据\r\n * @returns {string} 序列化后的字符串\r\n */\r\nfunction serializeData(data) {\r\n  if (typeof data === 'string') {\r\n    try {\r\n      data = JSON.parse(data)\r\n    } catch (error) {\r\n      return ''\r\n    }\r\n  }\r\n\r\n  if (!data || typeof data !== 'object') {\r\n    return ''\r\n  }\r\n\r\n  let result = ''\r\n  for (const key in data) {\r\n    if (data.hasOwnProperty(key)) {\r\n      result += key + '=' + data[key] + '&'\r\n    }\r\n  }\r\n\r\n  return result\r\n}\r\n\r\n/**\r\n * 生成签名\r\n * @param {Object} options 选项\r\n * @param {Object} options.data 数据\r\n * @param {string} options.url URL\r\n * @param {Object} options.headers 请求头\r\n * @returns {Object} 签名结果\r\n */\r\nexport function signatureGenerate({ data, url, headers }) {\r\n  const timestamp = new Date().getTime()\r\n  const dataStr = serializeData(data)\r\n  const signStr = '&timestamp=' + timestamp + '&url=' + url + dataStr\r\n\r\n  return {\r\n    signature: simpleMD5(signStr).toUpperCase(),\r\n    timestamp: timestamp\r\n  }\r\n}\r\n\r\n// 默认导出\r\nexport default {\r\n  signatureGenerate\r\n}"], "names": [], "mappings": ";AASA,SAAS,UAAU,KAAK;AAGtB,MAAI,OAAO;AACX,MAAI,IAAI,WAAW;AAAG,WAAO,KAAK,SAAU;AAE5C,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,YAAS,QAAQ,KAAK,OAAQ;AAC9B,WAAO,OAAO;AAAA,EACf;AAED,SAAO,KAAK,IAAI,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AACpD;AAOA,SAAS,cAAc,MAAM;AAC3B,MAAI,OAAO,SAAS,UAAU;AAC5B,QAAI;AACF,aAAO,KAAK,MAAM,IAAI;AAAA,IACvB,SAAQ,OAAO;AACd,aAAO;AAAA,IACR;AAAA,EACF;AAED,MAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,WAAO;AAAA,EACR;AAED,MAAI,SAAS;AACb,aAAW,OAAO,MAAM;AACtB,QAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,gBAAU,MAAM,MAAM,KAAK,GAAG,IAAI;AAAA,IACnC;AAAA,EACF;AAED,SAAO;AACT;AAUO,SAAS,kBAAkB,EAAE,MAAM,KAAK,QAAO,GAAI;AACxD,QAAM,aAAY,oBAAI,KAAM,GAAC,QAAS;AACtC,QAAM,UAAU,cAAc,IAAI;AAClC,QAAM,UAAU,gBAAgB,YAAY,UAAU,MAAM;AAE5D,SAAO;AAAA,IACL,WAAW,UAAU,OAAO,EAAE,YAAa;AAAA,IAC3C;AAAA,EACD;AACH;;"}