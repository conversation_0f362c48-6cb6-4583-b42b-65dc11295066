{"version": 3, "file": "index.js", "sources": ["pages_app/contactmanager/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGNvbnRhY3RtYW5hZ2VyXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"contactContent\">\r\n    <view class=\"content\">\r\n      <!-- 头部组件 -->\r\n      <my-header \r\n        v-if=\"!config.iSzgm\"\r\n        :menuClass=\"'bor'\"\r\n        :isFixed=\"true\"\r\n        :isBack=\"true\"\r\n        :isShowHome=\"true\"\r\n        :title=\"'联系人管理'\"\r\n        :color=\"'#000'\"\r\n      />\r\n      \r\n      <view class=\"contactBox\">\r\n        <!-- 添加联系人按钮 -->\r\n        <view class=\"addContact\" @tap=\"addContact\">\r\n          <text class=\"add\">+</text> 添加联系人\r\n        </view>\r\n        \r\n        <!-- 联系人列表 -->\r\n        <view class=\"contactList\">\r\n          <view \r\n            v-for=\"(item, index) in linkList\" \r\n            :key=\"item.id\"\r\n            class=\"contactItem\"\r\n            :class=\"{ isMove: item.isMove }\"\r\n            @touchstart=\"touchstart\"\r\n            @touchmove=\"touchmove($event, index)\"\r\n            :data-index=\"index\"\r\n          >\r\n            <view class=\"left\">\r\n              <view class=\"peopleName\">{{ item.linkmanName }}</view>\r\n              <view class=\"peopleCard\">证件号 {{ hideCardNum(index) }}</view>\r\n              <view class=\"peopleMablie\">\r\n                <text>手机号码 {{ item.linkmanPhone }}</text>\r\n                <text>年龄 {{ item.linkmanAge }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"handlePlace\">\r\n              <view class=\"edit\" @tap=\"editItem(item.id)\">编辑</view>\r\n              <view class=\"del\" @tap=\"deleteItem(item.id)\">删除</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { myRequest } from '../../api/api.js'\r\nimport config from '../../config.js'\r\n\r\nexport default {\r\n  name: 'ContactManager',\r\n  data() {\r\n    return {\r\n      maxNum: 0,\r\n      linkList: [],\r\n      checkAllNum: 0,\r\n      startX: '',\r\n      startY: ''\r\n    }\r\n  },\r\n  computed: {\r\n    config() {\r\n      return config\r\n    }\r\n  },\r\n  onShow() {\r\n    this.getLinkList()\r\n  },\r\n  onLoad(options) {\r\n    this.maxNum = options.num || 0\r\n  },\r\n  methods: {\r\n    // 获取联系人列表\r\n    getLinkList() {\r\n      myRequest({\r\n        url: '/auth/linkman/list'\r\n      }).then(res => {\r\n        if (res.data && res.data.rows) {\r\n          this.linkList = res.data.rows.map(item => ({\r\n            ...item,\r\n            isMove: false\r\n          }))\r\n        }\r\n      }).catch(err => {\r\n        console.error('获取联系人列表失败:', err)\r\n        uni.showToast({\r\n          title: '获取联系人列表失败',\r\n          icon: 'error'\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 隐藏身份证号中间部分\r\n    hideCardNum(index) {\r\n      const certificate = this.linkList[index].linkmanCertificate\r\n      const length = certificate.length\r\n      \r\n      if (length < 4) return certificate\r\n      \r\n      if (length > 4 && length !== 18) {\r\n        let result = certificate.substring(0, 2)\r\n        for (let i = 0; i < length; i++) {\r\n          if (i >= 2 && i < length - 2) {\r\n            result += '*'\r\n          }\r\n        }\r\n        result += certificate.substring(length - 2, length)\r\n        return result\r\n      }\r\n      \r\n      return certificate.replace(certificate.substring(4, 15), '*******')\r\n    },\r\n    \r\n    // 添加联系人\r\n    addContact() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/contacts/addcontact'\r\n      })\r\n    },\r\n    \r\n    // 删除联系人\r\n    deleteItem(id) {\r\n      uni.showModal({\r\n        title: '确认删除',\r\n        content: '确定要删除这个联系人吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            myRequest({\r\n              url: `/auth/linkman/${id}`,\r\n              method: 'DELETE'\r\n            }).then(res => {\r\n              uni.showToast({\r\n                title: '删除成功',\r\n                icon: 'success'\r\n              })\r\n              this.getLinkList()\r\n            }).catch(err => {\r\n              console.error('删除联系人失败:', err)\r\n              uni.showToast({\r\n                title: '删除失败',\r\n                icon: 'error'\r\n              })\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 编辑联系人\r\n    editItem(id) {\r\n      uni.navigateTo({\r\n        url: `/pages_app/contacts/addcontact?id=${id}`\r\n      })\r\n    },\r\n    \r\n    // 触摸开始\r\n    touchstart(e) {\r\n      this.linkList.forEach(item => {\r\n        if (item.isMove) {\r\n          item.isMove = false\r\n        }\r\n      })\r\n      this.startX = e.changedTouches[0].clientX\r\n      this.startY = e.changedTouches[0].clientY\r\n    },\r\n    \r\n    // 触摸移动\r\n    touchmove(e, index) {\r\n      const startX = this.startX\r\n      const startY = this.startY\r\n      const touchX = e.changedTouches[0].clientX\r\n      const touchY = e.changedTouches[0].clientY\r\n      const angle = this.angle({ x: startX, y: startY }, { x: touchX, y: touchY })\r\n      \r\n      if (Math.abs(angle) <= 30 && startX - touchX >= 30) {\r\n        this.linkList[index].isMove = true\r\n      }\r\n    },\r\n    \r\n    // 计算角度\r\n    angle(start, end) {\r\n      const x = end.x - start.x\r\n      const y = end.y - start.y\r\n      return Math.atan(y / x) * 360 / Math.PI\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contactContent {\r\n  background: #f3f4f6;\r\n  font-family: PingFang SC;\r\n  height: 100vh;\r\n  overflow: scroll;\r\n  width: 100%;\r\n}\r\n\r\n.content {\r\n  height: auto;\r\n  width: 100%;\r\n}\r\n\r\n.contactBox {\r\n  background: #f3f4f6;\r\n  box-sizing: border-box;\r\n  height: auto;\r\n  padding: 29rpx 28rpx;\r\n  width: 100%;\r\n}\r\n\r\n.addContact {\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  color: #5cb7ff;\r\n  font-size: 35rpx;\r\n  font-weight: 600;\r\n  height: 87rpx;\r\n  line-height: 87rpx;\r\n  margin-bottom: 28rpx;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.add {\r\n  font-size: 42rpx;\r\n  margin-right: 10rpx;\r\n}\r\n\r\n.contactList {\r\n  height: auto;\r\n  overflow: hidden;\r\n  width: 100%;\r\n}\r\n\r\n.contactItem {\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  height: 158rpx;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n  padding: 18rpx 28rpx;\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.contactItem.isMove .left {\r\n  transform: translate(-40%);\r\n}\r\n\r\n.contactItem.isMove .handlePlace {\r\n  width: 40%;\r\n}\r\n\r\n.left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  justify-content: center;\r\n  transition: all 0.3s;\r\n  width: auto;\r\n}\r\n\r\n.peopleName {\r\n  color: #000;\r\n  font-size: 29rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.peopleCard,\r\n.peopleMablie {\r\n  color: #888;\r\n  font-size: 23rpx;\r\n}\r\n\r\n.peopleMablie text:first-child {\r\n  display: inline-block;\r\n  margin-right: 96rpx;\r\n}\r\n\r\n.handlePlace {\r\n  color: #fff;\r\n  display: flex;\r\n  font-size: 28rpx;\r\n  height: 100%;\r\n  line-height: 158rpx;\r\n  position: absolute;\r\n  right: 0;\r\n  text-align: center;\r\n  top: 0;\r\n  transition: all 0.3s;\r\n  width: 0;\r\n  z-index: 99;\r\n}\r\n\r\n.edit {\r\n  background-color: #5cb7ff;\r\n  height: 100%;\r\n  width: 50%;\r\n}\r\n\r\n.del {\r\n  background-color: #fc5531;\r\n  height: 100%;\r\n  width: 50%;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/contactmanager/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["config", "myRequest", "uni", "res"], "mappings": ";;;;AAsDA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAE;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,SAAS;AACP,aAAOA,OAAK;AAAA,IACd;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,YAAY;AAAA,EAClB;AAAA,EACD,OAAO,SAAS;AACd,SAAK,SAAS,QAAQ,OAAO;AAAA,EAC9B;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AACZC,wBAAU;AAAA,QACR,KAAK;AAAA,OACN,EAAE,KAAK,SAAO;AACb,YAAI,IAAI,QAAQ,IAAI,KAAK,MAAM;AAC7B,eAAK,WAAW,IAAI,KAAK,KAAK,IAAI,WAAS;AAAA,YACzC,GAAG;AAAA,YACH,QAAQ;AAAA,UACV,EAAE;AAAA,QACJ;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,4CAAA,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,YAAM,cAAc,KAAK,SAAS,KAAK,EAAE;AACzC,YAAM,SAAS,YAAY;AAE3B,UAAI,SAAS;AAAG,eAAO;AAEvB,UAAI,SAAS,KAAK,WAAW,IAAI;AAC/B,YAAI,SAAS,YAAY,UAAU,GAAG,CAAC;AACvC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAI,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5B,sBAAU;AAAA,UACZ;AAAA,QACF;AACA,kBAAU,YAAY,UAAU,SAAS,GAAG,MAAM;AAClD,eAAO;AAAA,MACT;AAEA,aAAO,YAAY,QAAQ,YAAY,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,IACnE;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,IAAI;AACbA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACfD,8BAAU;AAAA,cACR,KAAK,iBAAiB,EAAE;AAAA,cACxB,QAAQ;AAAA,aACT,EAAE,KAAK,CAAAE,SAAO;AACbD,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,eACP;AACD,mBAAK,YAAY;AAAA,YACnB,CAAC,EAAE,MAAM,SAAO;AACdA,4BAAAA,MAAc,MAAA,SAAA,6CAAA,YAAY,GAAG;AAC7BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,eACP;AAAA,aACF;AAAA,UACH;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,SAAS,IAAI;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,EAAE;AAAA,OAC7C;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,GAAG;AACZ,WAAK,SAAS,QAAQ,UAAQ;AAC5B,YAAI,KAAK,QAAQ;AACf,eAAK,SAAS;AAAA,QAChB;AAAA,OACD;AACD,WAAK,SAAS,EAAE,eAAe,CAAC,EAAE;AAClC,WAAK,SAAS,EAAE,eAAe,CAAC,EAAE;AAAA,IACnC;AAAA;AAAA,IAGD,UAAU,GAAG,OAAO;AAClB,YAAM,SAAS,KAAK;AACpB,YAAM,SAAS,KAAK;AACpB,YAAM,SAAS,EAAE,eAAe,CAAC,EAAE;AACnC,YAAM,SAAS,EAAE,eAAe,CAAC,EAAE;AACnC,YAAM,QAAQ,KAAK,MAAM,EAAE,GAAG,QAAQ,GAAG,OAAK,GAAK,EAAE,GAAG,QAAQ,GAAG,OAAK,CAAG;AAE3E,UAAI,KAAK,IAAI,KAAK,KAAK,MAAM,SAAS,UAAU,IAAI;AAClD,aAAK,SAAS,KAAK,EAAE,SAAS;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,OAAO,KAAK;AAChB,YAAM,IAAI,IAAI,IAAI,MAAM;AACxB,YAAM,IAAI,IAAI,IAAI,MAAM;AACxB,aAAO,KAAK,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK;AAAA,IACvC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9LA,GAAG,WAAW,eAAe;"}