server {
    listen 8081;
    server_name localhost; # 或者换成你的域名或公网 IP
    root /var/www/baoanphp/php/public;

    index index.php index.html;

    #PHP-INFO-START  PHP引用配置，可以注释或修改
    location ~ [^/]\.php(/|$) {
        try_files $uri =404;
        fastcgi_pass 127.0.0.1:9000; # ✅ 用宿主机 IP 访问容器
        fastcgi_index index.php;

        fastcgi_param SCRIPT_FILENAME    $document_root$fastcgi_script_name;
        fastcgi_param QUERY_STRING       $query_string;
        fastcgi_param REQUEST_METHOD     $request_method;
        fastcgi_param CONTENT_TYPE       $content_type;
        fastcgi_param CONTENT_LENGTH     $content_length;

        fastcgi_param SCRIPT_NAME        $fastcgi_script_name;
        fastcgi_param REQUEST_URI        $request_uri;
        fastcgi_param DOCUMENT_URI       $document_uri;
        fastcgi_param DOCUMENT_ROOT      $document_root;
        fastcgi_param SERVER_PROTOCOL    $server_protocol;
        fastcgi_param REQUEST_SCHEME     $scheme;
        fastcgi_param HTTPS              $https if_not_empty;

        fastcgi_param GATEWAY_INTERFACE  CGI/1.1;
        fastcgi_param SERVER_SOFTWARE    nginx/$nginx_version;

        fastcgi_param REMOTE_ADDR        $remote_addr;
        fastcgi_param REMOTE_PORT        $remote_port;
        fastcgi_param SERVER_ADDR        $server_addr;
        fastcgi_param SERVER_PORT        $server_port;
        fastcgi_param SERVER_NAME        $server_name;

        # PHP only, required if PHP was built with --enable-force-cgi-redirect
        fastcgi_param REDIRECT_STATUS    200;
        fastcgi_param HTTP_HOST          $host;

        set $real_script_name $fastcgi_script_name;
        if ($fastcgi_script_name ~ "^(.+?\.php)(/.+)$") {
            set $real_script_name $1;
            set $path_info $2;
        }
        fastcgi_param SCRIPT_FILENAME $document_root$real_script_name;
        fastcgi_param SCRIPT_NAME     $real_script_name;
        fastcgi_param PATH_INFO       $path_info;
    }
    #PHP-INFO-END

    #REWRITE-START URL重写规则引用,修改后将导致面板设置的伪静态规则失效
    location ~* (runtime|application)/{
        return 403;
    }
    location / {
        if (!-e $request_filename){
            rewrite  ^(.*)$  /index.php?s=$1  last;   break;
        }
    }
    #REWRITE-END

    #禁止访问的文件或目录
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md)
    {
        return 404;
    }

    #禁止在证书验证目录放入敏感文件
    if ( $uri ~ "^/\.well-known/.*\.(php|jsp|py|js|css|lua|ts|go|zip|tar\.gz|rar|7z|sql|bak)$" ) {
        return 403;
    }

    location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$
    {
        expires      30d;
        error_log /dev/null;
        access_log /dev/null;
    }

    location ~ .*\.(js|css)?$
    {
        expires      12h;
        error_log /dev/null;
        access_log /dev/null;
    }
    access_log  /dev/null;
    error_log  /www/wwwlogs/www.j8j0.com.error.log;
}
