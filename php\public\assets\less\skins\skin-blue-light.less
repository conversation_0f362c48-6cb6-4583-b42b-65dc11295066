/*
 * Skin: Blue
 * ----------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

@light-blue: #4e73df;

.skin-blue-light {
    //Navbar
    .main-header {
        .navbar {
            .navbar-variant(@light-blue; #fff; #f6f6f6; rgba(0, 0, 0, 0.05));

            .sidebar-toggle {
                color: #fff;

                &:hover {
                    background-color: darken(@light-blue, 5%);
                }
            }

            @media (max-width: @screen-header-collapse) {
                .dropdown-menu {
                    li {
                        &.divider {
                            background-color: rgba(255, 255, 255, 0.1);
                        }

                        a {
                            color: #fff;

                            &:hover {
                                background: darken(@light-blue, 5%);
                            }
                        }
                    }
                }
            }
        }

        //Logo
        .logo {
            .logo-variant(@light-blue);
        }

        li.user-header {
            background-color: @light-blue;
        }
    }

    //Content Header
    .content-header {
        background: transparent;
    }

    //Create the sidebar skin
    .skin-light-sidebar(@light-blue);

    .sidebar-menu > li {
        > a {
            border-left: 3px solid transparent;
            padding-left: 12px;
        }
    }

    @media (min-width: @screen-sm) {
        &.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
            margin-left: -3px;
        }
    }

    .main-footer {
        border-top-color: @gray;
    }

    .main-sidebar {
        .box-shadow(7px 0 14px rgba(0, 0, 0, .03));
    }

    .content-wrapper, .main-footer {
        border-left: none;
    }

    &.multiplenav {
        @media (max-width: @screen-header-collapse) {
            .sidebar .mobilenav a.btn-app {
                background: #eceff3;
                color: #757575;

                &.active {
                    background: @light-blue;
                    color: #fff;
                }
            }
        }
    }
}
