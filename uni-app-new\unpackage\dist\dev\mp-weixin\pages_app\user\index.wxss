/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user {
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAwwAAAIoCAYAAADENWzvAAAAAXNSR0IArs4c6QAAaR1JREFUeNrsveuOrDrToBkBVNV63+6eVrc0tzAXMvd/KaPRSF/3t1dlJsT8IA9A4gPGkByeR9o7VyWJMeGwHQHhsP5f/7eZAAAAwOnRUkQ3WjeMFYDPUSECAACAs3sKIlps11m4VxEAPuYw0AMBAADO7S8UIoo9AAAOCkQAAACAswAA4KJijAAAADgpOAsAEOMwEJIEAABwQu7rFgAAQjBUAAAAnNFfwAIAgEjIkgQAAHA2Z6EU0g7BeSFHLw4DAAAAeMBZgNN7zIhgusOA0AAAAE5jKLHIGQCmOwwAAABwCmehYN0CAKQ4DDxoAAAAOL6zoCVigAwQ/39Oh4GQJAAAgIP7CwVh29jA+ZxPOKPDAAAAAIc28Fi3gA0MMAeiGQEAAI480TPTA8BMCEkCAAA4KOy3sFGIgYLdOQwAAABwPGehIBRpu42DCGBf8KISAADggAapMsMDQCZIqwoAAHAwClKoAkBOh4HXYgAAAMdBCyHkBQCywgtLAACAw3gLhCIBAA4DAAAAuCZ1QpEAYAEISQIAADgAZEQCgOUcBgAAANi/wxC7doE9AABgqsPAAwkAAICdOwvlhDcMTPyrgn8Gh3AYGDgAAAB27CwULHTedPsgAjgADDEAAAA7dxgAAHAYAAAAAGcBAD4Ci54BAAB26S20axcAAJxkWkTDGgYAAIA9+gs4CwAQHCjyFMPLTAAAgB0aAey7AABrQVpVAACAncGOzgCwqsNASBIAAMCOUCFXJ8BasJHG3WEAAACA3cDbBYCVHXRgDQMAAMBubBdmbQD4AIQkAQAA7MVh4O0CAHzEYQAAAIDtOwsF0RHQh/B6wGEAAACAnsMA0NMJRABrOQwoGwAAwMYNw1KkYMIGgE85DLinAAAA26bg7QIAfNRhAAAAgM2ihRB7clZYpAA4DAAAABCCtwtn9hYRAWzFYUAZAQAAtmkv8nYBADYAzy0AAAC2OkkzSwMADgMAAACMQRpVANgKpFUFAADYIEUhokzSALAFh4HYSAAAgI2hOAtZna+vRlS3nXKouRZiRqPDVh0GAAAA2JaBSzhSVr7/+0WKstl0Hf/5f/6I1DgMgMMAAAAAIRSHIbtI9S7YbTd72/Y/taia1H8/a6KxBQT0HQacWQAAgM1QlEIq1cUs8o3XT0W0MPn+P67S/Psml//4FrsViAs+Py4hAgAAgA1NzMzMICLFl8mf//ErX//1IqI87wccBgAAAMBZgCEqUv27lqJqkAV8lIosDAAAABuxDwlHWszw3kUd1VHX5zGTojRpbniWsC5oHAAAwEYMRh7i4dSMH2sPamHy8z9+5eu/XUQLwpQAhwEAAOBcE3KJDJbFNv459Bs6x61/H9WfWv78z79Sftc0K+AwAAAAnALeLqwj5F18jtXXOv/W5z+LL9Y2wDqQVhUAAODTpiyP75al84S+uRby+//9JBXzr//zn97ff//fP2IJm619/ZerVP++Zb3F7/92kev//hJrMOxgCYcBAAAAPkpRCIudV/XQdlaWBo6JSPmnlvK7lut/fsntL+bdpx1THAYAAADIZyMSjrSSg2DyCO+ZJ+5OOZqnHBF9laUj13n4DDo8b7DmoVD5+q8Xqf51a9+iGIq1W0d0aw4DgxQAAMDnYGfntRwzbU1t1XR5q4iKPkz8dCNRO/W5l9cry3Od7nm9JQ3d7ysTLUSMJQ6Qy2FABAAAAJ80ZPEXFpdx59Nslr8w+pmjHNe/uzriO8/3/dYgISwOAwAAAExxFvAW1rVSNUMZsnBZ5nAx1GNt56zbSg4c7MlhoNUAAAA+YzgRjrSehfqwuatGfv7n3yyW7vd//00sx8azqLp2e3YdG3vtMHYMYLbDAAAAAJ+xYzHoVsReMtc8QTFZd1vuLWSwmB/6vyfuB3AYAAAA9u8s4DCsKvH919V894EyAQ4DAADAscxX9l6Aob3vfPth7hAjdAjWcBh4ugEAALA+RYGtB127/54U1Ty+xCDOyLufmxqvsCCfw4AIAAAAVrcOseXWxDr/MJXmlib84qvpm/g3FUtYK6Blu0/C6EKDkc3Zxm9GfTcKgMMAAACwZ4oCGaztoLUfKk1dyPU/fpKK+fM//+mZ5tf/9S3WTG/M6l9XKf91e23K9lbd4aZt2jvmcg2Ud1awmMOAbgEAAKxrv7J+4SNOg83ci+FtzXFq6lLt1GdktzV7fI4cN09aVVd5APMdBgAAAFjXdsWQW9tXeD6Snx20Y28lzyvHBmUNI5GG17OJ5QHgMAAAAOAsQNg21yzx/Za5VpHHNOY81i/Akg4DAxcAAMBqFCVOwyecNL0Lfc7+Fzo8MbUs9ZQ1dkwcxzrXz1a3nTuGsJDDwJgFAACwrvF6yId1e7LWinw7PSeVpFPliCk8V6ww02FABAAAAOtZNFoc99524StUjfz8979Zyvr+b7955efbnG3qxm0seoac/QYRAAAArGRTY8ABwA5hDQMAAMBaDgOP6T5IdzO0LX766jv1e4w7yAtDFwAAAA7DGaS/8c9QfVO/B8BhAAAA2J3JChDrHrTfqddFQNdgaQhJAgAAWMMgVDYwbAEzleZSJp1b/tx6f9eXKimBUVE1omXzZuGrZ2Hz6DGfTqFvkNNhQJcAAACWp+Cd/fd) no-repeat top #f3f4f6;
  background-size: 100% auto;
  font-family: PingFang SC;
  height: auto;
  min-height: 100vh;
  width: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user .user_box.data-v-ae029d5f {
  box-sizing: border-box;
  height: 100%;
  padding: 20rpx 28rpx 0;
}
.user .user_box .user_info.data-v-ae029d5f {
  align-items: center;
  color: #fff;
  display: flex;
  padding-left: 26rpx;
  position: relative;
}
.user .user_box .user_info .user_info_avatar.data-v-ae029d5f {
  height: 120rpx;
  width: 120rpx;
}
.user .user_box .user_info .user_info_avatar image.data-v-ae029d5f {
  border: 5rpx solid #fff;
  border-radius: 50%;
  height: 100%;
  width: 100%;
}
.user .user_box .user_info .user_info_text.data-v-ae029d5f {
  display: flex;
  flex-direction: column;
  height: 120rpx;
  justify-content: space-around;
  margin-left: 30rpx;
}
.user .user_box .user_info .user_info_text .user_info_name.data-v-ae029d5f {
  font-size: 15px;
  font-weight: 600;
}
.user .user_box .user_info .user_info_text .user_info_phone.data-v-ae029d5f {
  align-items: center;
  background-color: #fff;
  border-radius: 38rpx;
  display: flex;
  height: 44rpx;
  justify-content: center;
  width: 220rpx;
}
.user .user_box .user_info .user_info_text .user_info_phone image.data-v-ae029d5f {
  height: 20rpx;
  width: 12rpx;
}
.user .user_box .user_info .user_info_text .user_info_phone text.data-v-ae029d5f {
  color: #888;
  font-family: PingFang SC;
  font-size: 24rpx;
  margin-left: 10rpx;
}
.user .user_box .user_info .user_info_out.data-v-ae029d5f {
  background: #07c160;
  border-radius: 8rpx;
  bottom: 0;
  height: 50rpx;
  line-height: 50rpx;
  position: absolute;
  right: 0;
  text-align: center;
  width: 160rpx;
}
.user .user_box .contacts_btn.data-v-ae029d5f {
  align-items: center;
  background-color: #fff;
  border-radius: 10rpx;
  display: flex;
  height: 86rpx;
  margin-top: 38rpx;
  position: relative;
  width: 100%;
}
.user .user_box .contacts_btn .contacts_image.data-v-ae029d5f {
  height: 44rpx;
  margin-left: 28rpx;
  width: 44rpx;
}
.user .user_box .contacts_btn text.data-v-ae029d5f {
  color: #000;
  font-family: PingFang SC;
  font-size: 27rpx;
  font-weight: 600;
  margin-left: 26rpx;
}
.user .user_box .contacts_btn .forward_icon.data-v-ae029d5f {
  height: 24rpx;
  position: absolute;
  right: 32rpx;
  width: 12rpx;
}
.user .user_box .my_order.data-v-ae029d5f {
  background-color: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  height: 250rpx;
  margin-top: 20rpx;
  padding: 20rpx;
  width: 100%;
}
.user .user_box .my_order .my_order_title.data-v-ae029d5f {
  color: #000;
  font-family: PingFang SC;
  font-size: 34rpx;
  font-weight: 600;
}
.user .user_box .my_order .my_order_btns.data-v-ae029d5f {
  display: flex;
  height: 120rpx;
  justify-content: space-between;
  margin-top: 28rpx;
  padding: 0 20rpx;
}
.user .user_box .my_order .my_order_btns .my_btn.data-v-ae029d5f {
  position: relative;
}
.user .user_box .my_order .my_order_btns .my_btn .btn_icon_box.data-v-ae029d5f {
  align-items: center;
  border-radius: 50%;
  display: flex;
  height: 76rpx;
  justify-content: center;
  margin: 0 auto 5rpx;
  position: relative;
  width: 76rpx;
}
.user .user_box .my_order .my_order_btns .my_btn .btn_icon_box image.data-v-ae029d5f {
  height: 100%;
  width: 100%;
}
.user .user_box .my_order .my_order_btns .my_btn .btn_icon_box .badge.data-v-ae029d5f {
  background: #d51d1d;
  border-radius: 50%;
  box-sizing: border-box;
  color: #fff;
  font-size: 27rpx;
  left: 58rpx;
  padding: 0 12rpx;
  position: absolute;
  top: -4rpx;
}
.user .user_box .my_order .my_order_btns .my_btn .btn_icon_box .badge.overflowOneLength.data-v-ae029d5f {
  border-radius: 19rpx;
}
.user .user_box .my_order .my_order_btns .my_btn text.data-v-ae029d5f {
  color: #000;
  font-family: PingFang SC;
  font-size: 26rpx;
  font-weight: 600;
}
.user_box.data-v-ae029d5f {
  padding-bottom: env(safe-area-inset-bottom);
}