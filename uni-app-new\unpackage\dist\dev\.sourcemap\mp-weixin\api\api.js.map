{"version": 3, "file": "api.js", "sources": ["api/api.js"], "sourcesContent": ["import { signatureGenerate } from '../utils/signatureUtil.js'\r\nimport config from '../config.js'\r\nimport { request, baseUrl } from '../utils/request.js'\r\nimport { getToken } from '../utils/auth.js'\r\n\r\nconst appId = config.appId\r\n\r\n// 处理图片URL\r\nexport function getImages(imagePath) {\r\n  if (!imagePath || imagePath === '') {\r\n    return ''\r\n  }\r\n  \r\n  if (imagePath.startsWith('http://') || \r\n      imagePath.startsWith('https://') || \r\n      imagePath.startsWith('data:image/jpeg;base64,')) {\r\n    return imagePath\r\n  }\r\n  \r\n  return baseUrl + imagePath\r\n}\r\n\r\n// 兼容旧版本的请求方法\r\nexport function myRequest(options) {\r\n  const token = getToken()\r\n  \r\n  // 显示加载提示\r\n  if (!options.noLoadingFlag) {\r\n    uni.showLoading({\r\n      title: '正在加载中...'\r\n    })\r\n  }\r\n  \r\n  return new Promise((resolve, reject) => {\r\n    const requestOptions = {\r\n      url: options.url,\r\n      method: options.method || 'GET',\r\n      data: options.data || {},\r\n      timeout: options.timeout || 10000\r\n    }\r\n    \r\n    // 处理请求头\r\n    if (!options.url.includes(`/wx/user/${appId}/login`)) {\r\n      const { signature, timestamp } = signatureGenerate(requestOptions)\r\n      requestOptions.headers = {\r\n        Authorization: 'wx ' + token,\r\n        sign: signature,\r\n        timestamp: timestamp\r\n      }\r\n    }\r\n    \r\n    // 使用新的request方法\r\n    request(requestOptions)\r\n      .then(response => {\r\n        resolve(response)\r\n      })\r\n      .catch(error => {\r\n        console.error('请求失败:', error)\r\n        uni.showToast({\r\n          title: '请求接口失败！',\r\n          icon: 'error'\r\n        })\r\n        reject(error)\r\n      })\r\n      .finally(() => {\r\n        uni.hideLoading()\r\n      })\r\n  })\r\n}\r\n\r\n// 预约相关API\r\nexport function getVenueList() {\r\n  return request({\r\n    url: '/apitp/venue/list',\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\nexport function createReservation(reservationData) {\r\n  return request({\r\n    url: '/apitp/reservation/create',\r\n    method: 'POST',\r\n    data: reservationData\r\n  })\r\n}\r\n\r\nexport function getReservationList(params) {\r\n  return request({\r\n    url: '/apitp/reservation/list',\r\n    method: 'GET',\r\n    params\r\n  })\r\n}\r\n\r\nexport function cancelReservation(reservationId) {\r\n  return request({\r\n    url: `/apitp/reservation/cancel/${reservationId}`,\r\n    method: 'PUT'\r\n  })\r\n}\r\n\r\n// 公告相关API\r\nexport function getAnnouncementInfo() {\r\n  return request({\r\n    url: '/apitp/announcement/getInfo',\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 电影相关API\r\nexport function getMovieList(params) {\r\n  return request({\r\n    url: '/apitp/movie/list',\r\n    method: 'GET',\r\n    params\r\n  })\r\n}\r\n\r\nexport function getMovieDetail(movieId) {\r\n  return request({\r\n    url: `/apitp/movie/detail/${movieId}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 课程相关API\r\nexport function getCourseList(params) {\r\n  return request({\r\n    url: '/apitp/course/list',\r\n    method: 'GET',\r\n    params\r\n  })\r\n}\r\n\r\nexport function getCourseDetail(courseId) {\r\n  return request({\r\n    url: `/apitp/course/detail/${courseId}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 联系人相关API\r\nexport function getContactList() {\r\n  return myRequest({\r\n    url: '/auth/linkman/list'\r\n  })\r\n}\r\n\r\nexport function getContactDetail(contactId) {\r\n  return myRequest({\r\n    url: `/auth/linkman/${contactId}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\nexport function addContact(contactData) {\r\n  return myRequest({\r\n    url: '/auth/linkman/add',\r\n    method: 'POST',\r\n    data: contactData\r\n  })\r\n}\r\n\r\nexport function updateContact(contactData) {\r\n  return myRequest({\r\n    url: '/auth/linkman/edit',\r\n    method: 'PUT',\r\n    data: contactData\r\n  })\r\n}\r\n\r\nexport function deleteContact(contactId) {\r\n  return myRequest({\r\n    url: `/auth/linkman/${contactId}`,\r\n    method: 'DELETE'\r\n  })\r\n}\r\n\r\n// 二维码相关API\r\nexport function generateQRCode(reservationId) {\r\n  return request({\r\n    url: `/apitp/qrcode/generate/${reservationId}`,\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 用户相关API\r\nexport function updateUserInfo(userInfo) {\r\n  return request({\r\n    url: '/apitp/user/update',\r\n    method: 'PUT',\r\n    data: userInfo\r\n  })\r\n}\r\n\r\nexport function getUserReservations(params) {\r\n  return request({\r\n    url: '/apitp/user/reservations',\r\n    method: 'GET',\r\n    params\r\n  })\r\n}"], "names": ["config", "baseUrl", "getToken", "uni", "signatureGenerate", "request"], "mappings": ";;;;;;AAKA,MAAM,QAAQA,OAAM,OAAC;AAGd,SAAS,UAAU,WAAW;AACnC,MAAI,CAAC,aAAa,cAAc,IAAI;AAClC,WAAO;AAAA,EACR;AAED,MAAI,UAAU,WAAW,SAAS,KAC9B,UAAU,WAAW,UAAU,KAC/B,UAAU,WAAW,yBAAyB,GAAG;AACnD,WAAO;AAAA,EACR;AAED,SAAOC,cAAO,UAAG;AACnB;AAGO,SAAS,UAAU,SAAS;AACjC,QAAM,QAAQC,WAAAA,SAAU;AAGxB,MAAI,CAAC,QAAQ,eAAe;AAC1BC,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,IACb,CAAK;AAAA,EACF;AAED,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,iBAAiB;AAAA,MACrB,KAAK,QAAQ;AAAA,MACb,QAAQ,QAAQ,UAAU;AAAA,MAC1B,MAAM,QAAQ,QAAQ,CAAE;AAAA,MACxB,SAAS,QAAQ,WAAW;AAAA,IAC7B;AAGD,QAAI,CAAC,QAAQ,IAAI,SAAS,YAAY,KAAK,QAAQ,GAAG;AACpD,YAAM,EAAE,WAAW,cAAcC,oBAAAA,kBAAkB,cAAc;AACjE,qBAAe,UAAU;AAAA,QACvB,eAAe,QAAQ;AAAA,QACvB,MAAM;AAAA,QACN;AAAA,MACD;AAAA,IACF;AAGDC,kBAAAA,QAAQ,cAAc,EACnB,KAAK,cAAY;AAChB,cAAQ,QAAQ;AAAA,IACxB,CAAO,EACA,MAAM,WAAS;AACdF,oBAAAA,MAAc,MAAA,SAAA,oBAAA,SAAS,KAAK;AAC5BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAChB,CAAS;AACD,aAAO,KAAK;AAAA,IACpB,CAAO,EACA,QAAQ,MAAM;AACbA,oBAAAA,MAAI,YAAa;AAAA,IACzB,CAAO;AAAA,EACP,CAAG;AACH;;;"}