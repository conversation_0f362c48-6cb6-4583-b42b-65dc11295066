<template>
  <view class="addLink">
    <!-- 头部组件 -->
    <my-header 
      v-if="!config.iSzgm"
      :isBack="true"
      :isShowHome="false"
      :title="linkmanID ? '编辑人员' : '新增人员'"
      :color="'#000'"
    />
    
    <view class="addBox">
      <!-- 证件类型选择 -->
      <view class="checkCardType">
        <view class="label">证件类型</view>
        <view class="checkItem-itm">
          <view 
            v-for="(item, index) in cardTypeList" 
            :key="index"
            class="checkItem"
            @tap="checkCardType(item.value)"
          >
            <view class="checkShow" :class="{ isCheck: index === form.linkmanCertificateType }"></view>
            <text class="checkName">{{ item.label }}</text>
          </view>
        </view>
      </view>
      
      <!-- 联系人信息 -->
      <view class="addInfo">
        <view class="infoItem">
          <view class="label">姓名</view>
          <input 
            class="uni-input"
            v-model="form.linkmanName"
            placeholder="与证件名称一致"
            placeholder-style="font-family: 'PingFang SC'"
          />
        </view>
        <view class="infoItem">
          <view class="label">证件号码</view>
          <input 
            class="uni-input"
            v-model="form.linkmanCertificate"
            placeholder="请输入联系人证件号码"
            placeholder-style="font-family: 'PingFang SC'"
          />
        </view>
        <view class="infoItem">
          <view class="label">手机号码</view>
          <input 
            class="uni-input"
            v-model="form.linkmanPhone"
            placeholder="请输入联系人手机号码"
            placeholder-style="font-family: 'PingFang SC'"
          />
        </view>
        <view class="infoItem" v-if="form.linkmanCertificateType === 1 || form.linkmanCertificateType === 3">
          <view class="label">年龄</view>
          <input 
            class="uni-input"
            v-model="form.linkmanAge"
            placeholder="请输入联系人年龄"
            placeholder-style="font-family: 'PingFang SC'"
            type="number"
          />
        </view>
      </view>
      
      <!-- 用户协议 -->
      <view class="user_agree">
        <view class="checkItem" @tap="changeAgree">
          <view class="checkShow" :class="{ isCheck: agree }"></view>
          <text class="checkName">同意</text>
          <text class="user_agreement_btn" @tap.stop="showUserAgreement(true)">《用户服务协议和隐私政策》</text>
        </view>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="sureChoose">
      <view class="upBtn" @tap="submit">提交</view>
    </view>
    
    <!-- 用户协议弹窗 -->
    <view class="mask" v-if="isShowMask">
      <view class="maskContent">
        <view class="noticeTitle">用户服务协议和隐私政策</view>
        <view class="noticeView" v-for="(notice, index) in noticeList" :key="index">
          <view class="title" v-if="notice.title">{{ notice.title }}</view>
          <view class="text" v-for="(text, textIndex) in notice.textList" :key="textIndex">{{ text }}</view>
          <view class="item" v-for="(item, itemIndex) in notice.ulList" :key="itemIndex">{{ item }}</view>
          <view class="foot" v-if="notice.footText">{{ notice.footText }}</view>
        </view>
        <view class="agreeBtn" @tap="showUserAgreement(false)">已阅读</view>
      </view>
    </view>
  </view>
</template>

<script>
import { myRequest } from '../../api/api.js'
import config from '../../config.js'

export default {
  name: 'AddContact',
  data() {
    return {
      form: {
        linkmanAge: '',
        linkmanCertificate: '',
        linkmanCertificateType: 0,
        linkmanName: '',
        linkmanPhone: ''
      },
      cardTypeList: [
        { label: '身份证', value: 0 },
        { label: '港澳台居民通行证/居住证', value: 1 },
        { label: '临时身份证', value: 2 },
        { label: '护照', value: 3 }
      ],
      linkmanID: null,
      agree: false,
      isShowMask: false,
      noticeList: [
        {
          textList: ['我们非常重视对您的个人隐私保护，有时候我们需要某些信息才能为您提供您请求的服务，本隐私声明解释了这些情况下的数据收集和使用情况。']
        },
        {
          title: '关于您的个人信息',
          textList: [
            '我们严格保护您个人信息的安全。我们使用各种安全技术和程序来保护您的个人信息不被未经授权的访问、使用或泄漏。',
            '我们会在法律要求或符合我们的相关服务条款、软件许可使用协议约定的情况下透露您的个人信息，或者有充分理由相信必须这样做才能：'
          ],
          ulList: [
            '满足法律或行政法规的明文规定，或者符合我们APP/小程序适用的法律程序；',
            '符合我们相关服务条款、软件许可使用协议的约定；',
            '在紧急情况下保护服务的用户或大众的个人安全。'
          ],
          footText: '我们不会未经您的允许将这些信息与第三方共享，本声明已经列出的上述情况除外。'
        },
        {
          title: '关于免责说明',
          textList: ['就下列相关事宜的发生，我们不承担任何法律责任：'],
          ulList: [
            '由于您将用户密码告知他人或与他人共享注册帐户，由此导致的任何个人信息的泄漏，或其他非因我们原因导致的个人信息的泄漏；',
            '我们根据法律规定或政府相关政策要求提供您的个人信息；',
            '任何由于黑客攻击、电脑病毒侵入或政府管制而造成的暂时性网站关闭；',
            '因不可抗力导致的任何后果；',
            '我们在各服务条款及声明中列明的使用方式或免责情形。'
          ]
        }
      ]
    }
  },
  computed: {
    config() {
      return config
    }
  },
  onLoad(options) {
    this.linkmanID = options.id || null
    
    if (this.linkmanID) {
      this.getContactDetail()
    }
  },
  methods: {
    // 获取联系人详情
    getContactDetail() {
      myRequest({
        url: `/auth/linkman/${this.linkmanID}`,
        method: 'GET'
      }).then(res => {
        if (res.data && res.data.data) {
          this.form = res.data.data
        }
      }).catch(err => {
        console.error('获取联系人详情失败:', err)
        uni.showToast({
          title: '获取联系人详情失败',
          icon: 'error'
        })
      })
    },
    
    // 选择证件类型
    checkCardType(value) {
      this.form.linkmanCertificateType = value
    },
    
    // 提交表单
    submit() {
      // 验证姓名
      if (!this.form.linkmanName || this.form.linkmanName.trim() === '') {
        uni.showModal({
          title: '提示',
          content: '请输入姓名',
          showCancel: false
        })
        return
      }
      
      // 验证证件号码
      if (!this.form.linkmanCertificate || this.form.linkmanCertificate.trim() === '') {
        uni.showModal({
          title: '提示',
          content: '请输入证件号码',
          showCancel: false
        })
        return
      }
      
      // 验证手机号码
      if (!this.form.linkmanPhone || this.form.linkmanPhone.trim() === '') {
        uni.showModal({
          title: '提示',
          content: '请输入手机号码',
          showCancel: false
        })
        return
      }
      
      // 验证手机号码格式
      if (!/^1[3-9]\d{9}$/.test(this.form.linkmanPhone)) {
        uni.showModal({
          title: '提示',
          content: '请输入正确的手机号码',
          showCancel: false
        })
        return
      }
      
      // 验证姓名格式（护照除外）
      if (this.form.linkmanCertificateType !== 3 && 
          !/^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·\s]{2,20}$/.test(this.form.linkmanName)) {
        uni.showModal({
          title: '提示',
          content: '请输入正确的姓名',
          showCancel: false
        })
        return
      }
      
      // 验证年龄（港澳台居民通行证/居住证或护照需要输入年龄）
      if ((this.form.linkmanCertificateType === 1 || this.form.linkmanCertificateType === 3)) {
        if (!this.form.linkmanAge || !/^\d{1,2}$/.test(this.form.linkmanAge)) {
          uni.showModal({
            title: '提示',
            content: '请输入年龄',
            showCancel: false
          })
          return
        }
        
        const age = parseInt(this.form.linkmanAge)
        if (age < 1 || age > 120) {
          uni.showModal({
            title: '提示',
            content: '请输入正确的年龄（1-120岁）',
            showCancel: false
          })
          return
        }
      }
      
      // 验证协议同意
      if (!this.agree) {
        uni.showModal({
          title: '提示',
          content: '请详细阅读并勾选下方《用户服务协议和隐私政策》',
          showCancel: false
        })
        return
      }
      
      // 提交数据
      const url = this.linkmanID ? '/auth/linkman/edit' : '/auth/linkman/add'
      const method = this.linkmanID ? 'PUT' : 'POST'
      
      myRequest({
        url,
        method,
        data: this.form
      }).then(res => {
        uni.showToast({
          title: this.linkmanID ? '修改成功' : '添加成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            setTimeout(() => {
              uni.navigateBack()
            }, 2000)
          }
        })
      }).catch(err => {
        console.error('提交失败:', err)
        uni.showToast({
          title: '提交失败',
          icon: 'error'
        })
      })
    },
    
    // 切换协议同意状态
    changeAgree() {
      this.agree = !this.agree
    },
    
    // 显示/隐藏用户协议
    showUserAgreement(show) {
      this.isShowMask = show
    }
  }
}
</script>

<style scoped>
.addLink {
  background-color: #f3f4f6;
  font-family: PingFang SC;
  height: auto;
  min-height: 100vh;
  width: 100%;
}

.addBox {
  box-sizing: border-box;
  height: auto;
  padding: 29rpx;
  width: 100%;
}

.label {
  color: #888;
  font-size: 27rpx;
  font-weight: 600;
  margin-right: 29rpx;
  min-width: 110rpx;
}

.checkCardType {
  align-items: center;
  background-color: #fff;
  border-radius: 15rpx;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 29rpx;
  padding: 0 15rpx 0 29rpx;
  width: 100%;
}

.checkItem-itm {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.checkItem {
  align-items: center;
  display: flex;
  margin-left: 20rpx;
  margin-top: 20rpx;
}

.checkShow {
  border: 2rpx solid #888;
  border-radius: 50%;
  font-size: 25rpx;
  font-weight: 700;
  height: 29rpx;
  line-height: 29rpx;
  margin-right: 10rpx;
  text-align: center;
  width: 29rpx;
}

.checkShow.isCheck {
  background: #ffba38;
  border: 2rpx solid transparent;
}

.checkShow.isCheck::before {
  content: "✓";
  display: inline-block;
  color: #fff;
}

.checkName {
  color: #000;
  font-size: 27rpx;
  font-weight: 600;
}

.addInfo {
  background-color: #fff;
  border-radius: 15rpx;
  box-sizing: border-box;
  padding: 0 29rpx;
  width: 100%;
}

.infoItem {
  align-items: center;
  border-bottom: 1rpx solid rgba(136, 136, 136, 0.5);
  display: flex;
  height: 92rpx;
  width: 100%;
}

.infoItem:last-of-type {
  border-bottom: 0;
}

.uni-input {
  color: #000;
  font-family: PingFang SC !important;
  font-size: 27rpx;
  font-weight: 600;
}

.user_agree {
  box-sizing: border-box;
  height: 150rpx;
  padding: 20rpx 29rpx;
  width: 100%;
}

.user_agree .checkItem {
  align-items: center;
  display: flex;
}

.user_agree .checkShow {
  border: 2rpx solid #888;
  border-radius: 50%;
  font-size: 25rpx;
  font-weight: 700;
  height: 29rpx;
  line-height: 29rpx;
  margin-right: 25rpx;
  text-align: center;
  width: 29rpx;
}

.user_agree .checkShow.isCheck {
  background: #ffba38;
  border: 2rpx solid transparent;
}

.user_agree .checkShow.isCheck::before {
  content: "✓";
  display: inline-block;
  color: #fff;
}

.user_agree .checkName {
  color: #000;
  font-size: 27rpx;
  font-weight: 600;
}

.user_agreement_btn {
  color: #5cb7ff;
  font-size: 27rpx;
  font-weight: 600;
}

.sureChoose {
  align-items: center;
  bottom: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 150rpx;
  justify-content: space-evenly;
  left: 0;
  padding: 0 29rpx;
  position: fixed;
  width: 100%;
}

.upBtn {
  background: #5cb7ff;
  border-radius: 10rpx;
  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);
  color: #fff;
  font-size: 35rpx;
  height: 77rpx;
  line-height: 77rpx;
  text-align: center;
  width: 654rpx;
}

.mask {
  background-color: rgba(0, 0, 0, 0.5);
  bottom: 0;
  height: auto;
  overflow: auto;
  padding: 237rpx 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}

.maskContent {
  background: #fff;
  border: 2rpx solid #707070;
  border-radius: 19rpx;
  box-sizing: border-box;
  font-family: PingFang SC;
  height: auto;
  margin: 0 auto;
  padding: 58rpx 58rpx 48rpx;
  width: 654rpx;
}

.noticeTitle {
  color: #1b1b1b;
  font-size: 35rpx;
  font-weight: 700;
  margin-bottom: 30rpx;
  text-align: center;
}

.noticeView {
  height: auto;
  line-height: 50rpx;
  text-align: justify;
  width: 100%;
}

.title {
  font-size: 29rpx;
  font-weight: 700;
}

.text {
  font-size: 27rpx;
  margin: 30rpx 0;
}

.item {
  font-size: 27rpx;
  line-height: 40rpx;
  margin-bottom: 20rpx;
  padding-left: 40rpx;
  position: relative;
}

.item::before {
  background-color: #000;
  border-radius: 50%;
  content: "";
  height: 10rpx;
  left: 0.5em;
  position: absolute;
  top: 0.5em;
  width: 10rpx;
}

.foot {
  font-size: 27rpx;
  margin: 40rpx 0;
}

.agreeBtn {
  background: #5cb7ff;
  border-radius: 10rpx;
  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);
  color: #fff;
  font-size: 35rpx;
  height: 77rpx;
  line-height: 77rpx;
  margin: 0 auto;
  text-align: center;
  width: 381rpx;
}
</style>