{"version": 3, "file": "my-header2.js", "sources": ["components/my-header/my-header.vue"], "sourcesContent": ["<template>\r\n  <view \r\n    class=\"my-header\" \r\n    :style=\"{\r\n      background: background,\r\n      color: color,\r\n      paddingTop: menuButtonInfo.top + 'px',\r\n      height: menuButtonInfo.height + 'px',\r\n      paddingBottom: '30px',\r\n      position: isFixed ? 'fixed' : 'static',\r\n      zIndex: isFixed ? 999 : 3\r\n    }\"\r\n  >\r\n    <view class=\"header_content\">\r\n      <view :class=\"['header_btns', menuClass]\">\r\n        <view \r\n          v-if=\"isBack\" \r\n          class=\"back icon_back\" \r\n          :style=\"{ lineHeight: menuButtonInfo.height + 'px' }\"\r\n          @tap=\"goBack\"\r\n        ></view>\r\n        <view \r\n          v-if=\"isShowHome\" \r\n          class=\"home icon_home\" \r\n          :style=\"{ lineHeight: menuButtonInfo.height + 'px' }\"\r\n          @tap=\"goHome\"\r\n        ></view>\r\n      </view>\r\n      <view class=\"title\">{{ title }}</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: '<PERSON><PERSON>eader',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    background: {\r\n      type: String,\r\n      default: '#ffffff'\r\n    },\r\n    isBack: {\r\n      type: [Boolean, String],\r\n      default: false\r\n    },\r\n    isShowHome: {\r\n      type: [Boolean, String],\r\n      default: false\r\n    },\r\n    isFixed: {\r\n      type: [Boolean, String],\r\n      default: false\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: '#ffffff'\r\n    },\r\n    menuClass: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      menuButtonInfo: {}\r\n    }\r\n  },\r\n  created() {\r\n    // 获取胶囊按钮信息，兼容不同平台\r\n    // #ifdef MP-WEIXIN\r\n    this.menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n    this.menuButtonInfo.top += 16\r\n    // #endif\r\n    \r\n    // #ifndef MP-WEIXIN\r\n    // 非微信小程序平台的默认值\r\n    this.menuButtonInfo = {\r\n      top: 44,\r\n      height: 32\r\n    }\r\n    // #endif\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    goHome() {\r\n      uni.reLaunch({\r\n        url: '/pages/index/index'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.my-header {\r\n  color: #333;\r\n  font-family: PingFang SC;\r\n  font-size: 35rpx;\r\n  width: 100%;\r\n}\r\n\r\n.my-header .header_content {\r\n  height: 100%;\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.my-header .header_content .header_btns {\r\n  align-items: center;\r\n  background-color: transparent;\r\n  border-radius: 100rpx;\r\n  display: flex;\r\n  height: 100%;\r\n  justify-content: flex-start;\r\n  margin-left: 29rpx;\r\n  position: relative;\r\n  text-align: center;\r\n  width: 160rpx;\r\n}\r\n\r\n.my-header .header_content .header_btns .back {\r\n  font-size: 33rpx;\r\n  height: 100%;\r\n  margin-top: -30px;\r\n  position: relative;\r\n  width: 50%;\r\n}\r\n\r\n.my-header .header_content .header_btns .back::after {\r\n  background-color: transparent;\r\n  content: \"\";\r\n  display: inline-block;\r\n  height: 36rpx;\r\n  position: absolute;\r\n  right: 1rpx;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 1rpx;\r\n}\r\n\r\n.my-header .header_content .header_btns .home {\r\n  font-size: 33rpx;\r\n  height: 100%;\r\n  margin-top: -30px;\r\n  width: 50%;\r\n}\r\n\r\n.my-header .header_content .title {\r\n  display: inline-block;\r\n  font-weight: 700;\r\n  left: 50%;\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n/* 图标样式 */\r\n.icon_back::before {\r\n  content: \"‹\";\r\n  font-size: 40rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.icon_home::before {\r\n  content: \"⌂\";\r\n  font-size: 36rpx;\r\n}\r\n</style>"], "names": ["uni"], "mappings": ";;;AAkCA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,QAAQ;AAAA,MACN,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,YAAY;AAAA,MACV,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,SAAS;AAAA,MACP,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACD;AAAA,EACD,UAAU;AAGR,SAAK,iBAAiBA,cAAG,MAAC,gCAAgC;AAC1D,SAAK,eAAe,OAAO;AAAA,EAU5B;AAAA,EACD,SAAS;AAAA,IACP,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA,IACD,SAAS;AACPA,oBAAAA,MAAI,SAAS;AAAA,QACX,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;"}