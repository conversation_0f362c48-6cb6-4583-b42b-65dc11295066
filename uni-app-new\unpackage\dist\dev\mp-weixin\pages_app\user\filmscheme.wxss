/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.film-scheme.data-v-bf630d19 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.film-scheme .scheme-list.data-v-bf630d19 {
  height: calc(100vh - 88rpx);
}
.film-scheme .scheme-list .list-container.data-v-bf630d19 {
  padding: 20rpx;
}
.film-scheme .empty-state.data-v-bf630d19 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}
.film-scheme .empty-state .empty-icon.data-v-bf630d19 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.film-scheme .empty-state .empty-text.data-v-bf630d19 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}
.film-scheme .scheme-item.data-v-bf630d19 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.film-scheme .scheme-item .item-header.data-v-bf630d19 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.film-scheme .scheme-item .item-header .venue-name.data-v-bf630d19 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.film-scheme .scheme-item .item-header .status-badge.data-v-bf630d19 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.film-scheme .scheme-item .item-header .status-badge.status-pending.data-v-bf630d19 {
  background: #e3f2fd;
  color: #1976d2;
}
.film-scheme .scheme-item .item-header .status-badge.status-expired.data-v-bf630d19 {
  background: #fce4ec;
  color: #c2185b;
}
.film-scheme .scheme-item .item-header .status-badge.status-cancelled.data-v-bf630d19 {
  background: #f3e5f5;
  color: #7b1fa2;
}
.film-scheme .scheme-item .item-header .status-badge.status-checkin.data-v-bf630d19 {
  background: #e8f5e8;
  color: #388e3c;
}
.film-scheme .scheme-item .item-header .status-badge.status-completed.data-v-bf630d19 {
  background: #e8f5e8;
  color: #388e3c;
}
.film-scheme .scheme-item .item-header .status-badge.status-unknown.data-v-bf630d19 {
  background: #f5f5f5;
  color: #666;
}
.film-scheme .scheme-item .film-info.data-v-bf630d19 {
  display: flex;
  margin-bottom: 20rpx;
}
.film-scheme .scheme-item .film-info .film-poster.data-v-bf630d19 {
  width: 120rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.film-scheme .scheme-item .film-info .film-poster .poster-image.data-v-bf630d19 {
  width: 100%;
  height: 100%;
}
.film-scheme .scheme-item .film-info .film-details.data-v-bf630d19 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.film-scheme .scheme-item .film-info .film-details .film-name.data-v-bf630d19 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.film-scheme .scheme-item .film-info .film-details .film-type.data-v-bf630d19,
.film-scheme .scheme-item .film-info .film-details .film-time.data-v-bf630d19,
.film-scheme .scheme-item .film-info .film-details .film-date.data-v-bf630d19 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
  line-height: 1.3;
}
.film-scheme .scheme-item .booking-info.data-v-bf630d19 {
  margin-bottom: 20rpx;
}
.film-scheme .scheme-item .booking-info .ticket-count.data-v-bf630d19 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.film-scheme .scheme-item .action-buttons.data-v-bf630d19 {
  display: flex;
  gap: 20rpx;
}
.film-scheme .scheme-item .action-buttons .cancel-btn.data-v-bf630d19,
.film-scheme .scheme-item .action-buttons .qrcode-btn.data-v-bf630d19 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}
.film-scheme .scheme-item .action-buttons .cancel-btn.data-v-bf630d19::after,
.film-scheme .scheme-item .action-buttons .qrcode-btn.data-v-bf630d19::after {
  border: none;
}
.film-scheme .scheme-item .action-buttons .cancel-btn.data-v-bf630d19 {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
}
.film-scheme .scheme-item .action-buttons .qrcode-btn.data-v-bf630d19 {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}
.film-scheme .loading-more.data-v-bf630d19 {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}
.film-scheme .loading-more .loading-text.data-v-bf630d19 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
}
.film-scheme .no-more.data-v-bf630d19 {
  display: flex;
  justify-content: center;
  padding: 40rpx;
}
.film-scheme .no-more .no-more-text.data-v-bf630d19 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.5);
}

/* 平台特定样式 */
.scheme-list.data-v-bf630d19 {
  padding-bottom: env(safe-area-inset-bottom);
}