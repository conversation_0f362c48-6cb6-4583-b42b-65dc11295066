var t = require("../../../../common/vendor.js"),
  e = {
    name: "UniSegmentedControl",
    emits: ["clickItem"],
    props: {
      current: {
        type: Number,
        default: 0
      },
      values: {
        type: Array,
        default: function() {
          return []
        }
      },
      activeColor: {
        type: String,
        default: "#2979FF"
      },
      styleType: {
        type: String,
        default: "button"
      }
    },
    data: function() {
      return {
        currentIndex: 0
      }
    },
    watch: {
      current: function(t) {
        t !== this.currentIndex && (this.currentIndex = t)
      }
    },
    created: function() {
      this.currentIndex = this.current
    },
    methods: {
      _onClick: function(t) {
        this.currentIndex !== t && (this.currentIndex = t, this.$emit("clickItem", {
          currentIndex: t
        }))
      }
    }
  };
var n = t._export_sfc(e, [
  ["render", function(e, n, r, o, c, u) {
    return {
      a: t.f(r.values, (function(e, n, o) {
        return {
          a: t.t(e),
          b: n === c.currentIndex ? "text" === r.styleType ? r.activeColor : "#fff" : "text" === r.styleType ? "#000" : r.activeColor,
          c: t.n("text" === r.styleType && n === c.currentIndex ? "segmented-control__item--text" : ""),
          d: t.n(n === c.currentIndex && "button" === r.styleType ? "segmented-control__item--button--active" : ""),
          e: t.n(0 === n && "button" === r.styleType ? "segmented-control__item--button--first" : ""),
          f: t.n(n === r.values.length - 1 && "button" === r.styleType ? "segmented-control__item--button--last" : ""),
          g: n,
          h: n === c.currentIndex && "button" === r.styleType ? r.activeColor : "",
          i: n !== c.currentIndex || "text" !== r.styleType && "button" !== r.styleType ? "#d9d9d9" : r.activeColor,
          j: t.o((function(t) {
            return u._onClick(n)
          }), n)
        }
      })),
      b: t.n("text" === r.styleType ? "" : "segmented-control__item--button"),
      c: t.n("text" === r.styleType ? "segmented-control--text" : "segmented-control--button"),
      d: "text" === r.styleType ? "" : r.activeColor
    }
  }]
]);
wx.createComponent(n);