<view class="register-container data-v-594d5248"><my-header wx:if="{{a}}" class="data-v-594d5248" u-i="594d5248-0" bind:__l="__l" u-p="{{a}}"/><view class="register-content data-v-594d5248"><view class="logo-section data-v-594d5248"><image class="logo data-v-594d5248" src="{{b}}" mode="aspectFit"/><text class="app-name data-v-594d5248">宝安科技馆</text><text class="subtitle data-v-594d5248">用户注册</text></view><view class="form-section data-v-594d5248"><view class="input-group data-v-594d5248"><view class="input-wrapper data-v-594d5248"><view class="input-icon data-v-594d5248"><text class="icon-user data-v-594d5248"></text></view><input class="form-input data-v-594d5248" placeholder="请输入用户名" type="text" maxlength="20" bindblur="{{c}}" value="{{d}}" bindinput="{{e}}"/></view><text wx:if="{{f}}" class="error-text data-v-594d5248">{{g}}</text></view><view class="input-group data-v-594d5248"><view class="input-wrapper data-v-594d5248"><view class="input-icon data-v-594d5248"><text class="icon-phone data-v-594d5248"></text></view><input class="form-input data-v-594d5248" placeholder="请输入手机号" type="number" maxlength="11" bindblur="{{h}}" value="{{i}}" bindinput="{{j}}"/></view><text wx:if="{{k}}" class="error-text data-v-594d5248">{{l}}</text></view><view class="input-group data-v-594d5248"><view class="input-wrapper verification-wrapper data-v-594d5248"><view class="input-icon data-v-594d5248"><text class="icon-code data-v-594d5248"></text></view><input class="form-input verification-input data-v-594d5248" placeholder="请输入短信验证码" type="number" maxlength="6" value="{{m}}" bindinput="{{n}}"/><button class="{{['verification-btn', 'data-v-594d5248', p && 'disabled']}}" disabled="{{q}}" bindtap="{{r}}">{{o}}</button></view><text wx:if="{{s}}" class="error-text data-v-594d5248">{{t}}</text></view><view class="input-group data-v-594d5248"><view class="input-wrapper data-v-594d5248"><view class="input-icon data-v-594d5248"><text class="icon-password data-v-594d5248"></text></view><input class="form-input data-v-594d5248" placeholder="请输入密码" type="password" maxlength="20" bindblur="{{v}}" value="{{w}}" bindinput="{{x}}"/></view><text wx:if="{{y}}" class="error-text data-v-594d5248">{{z}}</text><text class="password-hint data-v-594d5248">密码至少8位，必须包含字母、数字、特殊符号</text></view><view class="input-group data-v-594d5248"><view class="input-wrapper data-v-594d5248"><view class="input-icon data-v-594d5248"><text class="icon-password data-v-594d5248"></text></view><input class="form-input data-v-594d5248" placeholder="请再次输入密码" type="password" maxlength="20" bindblur="{{A}}" value="{{B}}" bindinput="{{C}}"/></view><text wx:if="{{D}}" class="error-text data-v-594d5248">{{E}}</text></view><view wx:if="{{F}}" class="input-group data-v-594d5248"><view class="input-wrapper captcha-wrapper data-v-594d5248"><view class="input-icon data-v-594d5248"><text class="icon-shield data-v-594d5248"></text></view><input class="form-input captcha-input data-v-594d5248" placeholder="请输入图形验证码" type="text" maxlength="4" value="{{G}}" bindinput="{{H}}"/><view class="captcha-image data-v-594d5248" bindtap="{{K}}"><image wx:if="{{I}}" class="captcha-img data-v-594d5248" src="{{J}}" mode="aspectFit"/><text wx:else class="refresh-text data-v-594d5248">点击刷新</text></view></view><text wx:if="{{L}}" class="error-text data-v-594d5248">{{M}}</text></view><view class="agreement-section data-v-594d5248"><label class="agreement-checkbox data-v-594d5248"><checkbox class="data-v-594d5248" checked="{{N}}" bindchange="{{O}}" color="#667eea"/><text class="agreement-text data-v-594d5248"> 我已阅读并同意 <text class="link-text data-v-594d5248" bindtap="{{P}}">《用户协议》</text> 和 <text class="link-text data-v-594d5248" bindtap="{{Q}}">《隐私政策》</text></text></label></view><view class="button-section data-v-594d5248"><button class="{{['register-button', 'data-v-594d5248', S && 'disabled']}}" disabled="{{T}}" bindtap="{{U}}">{{R}}</button></view><view class="login-link-section data-v-594d5248"><text class="login-link data-v-594d5248" bindtap="{{V}}">已有账号？立即登录</text></view></view></view></view>