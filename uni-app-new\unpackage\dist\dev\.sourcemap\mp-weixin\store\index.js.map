{"version": 3, "file": "index.js", "sources": ["store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\r\nimport user from './modules/user.js'\r\nimport getters from './getters.js'\r\n\r\nconst store = createStore({\r\n  modules: {\r\n    user\r\n  },\r\n  getters,\r\n  // 开启严格模式，在开发环境下检测状态变更\r\n  strict: process.env.NODE_ENV !== 'production'\r\n})\r\n\r\nexport default store"], "names": ["createStore", "user", "getters"], "mappings": ";;;;AAIA,MAAM,QAAQA,cAAAA,YAAY;AAAA,EACxB,SAAS;AAAA,IAAA,MACPC,mBAAA;AAAA,EACF;AAAA,EAAA,SACAC,cAAA;AAAA;AAAA,EAEA,QAAQ;AACV,CAAC;;"}