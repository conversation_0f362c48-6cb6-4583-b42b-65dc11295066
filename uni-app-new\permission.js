// permission.js - 路由权限拦截
import { getToken } from './utils/auth.js'

// 白名单路由 - 无需登录即可访问
const whiteList = [
  '/pages_app/login/index',
  '/pages_app/register/index', 
  '/pages/index/index',
  '/pages_app/entervenue/index',
  '/pages_app/vieworder/index',
  '/pages_app/curriculum/index',
  '/pages_app/user/index'
]

// 路由拦截器
const routerInterceptor = {
  invoke(to) {
    const token = getToken()
    
    if (token) {
      // 已登录用户访问登录页，重定向到首页
      if (to.url === '/pages_app/login/index') {
        uni.reLaunch({
          url: '/pages/index/index'
        })
        return false
      }
      return true
    } else {
      // 未登录用户
      const path = to.url.split('?')[0]
      if (whiteList.includes(path)) {
        return true
      } else {
        // 重定向到首页
        uni.reLaunch({
          url: '/pages/index/index'
        })
        return false
      }
    }
  },
  fail(err) {
    console.error('路由拦截失败:', err)
  }
}

// 注册路由拦截器
const routerMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
routerMethods.forEach(method => {
  uni.addInterceptor(method, routerInterceptor)
})
