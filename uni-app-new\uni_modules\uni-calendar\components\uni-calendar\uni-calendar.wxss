.uni-calendar.data-v-c3423b62 {
    display: flex;
    flex-direction: column
}

.uni-calendar__mask.data-v-c3423b62 {
    background-color: rgba(0,0,0,.4);
    bottom: 0;
    left: 0;
    opacity: 0;
    position: fixed;
    right: 0;
    top: 0;
    transition-duration: .3s;
    transition-property: opacity;
    z-index: 99
}

.uni-calendar--mask-show.data-v-c3423b62 {
    opacity: 1
}

.uni-calendar--fixed.data-v-c3423b62 {
    bottom: calc(var(--window-bottom));
    left: 0;
    position: fixed;
    right: 0;
    transform: translateY(460px);
    transition-duration: .3s;
    transition-property: transform;
    z-index: 99
}

.uni-calendar--ani-show.data-v-c3423b62 {
    transform: translateY(0)
}

.uni-calendar__content.data-v-c3423b62 {
    background-color: #fff
}

.uni-calendar__header.data-v-c3423b62 {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 52rpx;
    justify-content: center;
    position: relative
}

.uni-calendar--fixed-top.data-v-c3423b62 {
    border-top: 1px solid #e5e5e5;
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.uni-calendar--fixed-width.data-v-c3423b62 {
    width: 50px
}

.uni-calendar__backtoday.data-v-c3423b62 {
    background-color: #f1f1f1;
    border-bottom-left-radius: 25px;
    border-top-left-radius: 25px;
    color: #333;
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    padding: 0 5px 0 10px;
    position: absolute;
    right: 0;
    top: 25rpx
}

.uni-calendar__header-text.data-v-c3423b62 {
    color: #000;
    font-family: PingFang SC;
    font-size: 33rpx;
    text-align: center;
    width: 100px
}

.uni-calendar__header-btn-box.data-v-c3423b62 {
    align-items: center;
    display: flex;
    flex-direction: row;
    height: 50px;
    justify-content: center;
    width: 50px
}

.uni-calendar__header-btn.data-v-c3423b62 {
    border-left: 2px solid gray;
    border-top: 2px solid #555;
    height: 10px;
    width: 10px
}

.uni-calendar--left.data-v-c3423b62 {
    transform: rotate(-45deg)
}

.uni-calendar--right.data-v-c3423b62 {
    transform: rotate(135deg)
}

.uni-calendar__weeks.data-v-c3423b62 {
    display: flex;
    flex-direction: row;
    position: relative
}

.uni-calendar__weeks-item.data-v-c3423b62 {
    flex: 1
}

.uni-calendar__weeks-day.data-v-c3423b62 {
    align-items: center;
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 45px;
    justify-content: center
}

.uni-calendar__weeks-day-text.data-v-c3423b62 {
    color: #000;
    font-size: 21rpx
}

.uni-calendar__box.data-v-c3423b62 {
    position: relative
}

.uni-calendar__box-bg.data-v-c3423b62 {
    align-items: center;
    bottom: 0;
    display: flex;
    justify-content: center;
    left: 0;
    position: absolute;
    right: 0;
    top: 0
}

.uni-calendar__box-bg-text.data-v-c3423b62 {
    color: #999;
    font-size: 200px;
    font-weight: 700;
    line-height: 1;
    opacity: .1;
    text-align: center
}
