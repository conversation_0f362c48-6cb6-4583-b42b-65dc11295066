{"version": 3, "file": "index.js", "sources": ["pages_app/contacts/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGNvbnRhY3RzXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"contactContent\">\r\n    <!-- 头部组件 -->\r\n    <my-header \r\n      v-if=\"!config.iSzgm\"\r\n      :menuClass=\"'bor'\"\r\n      :isFixed=\"true\"\r\n      :isBack=\"true\"\r\n      :isShowHome=\"true\"\r\n      :title=\"'联系人'\"\r\n      :color=\"'#000'\"\r\n    />\r\n    \r\n    <view class=\"contactBox\">\r\n      <!-- 添加联系人按钮 -->\r\n      <view class=\"addContact\" @tap=\"addContact\">\r\n        <text class=\"add\">+</text>添加联系人\r\n      </view>\r\n      \r\n      <!-- 联系人列表 -->\r\n      <view class=\"contactList\">\r\n        <view \r\n          v-for=\"(link, index) in linkList\" \r\n          :key=\"link.id\"\r\n          class=\"contactItem\"\r\n          :class=\"{ isMove: link.isMove }\"\r\n          @touchstart=\"touchstart\"\r\n          @touchmove=\"touchmove($event, index)\"\r\n          @tap=\"chooseLink(index)\"\r\n          :data-index=\"index\"\r\n        >\r\n          <view class=\"left\">\r\n            <view class=\"peopleName\">{{ link.linkmanName }}</view>\r\n            <view class=\"peopleCard\">证件号 {{ hideCardNum(index) }}</view>\r\n            <view class=\"peopleMablie\">\r\n              <text>手机号码 {{ link.linkmanPhone }}</text>\r\n              <text>年龄{{ link.linkmanAge }}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"right\">\r\n            <view class=\"checkBtn\" :class=\"{ isCheck: link.isCheck }\"></view>\r\n          </view>\r\n          <view class=\"handlePlace\" :class=\"{ isMove: link.isMove }\">\r\n            <view class=\"edit\" @tap.stop=\"editItem(link.id)\">编辑</view>\r\n            <view class=\"del\" @tap.stop=\"deleteItem(index)\">删除</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 底部确认选择 -->\r\n    <view class=\"sureChoose\">\r\n      <view class=\"showNum\">\r\n        选择 <text class=\"setNum\">{{ checkAllNum }}</text> 人\r\n      </view>\r\n      <view class=\"upBtn\" @tap=\"sureNum\">确定选择</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { myRequest } from '../../api/api.js'\r\nimport config from '../../config.js'\r\n\r\nexport default {\r\n  name: 'ContactList',\r\n  data() {\r\n    return {\r\n      age: '0',\r\n      maxNum: 0,\r\n      linkList: [],\r\n      checkAllNum: 0,\r\n      startX: '',\r\n      startY: '',\r\n      type: ''\r\n    }\r\n  },\r\n  computed: {\r\n    config() {\r\n      return config\r\n    }\r\n  },\r\n  onShow() {\r\n    this.getLinkList()\r\n  },\r\n  onLoad(options) {\r\n    this.maxNum = options.num || 0\r\n    this.type = options.type || ''\r\n    this.age = options.age || ''\r\n  },\r\n  methods: {\r\n    // 获取联系人列表\r\n    getLinkList() {\r\n      myRequest({\r\n        url: '/auth/linkman/list'\r\n      }).then(res => {\r\n        if (res.data && res.data.rows) {\r\n          this.linkList = res.data.rows.map(item => ({\r\n            ...item,\r\n            isCheck: false,\r\n            isMove: false\r\n          }))\r\n        }\r\n      }).catch(err => {\r\n        console.error('获取联系人列表失败:', err)\r\n        uni.showToast({\r\n          title: '获取联系人列表失败',\r\n          icon: 'error'\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 隐藏身份证号中间部分\r\n    hideCardNum(index) {\r\n      const certificate = this.linkList[index].linkmanCertificate\r\n      const length = certificate.length\r\n      \r\n      if (length < 4) return certificate\r\n      \r\n      if (length > 4 && length !== 18) {\r\n        let result = certificate.substring(0, 2)\r\n        for (let i = 0; i < length; i++) {\r\n          if (i >= 2 && i < length - 2) {\r\n            result += '*'\r\n          }\r\n        }\r\n        result += certificate.substring(length - 2, length)\r\n        return result\r\n      }\r\n      \r\n      return certificate.replace(certificate.substring(4, 15), '*******')\r\n    },\r\n    \r\n    // 添加联系人\r\n    addContact() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/contacts/addcontact'\r\n      })\r\n    },\r\n    \r\n    // 选择联系人\r\n    chooseLink(index) {\r\n      this.linkList[index].isCheck = !this.linkList[index].isCheck\r\n      this.checkAllNum = this.linkList.filter(item => item.isCheck).length\r\n    },\r\n    \r\n    // 确认选择\r\n    sureNum() {\r\n      if (this.checkAllNum > this.maxNum) {\r\n        uni.showToast({\r\n          title: '选择人数超出',\r\n          icon: 'error',\r\n          duration: 2000\r\n        })\r\n        return\r\n      }\r\n      \r\n      if (this.checkAllNum < this.maxNum) {\r\n        uni.showToast({\r\n          title: '选择人数不足',\r\n          icon: 'error',\r\n          duration: 2000\r\n        })\r\n        return\r\n      }\r\n      \r\n      const selectedContacts = this.linkList.filter(item => item.isCheck)\r\n      \r\n      // 年龄限制检查\r\n      if (this.type === 'kcyy_link' && this.age && this.age !== '0') {\r\n        if (!this.checkAgeRestriction(selectedContacts)) {\r\n          return\r\n        }\r\n      } else if (this.type === 'rgyy_link') {\r\n        if (!this.checkVenueRestriction(selectedContacts)) {\r\n          return\r\n        }\r\n      } else if (this.type === 'gyyy_link') {\r\n        if (!this.checkMovieRestriction(selectedContacts)) {\r\n          return\r\n        }\r\n      }\r\n      \r\n      // 保存选中的联系人到本地存储\r\n      uni.setStorageSync(this.type, JSON.stringify(selectedContacts))\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 检查年龄限制\r\n    checkAgeRestriction(contacts) {\r\n      for (let contact of contacts) {\r\n        const ageRanges = this.age.split('-')\r\n        \r\n        if (ageRanges.length === 1) {\r\n          const ageStr = ageRanges[0].replace(/岁|以|上/g, '')\r\n          \r\n          if (ageRanges[0].includes('以上')) {\r\n            if (contact.linkmanAge < parseInt(ageStr)) {\r\n              uni.showToast({\r\n                title: `选择人员年龄小于限制【${this.age}】`,\r\n                icon: 'error',\r\n                duration: 2000\r\n              })\r\n              return false\r\n            }\r\n          } else if (contact.linkmanAge !== parseInt(ageStr)) {\r\n            uni.showToast({\r\n              title: `选择人员年龄不等于限制【${this.age}】`,\r\n              icon: 'error',\r\n              duration: 2000\r\n            })\r\n            return false\r\n          }\r\n        } else if (ageRanges.length === 2) {\r\n          const minAge = parseInt(ageRanges[0].replace(/岁|以|上/g, ''))\r\n          const maxAge = parseInt(ageRanges[1].replace(/岁|以|上/g, ''))\r\n          \r\n          if (contact.linkmanAge < minAge) {\r\n            uni.showToast({\r\n              title: `选择人员年龄小于限制【${this.age}】`,\r\n              icon: 'error',\r\n              duration: 2000\r\n            })\r\n            return false\r\n          }\r\n          \r\n          if (contact.linkmanAge > maxAge) {\r\n            uni.showToast({\r\n              title: `选择人员年龄大于限制【${this.age}】`,\r\n              icon: 'error',\r\n              duration: 2000\r\n            })\r\n            return false\r\n          }\r\n        }\r\n      }\r\n      return true\r\n    },\r\n    \r\n    // 检查场馆预约限制\r\n    checkVenueRestriction(contacts) {\r\n      let under8Count = 0\r\n      for (let contact of contacts) {\r\n        if (contact.linkmanAge < 8) {\r\n          under8Count++\r\n        }\r\n      }\r\n      \r\n      if (under8Count > 3) {\r\n        uni.showToast({\r\n          title: '8岁以下的人数不超过3人',\r\n          icon: 'error',\r\n          duration: 2000\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    },\r\n    \r\n    // 检查观影预约限制\r\n    checkMovieRestriction(contacts) {\r\n      let under3Count = 0\r\n      let under10Count = 0\r\n      \r\n      for (let contact of contacts) {\r\n        if (contact.linkmanAge < 3) {\r\n          under3Count++\r\n        } else if (contact.linkmanAge < 10) {\r\n          under10Count++\r\n        }\r\n      }\r\n      \r\n      if (under3Count > 0) {\r\n        uni.showToast({\r\n          title: '3岁以下限制预约',\r\n          icon: 'error',\r\n          duration: 2000\r\n        })\r\n        return false\r\n      }\r\n      \r\n      if (under10Count > 2) {\r\n        uni.showToast({\r\n          title: '10岁以下限制预约2人',\r\n          icon: 'error',\r\n          duration: 2000\r\n        })\r\n        return false\r\n      }\r\n      \r\n      return true\r\n    },\r\n    \r\n    // 删除联系人\r\n    deleteItem(index) {\r\n      this.linkList.splice(index, 1)\r\n    },\r\n    \r\n    // 编辑联系人\r\n    editItem(id) {\r\n      uni.navigateTo({\r\n        url: `/pages_app/contacts/addcontact?id=${id}`\r\n      })\r\n    },\r\n    \r\n    // 触摸开始\r\n    touchstart(e) {\r\n      this.linkList.forEach(item => {\r\n        if (item.isMove) {\r\n          item.isMove = false\r\n        }\r\n      })\r\n      this.startX = e.changedTouches[0].clientX\r\n      this.startY = e.changedTouches[0].clientY\r\n    },\r\n    \r\n    // 触摸移动\r\n    touchmove(e, index) {\r\n      const startX = this.startX\r\n      const startY = this.startY\r\n      const touchX = e.changedTouches[0].clientX\r\n      const touchY = e.changedTouches[0].clientY\r\n      const angle = this.angle({ x: startX, y: startY }, { x: touchX, y: touchY })\r\n      \r\n      if (Math.abs(angle) <= 30 && startX - touchX >= 30) {\r\n        this.linkList[index].isMove = true\r\n      }\r\n    },\r\n    \r\n    // 计算角度\r\n    angle(start, end) {\r\n      const x = end.x - start.x\r\n      const y = end.y - start.y\r\n      return Math.atan(y / x) * 360 / Math.PI\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.contactContent {\r\n  background-color: #f3f4f6;\r\n  font-family: PingFang SC;\r\n  height: 100vh;\r\n  overflow: scroll;\r\n  width: 100%;\r\n}\r\n\r\n.contactBox {\r\n  box-sizing: border-box;\r\n  height: auto;\r\n  margin-bottom: 200rpx;\r\n  padding: 29rpx 28rpx;\r\n  width: 100%;\r\n}\r\n\r\n.addContact {\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  color: #5cb7ff;\r\n  font-size: 35rpx;\r\n  font-weight: 600;\r\n  height: 87rpx;\r\n  line-height: 87rpx;\r\n  margin-bottom: 28rpx;\r\n  text-align: center;\r\n  width: 100%;\r\n}\r\n\r\n.add {\r\n  font-size: 42rpx;\r\n  margin-right: 5rpx;\r\n}\r\n\r\n.contactList {\r\n  height: auto;\r\n  overflow: hidden;\r\n  width: 100%;\r\n}\r\n\r\n.contactItem {\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  height: 158rpx;\r\n  justify-content: space-between;\r\n  margin-bottom: 20rpx;\r\n  padding: 18rpx 28rpx;\r\n  position: relative;\r\n  width: 100%;\r\n}\r\n\r\n.left {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  justify-content: center;\r\n  width: auto;\r\n}\r\n\r\n.peopleName {\r\n  color: #000;\r\n  font-size: 29rpx;\r\n  font-weight: 600;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.peopleCard,\r\n.peopleMablie {\r\n  color: #888;\r\n  font-size: 23rpx;\r\n}\r\n\r\n.peopleMablie text:first-child {\r\n  display: inline-block;\r\n  margin-right: 96rpx;\r\n}\r\n\r\n.right {\r\n  align-items: center;\r\n  display: flex;\r\n  height: 100%;\r\n  width: 38rpx;\r\n}\r\n\r\n.checkBtn {\r\n  border: 2rpx solid #888;\r\n  border-radius: 38rpx;\r\n  box-sizing: border-box;\r\n  font-weight: 700;\r\n  height: 38rpx;\r\n  line-height: 38rpx;\r\n  text-align: center;\r\n  width: 38rpx;\r\n}\r\n\r\n.checkBtn.isCheck {\r\n  background: #ffba38;\r\n  border: none;\r\n}\r\n\r\n.checkBtn.isCheck::before {\r\n  content: \"✓\";\r\n  display: inline-block;\r\n  color: #fff;\r\n}\r\n\r\n.handlePlace {\r\n  color: #fff;\r\n  display: flex;\r\n  font-size: 28rpx;\r\n  height: 100%;\r\n  line-height: 158rpx;\r\n  position: absolute;\r\n  right: 0;\r\n  text-align: center;\r\n  top: 0;\r\n  transition: all 0.3s;\r\n  width: 0;\r\n  z-index: 99;\r\n}\r\n\r\n.edit {\r\n  background-color: #5cb7ff;\r\n  height: 100%;\r\n  width: 50%;\r\n}\r\n\r\n.del {\r\n  background-color: #fc5531;\r\n  height: 100%;\r\n  width: 50%;\r\n}\r\n\r\n.handlePlace.isMove {\r\n  width: 40%;\r\n}\r\n\r\n.sureChoose {\r\n  align-items: center;\r\n  background-color: #fff;\r\n  bottom: 0;\r\n  box-shadow: 20rpx 10rpx 20rpx 10rpx rgba(0, 0, 0, 0.4);\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  height: 150rpx;\r\n  justify-content: space-between;\r\n  left: 0;\r\n  padding: 0 29rpx;\r\n  position: fixed;\r\n  width: 100%;\r\n}\r\n\r\n.showNum {\r\n  color: #888;\r\n  font-size: 27rpx;\r\n}\r\n\r\n.setNum {\r\n  color: #ffba38;\r\n  font-size: 31rpx;\r\n}\r\n\r\n.upBtn {\r\n  background: #5cb7ff;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);\r\n  color: #fff;\r\n  font-size: 35rpx;\r\n  height: 77rpx;\r\n  line-height: 77rpx;\r\n  text-align: center;\r\n  width: 362rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/contacts/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["config", "myRequest", "uni"], "mappings": ";;;;AAgEA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,UAAU,CAAE;AAAA,MACZ,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,SAAS;AACP,aAAOA,OAAK;AAAA,IACd;AAAA,EACD;AAAA,EACD,SAAS;AACP,SAAK,YAAY;AAAA,EAClB;AAAA,EACD,OAAO,SAAS;AACd,SAAK,SAAS,QAAQ,OAAO;AAC7B,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,MAAM,QAAQ,OAAO;AAAA,EAC3B;AAAA,EACD,SAAS;AAAA;AAAA,IAEP,cAAc;AACZC,wBAAU;AAAA,QACR,KAAK;AAAA,OACN,EAAE,KAAK,SAAO;AACb,YAAI,IAAI,QAAQ,IAAI,KAAK,MAAM;AAC7B,eAAK,WAAW,IAAI,KAAK,KAAK,IAAI,WAAS;AAAA,YACzC,GAAG;AAAA,YACH,SAAS;AAAA,YACT,QAAQ;AAAA,UACV,EAAE;AAAA,QACJ;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,uCAAA,cAAc,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,YAAY,OAAO;AACjB,YAAM,cAAc,KAAK,SAAS,KAAK,EAAE;AACzC,YAAM,SAAS,YAAY;AAE3B,UAAI,SAAS;AAAG,eAAO;AAEvB,UAAI,SAAS,KAAK,WAAW,IAAI;AAC/B,YAAI,SAAS,YAAY,UAAU,GAAG,CAAC;AACvC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAI,KAAK,KAAK,IAAI,SAAS,GAAG;AAC5B,sBAAU;AAAA,UACZ;AAAA,QACF;AACA,kBAAU,YAAY,UAAU,SAAS,GAAG,MAAM;AAClD,eAAO;AAAA,MACT;AAEA,aAAO,YAAY,QAAQ,YAAY,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,IACnE;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,OAAO;AAChB,WAAK,SAAS,KAAK,EAAE,UAAU,CAAC,KAAK,SAAS,KAAK,EAAE;AACrD,WAAK,cAAc,KAAK,SAAS,OAAO,UAAQ,KAAK,OAAO,EAAE;AAAA,IAC/D;AAAA;AAAA,IAGD,UAAU;AACR,UAAI,KAAK,cAAc,KAAK,QAAQ;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD;AAAA,MACF;AAEA,UAAI,KAAK,cAAc,KAAK,QAAQ;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD;AAAA,MACF;AAEA,YAAM,mBAAmB,KAAK,SAAS,OAAO,UAAQ,KAAK,OAAO;AAGlE,UAAI,KAAK,SAAS,eAAe,KAAK,OAAO,KAAK,QAAQ,KAAK;AAC7D,YAAI,CAAC,KAAK,oBAAoB,gBAAgB,GAAG;AAC/C;AAAA,QACF;AAAA,MACF,WAAW,KAAK,SAAS,aAAa;AACpC,YAAI,CAAC,KAAK,sBAAsB,gBAAgB,GAAG;AACjD;AAAA,QACF;AAAA,MACF,WAAW,KAAK,SAAS,aAAa;AACpC,YAAI,CAAC,KAAK,sBAAsB,gBAAgB,GAAG;AACjD;AAAA,QACF;AAAA,MACF;AAGAA,oBAAG,MAAC,eAAe,KAAK,MAAM,KAAK,UAAU,gBAAgB,CAAC;AAC9DA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,oBAAoB,UAAU;AAC5B,eAAS,WAAW,UAAU;AAC5B,cAAM,YAAY,KAAK,IAAI,MAAM,GAAG;AAEpC,YAAI,UAAU,WAAW,GAAG;AAC1B,gBAAM,SAAS,UAAU,CAAC,EAAE,QAAQ,UAAU,EAAE;AAEhD,cAAI,UAAU,CAAC,EAAE,SAAS,IAAI,GAAG;AAC/B,gBAAI,QAAQ,aAAa,SAAS,MAAM,GAAG;AACzCA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO,cAAc,KAAK,GAAG;AAAA,gBAC7B,MAAM;AAAA,gBACN,UAAU;AAAA,eACX;AACD,qBAAO;AAAA,YACT;AAAA,UACA,WAAS,QAAQ,eAAe,SAAS,MAAM,GAAG;AAClDA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,eAAe,KAAK,GAAG;AAAA,cAC9B,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AACD,mBAAO;AAAA,UACT;AAAA,mBACS,UAAU,WAAW,GAAG;AACjC,gBAAM,SAAS,SAAS,UAAU,CAAC,EAAE,QAAQ,UAAU,EAAE,CAAC;AAC1D,gBAAM,SAAS,SAAS,UAAU,CAAC,EAAE,QAAQ,UAAU,EAAE,CAAC;AAE1D,cAAI,QAAQ,aAAa,QAAQ;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,cAAc,KAAK,GAAG;AAAA,cAC7B,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AACD,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,aAAa,QAAQ;AAC/BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,cAAc,KAAK,GAAG;AAAA,cAC7B,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AACD,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,sBAAsB,UAAU;AAC9B,UAAI,cAAc;AAClB,eAAS,WAAW,UAAU;AAC5B,YAAI,QAAQ,aAAa,GAAG;AAC1B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,cAAc,GAAG;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,sBAAsB,UAAU;AAC9B,UAAI,cAAc;AAClB,UAAI,eAAe;AAEnB,eAAS,WAAW,UAAU;AAC5B,YAAI,QAAQ,aAAa,GAAG;AAC1B;AAAA,QACF,WAAW,QAAQ,aAAa,IAAI;AAClC;AAAA,QACF;AAAA,MACF;AAEA,UAAI,cAAc,GAAG;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,GAAG;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AACD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,WAAW,OAAO;AAChB,WAAK,SAAS,OAAO,OAAO,CAAC;AAAA,IAC9B;AAAA;AAAA,IAGD,SAAS,IAAI;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,qCAAqC,EAAE;AAAA,OAC7C;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,GAAG;AACZ,WAAK,SAAS,QAAQ,UAAQ;AAC5B,YAAI,KAAK,QAAQ;AACf,eAAK,SAAS;AAAA,QAChB;AAAA,OACD;AACD,WAAK,SAAS,EAAE,eAAe,CAAC,EAAE;AAClC,WAAK,SAAS,EAAE,eAAe,CAAC,EAAE;AAAA,IACnC;AAAA;AAAA,IAGD,UAAU,GAAG,OAAO;AAClB,YAAM,SAAS,KAAK;AACpB,YAAM,SAAS,KAAK;AACpB,YAAM,SAAS,EAAE,eAAe,CAAC,EAAE;AACnC,YAAM,SAAS,EAAE,eAAe,CAAC,EAAE;AACnC,YAAM,QAAQ,KAAK,MAAM,EAAE,GAAG,QAAQ,GAAG,OAAK,GAAK,EAAE,GAAG,QAAQ,GAAG,OAAK,CAAG;AAE3E,UAAI,KAAK,IAAI,KAAK,KAAK,MAAM,SAAS,UAAU,IAAI;AAClD,aAAK,SAAS,KAAK,EAAE,SAAS;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,OAAO,KAAK;AAChB,YAAM,IAAI,IAAI,IAAI,MAAM;AACxB,YAAM,IAAI,IAAI,IAAI,MAAM;AACxB,aAAO,KAAK,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK;AAAA,IACvC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/UA,GAAG,WAAW,eAAe;"}