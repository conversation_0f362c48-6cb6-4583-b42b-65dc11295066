/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.curriculum-cancel.data-v-0a9c3b4c {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.curriculum-cancel .content.data-v-0a9c3b4c {
  padding: 20rpx;
  padding-top: 120rpx;
}
.curriculum-cancel .content .course-info.data-v-0a9c3b4c {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.curriculum-cancel .content .course-info .info-header.data-v-0a9c3b4c {
  margin-bottom: 20rpx;
}
.curriculum-cancel .content .course-info .info-header .venue-name.data-v-0a9c3b4c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.curriculum-cancel .content .course-info .course-details.data-v-0a9c3b4c {
  display: flex;
}
.curriculum-cancel .content .course-info .course-details .course-poster.data-v-0a9c3b4c {
  width: 120rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.curriculum-cancel .content .course-info .course-details .course-poster .poster-image.data-v-0a9c3b4c {
  width: 100%;
  height: 100%;
}
.curriculum-cancel .content .course-info .course-details .course-meta.data-v-0a9c3b4c {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.curriculum-cancel .content .course-info .course-details .course-meta .course-name.data-v-0a9c3b4c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.curriculum-cancel .content .course-info .course-details .course-meta .course-teacher.data-v-0a9c3b4c,
.curriculum-cancel .content .course-info .course-details .course-meta .course-location.data-v-0a9c3b4c,
.curriculum-cancel .content .course-info .course-details .course-meta .course-time.data-v-0a9c3b4c,
.curriculum-cancel .content .course-info .course-details .course-meta .course-date.data-v-0a9c3b4c {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
  line-height: 1.3;
}
.curriculum-cancel .content .contacts-section.data-v-0a9c3b4c {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.curriculum-cancel .content .contacts-section .section-title.data-v-0a9c3b4c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item.data-v-0a9c3b4c {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item.data-v-0a9c3b4c:last-child {
  border-bottom: none;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .contact-info.data-v-0a9c3b4c {
  flex: 1;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-name.data-v-0a9c3b4c {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-phone.data-v-0a9c3b4c {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-id.data-v-0a9c3b4c {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .checkbox.data-v-0a9c3b4c {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .checkbox.checked.data-v-0a9c3b4c {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}
.curriculum-cancel .content .contacts-section .contacts-list .contact-item .checkbox.checked .check-icon.data-v-0a9c3b4c {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}
.curriculum-cancel .content .action-section .cancel-btn.data-v-0a9c3b4c {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.curriculum-cancel .content .action-section .cancel-btn.data-v-0a9c3b4c::after {
  border: none;
}
.curriculum-cancel .content .action-section .cancel-btn.data-v-0a9c3b4c:disabled {
  background: #ccc;
  color: #999;
}

/* 平台特定样式 */
.content.data-v-0a9c3b4c {
  padding-bottom: env(safe-area-inset-bottom);
}