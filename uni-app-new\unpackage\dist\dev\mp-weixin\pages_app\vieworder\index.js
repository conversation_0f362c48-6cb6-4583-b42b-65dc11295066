"use strict";
const common_vendor = require("../../common/vendor.js");
const MyHeader = () => "../../components/my-header/my-header.js";
const _sfc_main = {
  name: "ViewOrder",
  components: {
    MyHeader
  },
  data() {
    return {
      movieList: [],
      isLoading: false
    };
  },
  onLoad() {
    this.getMovieList();
  },
  onShow() {
    this.getMovieList();
  },
  onPullDownRefresh() {
    this.getMovieList().finally(() => {
      common_vendor.index.stopPullDownRefresh();
    });
  },
  methods: {
    // 获取电影列表
    async getMovieList() {
      try {
        this.isLoading = true;
        const res = await this.$myRequest({
          url: "/web/movie/getMovieList",
          method: "get"
        });
        if (res.code === 200) {
          this.movieList = res.data.data || [];
        } else {
          throw new Error(res.msg || "获取电影列表失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/vieworder/index.vue:108", "获取电影列表失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取电影列表失败",
          icon: "error"
        });
      } finally {
        this.isLoading = false;
      }
    },
    // 获取电影状态
    getMovieStatus(movie) {
      const now = /* @__PURE__ */ new Date();
      const startTime = new Date(movie.movieStartTime);
      const endTime = new Date(movie.movieEndTime);
      if (now < startTime) {
        return "即将上映";
      } else if (now >= startTime && now <= endTime) {
        return "正在上映";
      } else {
        return "已下映";
      }
    },
    // 是否可以预约
    canReserve(movie) {
      const now = /* @__PURE__ */ new Date();
      const startTime = new Date(movie.movieStartTime);
      const endTime = new Date(movie.movieEndTime);
      return now >= startTime && now <= endTime && movie.inventoryVotes > 0;
    },
    // 获取预约按钮文字
    getReserveText(movie) {
      if (!this.canReserve(movie)) {
        return "不可预约";
      }
      return "立即预约";
    },
    // 跳转到电影详情
    goToMovieDetail(movie) {
      common_vendor.index.navigateTo({
        url: `/pages_app/vieworder/filmdes?movieId=${movie.id}`
      });
    },
    // 预约电影
    reserveMovie(movie) {
      if (!this.canReserve(movie)) {
        common_vendor.index.showToast({
          title: "该电影暂不可预约",
          icon: "none"
        });
        return;
      }
      this.goToMovieDetail(movie);
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      isBack: true,
      isShowHome: true,
      title: "观影预约",
      background: "transparent"
    }),
    b: common_vendor.f($data.movieList, (item, index, i0) => {
      return common_vendor.e({
        a: item.movieCover,
        b: common_vendor.t(item.movieName),
        c: common_vendor.t(item.movieType),
        d: common_vendor.t(item.movieDuration),
        e: item.movieRating
      }, item.movieRating ? {
        f: common_vendor.t(item.movieRating)
      } : {}, {
        g: common_vendor.t(item.movieDesc),
        h: common_vendor.t($options.getMovieStatus(item)),
        i: common_vendor.t($options.getReserveText(item)),
        j: common_vendor.o(($event) => $options.reserveMovie(item), item.id),
        k: !$options.canReserve(item),
        l: item.id,
        m: common_vendor.o(($event) => $options.goToMovieDetail(item), item.id)
      });
    }),
    c: $data.movieList.length === 0 && !$data.isLoading
  }, $data.movieList.length === 0 && !$data.isLoading ? {} : {}, {
    d: $data.isLoading
  }, $data.isLoading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-87a111fe"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/vieworder/index.js.map
