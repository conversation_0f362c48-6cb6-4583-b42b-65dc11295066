{"version": 3, "file": "index.js", "sources": ["pages_app/register/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHJlZ2lzdGVyXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"register-container\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      title=\"用户注册\" \r\n      :isBack=\"true\" \r\n      background=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\r\n      color=\"#ffffff\"\r\n    />\r\n    \r\n    <view class=\"register-content\">\r\n      <!-- Logo区域 -->\r\n      <view class=\"logo-section\">\r\n        <image class=\"logo\" src=\"/static/images/logo.png\" mode=\"aspectFit\" />\r\n        <text class=\"app-name\">宝安科技馆</text>\r\n        <text class=\"subtitle\">用户注册</text>\r\n      </view>\r\n      \r\n      <!-- 注册表单 -->\r\n      <view class=\"form-section\">\r\n        <!-- 用户名输入 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-user\"></text>\r\n            </view>\r\n            <input \r\n              class=\"form-input\" \r\n              placeholder=\"请输入用户名\" \r\n              type=\"text\" \r\n              v-model=\"registerForm.username\"\r\n              maxlength=\"20\"\r\n              @blur=\"validateUsername\"\r\n            />\r\n          </view>\r\n          <text v-if=\"errors.username\" class=\"error-text\">{{ errors.username }}</text>\r\n        </view>\r\n        \r\n        <!-- 手机号输入 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-phone\"></text>\r\n            </view>\r\n            <input \r\n              class=\"form-input\" \r\n              placeholder=\"请输入手机号\" \r\n              type=\"number\" \r\n              v-model=\"registerForm.phone\"\r\n              maxlength=\"11\"\r\n              @blur=\"validatePhone\"\r\n            />\r\n          </view>\r\n          <text v-if=\"errors.phone\" class=\"error-text\">{{ errors.phone }}</text>\r\n        </view>\r\n        \r\n        <!-- 短信验证码 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper verification-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-code\"></text>\r\n            </view>\r\n            <input \r\n              class=\"form-input verification-input\" \r\n              placeholder=\"请输入短信验证码\" \r\n              type=\"number\" \r\n              v-model=\"registerForm.smsCode\"\r\n              maxlength=\"6\"\r\n            />\r\n            <button \r\n              class=\"verification-btn\"\r\n              :class=\"{ disabled: !canSendSms || countdown > 0 }\"\r\n              :disabled=\"!canSendSms || countdown > 0\"\r\n              @tap=\"sendSmsCode\"\r\n            >\r\n              {{ countdown > 0 ? `${countdown}s后重发` : '获取验证码' }}\r\n            </button>\r\n          </view>\r\n          <text v-if=\"errors.smsCode\" class=\"error-text\">{{ errors.smsCode }}</text>\r\n        </view>\r\n        \r\n        <!-- 密码输入 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-password\"></text>\r\n            </view>\r\n            <input \r\n              class=\"form-input\" \r\n              placeholder=\"请输入密码\" \r\n              type=\"password\" \r\n              v-model=\"registerForm.password\"\r\n              maxlength=\"20\"\r\n              @blur=\"validatePassword\"\r\n            />\r\n          </view>\r\n          <text v-if=\"errors.password\" class=\"error-text\">{{ errors.password }}</text>\r\n          <text class=\"password-hint\">密码至少8位，必须包含字母、数字、特殊符号</text>\r\n        </view>\r\n        \r\n        <!-- 确认密码 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-password\"></text>\r\n            </view>\r\n            <input \r\n              class=\"form-input\" \r\n              placeholder=\"请再次输入密码\" \r\n              type=\"password\" \r\n              v-model=\"registerForm.confirmPassword\"\r\n              maxlength=\"20\"\r\n              @blur=\"validateConfirmPassword\"\r\n            />\r\n          </view>\r\n          <text v-if=\"errors.confirmPassword\" class=\"error-text\">{{ errors.confirmPassword }}</text>\r\n        </view>\r\n        \r\n        <!-- 图形验证码 -->\r\n        <view class=\"input-group\" v-if=\"captchaEnabled\">\r\n          <view class=\"input-wrapper captcha-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-shield\"></text>\r\n            </view>\r\n            <input \r\n              class=\"form-input captcha-input\" \r\n              placeholder=\"请输入图形验证码\" \r\n              type=\"text\" \r\n              v-model=\"registerForm.captcha\"\r\n              maxlength=\"4\"\r\n            />\r\n            <view class=\"captcha-image\" @tap=\"refreshCaptcha\">\r\n              <image v-if=\"codeUrl\" class=\"captcha-img\" :src=\"codeUrl\" mode=\"aspectFit\" />\r\n              <text v-else class=\"refresh-text\">点击刷新</text>\r\n            </view>\r\n          </view>\r\n          <text v-if=\"errors.captcha\" class=\"error-text\">{{ errors.captcha }}</text>\r\n        </view>\r\n        \r\n        <!-- 用户协议 -->\r\n        <view class=\"agreement-section\">\r\n          <label class=\"agreement-checkbox\">\r\n            <checkbox \r\n              :checked=\"agreeToTerms\" \r\n              @change=\"onAgreementChange\"\r\n              color=\"#667eea\"\r\n            />\r\n            <text class=\"agreement-text\">\r\n              我已阅读并同意\r\n              <text class=\"link-text\" @tap=\"showUserAgreement\">《用户协议》</text>\r\n              和\r\n              <text class=\"link-text\" @tap=\"showPrivacyPolicy\">《隐私政策》</text>\r\n            </text>\r\n          </label>\r\n        </view>\r\n        \r\n        <!-- 注册按钮 -->\r\n        <view class=\"button-section\">\r\n          <button \r\n            class=\"register-button\"\r\n            :class=\"{ disabled: !canRegister }\"\r\n            :disabled=\"!canRegister\"\r\n            @tap=\"handleRegister\"\r\n          >\r\n            {{ isRegistering ? '注册中...' : '立即注册' }}\r\n          </button>\r\n        </view>\r\n        \r\n        <!-- 登录链接 -->\r\n        <view class=\"login-link-section\">\r\n          <text class=\"login-link\" @tap=\"goToLogin\">已有账号？立即登录</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register, registerWithPhone } from '@/api/login.js'\r\nimport { sendSmsCode, verifySmsCode, getUserAgreement, getPrivacyPolicy } from '@/api/common.js'\r\nimport MyHeader from '@/components/my-header/my-header.vue'\r\n\r\nexport default {\r\n  components: {\r\n    MyHeader\r\n  },\r\n  data() {\r\n    return {\r\n      // 验证码相关\r\n      codeUrl: '',\r\n      captchaEnabled: true,\r\n      countdown: 0,\r\n      timer: null,\r\n      \r\n      // 注册状态\r\n      isRegistering: false,\r\n      agreeToTerms: false,\r\n      \r\n      // 表单数据\r\n      registerForm: {\r\n        username: '',\r\n        phone: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        smsCode: '',\r\n        captcha: '',\r\n        uuid: ''\r\n      },\r\n      \r\n      // 表单验证错误\r\n      errors: {\r\n        username: '',\r\n        phone: '',\r\n        password: '',\r\n        confirmPassword: '',\r\n        smsCode: '',\r\n        captcha: ''\r\n      }\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 是否可以发送短信验证码\r\n    canSendSms() {\r\n      return this.isValidPhone(this.registerForm.phone) && this.countdown === 0\r\n    },\r\n    \r\n    // 是否可以注册\r\n    canRegister() {\r\n      return this.agreeToTerms && \r\n             !this.isRegistering &&\r\n             this.registerForm.username &&\r\n             this.registerForm.phone &&\r\n             this.registerForm.password &&\r\n             this.registerForm.confirmPassword &&\r\n             this.registerForm.smsCode &&\r\n             (!this.captchaEnabled || this.registerForm.captcha) &&\r\n             !Object.values(this.errors).some(error => error)\r\n    }\r\n  },\r\n  \r\n  created() {\r\n    this.getCaptcha()\r\n  },\r\n  \r\n  onUnload() {\r\n    // 清理定时器\r\n    if (this.timer) {\r\n      clearInterval(this.timer)\r\n      this.timer = null\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 获取图形验证码\r\n    async getCaptcha() {\r\n      try {\r\n        const res = await getCodeImg()\r\n        if (res.code === 200) {\r\n          this.captchaEnabled = res.captchaEnabled !== false\r\n          if (this.captchaEnabled) {\r\n            this.codeUrl = 'data:image/gif;base64,' + res.img\r\n            this.registerForm.uuid = res.uuid\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取验证码失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 刷新图形验证码\r\n    refreshCaptcha() {\r\n      this.registerForm.captcha = ''\r\n      this.errors.captcha = ''\r\n      this.getCaptcha()\r\n    },\r\n    \r\n    // 发送短信验证码\r\n    async sendSmsCode() {\r\n      if (!this.canSendSms) return\r\n      \r\n      try {\r\n        uni.showLoading({ title: '发送中...' })\r\n        \r\n        const res = await sendSmsCode(this.registerForm.phone, 'register')\r\n        \r\n        if (res.code === 200) {\r\n          uni.showToast({\r\n            title: '验证码已发送',\r\n            icon: 'success'\r\n          })\r\n          \r\n          // 开始倒计时\r\n          this.startCountdown()\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '发送失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '发送失败，请重试',\r\n          icon: 'none'\r\n        })\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    },\r\n    \r\n    // 开始倒计时\r\n    startCountdown() {\r\n      this.countdown = 60\r\n      this.timer = setInterval(() => {\r\n        this.countdown--\r\n        if (this.countdown <= 0) {\r\n          clearInterval(this.timer)\r\n          this.timer = null\r\n        }\r\n      }, 1000)\r\n    },\r\n    \r\n    // 表单验证方法\r\n    validateUsername() {\r\n      const username = this.registerForm.username.trim()\r\n      if (!username) {\r\n        this.errors.username = '请输入用户名'\r\n      } else if (username.length < 3) {\r\n        this.errors.username = '用户名至少3个字符'\r\n      } else if (username.length > 20) {\r\n        this.errors.username = '用户名不能超过20个字符'\r\n      } else if (!/^[a-zA-Z0-9_\\u4e00-\\u9fa5]+$/.test(username)) {\r\n        this.errors.username = '用户名只能包含字母、数字、下划线和中文'\r\n      } else {\r\n        this.errors.username = ''\r\n      }\r\n    },\r\n    \r\n    validatePhone() {\r\n      const phone = this.registerForm.phone.trim()\r\n      if (!phone) {\r\n        this.errors.phone = '请输入手机号'\r\n      } else if (!this.isValidPhone(phone)) {\r\n        this.errors.phone = '请输入正确的手机号'\r\n      } else {\r\n        this.errors.phone = ''\r\n      }\r\n    },\r\n    \r\n    validatePassword() {\r\n      const password = this.registerForm.password\r\n      if (!password) {\r\n        this.errors.password = '请输入密码'\r\n      } else if (password.length < 8) {\r\n        this.errors.password = '密码至少8位'\r\n      } else if (!/^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]).{8,}$/.test(password)) {\r\n        this.errors.password = '密码必须包含字母、数字、特殊符号'\r\n      } else {\r\n        this.errors.password = ''\r\n      }\r\n    },\r\n    \r\n    validateConfirmPassword() {\r\n      const confirmPassword = this.registerForm.confirmPassword\r\n      if (!confirmPassword) {\r\n        this.errors.confirmPassword = '请确认密码'\r\n      } else if (confirmPassword !== this.registerForm.password) {\r\n        this.errors.confirmPassword = '两次输入的密码不一致'\r\n      } else {\r\n        this.errors.confirmPassword = ''\r\n      }\r\n    },\r\n    \r\n    // 验证手机号格式\r\n    isValidPhone(phone) {\r\n      return /^1[3-9]\\d{9}$/.test(phone)\r\n    },\r\n    \r\n    // 全面表单验证\r\n    validateForm() {\r\n      this.validateUsername()\r\n      this.validatePhone()\r\n      this.validatePassword()\r\n      this.validateConfirmPassword()\r\n      \r\n      // 验证短信验证码\r\n      if (!this.registerForm.smsCode) {\r\n        this.errors.smsCode = '请输入短信验证码'\r\n      } else if (this.registerForm.smsCode.length !== 6) {\r\n        this.errors.smsCode = '验证码应为6位数字'\r\n      } else {\r\n        this.errors.smsCode = ''\r\n      }\r\n      \r\n      // 验证图形验证码\r\n      if (this.captchaEnabled) {\r\n        if (!this.registerForm.captcha) {\r\n          this.errors.captcha = '请输入图形验证码'\r\n        } else if (this.registerForm.captcha.length !== 4) {\r\n          this.errors.captcha = '验证码应为4位'\r\n        } else {\r\n          this.errors.captcha = ''\r\n        }\r\n      }\r\n      \r\n      return !Object.values(this.errors).some(error => error)\r\n    },\r\n    \r\n    // 用户协议变更\r\n    onAgreementChange(e) {\r\n      this.agreeToTerms = e.detail.value.length > 0\r\n    },\r\n    \r\n    // 显示用户协议\r\n    async showUserAgreement() {\r\n      try {\r\n        const res = await getUserAgreement()\r\n        if (res.code === 200) {\r\n          uni.showModal({\r\n            title: '用户协议',\r\n            content: res.data.content || '用户协议内容',\r\n            showCancel: false,\r\n            confirmText: '我知道了'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '获取协议失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 显示隐私政策\r\n    async showPrivacyPolicy() {\r\n      try {\r\n        const res = await getPrivacyPolicy()\r\n        if (res.code === 200) {\r\n          uni.showModal({\r\n            title: '隐私政策',\r\n            content: res.data.content || '隐私政策内容',\r\n            showCancel: false,\r\n            confirmText: '我知道了'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '获取政策失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 注册处理\r\n    async handleRegister() {\r\n      if (!this.validateForm()) {\r\n        return\r\n      }\r\n      \r\n      if (!this.agreeToTerms) {\r\n        uni.showToast({\r\n          title: '请先同意用户协议和隐私政策',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      this.isRegistering = true\r\n      \r\n      try {\r\n        // 先验证短信验证码\r\n        const verifyRes = await verifySmsCode(\r\n          this.registerForm.phone, \r\n          this.registerForm.smsCode, \r\n          'register'\r\n        )\r\n        \r\n        if (verifyRes.code !== 200) {\r\n          uni.showToast({\r\n            title: '短信验证码错误',\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        \r\n        // 准备注册数据\r\n        const registerData = {\r\n          username: this.registerForm.username.trim(),\r\n          phone: this.registerForm.phone.trim(),\r\n          password: this.registerForm.password,\r\n          smsCode: this.registerForm.smsCode\r\n        }\r\n        \r\n        // 如果启用了图形验证码，添加相关字段\r\n        if (this.captchaEnabled) {\r\n          registerData.code = this.registerForm.captcha\r\n          registerData.uuid = this.registerForm.uuid\r\n        }\r\n        \r\n        const res = await registerWithPhone(registerData)\r\n        \r\n        if (res.code === 200) {\r\n          uni.showModal({\r\n            title: '注册成功',\r\n            content: `恭喜您，账号 ${this.registerForm.username} 注册成功！`,\r\n            showCancel: false,\r\n            confirmText: '立即登录',\r\n            success: (modalRes) => {\r\n              if (modalRes.confirm) {\r\n                this.goToLogin()\r\n              }\r\n            }\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '注册失败',\r\n            icon: 'none'\r\n          })\r\n          \r\n          // 刷新图形验证码\r\n          if (this.captchaEnabled) {\r\n            this.refreshCaptcha()\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('注册失败:', error)\r\n        uni.showToast({\r\n          title: '注册失败，请重试',\r\n          icon: 'none'\r\n        })\r\n        \r\n        // 刷新图形验证码\r\n        if (this.captchaEnabled) {\r\n          this.refreshCaptcha()\r\n        }\r\n      } finally {\r\n        this.isRegistering = false\r\n      }\r\n    },\r\n    \r\n    // 跳转到登录页面\r\n    goToLogin() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/login/index'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n}\r\n\r\n.register-content {\r\n  padding: 40rpx;\r\n  min-height: calc(100vh - 88px);\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* Logo区域 */\r\n.logo-section {\r\n  text-align: center;\r\n  margin-bottom: 60rpx;\r\n  margin-top: 40rpx;\r\n}\r\n\r\n.logo {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.app-name {\r\n  display: block;\r\n  font-size: 48rpx;\r\n  font-weight: bold;\r\n  color: #ffffff;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.subtitle {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n/* 表单区域 */\r\n.form-section {\r\n  flex: 1;\r\n  background: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 60rpx 40rpx;\r\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 输入组 */\r\n.input-group {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #f8f9fa;\r\n  border-radius: 12rpx;\r\n  border: 2rpx solid #e9ecef;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.input-wrapper:focus-within {\r\n  border-color: #667eea;\r\n  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\r\n}\r\n\r\n.input-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #6c757d;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.form-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 28rpx;\r\n  color: #333333;\r\n  background: transparent;\r\n  border: none;\r\n}\r\n\r\n.form-input::placeholder {\r\n  color: #adb5bd;\r\n}\r\n\r\n/* 验证码相关 */\r\n.verification-wrapper {\r\n  padding-right: 20rpx;\r\n}\r\n\r\n.verification-input {\r\n  flex: 1;\r\n}\r\n\r\n.verification-btn {\r\n  padding: 0 24rpx;\r\n  height: 60rpx;\r\n  line-height: 60rpx;\r\n  background: #667eea;\r\n  color: #ffffff;\r\n  border-radius: 8rpx;\r\n  font-size: 24rpx;\r\n  border: none;\r\n  margin-left: 20rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.verification-btn.disabled {\r\n  background: #adb5bd;\r\n  color: #ffffff;\r\n}\r\n\r\n.verification-btn:not(.disabled):active {\r\n  background: #5a67d8;\r\n}\r\n\r\n/* 图形验证码 */\r\n.captcha-wrapper {\r\n  padding-right: 20rpx;\r\n}\r\n\r\n.captcha-input {\r\n  flex: 1;\r\n}\r\n\r\n.captcha-image {\r\n  width: 120rpx;\r\n  height: 60rpx;\r\n  margin-left: 20rpx;\r\n  border-radius: 8rpx;\r\n  overflow: hidden;\r\n  background: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1rpx solid #e9ecef;\r\n}\r\n\r\n.captcha-img {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.refresh-text {\r\n  font-size: 20rpx;\r\n  color: #6c757d;\r\n}\r\n\r\n/* 错误提示 */\r\n.error-text {\r\n  display: block;\r\n  color: #dc3545;\r\n  font-size: 24rpx;\r\n  margin-top: 10rpx;\r\n  margin-left: 80rpx;\r\n}\r\n\r\n/* 密码提示 */\r\n.password-hint {\r\n  display: block;\r\n  color: #6c757d;\r\n  font-size: 22rpx;\r\n  margin-top: 10rpx;\r\n  margin-left: 80rpx;\r\n}\r\n\r\n/* 用户协议 */\r\n.agreement-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.agreement-checkbox {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  font-size: 26rpx;\r\n  color: #6c757d;\r\n  line-height: 1.5;\r\n}\r\n\r\n.agreement-text {\r\n  margin-left: 20rpx;\r\n  flex: 1;\r\n}\r\n\r\n.link-text {\r\n  color: #667eea;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* 按钮区域 */\r\n.button-section {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.register-button {\r\n  width: 100%;\r\n  height: 88rpx;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  border-radius: 12rpx;\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  border: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.register-button.disabled {\r\n  background: #adb5bd;\r\n  color: #ffffff;\r\n}\r\n\r\n.register-button:not(.disabled):active {\r\n  transform: translateY(2rpx);\r\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* 登录链接 */\r\n.login-link-section {\r\n  text-align: center;\r\n}\r\n\r\n.login-link {\r\n  color: #667eea;\r\n  font-size: 28rpx;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* 响应式适配 */\r\n@media screen and (max-width: 750rpx) {\r\n  .register-content {\r\n    padding: 30rpx;\r\n  }\r\n  \r\n  .form-section {\r\n    padding: 40rpx 30rpx;\r\n  }\r\n  \r\n  .logo {\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n  }\r\n  \r\n  .app-name {\r\n    font-size: 42rpx;\r\n  }\r\n}\r\n\r\n/* 平台兼容性 */\r\n/* #ifdef H5 */\r\n.register-container {\r\n  max-width: 750rpx;\r\n  margin: 0 auto;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.input-wrapper:focus-within {\r\n  border-color: #667eea;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef APP-PLUS */\r\n.form-input {\r\n  padding-left: 20rpx;\r\n}\r\n/* #endif */\r\n\r\n/* 通用工具类 */\r\n.flex {\r\n  display: flex;\r\n}\r\n\r\n.align-center {\r\n  align-items: center;\r\n}\r\n\r\n.justify-center {\r\n  justify-content: center;\r\n}\r\n\r\n.text-center {\r\n  text-align: center;\r\n}\r\n\r\n/* 图标字体 - 使用简单文本图标 */\r\n.icon-user::before {\r\n  content: '👤';\r\n}\r\n\r\n.icon-phone::before {\r\n  content: '📱';\r\n}\r\n\r\n.icon-code::before {\r\n  content: '🔢';\r\n}\r\n\r\n.icon-password::before {\r\n  content: '🔒';\r\n}\r\n\r\n.icon-shield::before {\r\n  content: '🛡️';\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/register/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getCodeImg", "uni", "sendSmsCode", "getUserAgreement", "getPrivacyPolicy", "verifySmsCode", "registerWithPhone"], "mappings": ";;;;;AAoLA,MAAK,WAAY,MAAW;AAE5B,MAAK,YAAU;AAAA,EACb,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,OAAO;AAAA;AAAA,MAGP,eAAe;AAAA,MACf,cAAc;AAAA;AAAA,MAGd,cAAc;AAAA,QACZ,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,MACP;AAAA;AAAA,MAGD,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,QACP,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,aAAa;AACX,aAAO,KAAK,aAAa,KAAK,aAAa,KAAK,KAAK,KAAK,cAAc;AAAA,IACzE;AAAA;AAAA,IAGD,cAAc;AACZ,aAAO,KAAK,gBACL,CAAC,KAAK,iBACN,KAAK,aAAa,YAClB,KAAK,aAAa,SAClB,KAAK,aAAa,YAClB,KAAK,aAAa,mBAClB,KAAK,aAAa,YACjB,CAAC,KAAK,kBAAkB,KAAK,aAAa,YAC3C,CAAC,OAAO,OAAO,KAAK,MAAM,EAAE,KAAK,WAAS,KAAK;AAAA,IACxD;AAAA,EACD;AAAA,EAED,UAAU;AACR,SAAK,WAAW;AAAA,EACjB;AAAA,EAED,WAAW;AAET,QAAI,KAAK,OAAO;AACd,oBAAc,KAAK,KAAK;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACD;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,aAAa;AACjB,UAAI;AACF,cAAM,MAAM,MAAMA,qBAAW;AAC7B,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,iBAAiB,IAAI,mBAAmB;AAC7C,cAAI,KAAK,gBAAgB;AACvB,iBAAK,UAAU,2BAA2B,IAAI;AAC9C,iBAAK,aAAa,OAAO,IAAI;AAAA,UAC/B;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdC,sBAAAA,MAAc,MAAA,SAAA,uCAAA,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,aAAa,UAAU;AAC5B,WAAK,OAAO,UAAU;AACtB,WAAK,WAAW;AAAA,IACjB;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK;AAAY;AAEtB,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAU;AAEnC,cAAM,MAAM,MAAMC,WAAW,YAAC,KAAK,aAAa,OAAO,UAAU;AAEjE,YAAI,IAAI,SAAS,KAAK;AACpBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAGD,eAAK,eAAe;AAAA,eACf;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACRA,sBAAAA,MAAI,YAAY;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,YAAY;AACjB,WAAK,QAAQ,YAAY,MAAM;AAC7B,aAAK;AACL,YAAI,KAAK,aAAa,GAAG;AACvB,wBAAc,KAAK,KAAK;AACxB,eAAK,QAAQ;AAAA,QACf;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB;AACjB,YAAM,WAAW,KAAK,aAAa,SAAS,KAAK;AACjD,UAAI,CAAC,UAAU;AACb,aAAK,OAAO,WAAW;AAAA,iBACd,SAAS,SAAS,GAAG;AAC9B,aAAK,OAAO,WAAW;AAAA,iBACd,SAAS,SAAS,IAAI;AAC/B,aAAK,OAAO,WAAW;AAAA,MACvB,WAAS,CAAC,+BAA+B,KAAK,QAAQ,GAAG;AACzD,aAAK,OAAO,WAAW;AAAA,aAClB;AACL,aAAK,OAAO,WAAW;AAAA,MACzB;AAAA,IACD;AAAA,IAED,gBAAgB;AACd,YAAM,QAAQ,KAAK,aAAa,MAAM,KAAK;AAC3C,UAAI,CAAC,OAAO;AACV,aAAK,OAAO,QAAQ;AAAA,MACtB,WAAW,CAAC,KAAK,aAAa,KAAK,GAAG;AACpC,aAAK,OAAO,QAAQ;AAAA,aACf;AACL,aAAK,OAAO,QAAQ;AAAA,MACtB;AAAA,IACD;AAAA,IAED,mBAAmB;AACjB,YAAM,WAAW,KAAK,aAAa;AACnC,UAAI,CAAC,UAAU;AACb,aAAK,OAAO,WAAW;AAAA,iBACd,SAAS,SAAS,GAAG;AAC9B,aAAK,OAAO,WAAW;AAAA,MACvB,WAAS,CAAC,2EAA2E,KAAK,QAAQ,GAAG;AACrG,aAAK,OAAO,WAAW;AAAA,aAClB;AACL,aAAK,OAAO,WAAW;AAAA,MACzB;AAAA,IACD;AAAA,IAED,0BAA0B;AACxB,YAAM,kBAAkB,KAAK,aAAa;AAC1C,UAAI,CAAC,iBAAiB;AACpB,aAAK,OAAO,kBAAkB;AAAA,MAC9B,WAAS,oBAAoB,KAAK,aAAa,UAAU;AACzD,aAAK,OAAO,kBAAkB;AAAA,aACzB;AACL,aAAK,OAAO,kBAAkB;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,aAAa,OAAO;AAClB,aAAO,gBAAgB,KAAK,KAAK;AAAA,IAClC;AAAA;AAAA,IAGD,eAAe;AACb,WAAK,iBAAiB;AACtB,WAAK,cAAc;AACnB,WAAK,iBAAiB;AACtB,WAAK,wBAAwB;AAG7B,UAAI,CAAC,KAAK,aAAa,SAAS;AAC9B,aAAK,OAAO,UAAU;AAAA,MACxB,WAAW,KAAK,aAAa,QAAQ,WAAW,GAAG;AACjD,aAAK,OAAO,UAAU;AAAA,aACjB;AACL,aAAK,OAAO,UAAU;AAAA,MACxB;AAGA,UAAI,KAAK,gBAAgB;AACvB,YAAI,CAAC,KAAK,aAAa,SAAS;AAC9B,eAAK,OAAO,UAAU;AAAA,QACxB,WAAW,KAAK,aAAa,QAAQ,WAAW,GAAG;AACjD,eAAK,OAAO,UAAU;AAAA,eACjB;AACL,eAAK,OAAO,UAAU;AAAA,QACxB;AAAA,MACF;AAEA,aAAO,CAAC,OAAO,OAAO,KAAK,MAAM,EAAE,KAAK,WAAS,KAAK;AAAA,IACvD;AAAA;AAAA,IAGD,kBAAkB,GAAG;AACnB,WAAK,eAAe,EAAE,OAAO,MAAM,SAAS;AAAA,IAC7C;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI;AACF,cAAM,MAAM,MAAME,4BAAiB;AACnC,YAAI,IAAI,SAAS,KAAK;AACpBF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,IAAI,KAAK,WAAW;AAAA,YAC7B,YAAY;AAAA,YACZ,aAAa;AAAA,WACd;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI;AACF,cAAM,MAAM,MAAMG,4BAAiB;AACnC,YAAI,IAAI,SAAS,KAAK;AACpBH,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,IAAI,KAAK,WAAW;AAAA,YAC7B,YAAY;AAAA,YACZ,aAAa;AAAA,WACd;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,cAAc;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,gBAAgB;AAErB,UAAI;AAEF,cAAM,YAAY,MAAMI,WAAa;AAAA,UACnC,KAAK,aAAa;AAAA,UAClB,KAAK,aAAa;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,UAAU,SAAS,KAAK;AAC1BJ,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AACD;AAAA,QACF;AAGA,cAAM,eAAe;AAAA,UACnB,UAAU,KAAK,aAAa,SAAS,KAAM;AAAA,UAC3C,OAAO,KAAK,aAAa,MAAM,KAAM;AAAA,UACrC,UAAU,KAAK,aAAa;AAAA,UAC5B,SAAS,KAAK,aAAa;AAAA,QAC7B;AAGA,YAAI,KAAK,gBAAgB;AACvB,uBAAa,OAAO,KAAK,aAAa;AACtC,uBAAa,OAAO,KAAK,aAAa;AAAA,QACxC;AAEA,cAAM,MAAM,MAAMK,UAAiB,kBAAC,YAAY;AAEhD,YAAI,IAAI,SAAS,KAAK;AACpBL,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,SAAS,UAAU,KAAK,aAAa,QAAQ;AAAA,YAC7C,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,CAAC,aAAa;AACrB,kBAAI,SAAS,SAAS;AACpB,qBAAK,UAAU;AAAA,cACjB;AAAA,YACF;AAAA,WACD;AAAA,eACI;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,WACP;AAGD,cAAI,KAAK,gBAAgB;AACvB,iBAAK,eAAe;AAAA,UACtB;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe;AAAA,QACtB;AAAA,MACF,UAAU;AACR,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,YAAY;AACVA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACliBA,GAAG,WAAW,eAAe;"}