<view class="contactContent data-v-e45255a1"><my-header wx:if="{{a}}" class="data-v-e45255a1" u-i="e45255a1-0" bind:__l="__l" u-p="{{b}}"/><view class="contactBox data-v-e45255a1"><view class="addContact data-v-e45255a1" bindtap="{{c}}"><text class="add data-v-e45255a1">+</text>添加联系人 </view><view class="contactList data-v-e45255a1"><view wx:for="{{d}}" wx:for-item="link" wx:key="i" class="{{['contactItem', 'data-v-e45255a1', link.j && 'isMove']}}" bindtouchstart="{{link.k}}" bindtouchmove="{{link.l}}" bindtap="{{link.m}}" data-index="{{link.n}}"><view class="left data-v-e45255a1"><view class="peopleName data-v-e45255a1">{{link.a}}</view><view class="peopleCard data-v-e45255a1">证件号 {{link.b}}</view><view class="peopleMablie data-v-e45255a1"><text class="data-v-e45255a1">手机号码 {{link.c}}</text><text class="data-v-e45255a1">年龄{{link.d}}</text></view></view><view class="right data-v-e45255a1"><view class="{{['checkBtn', 'data-v-e45255a1', link.e && 'isCheck']}}"></view></view><view class="{{['handlePlace', 'data-v-e45255a1', link.h && 'isMove']}}"><view class="edit data-v-e45255a1" catchtap="{{link.f}}">编辑</view><view class="del data-v-e45255a1" catchtap="{{link.g}}">删除</view></view></view></view></view><view class="sureChoose data-v-e45255a1"><view class="showNum data-v-e45255a1"> 选择 <text class="setNum data-v-e45255a1">{{e}}</text> 人 </view><view class="upBtn data-v-e45255a1" bindtap="{{f}}">确定选择</view></view></view>