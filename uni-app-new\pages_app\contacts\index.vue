<template>
  <view class="contactContent">
    <!-- 头部组件 -->
    <my-header 
      v-if="!config.iSzgm"
      :menuClass="'bor'"
      :isFixed="true"
      :isBack="true"
      :isShowHome="true"
      :title="'联系人'"
      :color="'#000'"
    />
    
    <view class="contactBox">
      <!-- 添加联系人按钮 -->
      <view class="addContact" @tap="addContact">
        <text class="add">+</text>添加联系人
      </view>
      
      <!-- 联系人列表 -->
      <view class="contactList">
        <view 
          v-for="(link, index) in linkList" 
          :key="link.id"
          class="contactItem"
          :class="{ isMove: link.isMove }"
          @touchstart="touchstart"
          @touchmove="touchmove($event, index)"
          @tap="chooseLink(index)"
          :data-index="index"
        >
          <view class="left">
            <view class="peopleName">{{ link.linkmanName }}</view>
            <view class="peopleCard">证件号 {{ hideCardNum(index) }}</view>
            <view class="peopleMablie">
              <text>手机号码 {{ link.linkmanPhone }}</text>
              <text>年龄{{ link.linkmanAge }}</text>
            </view>
          </view>
          <view class="right">
            <view class="checkBtn" :class="{ isCheck: link.isCheck }"></view>
          </view>
          <view class="handlePlace" :class="{ isMove: link.isMove }">
            <view class="edit" @tap.stop="editItem(link.id)">编辑</view>
            <view class="del" @tap.stop="deleteItem(index)">删除</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部确认选择 -->
    <view class="sureChoose">
      <view class="showNum">
        选择 <text class="setNum">{{ checkAllNum }}</text> 人
      </view>
      <view class="upBtn" @tap="sureNum">确定选择</view>
    </view>
  </view>
</template>

<script>
import { myRequest } from '../../api/api.js'
import config from '../../config.js'

export default {
  name: 'ContactList',
  data() {
    return {
      age: '0',
      maxNum: 0,
      linkList: [],
      checkAllNum: 0,
      startX: '',
      startY: '',
      type: ''
    }
  },
  computed: {
    config() {
      return config
    }
  },
  onShow() {
    this.getLinkList()
  },
  onLoad(options) {
    this.maxNum = options.num || 0
    this.type = options.type || ''
    this.age = options.age || ''
  },
  methods: {
    // 获取联系人列表
    getLinkList() {
      myRequest({
        url: '/auth/linkman/list'
      }).then(res => {
        if (res.data && res.data.rows) {
          this.linkList = res.data.rows.map(item => ({
            ...item,
            isCheck: false,
            isMove: false
          }))
        }
      }).catch(err => {
        console.error('获取联系人列表失败:', err)
        uni.showToast({
          title: '获取联系人列表失败',
          icon: 'error'
        })
      })
    },
    
    // 隐藏身份证号中间部分
    hideCardNum(index) {
      const certificate = this.linkList[index].linkmanCertificate
      const length = certificate.length
      
      if (length < 4) return certificate
      
      if (length > 4 && length !== 18) {
        let result = certificate.substring(0, 2)
        for (let i = 0; i < length; i++) {
          if (i >= 2 && i < length - 2) {
            result += '*'
          }
        }
        result += certificate.substring(length - 2, length)
        return result
      }
      
      return certificate.replace(certificate.substring(4, 15), '*******')
    },
    
    // 添加联系人
    addContact() {
      uni.navigateTo({
        url: '/pages_app/contacts/addcontact'
      })
    },
    
    // 选择联系人
    chooseLink(index) {
      this.linkList[index].isCheck = !this.linkList[index].isCheck
      this.checkAllNum = this.linkList.filter(item => item.isCheck).length
    },
    
    // 确认选择
    sureNum() {
      if (this.checkAllNum > this.maxNum) {
        uni.showToast({
          title: '选择人数超出',
          icon: 'error',
          duration: 2000
        })
        return
      }
      
      if (this.checkAllNum < this.maxNum) {
        uni.showToast({
          title: '选择人数不足',
          icon: 'error',
          duration: 2000
        })
        return
      }
      
      const selectedContacts = this.linkList.filter(item => item.isCheck)
      
      // 年龄限制检查
      if (this.type === 'kcyy_link' && this.age && this.age !== '0') {
        if (!this.checkAgeRestriction(selectedContacts)) {
          return
        }
      } else if (this.type === 'rgyy_link') {
        if (!this.checkVenueRestriction(selectedContacts)) {
          return
        }
      } else if (this.type === 'gyyy_link') {
        if (!this.checkMovieRestriction(selectedContacts)) {
          return
        }
      }
      
      // 保存选中的联系人到本地存储
      uni.setStorageSync(this.type, JSON.stringify(selectedContacts))
      uni.navigateBack()
    },
    
    // 检查年龄限制
    checkAgeRestriction(contacts) {
      for (let contact of contacts) {
        const ageRanges = this.age.split('-')
        
        if (ageRanges.length === 1) {
          const ageStr = ageRanges[0].replace(/岁|以|上/g, '')
          
          if (ageRanges[0].includes('以上')) {
            if (contact.linkmanAge < parseInt(ageStr)) {
              uni.showToast({
                title: `选择人员年龄小于限制【${this.age}】`,
                icon: 'error',
                duration: 2000
              })
              return false
            }
          } else if (contact.linkmanAge !== parseInt(ageStr)) {
            uni.showToast({
              title: `选择人员年龄不等于限制【${this.age}】`,
              icon: 'error',
              duration: 2000
            })
            return false
          }
        } else if (ageRanges.length === 2) {
          const minAge = parseInt(ageRanges[0].replace(/岁|以|上/g, ''))
          const maxAge = parseInt(ageRanges[1].replace(/岁|以|上/g, ''))
          
          if (contact.linkmanAge < minAge) {
            uni.showToast({
              title: `选择人员年龄小于限制【${this.age}】`,
              icon: 'error',
              duration: 2000
            })
            return false
          }
          
          if (contact.linkmanAge > maxAge) {
            uni.showToast({
              title: `选择人员年龄大于限制【${this.age}】`,
              icon: 'error',
              duration: 2000
            })
            return false
          }
        }
      }
      return true
    },
    
    // 检查场馆预约限制
    checkVenueRestriction(contacts) {
      let under8Count = 0
      for (let contact of contacts) {
        if (contact.linkmanAge < 8) {
          under8Count++
        }
      }
      
      if (under8Count > 3) {
        uni.showToast({
          title: '8岁以下的人数不超过3人',
          icon: 'error',
          duration: 2000
        })
        return false
      }
      return true
    },
    
    // 检查观影预约限制
    checkMovieRestriction(contacts) {
      let under3Count = 0
      let under10Count = 0
      
      for (let contact of contacts) {
        if (contact.linkmanAge < 3) {
          under3Count++
        } else if (contact.linkmanAge < 10) {
          under10Count++
        }
      }
      
      if (under3Count > 0) {
        uni.showToast({
          title: '3岁以下限制预约',
          icon: 'error',
          duration: 2000
        })
        return false
      }
      
      if (under10Count > 2) {
        uni.showToast({
          title: '10岁以下限制预约2人',
          icon: 'error',
          duration: 2000
        })
        return false
      }
      
      return true
    },
    
    // 删除联系人
    deleteItem(index) {
      this.linkList.splice(index, 1)
    },
    
    // 编辑联系人
    editItem(id) {
      uni.navigateTo({
        url: `/pages_app/contacts/addcontact?id=${id}`
      })
    },
    
    // 触摸开始
    touchstart(e) {
      this.linkList.forEach(item => {
        if (item.isMove) {
          item.isMove = false
        }
      })
      this.startX = e.changedTouches[0].clientX
      this.startY = e.changedTouches[0].clientY
    },
    
    // 触摸移动
    touchmove(e, index) {
      const startX = this.startX
      const startY = this.startY
      const touchX = e.changedTouches[0].clientX
      const touchY = e.changedTouches[0].clientY
      const angle = this.angle({ x: startX, y: startY }, { x: touchX, y: touchY })
      
      if (Math.abs(angle) <= 30 && startX - touchX >= 30) {
        this.linkList[index].isMove = true
      }
    },
    
    // 计算角度
    angle(start, end) {
      const x = end.x - start.x
      const y = end.y - start.y
      return Math.atan(y / x) * 360 / Math.PI
    }
  }
}
</script>

<style scoped>
.contactContent {
  background-color: #f3f4f6;
  font-family: PingFang SC;
  height: 100vh;
  overflow: scroll;
  width: 100%;
}

.contactBox {
  box-sizing: border-box;
  height: auto;
  margin-bottom: 200rpx;
  padding: 29rpx 28rpx;
  width: 100%;
}

.addContact {
  background-color: #fff;
  border-radius: 10rpx;
  color: #5cb7ff;
  font-size: 35rpx;
  font-weight: 600;
  height: 87rpx;
  line-height: 87rpx;
  margin-bottom: 28rpx;
  text-align: center;
  width: 100%;
}

.add {
  font-size: 42rpx;
  margin-right: 5rpx;
}

.contactList {
  height: auto;
  overflow: hidden;
  width: 100%;
}

.contactItem {
  background-color: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  height: 158rpx;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 18rpx 28rpx;
  position: relative;
  width: 100%;
}

.left {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  width: auto;
}

.peopleName {
  color: #000;
  font-size: 29rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.peopleCard,
.peopleMablie {
  color: #888;
  font-size: 23rpx;
}

.peopleMablie text:first-child {
  display: inline-block;
  margin-right: 96rpx;
}

.right {
  align-items: center;
  display: flex;
  height: 100%;
  width: 38rpx;
}

.checkBtn {
  border: 2rpx solid #888;
  border-radius: 38rpx;
  box-sizing: border-box;
  font-weight: 700;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
  width: 38rpx;
}

.checkBtn.isCheck {
  background: #ffba38;
  border: none;
}

.checkBtn.isCheck::before {
  content: "✓";
  display: inline-block;
  color: #fff;
}

.handlePlace {
  color: #fff;
  display: flex;
  font-size: 28rpx;
  height: 100%;
  line-height: 158rpx;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  transition: all 0.3s;
  width: 0;
  z-index: 99;
}

.edit {
  background-color: #5cb7ff;
  height: 100%;
  width: 50%;
}

.del {
  background-color: #fc5531;
  height: 100%;
  width: 50%;
}

.handlePlace.isMove {
  width: 40%;
}

.sureChoose {
  align-items: center;
  background-color: #fff;
  bottom: 0;
  box-shadow: 20rpx 10rpx 20rpx 10rpx rgba(0, 0, 0, 0.4);
  box-sizing: border-box;
  display: flex;
  height: 150rpx;
  justify-content: space-between;
  left: 0;
  padding: 0 29rpx;
  position: fixed;
  width: 100%;
}

.showNum {
  color: #888;
  font-size: 27rpx;
}

.setNum {
  color: #ffba38;
  font-size: 31rpx;
}

.upBtn {
  background: #5cb7ff;
  border-radius: 10rpx;
  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);
  color: #fff;
  font-size: 35rpx;
  height: 77rpx;
  line-height: 77rpx;
  text-align: center;
  width: 362rpx;
}
</style>