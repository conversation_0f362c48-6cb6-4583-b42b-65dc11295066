import { signatureGenerate } from '../utils/signatureUtil.js'
import config from '../config.js'
import { request, baseUrl } from '../utils/request.js'
import { getToken } from '../utils/auth.js'

const appId = config.appId

// 处理图片URL
export function getImages(imagePath) {
  if (!imagePath || imagePath === '') {
    return ''
  }
  
  if (imagePath.startsWith('http://') || 
      imagePath.startsWith('https://') || 
      imagePath.startsWith('data:image/jpeg;base64,')) {
    return imagePath
  }
  
  return baseUrl + imagePath
}

// 兼容旧版本的请求方法
export function myRequest(options) {
  const token = getToken()
  
  // 显示加载提示
  if (!options.noLoadingFlag) {
    uni.showLoading({
      title: '正在加载中...'
    })
  }
  
  return new Promise((resolve, reject) => {
    const requestOptions = {
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      timeout: options.timeout || 10000
    }
    
    // 处理请求头
    if (!options.url.includes(`/wx/user/${appId}/login`)) {
      const { signature, timestamp } = signatureGenerate(requestOptions)
      requestOptions.headers = {
        Authorization: 'wx ' + token,
        sign: signature,
        timestamp: timestamp
      }
    }
    
    // 使用新的request方法
    request(requestOptions)
      .then(response => {
        resolve(response)
      })
      .catch(error => {
        console.error('请求失败:', error)
        uni.showToast({
          title: '请求接口失败！',
          icon: 'error'
        })
        reject(error)
      })
      .finally(() => {
        uni.hideLoading()
      })
  })
}

// 预约相关API
export function getVenueList() {
  return request({
    url: '/apitp/venue/list',
    method: 'GET'
  })
}

export function createReservation(reservationData) {
  return request({
    url: '/apitp/reservation/create',
    method: 'POST',
    data: reservationData
  })
}

export function getReservationList(params) {
  return request({
    url: '/apitp/reservation/list',
    method: 'GET',
    params
  })
}

export function cancelReservation(reservationId) {
  return request({
    url: `/apitp/reservation/cancel/${reservationId}`,
    method: 'PUT'
  })
}

// 公告相关API
export function getAnnouncementInfo() {
  return request({
    url: '/apitp/announcement/getInfo',
    method: 'GET'
  })
}

// 电影相关API
export function getMovieList(params) {
  return request({
    url: '/apitp/movie/list',
    method: 'GET',
    params
  })
}

export function getMovieDetail(movieId) {
  return request({
    url: `/apitp/movie/detail/${movieId}`,
    method: 'GET'
  })
}

// 课程相关API
export function getCourseList(params) {
  return request({
    url: '/apitp/course/list',
    method: 'GET',
    params
  })
}

export function getCourseDetail(courseId) {
  return request({
    url: `/apitp/course/detail/${courseId}`,
    method: 'GET'
  })
}

// 联系人相关API
export function getContactList() {
  return myRequest({
    url: '/auth/linkman/list'
  })
}

export function getContactDetail(contactId) {
  return myRequest({
    url: `/auth/linkman/${contactId}`,
    method: 'GET'
  })
}

export function addContact(contactData) {
  return myRequest({
    url: '/auth/linkman/add',
    method: 'POST',
    data: contactData
  })
}

export function updateContact(contactData) {
  return myRequest({
    url: '/auth/linkman/edit',
    method: 'PUT',
    data: contactData
  })
}

export function deleteContact(contactId) {
  return myRequest({
    url: `/auth/linkman/${contactId}`,
    method: 'DELETE'
  })
}

// 二维码相关API
export function generateQRCode(reservationId) {
  return request({
    url: `/apitp/qrcode/generate/${reservationId}`,
    method: 'GET'
  })
}

// 用户相关API
export function updateUserInfo(userInfo) {
  return request({
    url: '/apitp/user/update',
    method: 'PUT',
    data: userInfo
  })
}

export function getUserReservations(params) {
  return request({
    url: '/apitp/user/reservations',
    method: 'GET',
    params
  })
}