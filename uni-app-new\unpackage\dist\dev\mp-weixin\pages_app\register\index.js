"use strict";
const common_vendor = require("../../common/vendor.js");
const api_login = require("../../api/login.js");
const api_common = require("../../api/common.js");
const common_assets = require("../../common/assets.js");
const MyHeader = () => "../../components/my-header/my-header.js";
const _sfc_main = {
  components: {
    MyHeader
  },
  data() {
    return {
      // 验证码相关
      codeUrl: "",
      captchaEnabled: true,
      countdown: 0,
      timer: null,
      // 注册状态
      isRegistering: false,
      agreeToTerms: false,
      // 表单数据
      registerForm: {
        username: "",
        phone: "",
        password: "",
        confirmPassword: "",
        smsCode: "",
        captcha: "",
        uuid: ""
      },
      // 表单验证错误
      errors: {
        username: "",
        phone: "",
        password: "",
        confirmPassword: "",
        smsCode: "",
        captcha: ""
      }
    };
  },
  computed: {
    // 是否可以发送短信验证码
    canSendSms() {
      return this.isValidPhone(this.registerForm.phone) && this.countdown === 0;
    },
    // 是否可以注册
    canRegister() {
      return this.agreeToTerms && !this.isRegistering && this.registerForm.username && this.registerForm.phone && this.registerForm.password && this.registerForm.confirmPassword && this.registerForm.smsCode && (!this.captchaEnabled || this.registerForm.captcha) && !Object.values(this.errors).some((error) => error);
    }
  },
  created() {
    this.getCaptcha();
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: {
    // 获取图形验证码
    async getCaptcha() {
      try {
        const res = await api_login.getCodeImg();
        if (res.code === 200) {
          this.captchaEnabled = res.captchaEnabled !== false;
          if (this.captchaEnabled) {
            this.codeUrl = "data:image/gif;base64," + res.img;
            this.registerForm.uuid = res.uuid;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/register/index.vue:267", "获取验证码失败:", error);
      }
    },
    // 刷新图形验证码
    refreshCaptcha() {
      this.registerForm.captcha = "";
      this.errors.captcha = "";
      this.getCaptcha();
    },
    // 发送短信验证码
    async sendSmsCode() {
      if (!this.canSendSms)
        return;
      try {
        common_vendor.index.showLoading({ title: "发送中..." });
        const res = await api_common.sendSmsCode(this.registerForm.phone, "register");
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "验证码已发送",
            icon: "success"
          });
          this.startCountdown();
        } else {
          common_vendor.index.showToast({
            title: res.msg || "发送失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "发送失败，请重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 开始倒计时
    startCountdown() {
      this.countdown = 60;
      this.timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1e3);
    },
    // 表单验证方法
    validateUsername() {
      const username = this.registerForm.username.trim();
      if (!username) {
        this.errors.username = "请输入用户名";
      } else if (username.length < 3) {
        this.errors.username = "用户名至少3个字符";
      } else if (username.length > 20) {
        this.errors.username = "用户名不能超过20个字符";
      } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
        this.errors.username = "用户名只能包含字母、数字、下划线和中文";
      } else {
        this.errors.username = "";
      }
    },
    validatePhone() {
      const phone = this.registerForm.phone.trim();
      if (!phone) {
        this.errors.phone = "请输入手机号";
      } else if (!this.isValidPhone(phone)) {
        this.errors.phone = "请输入正确的手机号";
      } else {
        this.errors.phone = "";
      }
    },
    validatePassword() {
      const password = this.registerForm.password;
      if (!password) {
        this.errors.password = "请输入密码";
      } else if (password.length < 8) {
        this.errors.password = "密码至少8位";
      } else if (!/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).{8,}$/.test(password)) {
        this.errors.password = "密码必须包含字母、数字、特殊符号";
      } else {
        this.errors.password = "";
      }
    },
    validateConfirmPassword() {
      const confirmPassword = this.registerForm.confirmPassword;
      if (!confirmPassword) {
        this.errors.confirmPassword = "请确认密码";
      } else if (confirmPassword !== this.registerForm.password) {
        this.errors.confirmPassword = "两次输入的密码不一致";
      } else {
        this.errors.confirmPassword = "";
      }
    },
    // 验证手机号格式
    isValidPhone(phone) {
      return /^1[3-9]\d{9}$/.test(phone);
    },
    // 全面表单验证
    validateForm() {
      this.validateUsername();
      this.validatePhone();
      this.validatePassword();
      this.validateConfirmPassword();
      if (!this.registerForm.smsCode) {
        this.errors.smsCode = "请输入短信验证码";
      } else if (this.registerForm.smsCode.length !== 6) {
        this.errors.smsCode = "验证码应为6位数字";
      } else {
        this.errors.smsCode = "";
      }
      if (this.captchaEnabled) {
        if (!this.registerForm.captcha) {
          this.errors.captcha = "请输入图形验证码";
        } else if (this.registerForm.captcha.length !== 4) {
          this.errors.captcha = "验证码应为4位";
        } else {
          this.errors.captcha = "";
        }
      }
      return !Object.values(this.errors).some((error) => error);
    },
    // 用户协议变更
    onAgreementChange(e) {
      this.agreeToTerms = e.detail.value.length > 0;
    },
    // 显示用户协议
    async showUserAgreement() {
      try {
        const res = await api_common.getUserAgreement();
        if (res.code === 200) {
          common_vendor.index.showModal({
            title: "用户协议",
            content: res.data.content || "用户协议内容",
            showCancel: false,
            confirmText: "我知道了"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "获取协议失败",
          icon: "none"
        });
      }
    },
    // 显示隐私政策
    async showPrivacyPolicy() {
      try {
        const res = await api_common.getPrivacyPolicy();
        if (res.code === 200) {
          common_vendor.index.showModal({
            title: "隐私政策",
            content: res.data.content || "隐私政策内容",
            showCancel: false,
            confirmText: "我知道了"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "获取政策失败",
          icon: "none"
        });
      }
    },
    // 注册处理
    async handleRegister() {
      if (!this.validateForm()) {
        return;
      }
      if (!this.agreeToTerms) {
        common_vendor.index.showToast({
          title: "请先同意用户协议和隐私政策",
          icon: "none"
        });
        return;
      }
      this.isRegistering = true;
      try {
        const verifyRes = await api_common.verifySmsCode(
          this.registerForm.phone,
          this.registerForm.smsCode,
          "register"
        );
        if (verifyRes.code !== 200) {
          common_vendor.index.showToast({
            title: "短信验证码错误",
            icon: "none"
          });
          return;
        }
        const registerData = {
          username: this.registerForm.username.trim(),
          phone: this.registerForm.phone.trim(),
          password: this.registerForm.password,
          smsCode: this.registerForm.smsCode
        };
        if (this.captchaEnabled) {
          registerData.code = this.registerForm.captcha;
          registerData.uuid = this.registerForm.uuid;
        }
        const res = await api_login.registerWithPhone(registerData);
        if (res.code === 200) {
          common_vendor.index.showModal({
            title: "注册成功",
            content: `恭喜您，账号 ${this.registerForm.username} 注册成功！`,
            showCancel: false,
            confirmText: "立即登录",
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.goToLogin();
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "注册失败",
            icon: "none"
          });
          if (this.captchaEnabled) {
            this.refreshCaptcha();
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/register/index.vue:526", "注册失败:", error);
        common_vendor.index.showToast({
          title: "注册失败，请重试",
          icon: "none"
        });
        if (this.captchaEnabled) {
          this.refreshCaptcha();
        }
      } finally {
        this.isRegistering = false;
      }
    },
    // 跳转到登录页面
    goToLogin() {
      common_vendor.index.navigateTo({
        url: "/pages_app/login/index"
      });
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "用户注册",
      isBack: true,
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      color: "#ffffff"
    }),
    b: common_assets._imports_0,
    c: common_vendor.o((...args) => $options.validateUsername && $options.validateUsername(...args)),
    d: $data.registerForm.username,
    e: common_vendor.o(($event) => $data.registerForm.username = $event.detail.value),
    f: $data.errors.username
  }, $data.errors.username ? {
    g: common_vendor.t($data.errors.username)
  } : {}, {
    h: common_vendor.o((...args) => $options.validatePhone && $options.validatePhone(...args)),
    i: $data.registerForm.phone,
    j: common_vendor.o(($event) => $data.registerForm.phone = $event.detail.value),
    k: $data.errors.phone
  }, $data.errors.phone ? {
    l: common_vendor.t($data.errors.phone)
  } : {}, {
    m: $data.registerForm.smsCode,
    n: common_vendor.o(($event) => $data.registerForm.smsCode = $event.detail.value),
    o: common_vendor.t($data.countdown > 0 ? `${$data.countdown}s后重发` : "获取验证码"),
    p: !$options.canSendSms || $data.countdown > 0 ? 1 : "",
    q: !$options.canSendSms || $data.countdown > 0,
    r: common_vendor.o((...args) => $options.sendSmsCode && $options.sendSmsCode(...args)),
    s: $data.errors.smsCode
  }, $data.errors.smsCode ? {
    t: common_vendor.t($data.errors.smsCode)
  } : {}, {
    v: common_vendor.o((...args) => $options.validatePassword && $options.validatePassword(...args)),
    w: $data.registerForm.password,
    x: common_vendor.o(($event) => $data.registerForm.password = $event.detail.value),
    y: $data.errors.password
  }, $data.errors.password ? {
    z: common_vendor.t($data.errors.password)
  } : {}, {
    A: common_vendor.o((...args) => $options.validateConfirmPassword && $options.validateConfirmPassword(...args)),
    B: $data.registerForm.confirmPassword,
    C: common_vendor.o(($event) => $data.registerForm.confirmPassword = $event.detail.value),
    D: $data.errors.confirmPassword
  }, $data.errors.confirmPassword ? {
    E: common_vendor.t($data.errors.confirmPassword)
  } : {}, {
    F: $data.captchaEnabled
  }, $data.captchaEnabled ? common_vendor.e({
    G: $data.registerForm.captcha,
    H: common_vendor.o(($event) => $data.registerForm.captcha = $event.detail.value),
    I: $data.codeUrl
  }, $data.codeUrl ? {
    J: $data.codeUrl
  } : {}, {
    K: common_vendor.o((...args) => $options.refreshCaptcha && $options.refreshCaptcha(...args)),
    L: $data.errors.captcha
  }, $data.errors.captcha ? {
    M: common_vendor.t($data.errors.captcha)
  } : {}) : {}, {
    N: $data.agreeToTerms,
    O: common_vendor.o((...args) => $options.onAgreementChange && $options.onAgreementChange(...args)),
    P: common_vendor.o((...args) => $options.showUserAgreement && $options.showUserAgreement(...args)),
    Q: common_vendor.o((...args) => $options.showPrivacyPolicy && $options.showPrivacyPolicy(...args)),
    R: common_vendor.t($data.isRegistering ? "注册中..." : "立即注册"),
    S: !$options.canRegister ? 1 : "",
    T: !$options.canRegister,
    U: common_vendor.o((...args) => $options.handleRegister && $options.handleRegister(...args)),
    V: common_vendor.o((...args) => $options.goToLogin && $options.goToLogin(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-594d5248"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/register/index.js.map
