﻿/**
 * Bootstrap Table Croatian translation
 * Author: <PERSON> (<EMAIL>)
 * Author: <PERSON> (<EMAIL>)
 */
(function ($) {
    'use strict';

    $.fn.bootstrapTable.locales['hr-HR'] = {
        formatLoadingMessage: function () {
            return 'Molimo pričekajte ...';
        },
        formatRecordsPerPage: function (pageNumber) {
            return pageNumber + ' broj zapisa po stranici';
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            return 'Prikazujem ' + pageFrom + '. - ' + pageTo + '. od ukupnog broja zapisa ' + totalRows;
        },
        formatSearch: function () {
            return 'Pretraži';
        },
        formatNoMatches: function () {
            return 'Nije pronađen niti jedan zapis';
        },
        formatPaginationSwitch: function () {
            return 'Prikaži/sakrij stranice';
        },
        formatRefresh: function () {
            return 'Osv<PERSON><PERSON><PERSON>';
        },
        formatToggle: function () {
            return 'Promijeni prikaz';
        },
        formatColumns: function () {
            return 'Kolone';
        },
        formatAllRows: function () {
            return 'Sve';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['hr-HR']);

})(jQuery);
