<script src="/assets/libs/xlsx/xlsx.full.min.js"></script>
<form id="import-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
  <div class="form-group">

    <div class="col-xs-12">
      <button id="btn-choose-file" class="btn btn-primary btn-sm" type="button">选择文件</button>
      <a class="btn btn-default btn-sm" href="/assets/课程模板.xlsx" download>下载模板</a>
      <input type="file" id="excelFile" accept=".xlsx" style="display: none;">
    </div>
  </div>

  <div id="preview-list" style="padding: 10px 0;"></div>

  <!-- 隐藏字段区域 -->
  <div id="import-hidden-fields"></div>

  <div class="form-group">
    <div class="col-xs-12 text-center">
      <button type="submit" class="btn btn-success">确定导入</button>
    </div>
  </div>
</form>