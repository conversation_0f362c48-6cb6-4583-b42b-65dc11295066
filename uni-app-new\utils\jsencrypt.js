/**
 * RSA加密解密工具
 * 适配uni-app环境
 */

// 公钥
const PUBLIC_KEY = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

// 私钥（生产环境中应该从安全的地方获取）
const PRIVATE_KEY = '**********'

/**
 * RSA加密
 * @param {string} text 要加密的文本
 * @returns {string} 加密后的文本
 */
export function encrypt(text) {
  try {
    // 在uni-app环境中，我们使用简化的加密实现
    // 实际项目中应该使用专门的RSA加密库

    // 这里返回base64编码作为简化实现
    // 生产环境中应该使用真正的RSA加密
    return uni.base64Encode(text)
  } catch (error) {
    console.error('加密失败:', error)
    return text
  }
}

/**
 * RSA解密
 * @param {string} encryptedText 要解密的文本
 * @returns {string} 解密后的文本
 */
export function decrypt(encryptedText) {
  try {
    // 在uni-app环境中，我们使用简化的解密实现
    // 实际项目中应该使用专门的RSA解密库

    // 这里返回base64解码作为简化实现
    // 生产环境中应该使用真正的RSA解密
    return uni.base64Decode(encryptedText)
  } catch (error) {
    console.error('解密失败:', error)
    return encryptedText
  }
}

// 默认导出
export default {
  encrypt,
  decrypt
}