<template>
  <view class="user">
    <!-- 自定义头部 -->
    <my-header
      v-if="!config.iSzgm"
      title="个人中心"
      :isBack="false"
      :isShowHome="false"
      background="transparent"
    />

    <view class="user_box">
      <!-- 用户信息 -->
      <view class="user_info">
        <view class="user_info_avatar">
          <image
            :src="userInfo.avatar || '/static/img/user/default-avatar.png'"
            mode="aspectFill"
          />
        </view>
        <view class="user_info_text">
          <view class="user_info_name">{{ userInfo.nickName || '未设置昵称' }}</view>
          <view class="user_info_phone">
            <image src="/static/img/user/phone.png" mode="aspectFit" />
            <text>{{ userInfo.phonenumber || '未绑定手机' }}</text>
          </view>
        </view>
        <view class="user_info_out" @tap="logout">
          退出登录
        </view>
      </view>

      <!-- 联系人管理 -->
      <view class="contacts_btn" @tap="goToContacts">
        <image src="/static/img/user/contacts.png" mode="aspectFit" class="contacts_image" />
        <text>联系人管理</text>
        <image src="/static/img/user/back.png" mode="aspectFit" class="forward_icon" />
      </view>

      <!-- 常用功能 -->
      <view class="my_order">
        <view class="my_order_title">常用功能</view>
        <view class="my_order_btns">
          <view
            v-for="(item, index) in functionList"
            :key="index"
            class="my_btn"
            @tap="handleFunctionClick(item)"
          >
            <view class="btn_icon_box">
              <image :src="item.icon" mode="aspectFit" />
              <view v-if="item.showBadge" :class="['badge', item.badgeClass]">
                {{ item.badgeText }}
              </view>
            </view>
            <text>{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- 我的预约 -->
      <view class="my_order">
        <view class="my_order_title">我的预约</view>
        <view class="my_order_btns">
          <view
            v-for="(item, index) in bookingList"
            :key="index"
            class="my_btn"
            @tap="handleBookingClick(item)"
          >
            <view class="btn_icon_box">
              <image :src="item.icon" mode="aspectFit" />
              <view v-if="item.showBadge" :class="['badge', item.badgeClass]">
                {{ item.badgeText }}
              </view>
            </view>
            <text>{{ item.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserIndex',
  data() {
    return {
      config: {
        iSzgm: false // 根据实际配置设置
      },
      userInfo: {
        avatar: '',
        nickName: '',
        sex: '',
        phonenumber: ''
      },
      allFunctionList: [
        {
          icon: '/static/img/user/app_getbalance.png',
          name: '管理员码',
          path: '/pages_app/user/oneqrcode',
          key: 'code',
          showBadge: false,
          badgeText: '',
          badgeClass: '',
          platforms: ['all'] // 所有平台都显示
        },
        {
          icon: '/static/img/user/app_getbalance.png',
          name: '订阅通知',
          path: 'noticeSubcribe',
          key: 'notice',
          showBadge: false,
          badgeText: '',
          badgeClass: '',
          platforms: ['mp-weixin'] // 仅微信小程序显示
        }
      ],
      bookingList: [
        {
          icon: '/static/img/user/venue.png',
          name: '参观预约',
          path: '/pages_app/user/venuescheme',
          key: 'venue',
          showBadge: false,
          badgeText: '',
          badgeClass: 'badge-blue'
        },
        {
          icon: '/static/img/user/film.png',
          name: '观影预约',
          path: '/pages_app/user/filmscheme',
          key: 'film',
          showBadge: false,
          badgeText: '',
          badgeClass: 'badge-orange'
        },
        {
          icon: '/static/img/user/course.png',
          name: '课程预约',
          path: '/pages_app/user/curriculumscheme',
          key: 'course',
          showBadge: false,
          badgeText: '',
          badgeClass: 'badge-purple'
        }
      ]
    }
  },

  computed: {
    // 根据当前平台过滤功能列表
    functionList() {
      return this.allFunctionList.filter(item => {
        if (item.platforms.includes('all')) {
          return true
        }
        // #ifdef MP-WEIXIN
        return item.platforms.includes('mp-weixin')
        // #endif
        // #ifndef MP-WEIXIN
        return !item.platforms.includes('mp-weixin')
        // #endif
      })
    }
  },

  onShow() {
    this.getUserInfo()
    this.updateBadges()
  },

  methods: {
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await this.$myRequest({
          url: '/web/common/getUserInfo',
          method: 'get'
        })

        if (res.code === 200) {
          this.userInfo = res.data.data || {}
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    // 更新徽章数量
    async updateBadges() {
      try {
        // 这里可以添加获取各种预约数量的逻辑
        // 例如获取待使用的预约数量等

        // 示例：获取待处理的预约数量
        // const venueCount = await this.getVenueBookingCount()
        // const filmCount = await this.getFilmBookingCount()
        // const courseCount = await this.getCourseBookingCount()

        // 更新徽章显示
        // this.updateBookingBadge('venue', venueCount)
        // this.updateBookingBadge('film', filmCount)
        // this.updateBookingBadge('course', courseCount)
      } catch (error) {
        console.error('更新徽章失败:', error)
      }
    },

    // 更新预约徽章
    updateBookingBadge(key, count) {
      const item = this.bookingList.find(item => item.key === key)
      if (item && count > 0) {
        item.showBadge = true
        item.badgeText = count > 99 ? '99+' : count.toString()
      } else if (item) {
        item.showBadge = false
      }
    },

    // 处理功能点击
    handleFunctionClick(item) {
      if (item.path === 'noticeSubcribe') {
        // 处理订阅通知
        this.handleNoticeSubscribe()
      } else if (item.path) {
        uni.navigateTo({
          url: item.path
        })
      }
    },

    // 处理订阅通知
    handleNoticeSubscribe() {
      // 微信小程序订阅消息模板ID
      const tmplIds = [
        'sWn1mSByjsKEuiD-QOg48VlKvbjhcp_XfZpUmMJjt5g',
        '4FDnApuDczeYIFSYDNGBw9FWwZG3Fr6J6Sq8PqWE6j0'
      ]

      // #ifdef MP-WEIXIN
      uni.requestSubscribeMessage({
        tmplIds: tmplIds,
        success: (res) => {
          if (res[tmplIds[0]] === 'accept' || res[tmplIds[1]] === 'accept') {
            uni.showToast({
              title: '订阅成功',
              icon: 'success',
              duration: 2000
            })
          }
        },
        fail: () => {
          uni.showToast({
            title: '订阅失败',
            icon: 'error',
            duration: 2000
          })
        }
      })
      // #endif

      // #ifndef MP-WEIXIN
      uni.showToast({
        title: '该功能仅在微信小程序中可用',
        icon: 'none',
        duration: 2000
      })
      // #endif
    },

    // 处理预约点击
    handleBookingClick(item) {
      if (item.path) {
        uni.navigateTo({
          url: item.path
        })
      }
    },

    // 跳转到联系人管理
    goToContacts() {
      uni.navigateTo({
        url: '/pages_app/contacts/index'
      })
    },

    // 退出登录
    logout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            this.performLogout()
          }
        }
      })
    },

    // 执行退出登录
    async performLogout() {
      try {
        uni.showLoading({
          title: '退出中...'
        })

        // 调用退出登录接口
        await this.$myRequest({
          url: '/auth/logout',
          method: 'post'
        })

        // 清除本地存储
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')

        uni.hideLoading()

        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index'
        })
      } catch (error) {
        uni.hideLoading()
        console.error('退出登录失败:', error)
        uni.showToast({
          title: '退出失败',
          icon: 'error'
        })
      }
    }
  }
}
</script>

<style lang="scss">
/* 覆盖全局页面背景色 */
page {
  background-color: transparent !important;
}

/* 确保状态栏不遮挡背景 */
.uni-page-head {
  background: transparent !important;
}

/* 头部组件样式调整 */
.user :deep(.my-header) {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background: transparent !important;
}

/* 设置用户页面背景 */
.user {
  background: url(data:image/png;base64,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) no-repeat top #f3f4f6;
  background-size: 100% auto;
  font-family: PingFang SC;
  height: auto;
  min-height: 100vh;
  width: 100%;
}
</style>

<style lang="scss" scoped>
.user {
  .user_box {
    background: transparent;
    box-sizing: border-box;
    height: 100%;
    padding: 160rpx 28rpx 0;

    // 用户信息
    .user_info {
      align-items: center;
      background: transparent;
      color: #fff;
      display: flex;
      padding-left: 26rpx;
      position: relative;
      margin-top: 40rpx;
      z-index: 2;

      .user_info_avatar {
        height: 120rpx;
        width: 120rpx;

        image {
          border: 5rpx solid #fff;
          border-radius: 50%;
          height: 100%;
          width: 100%;
        }
      }

      .user_info_text {
        display: flex;
        flex-direction: column;
        height: 120rpx;
        justify-content: space-around;
        margin-left: 30rpx;

        .user_info_name {
          font-size: 15px;
          font-weight: 600;
        }

        .user_info_phone {
          align-items: center;
          background-color: #fff;
          border-radius: 38rpx;
          display: flex;
          height: 44rpx;
          justify-content: center;
          width: 220rpx;

          image {
            height: 20rpx;
            width: 12rpx;
          }

          text {
            color: #888;
            font-family: PingFang SC;
            font-size: 24rpx;
            margin-left: 10rpx;
          }
        }
      }

      .user_info_out {
        background: #07c160;
        border-radius: 8rpx;
        bottom: 0;
        height: 50rpx;
        line-height: 50rpx;
        position: absolute;
        right: 0;
        text-align: center;
        width: 160rpx;
      }
    }

    // 联系人管理
    .contacts_btn {
      align-items: center;
      background-color: #fff;
      border-radius: 10rpx;
      display: flex;
      height: 86rpx;
      margin-top: 38rpx;
      position: relative;
      width: 100%;
      z-index: 1;

      .contacts_image {
        height: 44rpx;
        margin-left: 28rpx;
        width: 44rpx;
      }

      text {
        color: #000;
        font-family: PingFang SC;
        font-size: 27rpx;
        font-weight: 600;
        margin-left: 26rpx;
      }

      .forward_icon {
        height: 24rpx;
        position: absolute;
        right: 32rpx;
        width: 12rpx;
      }
    }

    // 功能区域和预约区域
    .my_order {
      background-color: #fff;
      border-radius: 10rpx;
      box-sizing: border-box;
      height: 250rpx;
      margin-top: 20rpx;
      padding: 20rpx;
      width: 100%;
      z-index: 1;
      position: relative;

      .my_order_title {
        color: #000;
        font-family: PingFang SC;
        font-size: 34rpx;
        font-weight: 600;
      }

      .my_order_btns {
        display: flex;
        height: 120rpx;
        justify-content: space-between;
        margin-top: 28rpx;
        padding: 0 20rpx;

        .my_btn {
          position: relative;

          .btn_icon_box {
            align-items: center;
            border-radius: 50%;
            display: flex;
            height: 76rpx;
            justify-content: center;
            margin: 0 auto 5rpx;
            position: relative;
            width: 76rpx;

            image {
              height: 100%;
              width: 100%;
            }

            .badge {
              background: #d51d1d;
              border-radius: 50%;
              box-sizing: border-box;
              color: #fff;
              font-size: 27rpx;
              left: 58rpx;
              padding: 0 12rpx;
              position: absolute;
              top: -4rpx;

              &.overflowOneLength {
                border-radius: 19rpx;
              }
            }
          }

          text {
            color: #000;
            font-family: PingFang SC;
            font-size: 26rpx;
            font-weight: 600;
          }
        }
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.user {
  background-attachment: scroll;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.user_box {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */

/* 修复背景样式 */
.user {
  background: linear-gradient(180deg, #4A90E2 0%, #357ABD 50%, #f3f4f6 100%) !important;
}
</style>