
.contactContent.data-v-e45255a1 {
  background-color: #f3f4f6;
  font-family: PingFang SC;
  height: 100vh;
  overflow: scroll;
  width: 100%;
}
.contactBox.data-v-e45255a1 {
  box-sizing: border-box;
  height: auto;
  margin-bottom: 200rpx;
  padding: 29rpx 28rpx;
  width: 100%;
}
.addContact.data-v-e45255a1 {
  background-color: #fff;
  border-radius: 10rpx;
  color: #5cb7ff;
  font-size: 35rpx;
  font-weight: 600;
  height: 87rpx;
  line-height: 87rpx;
  margin-bottom: 28rpx;
  text-align: center;
  width: 100%;
}
.add.data-v-e45255a1 {
  font-size: 42rpx;
  margin-right: 5rpx;
}
.contactList.data-v-e45255a1 {
  height: auto;
  overflow: hidden;
  width: 100%;
}
.contactItem.data-v-e45255a1 {
  background-color: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  height: 158rpx;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 18rpx 28rpx;
  position: relative;
  width: 100%;
}
.left.data-v-e45255a1 {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  width: auto;
}
.peopleName.data-v-e45255a1 {
  color: #000;
  font-size: 29rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}
.peopleCard.data-v-e45255a1,
.peopleMablie.data-v-e45255a1 {
  color: #888;
  font-size: 23rpx;
}
.peopleMablie text.data-v-e45255a1:first-child {
  display: inline-block;
  margin-right: 96rpx;
}
.right.data-v-e45255a1 {
  align-items: center;
  display: flex;
  height: 100%;
  width: 38rpx;
}
.checkBtn.data-v-e45255a1 {
  border: 2rpx solid #888;
  border-radius: 38rpx;
  box-sizing: border-box;
  font-weight: 700;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
  width: 38rpx;
}
.checkBtn.isCheck.data-v-e45255a1 {
  background: #ffba38;
  border: none;
}
.checkBtn.isCheck.data-v-e45255a1::before {
  content: "✓";
  display: inline-block;
  color: #fff;
}
.handlePlace.data-v-e45255a1 {
  color: #fff;
  display: flex;
  font-size: 28rpx;
  height: 100%;
  line-height: 158rpx;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  transition: all 0.3s;
  width: 0;
  z-index: 99;
}
.edit.data-v-e45255a1 {
  background-color: #5cb7ff;
  height: 100%;
  width: 50%;
}
.del.data-v-e45255a1 {
  background-color: #fc5531;
  height: 100%;
  width: 50%;
}
.handlePlace.isMove.data-v-e45255a1 {
  width: 40%;
}
.sureChoose.data-v-e45255a1 {
  align-items: center;
  background-color: #fff;
  bottom: 0;
  box-shadow: 20rpx 10rpx 20rpx 10rpx rgba(0, 0, 0, 0.4);
  box-sizing: border-box;
  display: flex;
  height: 150rpx;
  justify-content: space-between;
  left: 0;
  padding: 0 29rpx;
  position: fixed;
  width: 100%;
}
.showNum.data-v-e45255a1 {
  color: #888;
  font-size: 27rpx;
}
.setNum.data-v-e45255a1 {
  color: #ffba38;
  font-size: 31rpx;
}
.upBtn.data-v-e45255a1 {
  background: #5cb7ff;
  border-radius: 10rpx;
  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);
  color: #fff;
  font-size: 35rpx;
  height: 77rpx;
  line-height: 77rpx;
  text-align: center;
  width: 362rpx;
}
