<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 场馆管理
 *
 * @icon fa fa-circle-o
 */
class Venue extends Backend
{

    /**
     * Venue模型对象
     * @var \app\admin\model\Venue
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Venue;
    }

    /**
     * 获取指定日期的场馆数据（含闭馆日判断）
     * GET /admin/venue/getDetail?date=2025-07-25
     */

public function getDetail()
{
    $dateStr = $this->request->get('date'); // 示例: 2025-07-25
    if (!$dateStr) {
        return json(['code' => 0, 'msg' => '缺少参数: date']);
    }
    // 强制标准化为 YYYY-MM-DD 格式
  
    // ① 解析日期（只保留 yyyy-MM-dd）
    $inputDate = strtotime(date('Y-m-d', strtotime($dateStr)));
    $today = strtotime(date('Y-m-d'));


        // ② 如果是过去的日期，返回空数组
        if ($inputDate < $today) {
            return json(['code' => 1, 'msg' => '过去的日期', 'data' => []]);
        }

        // ③ 查询是否闭馆（holidays 表中 is_close = 0 表示闭馆）
        $holidays_model = new \app\common\model\Holidays;
        $isClose = $holidays_model
            ->whereRaw("DATE_FORMAT(holidays_date, '%Y-%m-%d') = :date", ['date' => $dateStr])
            ->where('is_close', 0)
            ->find();

        if ($isClose) {
            return json(['code' => 1, 'msg' => '闭馆', 'data' => []]);
        }

        // ④ 查询场次（venue 表中匹配 venue_start_time 日期）
        $result = $this->model
            ->field([
                'id',
                'venue_poll',
                'venue_start_time',
                'venue_end_time',
                'is_open',
                'week',
                'inventory_votes',
            ])
            ->whereRaw("DATE_FORMAT(venue_start_time, '%Y-%m-%d') = :date", ['date' => $dateStr])
            ->where('del_flag', 0)
            ->select();

        return json(['code' => 1, 'msg' => 'success2', 'data' => $result]);
    }

    public function changeStatus()
    {
        $id = $this->request->post('id');
        $status = $this->request->post('status');

        if (!$id || !in_array($status, ['0', '1'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $row =   $this->model->find($id);
        if (!$row) {
            return json(['code' => 0, 'msg' => '场次不存在']);
        }

        $row->is_open = $status;
        $row->save();

        return json(['code' => 1, 'msg' => '操作成功']);
    }

    public function updateTime()
    {
        $id = $this->request->post('id');
        $start = $this->request->post('start');
        $end = $this->request->post('end');

        if (!$id || !$start || !$end || $start >= $end) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        $row = \app\admin\model\Venue::find($id);
        if (!$row) {
            return json(['code' => 0, 'msg' => '场次不存在']);
        }

        $date = substr($row['venue_start_time'], 0, 10); // 获取原日期
        $row->venue_start_time = $date . ' ' . $start . ':00';
        $row->venue_end_time = $date . ' ' . $end . ':00';
        $row->save();

        return json(['code' => 1, 'msg' => '操作成功']);
    }

    /**
     * 获取某天的上午、下午场馆统计数据
     * @param Request $request
     * @return \think\Response
     */

    public function getstats()
    {
        $dateStr = $this->request->get("date"); // 格式：2025-07-27
        if (!$dateStr) {
            return json(['code' => 0, 'msg' => '缺少参数: date']);
        }

        // 转换为 yyyyMMdd 格式用于比较
        $date = date("Ymd", strtotime($dateStr));

        // 查询当天所有场次
        $list = \app\admin\model\Venue::alias('v')
            ->field('v.id, v.venue_start_time, v.venue_end_time, v.venue_poll, v.inventory_votes,
                (SELECT COUNT(*) FROM venue_subscribe vs WHERE vs.venue_id = v.id AND vs.del_flag = 0) AS register,
                (SELECT COUNT(*) FROM venue_subscribe vs WHERE vs.venue_id = v.id AND vs.del_flag = 0 AND vs.sign_state = 1) AS hadSign')
            ->whereRaw("DATE_FORMAT(v.venue_start_time, '%Y%m%d') = ?", [$date])
            ->select();

        $morning = ['flag' => '0', 'register' => 0, 'hadSign' => 0, 'venuePoll' => 0, 'inventoryVotes' => 0];
        $afternoon = ['flag' => '1', 'register' => 0, 'hadSign' => 0, 'venuePoll' => 0, 'inventoryVotes' => 0];

        foreach ($list as $item) {
            $hour = intval(date("H", strtotime($item['venue_start_time'])));
            if ($hour < 12) {
                $morning['venuePoll'] += $item['venue_poll'];
                $morning['inventoryVotes'] += $item['inventory_votes'];
                $morning['register'] += $item['register'];
                $morning['hadSign'] += $item['hadSign'];
            } else {
                $afternoon['venuePoll'] += $item['venue_poll'];
                $afternoon['inventoryVotes'] += $item['inventory_votes'];
                $afternoon['register'] += $item['register'];
                $afternoon['hadSign'] += $item['hadSign'];
            }
        }

        return json(['code' => 1, 'msg' => 'success', 'data' => [$morning, $afternoon]]);
    }
}
