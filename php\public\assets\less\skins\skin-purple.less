/*
 * Skin: Purple
 * ------------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

@sidebar-dark-bg: @purple;
@sidebar-dark-color: #c8c5ff;
@sidebar-dark-submenu-color: #c8c5ff;
@sidebar-dark-submenu-bg: darken(@sidebar-dark-bg, 3%);

.skin-purple {
    //Navbar
    .main-header {
        .navbar {
            .navbar-variant(#fff; #444; @purple; rgba(0, 0, 0, .02));

            @media (max-width: @screen-header-collapse) {
                .dropdown-menu {
                    li {
                        &.divider {
                            background-color: rgba(255, 255, 255, 0.1);
                        }

                        a {
                            color: #fff;

                            &:hover {
                                background: darken(@purple, 5%);
                            }
                        }
                    }
                }
            }
        }

        //Logo

        > .logo {
            .logo-variant(@purple; #fff);
            border-right: 1px solid @purple;
            box-shadow: none;
            @media (max-width: @screen-header-collapse) {
                .logo-variant(#fff; #222);
                border-right: none;
            }
        }

        li.user-header {
            background-color: @purple;
        }

        .nav-addtabs > li > a, .nav-addtabs > li.active > a {
            border-right-color: transparent;
        }

    }

    //Content Header
    .content-header {
        background: transparent;
    }

    //Create the sidebar skin
    .skin-dark-sidebar(#fff);

    .sidebar-menu > li {
        > a {
            border-left: 3px solid transparent;
            padding-left: 12px;
        }
    }

    @media (min-width: @screen-sm) {
        &.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
            margin-left: -3px;
        }
    }

    .sidebar-form input[type="text"] {
        .placeholder(#fff);
    }

    .sidebar-form input[type="text"], .sidebar-form .btn {
        color: #fff;
    }

    &.multiplenav {
        @media (max-width: @screen-header-collapse) {
            .main-header {
                .navbar {
                    .navbar-variant(@sidebar-dark-bg; #fff);
                }


                > .logo {
                    .logo-variant(@sidebar-dark-bg; #fff);
                }
            }

            .sidebar .mobilenav a.btn-app {
                background: lighten(@sidebar-dark-bg, 10%);
                color: #fff;

                &.active {
                    background: #fff;
                    color: lighten(@sidebar-dark-bg, 10%);
                }
            }
        }
    }
}
