{"id": "ws-wx-privacy", "displayName": "微信小程序隐私保护弹出框 隐私协议弹出框 隐私弹框", "version": "1.0.12", "description": "隐私协议弹出框，支持vue2和vue3，支持自定义文字和颜色。需要看广告？3毛钱赞赏即可跳过广告。", "keywords": ["微信小程序", "隐私保护", "隐私弹框", "隐私协议", "隐私"], "repository": "", "engines": {"HBuilderX": "^4.06"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uni-popup"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "n", "app-nvue": "n", "app-harmony": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "y", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}