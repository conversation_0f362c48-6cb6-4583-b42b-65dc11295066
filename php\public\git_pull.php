<?php
echo "<pre>";
// var_dump(opcache_reset()); // 清除PHP缓存

// 切换到上级目录
$baseDir = dirname(__DIR__); // 获取public的上级目录
chdir($baseDir); // PHP 直接切换工作目录

// 以下命令默认在上级目录下执行
print_r("\n\r" . '---------------git stash---------------' . "\n\r");
$a = system('LANG="zh_CN.UTF-8" git stash');

if (1) {
    print_r("\n\r" . '---------------git pull---------------' . "\n\r");
    $a = system('LANG="zh_CN.UTF-8" git pull');
} else {
    print_r("\n\r" . '---------------重做分支----------------' . "\n\r");
    $a = system('git fetch --all && git reset --hard origin/php_develop && git pull');
}

print_r("\n\r" . '---------------git stash apply---------------' . "\n\r");
$a = system('LANG="zh_CN.UTF-8" git stash apply');

print_r("\n\r" . '---------------git log ----------------' . "\n\r");
$a = system('git log | head -300');
