"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "FilmScheme",
  data() {
    return {
      filmList: [],
      filmTypeList: ["球幕电影", "4D电影"],
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      statusTextList: ["去签到", "已过期(未检票)", "已取消", "去检票", "已完成", "已过期(未签到)", "场次取消"],
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      // 安全区域
      safeAreaBottom: 0
    };
  },
  onLoad() {
    var _a;
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.safeAreaBottom = ((_a = systemInfo.safeAreaInsets) == null ? void 0 : _a.bottom) || 0;
  },
  onShow() {
    this.resetData();
    this.getFilmList();
  },
  methods: {
    // 重置数据
    resetData() {
      this.filmList = [];
      this.pageNum = 1;
      this.total = 0;
      this.hasMore = true;
    },
    // 获取观影记录列表
    async getFilmList() {
      if (this.loading || !this.hasMore)
        return;
      this.loading = true;
      try {
        const res = await this.$myRequest({
          url: "/web/fileSession/personalCenterFilm",
          method: "get",
          data: {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        });
        if (res.code === 200) {
          const dataList = res.data.data.rows || [];
          this.total = res.data.data.total || 0;
          const processedList = dataList.map((item) => ({
            ...item,
            filmStartTime: this.formatTime(item.filmStartTime),
            filmEndTime: this.formatTime(item.filmEndTime)
          }));
          if (this.pageNum === 1) {
            this.filmList = processedList;
          } else {
            this.filmList.push(...processedList);
          }
          this.hasMore = this.filmList.length < this.total;
        } else {
          throw new Error(res.msg || "获取记录失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/filmscheme.vue:178", "获取观影记录失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取记录失败",
          icon: "error"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.pageNum++;
      this.getFilmList();
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 获取星期
    getWeekDay(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr.replace(/-/g, "/"));
        return this.weekList[date.getDay()];
      } catch (error) {
        return "";
      }
    },
    // 获取状态文本
    getStatusText(state) {
      if (state >= 0 && state < this.statusTextList.length) {
        return this.statusTextList[state];
      }
      return "未知状态";
    },
    // 获取状态样式类
    getStatusClass(state) {
      const statusMap = {
        0: "status-pending",
        // 去签到
        1: "status-expired",
        // 已过期(未检票)
        2: "status-cancelled",
        // 已取消
        3: "status-checkin",
        // 去检票
        4: "status-completed",
        // 已完成
        5: "status-expired",
        // 已过期(未签到)
        6: "status-cancelled"
        // 场次取消
      };
      return statusMap[state] || "status-unknown";
    },
    // 是否显示操作按钮
    showActionButtons(state) {
      return state === 1 || state === 4 || this.canCancel(state);
    },
    // 是否可以取消
    canCancel(state) {
      return state === 1 || state === 4;
    },
    // 是否可以查看二维码
    canViewQRCode(state) {
      return state === 1 || state === 4;
    },
    // 取消预约
    cancelBooking(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/filmcancel?vote=${item.subscribeType}&batchNumber=${item.batchNumber}&filmSessionId=${item.filmSessionId}`
      });
    },
    // 查看二维码
    viewQRCode(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/filmsuccess?batchNumber=${item.batchNumber}&filmSessionId=${item.filmSessionId}`
      });
    },
    // 图片加载失败处理
    handleImageError(e) {
      common_vendor.index.__f__("warn", "at pages_app/user/filmscheme.vue:275", "图片加载失败:", e);
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "观影记录",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333"
    }),
    b: $data.filmList.length === 0 && !$data.loading
  }, $data.filmList.length === 0 && !$data.loading ? {
    c: common_assets._imports_0$2
  } : {}, {
    d: common_vendor.f($data.filmList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getStatusText(item.subscribeState)),
        b: common_vendor.n($options.getStatusClass(item.subscribeState)),
        c: item.filmCover,
        d: common_vendor.o((...args) => $options.handleImageError && $options.handleImageError(...args), item.filmSessionId || index),
        e: common_vendor.t(item.filmName),
        f: common_vendor.t($data.filmTypeList[item.filmType - 1] || "未知"),
        g: common_vendor.t(item.filmStartTime),
        h: common_vendor.t(item.filmEndTime),
        i: common_vendor.t(item.filmArrangedDate),
        j: common_vendor.t($options.getWeekDay(item.filmArrangedDate)),
        k: common_vendor.t(item.subscribeType),
        l: $options.showActionButtons(item.subscribeState)
      }, $options.showActionButtons(item.subscribeState) ? common_vendor.e({
        m: $options.canCancel(item.subscribeState)
      }, $options.canCancel(item.subscribeState) ? {
        n: common_vendor.o(($event) => $options.cancelBooking(item), item.filmSessionId || index)
      } : {}, {
        o: $options.canViewQRCode(item.subscribeState)
      }, $options.canViewQRCode(item.subscribeState) ? {
        p: common_vendor.o(($event) => $options.viewQRCode(item), item.filmSessionId || index)
      } : {}) : {}, {
        q: item.filmSessionId || index
      });
    }),
    e: $data.loading
  }, $data.loading ? {} : {}, {
    f: !$data.hasMore && $data.filmList.length > 0
  }, !$data.hasMore && $data.filmList.length > 0 ? {} : {}, {
    g: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    h: $data.safeAreaBottom + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bf630d19"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/user/filmscheme.js.map
