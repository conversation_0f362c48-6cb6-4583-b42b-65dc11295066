var e = require("../../../../@babel/runtime/helpers/defineProperty"),
  t = require("../../../../@babel/runtime/helpers/typeof"),
  n = require("../uni-data-pickerview/uni-data-picker.js"),
  i = require("../../../../common/vendor.js"),
  a = {
    name: "UniDataPicker",
    emits: ["popupopened", "popupclosed", "nodeclick", "input", "change", "update:modelValue", "inputclick"],
    mixins: [n.dataPicker],
    components: {
      DataPickerView: function() {
        return "../uni-data-pickerview/uni-data-pickerview.js"
      }
    },
    props: {
      options: {
        type: [Object, Array],
        default: function() {
          return {}
        }
      },
      popupTitle: {
        type: String,
        default: "请选择"
      },
      placeholder: {
        type: String,
        default: "请选择"
      },
      heightMobile: {
        type: String,
        default: ""
      },
      readonly: {
        type: Boolean,
        default: !1
      },
      clearIcon: {
        type: Boolean,
        default: !0
      },
      border: {
        type: Boolean,
        default: !0
      },
      split: {
        type: String,
        default: "/"
      },
      ellipsis: {
        type: Boolean,
        default: !0
      }
    },
    data: function() {
      return {
        isOpened: !1,
        inputSelected: []
      }
    },
    created: function() {
      var e = this;
      this.$nextTick((function() {
        e.load()
      }))
    },
    watch: {
      localdata: {
        handler: function() {
          this.load()
        },
        deep: !0
      }
    },
    methods: {
      clear: function() {
        this._dispatchEvent([])
      },
      onPropsChange: function() {
        this._treeData = [], this.selectedIndex = 0, this.load()
      },
      load: function() {
        var e = this;
        this.readonly ? this._processReadonly(this.localdata, this.dataValue) : this.isLocalData ? (this.loadData(), this.inputSelected = this.selected.slice(0)) : (this.isCloudDataList || this.isCloudDataTree) && (this.loading = !0, this.getCloudDataValue().then((function(t) {
          e.loading = !1, e.inputSelected = t
        })).catch((function(t) {
          e.loading = !1, e.errorMessage = t
        })))
      },
      show: function() {
        var e = this;
        this.isOpened = !0, setTimeout((function() {
          e.$refs.pickerView.updateData({
            treeData: e._treeData,
            selected: e.selected,
            selectedIndex: e.selectedIndex
          })
        }), 200), this.$emit("popupopened")
      },
      hide: function() {
        this.isOpened = !1, this.$emit("popupclosed")
      },
      handleInput: function() {
        this.readonly ? this.$emit("inputclick") : this.show()
      },
      handleClose: function(e) {
        this.hide()
      },
      onnodeclick: function(e) {
        this.$emit("nodeclick", e)
      },
      ondatachange: function(e) {
        this._treeData = this.$refs.pickerView._treeData
      },
      onchange: function(e) {
        var t = this;
        this.hide(), this.$nextTick((function() {
          t.inputSelected = e
        })), this._dispatchEvent(e)
      },
      _processReadonly: function(e, n) {
        var i;
        if (e.findIndex((function(e) {
            return e.children
          })) > -1) return Array.isArray(n) ? (i = n[n.length - 1], "object" == t(i) && i.value && (i = i.value)) : i = n, void(this.inputSelected = this._findNodePath(i, this.localdata));
        if (this.hasValue) {
          for (var a = [], o = 0; o < n.length; o++) {
            var l = n[o],
              r = e.find((function(e) {
                return e.value == l
              }));
            r && a.push(r)
          }
          a.length && (this.inputSelected = a)
        } else this.inputSelected = []
      },
      _filterForArray: function(e, t) {
        for (var n = [], i = 0; i < t.length; i++) {
          var a = t[i],
            o = e.find((function(e) {
              return e.value == a
            }));
          o && n.push(o)
        }
        return n
      },
      _dispatchEvent: function(e) {
        var t = {};
        if (e.length) {
          for (var n = new Array(e.length), i = 0; i < e.length; i++) n[i] = e[i].value;
          t = e[e.length - 1]
        } else t.value = "";
        this.formItem && this.formItem.setValue(t.value), this.$emit("input", t.value), this.$emit("update:modelValue", t.value), this.$emit("change", {
          detail: {
            value: e
          }
        })
      }
    }
  };
Array || (i.resolveComponent("uni-load-more") + i.resolveComponent("uni-icons") + i.resolveComponent("data-picker-view"))();
Math || (function() {
  return "../../../uni-load-more/components/uni-load-more/uni-load-more.js"
} + function() {
  return "../../../uni-icons/components/uni-icons/uni-icons.js"
})();
var o = i._export_sfc(a, [
  ["render", function(t, n, a, o, l, r) {
    return i.e({
      a: t.errorMessage
    }, t.errorMessage ? {
      b: i.t(t.errorMessage)
    } : t.loading && !l.isOpened ? {
      d: i.p({
        contentText: t.loadMore,
        status: "loading"
      })
    } : l.inputSelected.length ? {
      f: i.f(l.inputSelected, (function(e, t, n) {
        return i.e({
          a: i.t(e.text),
          b: t < l.inputSelected.length - 1
        }, t < l.inputSelected.length - 1 ? {
          c: i.t(a.split)
        } : {}, {
          d: t
        })
      }))
    } : {
      g: i.t(a.placeholder)
    }, {
      c: t.loading && !l.isOpened,
      e: l.inputSelected.length,
      h: a.clearIcon && !a.readonly && l.inputSelected.length
    }, a.clearIcon && !a.readonly && l.inputSelected.length ? {
      i: i.p({
        type: "clear",
        color: "#c0c4cc",
        size: "24"
      }),
      j: i.o((function() {
        return r.clear && r.clear.apply(r, arguments)
      }))
    } : {}, {
      k: !(a.clearIcon && l.inputSelected.length || a.readonly)
    }, (a.clearIcon && l.inputSelected.length || a.readonly, {}), {
      l: a.border ? 1 : "",
      m: i.r("d", {
        options: a.options,
        data: l.inputSelected,
        error: t.errorMessage
      }),
      n: i.o((function() {
        return r.handleInput && r.handleInput.apply(r, arguments)
      })),
      o: l.isOpened
    }, l.isOpened ? {
      p: i.o((function() {
        return r.handleClose && r.handleClose.apply(r, arguments)
      }))
    } : {}, {
      q: l.isOpened
    }, l.isOpened ? {
      r: i.t(a.popupTitle),
      s: i.o((function() {
        return r.handleClose && r.handleClose.apply(r, arguments)
      })),
      t: i.sr("pickerView", "2ff04fdd-2"),
      v: i.o(r.onchange),
      w: i.o(r.ondatachange),
      x: i.o(r.onnodeclick),
      y: i.o((function(e) {
        return t.dataValue = e
      })),
      z: i.p(e(e(e(e(e(e(e({
        localdata: t.localdata,
        preload: t.preload,
        collection: t.collection,
        field: t.field,
        orderby: t.orderby,
        where: t.where
      }, "step-searh", t.stepSearh), "self-field", t.selfField), "parent-field", t.parentField), "managed-mode", !0), "map", t.map), "ellipsis", a.ellipsis), "modelValue", t.dataValue))
    } : {})
  }]
]);
wx.createComponent(o);