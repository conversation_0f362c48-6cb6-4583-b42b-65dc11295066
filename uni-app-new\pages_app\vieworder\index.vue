<template>
  <view class="vieworder">
    <view class="content">
      <my-header
        :isBack="true"
        :isShowHome="true"
        title="观影预约"
        background="transparent"
      />

      <view class="movie_content">
        <view class="movie_list">
          <view
            class="movie_item"
            v-for="(item, index) in movieList"
            :key="item.id"
            @click="goToMovieDetail(item)"
          >
            <view class="movie_poster">
              <image
                :src="item.movieCover"
                mode="aspectFill"
                class="poster_image"
              />
            </view>
            <view class="movie_info">
              <view class="movie_title">{{ item.movieName }}</view>
              <view class="movie_type">{{ item.movieType }}</view>
              <view class="movie_duration">时长：{{ item.movieDuration }}分钟</view>
              <view class="movie_rating" v-if="item.movieRating">
                评分：{{ item.movieRating }}
              </view>
              <view class="movie_desc">{{ item.movieDesc }}</view>
            </view>
            <view class="movie_action">
              <view class="movie_status">{{ getMovieStatus(item) }}</view>
              <button
                class="reserve_btn"
                @click.stop="reserveMovie(item)"
                :disabled="!canReserve(item)"
              >
                {{ getReserveText(item) }}
              </button>
            </view>
          </view>
        </view>

        <view class="empty_state" v-if="movieList.length === 0 && !isLoading">
          <view class="empty_icon">🎬</view>
          <view class="empty_text">暂无电影信息</view>
        </view>

        <view class="loading_state" v-if="isLoading">
          <view class="loading_icon">⏳</view>
          <view class="loading_text">加载中...</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import MyHeader from '@/components/my-header/my-header.vue'

export default {
  name: 'ViewOrder',
  components: {
    MyHeader
  },
  data() {
    return {
      movieList: [],
      isLoading: false
    }
  },

  onLoad() {
    this.getMovieList()
  },

  onShow() {
    this.getMovieList()
  },

  onPullDownRefresh() {
    this.getMovieList().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    // 获取电影列表
    async getMovieList() {
      try {
        this.isLoading = true

        const res = await this.$myRequest({
          url: '/web/movie/getMovieList',
          method: 'get'
        })

        if (res.code === 200) {
          this.movieList = res.data.data || []
        } else {
          throw new Error(res.msg || '获取电影列表失败')
        }
      } catch (error) {
        console.error('获取电影列表失败:', error)
        uni.showToast({
          title: error.message || '获取电影列表失败',
          icon: 'error'
        })
      } finally {
        this.isLoading = false
      }
    },

    // 获取电影状态
    getMovieStatus(movie) {
      const now = new Date()
      const startTime = new Date(movie.movieStartTime)
      const endTime = new Date(movie.movieEndTime)

      if (now < startTime) {
        return '即将上映'
      } else if (now >= startTime && now <= endTime) {
        return '正在上映'
      } else {
        return '已下映'
      }
    },

    // 是否可以预约
    canReserve(movie) {
      const now = new Date()
      const startTime = new Date(movie.movieStartTime)
      const endTime = new Date(movie.movieEndTime)

      return now >= startTime && now <= endTime && movie.inventoryVotes > 0
    },

    // 获取预约按钮文字
    getReserveText(movie) {
      if (!this.canReserve(movie)) {
        return '不可预约'
      }
      return '立即预约'
    },

    // 跳转到电影详情
    goToMovieDetail(movie) {
      uni.navigateTo({
        url: `/pages_app/vieworder/filmdes?movieId=${movie.id}`
      })
    },

    // 预约电影
    reserveMovie(movie) {
      if (!this.canReserve(movie)) {
        uni.showToast({
          title: '该电影暂不可预约',
          icon: 'none'
        })
        return
      }

      this.goToMovieDetail(movie)
    }
  }
}
</script>

<style lang="scss" scoped>
.vieworder {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding-top: 120rpx; // 为头部留出空间

    .movie_content {
      padding: 20rpx;

      .movie_list {
        .movie_item {
          background: rgba(255, 255, 255, 0.95);
          border-radius: 20rpx;
          margin-bottom: 30rpx;
          padding: 30rpx;
          box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
          display: flex;
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.98);
          }

          .movie_poster {
            width: 120rpx;
            height: 160rpx;
            margin-right: 20rpx;
            border-radius: 12rpx;
            overflow: hidden;

            .poster_image {
              width: 100%;
              height: 100%;
            }
          }

          .movie_info {
            flex: 1;
            margin-right: 20rpx;

            .movie_title {
              font-size: 32rpx;
              font-weight: 600;
              color: #333;
              margin-bottom: 8rpx;
              line-height: 1.4;
            }

            .movie_type {
              font-size: 26rpx;
              color: #666;
              margin-bottom: 6rpx;
            }

            .movie_duration,
            .movie_rating {
              font-size: 24rpx;
              color: #999;
              margin-bottom: 4rpx;
            }

            .movie_desc {
              font-size: 24rpx;
              color: #666;
              line-height: 1.4;
              margin-top: 8rpx;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .movie_action {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            min-width: 120rpx;

            .movie_status {
              font-size: 24rpx;
              color: #666;
              margin-bottom: 20rpx;
              text-align: center;
            }

            .reserve_btn {
              width: 120rpx;
              height: 60rpx;
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              border-radius: 30rpx;
              font-size: 24rpx;
              border: none;

              &::after {
                border: none;
              }

              &:disabled {
                background: #ccc;
                color: #999;
              }
            }
          }
        }
      }

      .empty_state,
      .loading_state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 100rpx 40rpx;

        .empty_icon,
        .loading_icon {
          font-size: 80rpx;
          margin-bottom: 20rpx;
        }

        .empty_text,
        .loading_text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.vieworder {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>