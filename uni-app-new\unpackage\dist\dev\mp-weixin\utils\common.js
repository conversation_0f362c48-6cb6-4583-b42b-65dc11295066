"use strict";
const common_vendor = require("../common/vendor.js");
function tansParams(params) {
  if (!params || typeof params !== "object") {
    return "";
  }
  let result = "";
  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key];
      const encodedKey = encodeURIComponent(key) + "=";
      if (value !== null && value !== "" && value !== void 0) {
        if (typeof value === "object") {
          for (const subKey in value) {
            if (value.hasOwnProperty(subKey)) {
              const subValue = value[subKey];
              if (subValue !== null && subValue !== "" && subValue !== void 0) {
                result += encodeURIComponent(key + "[" + subKey + "]") + "=" + encodeURIComponent(subValue) + "&";
              }
            }
          }
        } else {
          result += encodedKey + encodeURIComponent(value) + "&";
        }
      }
    }
  }
  return result;
}
function toast(message, icon = "none") {
  common_vendor.index.showToast({
    icon,
    title: message,
    duration: 2e3
  });
}
function formatTime(time) {
  if (!time)
    return "";
  try {
    const date = new Date(time.replace(/-/g, "/"));
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    return `${hours}:${minutes}`;
  } catch (error) {
    common_vendor.index.__f__("warn", "at utils/common.js:142", "格式化时间失败:", error);
    return time;
  }
}
exports.formatTime = formatTime;
exports.tansParams = tansParams;
exports.toast = toast;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/common.js.map
