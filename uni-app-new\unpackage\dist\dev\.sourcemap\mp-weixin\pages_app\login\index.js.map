{"version": 3, "file": "index.js", "sources": ["pages_app/login/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGxvZ2luXGluZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"login-container\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      title=\"用户登录\" \r\n      :isBack=\"true\" \r\n      background=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\r\n      color=\"#ffffff\"\r\n    />\r\n    \r\n    <view class=\"login-content\">\r\n      <!-- Logo区域 -->\r\n      <view class=\"logo-section\">\r\n        <image class=\"logo\" src=\"/static/images/logo.png\" mode=\"aspectFit\" />\r\n        <text class=\"app-name\">宝安科技馆</text>\r\n        <text class=\"subtitle\">用户登录</text>\r\n      </view>\r\n      \r\n      <!-- 登录表单 -->\r\n      <view class=\"form-section\">\r\n        <!-- 账号输入 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-user\"></text>\r\n            </view>\r\n            <input\r\n              class=\"form-input\"\r\n              placeholder=\"请输入账号\"\r\n              type=\"text\"\r\n              v-model=\"loginForm.username\"\r\n              maxlength=\"30\"\r\n              @blur=\"validateUsername\"\r\n            />\r\n          </view>\r\n          <text v-if=\"errors.username\" class=\"error-text\">{{ errors.username }}</text>\r\n        </view>\r\n        \r\n        <!-- 密码输入 -->\r\n        <view class=\"input-group\">\r\n          <view class=\"input-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-password\"></text>\r\n            </view>\r\n            <input\r\n              class=\"form-input\"\r\n              placeholder=\"请输入密码\"\r\n              type=\"password\"\r\n              v-model=\"loginForm.password\"\r\n              maxlength=\"20\"\r\n              @blur=\"validatePassword\"\r\n            />\r\n          </view>\r\n          <text v-if=\"errors.password\" class=\"error-text\">{{ errors.password }}</text>\r\n        </view>\r\n\r\n        <!-- 验证码输入 -->\r\n        <view class=\"input-group\" v-if=\"captchaEnabled\">\r\n          <view class=\"input-wrapper captcha-wrapper\">\r\n            <view class=\"input-icon\">\r\n              <text class=\"icon-code\"></text>\r\n            </view>\r\n            <input\r\n              class=\"form-input captcha-input\"\r\n              placeholder=\"请输入验证码\"\r\n              type=\"number\"\r\n              v-model=\"loginForm.code\"\r\n              maxlength=\"4\"\r\n              @blur=\"validateCode\"\r\n            />\r\n            <view class=\"captcha-image\">\r\n              <image\r\n                class=\"captcha-img\"\r\n                :src=\"codeUrl\"\r\n                mode=\"aspectFit\"\r\n                v-if=\"codeUrl\"\r\n                @tap=\"getCode\"\r\n              />\r\n              <text v-else class=\"refresh-text\" @tap=\"getCode\">点击刷新</text>\r\n            </view>\r\n          </view>\r\n          <text v-if=\"errors.code\" class=\"error-text\">{{ errors.code }}</text>\r\n        </view>\r\n\r\n        <!-- 登录按钮 -->\r\n        <view class=\"button-group\">\r\n          <button\r\n            class=\"login-btn\"\r\n            @tap=\"handleLogin\"\r\n            :disabled=\"isLoading\"\r\n          >\r\n            {{ isLoading ? '登录中...' : '登录' }}\r\n          </button>\r\n        </view>\r\n\r\n        <!-- 注册链接 -->\r\n        <view class=\"register-link\">\r\n          <text class=\"link-text\">没有账号？</text>\r\n          <text class=\"link-button\" @tap=\"handleUserRegister\">立即注册</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { login, getCodeImg } from '@/api/login.js'\r\nimport { encrypt } from '@/utils/jsencrypt.js'\r\nimport config from '@/config.js'\r\nimport MyHeader from '@/components/my-header/my-header.vue'\r\n\r\nexport default {\r\n  name: 'Login',\r\n  components: {\r\n    MyHeader\r\n  },\r\n  data() {\r\n    return {\r\n      // 表单数据\r\n      loginForm: {\r\n        username: '',\r\n        password: '',\r\n        code: '',\r\n        uuid: ''\r\n      },\r\n\r\n      // 验证码相关\r\n      codeUrl: '',\r\n      captchaEnabled: true,\r\n\r\n      // 控制状态\r\n      register: true,\r\n      isLoading: false,\r\n      showWechatLogin: true,\r\n\r\n      // 表单验证错误\r\n      errors: {\r\n        username: '',\r\n        password: '',\r\n        code: ''\r\n      }\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.getCode()\r\n  },\r\n\r\n  methods: {\r\n    // 获取验证码\r\n    getCode() {\r\n      getCodeImg().then(res => {\r\n        if (res.code === 200) {\r\n          this.captchaEnabled = res.captchaEnabled !== false\r\n          if (this.captchaEnabled) {\r\n            this.codeUrl = 'data:image/gif;base64,' + res.img\r\n            this.loginForm.uuid = res.uuid\r\n          }\r\n        }\r\n      }).catch(err => {\r\n        console.error('获取验证码失败:', err)\r\n      })\r\n    },\r\n\r\n    // 表单验证\r\n    validateUsername() {\r\n      if (!this.loginForm.username.trim()) {\r\n        this.errors.username = '请输入账号'\r\n        return false\r\n      }\r\n      this.errors.username = ''\r\n      return true\r\n    },\r\n\r\n    validatePassword() {\r\n      if (!this.loginForm.password.trim()) {\r\n        this.errors.password = '请输入密码'\r\n        return false\r\n      }\r\n      this.errors.password = ''\r\n      return true\r\n    },\r\n\r\n    validateCode() {\r\n      if (this.captchaEnabled && !this.loginForm.code.trim()) {\r\n        this.errors.code = '请输入验证码'\r\n        return false\r\n      }\r\n      this.errors.code = ''\r\n      return true\r\n    },\r\n\r\n    // 表单整体验证\r\n    validateForm() {\r\n      const usernameValid = this.validateUsername()\r\n      const passwordValid = this.validatePassword()\r\n      const codeValid = this.validateCode()\r\n\r\n      return usernameValid && passwordValid && codeValid\r\n    },\r\n\r\n    // 处理登录\r\n    async handleLogin() {\r\n      if (!this.validateForm()) {\r\n        return\r\n      }\r\n\r\n      this.isLoading = true\r\n\r\n      try {\r\n        uni.showLoading({\r\n          title: '正在登录中'\r\n        })\r\n\r\n        await this.pwdLogin()\r\n\r\n      } catch (error) {\r\n        console.error('登录失败:', error)\r\n        uni.showToast({\r\n          title: '登录失败',\r\n          icon: 'error'\r\n        })\r\n\r\n        // 刷新验证码\r\n        if (this.captchaEnabled) {\r\n          this.getCode()\r\n        }\r\n      } finally {\r\n        this.isLoading = false\r\n        uni.hideLoading()\r\n      }\r\n    },\r\n\r\n    // 密码登录\r\n    async pwdLogin() {\r\n      const encryptedPassword = encrypt(this.loginForm.password)\r\n\r\n      const res = await login(\r\n        this.loginForm.username,\r\n        encryptedPassword,\r\n        this.loginForm.code,\r\n        this.loginForm.uuid\r\n      )\r\n\r\n      if (res.code === 200) {\r\n        uni.showToast({\r\n          title: '登录成功'\r\n        })\r\n\r\n        // 保存token\r\n        uni.setStorageSync('token', res.token)\r\n\r\n        // 获取用户信息\r\n        try {\r\n          const userInfo = await this.getUserInfo()\r\n          uni.setStorageSync('userInfo', userInfo)\r\n\r\n          // 跳转到首页\r\n          uni.reLaunch({\r\n            url: '/pages/index/index'\r\n          })\r\n        } catch (error) {\r\n          console.error('获取用户信息失败:', error)\r\n          // 即使获取用户信息失败，也跳转到首页\r\n          uni.reLaunch({\r\n            url: '/pages/index/index'\r\n          })\r\n        }\r\n      } else {\r\n        throw new Error(res.msg || '登录失败')\r\n      }\r\n    },\r\n\r\n    // 获取用户信息\r\n    async getUserInfo() {\r\n      // 这里应该调用获取用户信息的API\r\n      // 暂时返回默认用户信息\r\n      return {\r\n        avatar: null,\r\n        nickName: null,\r\n        sex: null,\r\n        phonenumber: null\r\n      }\r\n    },\r\n\r\n    // 微信登录\r\n    wechatLogin(e) {\r\n      if (!e.detail.userInfo) {\r\n        uni.showToast({\r\n          title: '您拒绝了授权',\r\n          icon: 'error'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.isLoading = true\r\n\r\n      uni.login({\r\n        provider: 'weixin',\r\n        success: (res) => {\r\n          const params = {\r\n            data: {\r\n              code: res.code,\r\n              appid: config.appId\r\n            },\r\n            url: '/api/user/wxlogin',\r\n            method: 'get'\r\n          }\r\n\r\n          this.$myRequest(params).then(result => {\r\n            uni.showToast({\r\n              title: '登录成功'\r\n            })\r\n\r\n            const user = result.data.data.user\r\n            uni.setStorageSync('token', result.data.data.token)\r\n            uni.setStorageSync('userInfo', user)\r\n\r\n            // 跳转到首页\r\n            uni.reLaunch({\r\n              url: '/pages/index/index'\r\n            })\r\n          }).catch(err => {\r\n            console.error('微信登录失败:', err)\r\n            uni.showToast({\r\n              title: '微信登录失败',\r\n              icon: 'error'\r\n            })\r\n          }).finally(() => {\r\n            this.isLoading = false\r\n          })\r\n        },\r\n        fail: () => {\r\n          this.isLoading = false\r\n          uni.showToast({\r\n            title: '微信登录失败',\r\n            icon: 'error'\r\n          })\r\n        }\r\n      })\r\n    },\r\n\r\n    // 跳转注册页面\r\n    handleUserRegister() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/register/index'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n}\r\n.login-content {\r\n  padding: 40rpx;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.logo-section {\r\n  text-align: center;\r\n  margin-bottom: 60rpx;\r\n  margin-top: 80rpx;\r\n  padding-top: 60rpx;\r\n\r\n  .logo {\r\n    width: 140rpx;\r\n    height: 140rpx;\r\n    border-radius: 30rpx;\r\n    margin-bottom: 40rpx;\r\n    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);\r\n  }\r\n\r\n  .app-name {\r\n    display: block;\r\n    font-size: 52rpx;\r\n    font-weight: bold;\r\n    color: #ffffff;\r\n    margin-bottom: 20rpx;\r\n    text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);\r\n  }\r\n\r\n  .subtitle {\r\n    display: block;\r\n    font-size: 30rpx;\r\n    color: rgba(255, 255, 255, 0.9);\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.form-section {\r\n  flex: 1;\r\n  background: rgba(255, 255, 255, 0.98);\r\n  border-radius: 40rpx 40rpx 0 0;\r\n  padding: 80rpx 50rpx 60rpx;\r\n  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.15);\r\n  backdrop-filter: blur(10rpx);\r\n  margin-top: auto;\r\n}\r\n\r\n.input-group {\r\n  margin-bottom: 50rpx;\r\n\r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.input-wrapper {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  border-radius: 20rpx;\r\n  border: 2rpx solid #e8ecf4;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\r\n\r\n  &:focus-within {\r\n    border-color: #667eea;\r\n    box-shadow: 0 4rpx 25rpx rgba(102, 126, 234, 0.2);\r\n    transform: translateY(-2rpx);\r\n  }\r\n\r\n  &.captcha-wrapper {\r\n    padding-right: 20rpx;\r\n  }\r\n}\r\n\r\n.input-icon {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #667eea;\r\n  font-size: 36rpx;\r\n}\r\n\r\n.form-input {\r\n  flex: 1;\r\n  height: 100rpx;\r\n  padding: 0 20rpx;\r\n  font-size: 32rpx;\r\n  color: #333333;\r\n  background: transparent;\r\n  border: none;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-input::placeholder {\r\n  color: #a8b2c8;\r\n  font-weight: 400;\r\n}\r\n\r\n.captcha-image {\r\n  width: 140rpx;\r\n  height: 80rpx;\r\n  margin-left: 20rpx;\r\n  border-radius: 12rpx;\r\n  overflow: hidden;\r\n  background: #f8f9fa;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 2rpx solid #e8ecf4;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n\r\n  &:hover {\r\n    border-color: #667eea;\r\n  }\r\n\r\n  .captcha-img {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n\r\n  .refresh-text {\r\n    font-size: 22rpx;\r\n    color: #667eea;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n.error-text {\r\n  display: block;\r\n  color: #ff4757;\r\n  font-size: 26rpx;\r\n  margin-top: 15rpx;\r\n  margin-left: 100rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.button-group {\r\n  margin: 80rpx 0 50rpx;\r\n}\r\n\r\n.login-btn {\r\n  width: 100%;\r\n  height: 100rpx;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  border: none;\r\n  border-radius: 20rpx;\r\n  color: #ffffff;\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);\r\n\r\n  &:not([disabled]):active {\r\n    transform: translateY(2rpx);\r\n    box-shadow: 0 12rpx 35rpx rgba(102, 126, 234, 0.4);\r\n  }\r\n\r\n  &[disabled] {\r\n    opacity: 0.6;\r\n    cursor: not-allowed;\r\n    transform: none;\r\n  }\r\n}\r\n\r\n.register-link {\r\n  text-align: center;\r\n  margin-bottom: 40rpx;\r\n\r\n  .link-text {\r\n    color: #8892b0;\r\n    font-size: 30rpx;\r\n    font-weight: 400;\r\n  }\r\n\r\n  .link-button {\r\n    color: #667eea;\r\n    font-size: 30rpx;\r\n    font-weight: bold;\r\n    margin-left: 10rpx;\r\n    text-decoration: underline;\r\n  }\r\n}\r\n\r\n/* 响应式适配 */\r\n@media screen and (max-width: 750rpx) {\r\n  .login-content {\r\n    padding: 30rpx;\r\n  }\r\n\r\n  .logo-section {\r\n    margin-top: 40rpx;\r\n    padding-top: 40rpx;\r\n  }\r\n\r\n  .form-section {\r\n    padding: 60rpx 40rpx 50rpx;\r\n  }\r\n\r\n  .logo {\r\n    width: 120rpx;\r\n    height: 120rpx;\r\n  }\r\n\r\n  .app-name {\r\n    font-size: 46rpx;\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.login-container {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.login-content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n\r\n/* 图标字体优化 */\r\n.icon-user::before {\r\n  content: '👤';\r\n  font-size: 36rpx;\r\n}\r\n\r\n.icon-password::before {\r\n  content: '🔒';\r\n  font-size: 36rpx;\r\n}\r\n\r\n.icon-code::before {\r\n  content: '🔢';\r\n  font-size: 36rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/login/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getCodeImg", "uni", "encrypt", "login", "config"], "mappings": ";;;;;;AA6GA,MAAK,WAAY,MAAW;AAE5B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,WAAW;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA;AAAA,MAGD,SAAS;AAAA,MACT,gBAAgB;AAAA;AAAA,MAGhB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA,MAGjB,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACD;AAAA,EAED,UAAU;AACR,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,UAAU;AACRA,2BAAY,EAAC,KAAK,SAAO;AACvB,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,iBAAiB,IAAI,mBAAmB;AAC7C,cAAI,KAAK,gBAAgB;AACvB,iBAAK,UAAU,2BAA2B,IAAI;AAC9C,iBAAK,UAAU,OAAO,IAAI;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,CAAC,EAAE,MAAM,SAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,oCAAc,YAAY,GAAG;AAAA,OAC9B;AAAA,IACF;AAAA;AAAA,IAGD,mBAAmB;AACjB,UAAI,CAAC,KAAK,UAAU,SAAS,KAAI,GAAI;AACnC,aAAK,OAAO,WAAW;AACvB,eAAO;AAAA,MACT;AACA,WAAK,OAAO,WAAW;AACvB,aAAO;AAAA,IACR;AAAA,IAED,mBAAmB;AACjB,UAAI,CAAC,KAAK,UAAU,SAAS,KAAI,GAAI;AACnC,aAAK,OAAO,WAAW;AACvB,eAAO;AAAA,MACT;AACA,WAAK,OAAO,WAAW;AACvB,aAAO;AAAA,IACR;AAAA,IAED,eAAe;AACb,UAAI,KAAK,kBAAkB,CAAC,KAAK,UAAU,KAAK,QAAQ;AACtD,aAAK,OAAO,OAAO;AACnB,eAAO;AAAA,MACT;AACA,WAAK,OAAO,OAAO;AACnB,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,eAAe;AACb,YAAM,gBAAgB,KAAK,iBAAiB;AAC5C,YAAM,gBAAgB,KAAK,iBAAiB;AAC5C,YAAM,YAAY,KAAK,aAAa;AAEpC,aAAO,iBAAiB,iBAAiB;AAAA,IAC1C;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,MACF;AAEA,WAAK,YAAY;AAEjB,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,SACR;AAED,cAAM,KAAK,SAAS;AAAA,MAEpB,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,oCAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,YAAI,KAAK,gBAAgB;AACvB,eAAK,QAAQ;AAAA,QACf;AAAA,MACF,UAAU;AACR,aAAK,YAAY;AACjBA,sBAAAA,MAAI,YAAY;AAAA,MAClB;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,WAAW;AACf,YAAM,oBAAoBC,gBAAO,QAAC,KAAK,UAAU,QAAQ;AAEzD,YAAM,MAAM,MAAMC,UAAK;AAAA,QACrB,KAAK,UAAU;AAAA,QACf;AAAA,QACA,KAAK,UAAU;AAAA,QACf,KAAK,UAAU;AAAA,MACjB;AAEA,UAAI,IAAI,SAAS,KAAK;AACpBF,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,SACR;AAGDA,sBAAAA,MAAI,eAAe,SAAS,IAAI,KAAK;AAGrC,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,YAAY;AACxCA,8BAAI,eAAe,YAAY,QAAQ;AAGvCA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,WACN;AAAA,QACD,SAAO,OAAO;AACdA,wBAAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAEhCA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,WACN;AAAA,QACH;AAAA,aACK;AACL,cAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,MACnC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAGlB,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,KAAK;AAAA,QACL,aAAa;AAAA,MACf;AAAA,IACD;AAAA;AAAA,IAGD,YAAY,GAAG;AACb,UAAI,CAAC,EAAE,OAAO,UAAU;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,YAAY;AAEjBA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAChB,gBAAM,SAAS;AAAA,YACb,MAAM;AAAA,cACJ,MAAM,IAAI;AAAA,cACV,OAAOG,OAAM,OAAC;AAAA,YACf;AAAA,YACD,KAAK;AAAA,YACL,QAAQ;AAAA,UACV;AAEA,eAAK,WAAW,MAAM,EAAE,KAAK,YAAU;AACrCH,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,aACR;AAED,kBAAM,OAAO,OAAO,KAAK,KAAK;AAC9BA,0BAAG,MAAC,eAAe,SAAS,OAAO,KAAK,KAAK,KAAK;AAClDA,gCAAI,eAAe,YAAY,IAAI;AAGnCA,0BAAAA,MAAI,SAAS;AAAA,cACX,KAAK;AAAA,aACN;AAAA,UACH,CAAC,EAAE,MAAM,SAAO;AACdA,0BAAAA,MAAc,MAAA,SAAA,oCAAA,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,aACP;AAAA,UACH,CAAC,EAAE,QAAQ,MAAM;AACf,iBAAK,YAAY;AAAA,WAClB;AAAA,QACF;AAAA,QACD,MAAM,MAAM;AACV,eAAK,YAAY;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,QACH;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,qBAAqB;AACnBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5VA,GAAG,WAAW,eAAe;"}