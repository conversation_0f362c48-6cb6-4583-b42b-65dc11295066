"use strict";
const common_vendor = require("../../common/vendor.js");
const api_api = require("../../api/api.js");
const config = require("../../config.js");
const _sfc_main = {
  name: "ContactList",
  data() {
    return {
      age: "0",
      maxNum: 0,
      linkList: [],
      checkAllNum: 0,
      startX: "",
      startY: "",
      type: ""
    };
  },
  computed: {
    config() {
      return config.config;
    }
  },
  onShow() {
    this.getLinkList();
  },
  onLoad(options) {
    this.maxNum = options.num || 0;
    this.type = options.type || "";
    this.age = options.age || "";
  },
  methods: {
    // 获取联系人列表
    getLinkList() {
      api_api.myRequest({
        url: "/auth/linkman/list"
      }).then((res) => {
        if (res.data && res.data.rows) {
          this.linkList = res.data.rows.map((item) => ({
            ...item,
            isCheck: false,
            isMove: false
          }));
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages_app/contacts/index.vue:105", "获取联系人列表失败:", err);
        common_vendor.index.showToast({
          title: "获取联系人列表失败",
          icon: "error"
        });
      });
    },
    // 隐藏身份证号中间部分
    hideCardNum(index) {
      const certificate = this.linkList[index].linkmanCertificate;
      const length = certificate.length;
      if (length < 4)
        return certificate;
      if (length > 4 && length !== 18) {
        let result = certificate.substring(0, 2);
        for (let i = 0; i < length; i++) {
          if (i >= 2 && i < length - 2) {
            result += "*";
          }
        }
        result += certificate.substring(length - 2, length);
        return result;
      }
      return certificate.replace(certificate.substring(4, 15), "*******");
    },
    // 添加联系人
    addContact() {
      common_vendor.index.navigateTo({
        url: "/pages_app/contacts/addcontact"
      });
    },
    // 选择联系人
    chooseLink(index) {
      this.linkList[index].isCheck = !this.linkList[index].isCheck;
      this.checkAllNum = this.linkList.filter((item) => item.isCheck).length;
    },
    // 确认选择
    sureNum() {
      if (this.checkAllNum > this.maxNum) {
        common_vendor.index.showToast({
          title: "选择人数超出",
          icon: "error",
          duration: 2e3
        });
        return;
      }
      if (this.checkAllNum < this.maxNum) {
        common_vendor.index.showToast({
          title: "选择人数不足",
          icon: "error",
          duration: 2e3
        });
        return;
      }
      const selectedContacts = this.linkList.filter((item) => item.isCheck);
      if (this.type === "kcyy_link" && this.age && this.age !== "0") {
        if (!this.checkAgeRestriction(selectedContacts)) {
          return;
        }
      } else if (this.type === "rgyy_link") {
        if (!this.checkVenueRestriction(selectedContacts)) {
          return;
        }
      } else if (this.type === "gyyy_link") {
        if (!this.checkMovieRestriction(selectedContacts)) {
          return;
        }
      }
      common_vendor.index.setStorageSync(this.type, JSON.stringify(selectedContacts));
      common_vendor.index.navigateBack();
    },
    // 检查年龄限制
    checkAgeRestriction(contacts) {
      for (let contact of contacts) {
        const ageRanges = this.age.split("-");
        if (ageRanges.length === 1) {
          const ageStr = ageRanges[0].replace(/岁|以|上/g, "");
          if (ageRanges[0].includes("以上")) {
            if (contact.linkmanAge < parseInt(ageStr)) {
              common_vendor.index.showToast({
                title: `选择人员年龄小于限制【${this.age}】`,
                icon: "error",
                duration: 2e3
              });
              return false;
            }
          } else if (contact.linkmanAge !== parseInt(ageStr)) {
            common_vendor.index.showToast({
              title: `选择人员年龄不等于限制【${this.age}】`,
              icon: "error",
              duration: 2e3
            });
            return false;
          }
        } else if (ageRanges.length === 2) {
          const minAge = parseInt(ageRanges[0].replace(/岁|以|上/g, ""));
          const maxAge = parseInt(ageRanges[1].replace(/岁|以|上/g, ""));
          if (contact.linkmanAge < minAge) {
            common_vendor.index.showToast({
              title: `选择人员年龄小于限制【${this.age}】`,
              icon: "error",
              duration: 2e3
            });
            return false;
          }
          if (contact.linkmanAge > maxAge) {
            common_vendor.index.showToast({
              title: `选择人员年龄大于限制【${this.age}】`,
              icon: "error",
              duration: 2e3
            });
            return false;
          }
        }
      }
      return true;
    },
    // 检查场馆预约限制
    checkVenueRestriction(contacts) {
      let under8Count = 0;
      for (let contact of contacts) {
        if (contact.linkmanAge < 8) {
          under8Count++;
        }
      }
      if (under8Count > 3) {
        common_vendor.index.showToast({
          title: "8岁以下的人数不超过3人",
          icon: "error",
          duration: 2e3
        });
        return false;
      }
      return true;
    },
    // 检查观影预约限制
    checkMovieRestriction(contacts) {
      let under3Count = 0;
      let under10Count = 0;
      for (let contact of contacts) {
        if (contact.linkmanAge < 3) {
          under3Count++;
        } else if (contact.linkmanAge < 10) {
          under10Count++;
        }
      }
      if (under3Count > 0) {
        common_vendor.index.showToast({
          title: "3岁以下限制预约",
          icon: "error",
          duration: 2e3
        });
        return false;
      }
      if (under10Count > 2) {
        common_vendor.index.showToast({
          title: "10岁以下限制预约2人",
          icon: "error",
          duration: 2e3
        });
        return false;
      }
      return true;
    },
    // 删除联系人
    deleteItem(index) {
      this.linkList.splice(index, 1);
    },
    // 编辑联系人
    editItem(id) {
      common_vendor.index.navigateTo({
        url: `/pages_app/contacts/addcontact?id=${id}`
      });
    },
    // 触摸开始
    touchstart(e) {
      this.linkList.forEach((item) => {
        if (item.isMove) {
          item.isMove = false;
        }
      });
      this.startX = e.changedTouches[0].clientX;
      this.startY = e.changedTouches[0].clientY;
    },
    // 触摸移动
    touchmove(e, index) {
      const startX = this.startX;
      const startY = this.startY;
      const touchX = e.changedTouches[0].clientX;
      const touchY = e.changedTouches[0].clientY;
      const angle = this.angle({ x: startX, y: startY }, { x: touchX, y: touchY });
      if (Math.abs(angle) <= 30 && startX - touchX >= 30) {
        this.linkList[index].isMove = true;
      }
    },
    // 计算角度
    angle(start, end) {
      const x = end.x - start.x;
      const y = end.y - start.y;
      return Math.atan(y / x) * 360 / Math.PI;
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$options.config.iSzgm
  }, !$options.config.iSzgm ? {
    b: common_vendor.p({
      menuClass: "bor",
      isFixed: true,
      isBack: true,
      isShowHome: true,
      title: "联系人",
      color: "#000"
    })
  } : {}, {
    c: common_vendor.o((...args) => $options.addContact && $options.addContact(...args)),
    d: common_vendor.f($data.linkList, (link, index, i0) => {
      return {
        a: common_vendor.t(link.linkmanName),
        b: common_vendor.t($options.hideCardNum(index)),
        c: common_vendor.t(link.linkmanPhone),
        d: common_vendor.t(link.linkmanAge),
        e: link.isCheck ? 1 : "",
        f: common_vendor.o(($event) => $options.editItem(link.id), link.id),
        g: common_vendor.o(($event) => $options.deleteItem(index), link.id),
        h: link.isMove ? 1 : "",
        i: link.id,
        j: link.isMove ? 1 : "",
        k: common_vendor.o((...args) => $options.touchstart && $options.touchstart(...args), link.id),
        l: common_vendor.o(($event) => $options.touchmove($event, index), link.id),
        m: common_vendor.o(($event) => $options.chooseLink(index), link.id),
        n: index
      };
    }),
    e: common_vendor.t($data.checkAllNum),
    f: common_vendor.o((...args) => $options.sureNum && $options.sureNum(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e45255a1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/contacts/index.js.map
