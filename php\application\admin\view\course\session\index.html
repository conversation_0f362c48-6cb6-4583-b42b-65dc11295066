<div class="panel panel-default panel-intro">
  {:build_heading()}
  <!-- 本地 FullCalendar CSS -->
  <link rel="stylesheet" href="/assets/libs/fullcalendar/main.min.css">

  <!-- 本地 FullCalendar JS -->
  <script src="/assets/libs/fullcalendar/main.min.js"></script>
  <script src="/assets/libs/fullcalendar/locales/zh-cn.min.js"></script>
  <style>
    /* 去掉事件默认背景色 */
    .fc-daygrid-event {
      background-color: transparent !important;
      padding: 0 !important;
      border: none !important;
    }

    /* 星期栏：周一、周二、周三…… */
    .fc .fc-col-header-cell-cushion {
      color: #000 !important;
    }

    /* 日期数字：1日、2日、3日…… */
    .fc .fc-daygrid-day-number {
      color: #000 !important;
      /* 日期数字居中 */
      display: block;
      text-align: center;
      width: 100%;
    }

    /* 开馆日 / 闭馆日 标签居中 */
    .fc .fc-event {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }

    .fc-daygrid-event {
      pointer-events: none !important;
      /* 让事件不捕获点击 */
    }

    .calendar-open {
      color: #1b8c00;
      /*    background-color: #e8f5e9;*/
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
    }

    .calendar-close {
      color: #c62828;
      /*    background-color: #ffebee;*/
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
    }

    /* 1. 去掉今日的背景颜色 */
    .fc-day-today {
      background-color: transparent !important;
    }

    /* 2. 把“今日”的日期文字改为蓝色 */
    .fc-day-today .fc-daygrid-day-number {
      color: #1890ff !important;
      font-weight: bold;
    }

    /*当前选中日期的样式*/
    .fc-selected {
      border: 2px solid #007bff !important;
      background-color: rgba(0, 123, 255, 0.1);
      border-radius: 4px;
    }

    /*带打钩图标）*/
    .fc-daygrid-day.fc-selected::after {
      content: "✓";
      color: #007bff;
      font-weight: bold;
      font-size: 16px;
      position: absolute;
      top: 2px;
      right: 4px;
      pointer-events: none;
    }

    .fc-daygrid-day.fc-selected {
      position: relative;
    }

    /*    让“2025年11月”看起来更小一些、更加紧凑。*/
    .fc-toolbar-title {
      font-size: 18px !important;
      /* 原本可能是 24px 或更大 */
      font-weight: 600;
      /* 可选：更紧凑一点 */
      line-height: 1.2;
    }

    /*微调按钮垂直对齐与间距*/
    .btn-group-vertical .btn {
      margin-bottom: 6px;
    }

    .btn-group-vertical .btn:last-child {
      margin-bottom: 0;
    }
  </style>


  <div class="panel-body">
    <div id="myTabContent" class="tab-content">
      <div class="tab-pane fade active in" id="one">
        <div class="widget-body no-padding">

          <div class="row">
            <!-- 左侧日历 -->
            <div class="col-md-6">
              <div id="holiday-calendar"></div>
            </div>


            <!-- 右侧内容区域 -->
            <div class="col-md-6">
              <!-- 一键导入按钮 -->
              <div style="margin-bottom: 15px;">
                <button id='btn-import' class="btn btn-primary btn-sm">一键导入</button>
              </div>

              <!-- 课程卡片列表 -->
              <div id="course-list">

                <!-- 上面那段课程卡片代码复制多份 -->
                <div class="panel panel-default" style="padding: 15px; margin-bottom: 15px;">
                  <div class="media">
                    <!-- 左侧图片 -->
                    <div class="media-left">
                      <img src="" width="90" height="120" style="object-fit: cover;"
                        onerror="this.style.display='none'" />
                    </div>

                    <!-- 中间课程信息 -->
                    <div class="media-body" style="padding-left: 15px;">
                      <strong style="font-size: 18px;"></strong>
                      <div style="margin: 5px 0;"></div>
                      <div></div>
                    </div>

                    <!-- 右侧操作按钮 -->
                    <div class="media-right text-right" style="min-width: 80px;">
                      <div class="btn-group-vertical">
                        <button class="btn btn-success btn-xs btn-edit" data-id="${row.id}">编辑</button>
                        <!-- <br> -->
                        <button class="btn btn-danger btn-xs">删除</button>
                        <!-- <br> -->
                        <button class="btn btn-danger btn-xs">停止预约</button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 课程卡片列表 -->
              </div>

              <!-- 添加按钮 -->
              <!-- 添加按钮 -->
              <div class="panel panel-default text-center" id="btn-add-session"
                style="padding: 20px; font-size: 36px; cursor: pointer;">
                +
              </div>

            </div>


          </div>
        </div>
      </div>

    </div>
  </div>
</div>