/*
 * Skin: Purple
 * ------------
 */
.skin-purple .main-header .navbar {
  background-color: #fff;
}
.skin-purple .main-header .navbar .nav > li > a {
  color: #444;
}
.skin-purple .main-header .navbar .nav > li > a:hover,
.skin-purple .main-header .navbar .nav > li > a:active,
.skin-purple .main-header .navbar .nav > li > a:focus,
.skin-purple .main-header .navbar .nav .open > a,
.skin-purple .main-header .navbar .nav .open > a:hover,
.skin-purple .main-header .navbar .nav .open > a:focus,
.skin-purple .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #605ca8;
}
.skin-purple .main-header .navbar .nav-addtabs li > .close-tab {
  color: #605ca8;
}
.skin-purple .main-header .navbar .sidebar-toggle {
  color: #444;
}
.skin-purple .main-header .navbar .sidebar-toggle:hover {
  color: #605ca8;
  background: rgba(0, 0, 0, 0.02);
}
@media (max-width: 767px) {
  .skin-purple .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-purple .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-purple .main-header .navbar .dropdown-menu li a:hover {
    background: #555299;
  }
}
.skin-purple .main-header > .logo {
  background-color: #605ca8;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #605ca8;
  box-shadow: none;
}
.skin-purple .main-header > .logo:hover {
  background-color: #5d59a6;
}
@media (max-width: 767px) {
  .skin-purple .main-header > .logo {
    background-color: #fff;
    color: #222;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-purple .main-header > .logo:hover {
    background-color: #fcfcfc;
  }
}
.skin-purple .main-header li.user-header {
  background-color: #605ca8;
}
.skin-purple .main-header .nav-addtabs > li > a,
.skin-purple .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-purple .content-header {
  background: transparent;
}
.skin-purple .wrapper,
.skin-purple .main-sidebar,
.skin-purple .left-side {
  background-color: #605ca8;
}
.skin-purple .user-panel > .info,
.skin-purple .user-panel > .info > a {
  color: #fff;
}
.skin-purple .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-purple .sidebar-menu > li.header {
  color: #a19fcb;
  background: #57539c;
}
.skin-purple .sidebar-menu > li:hover > a,
.skin-purple .sidebar-menu > li.active > a {
  color: #fff;
  background: #5b57a3;
  border-left-color: #fff;
}
.skin-purple .sidebar-menu > li > .treeview-menu {
  background: #5955a0;
}
.skin-purple .sidebar a {
  color: #c8c5ff;
}
.skin-purple .sidebar a:hover {
  text-decoration: none;
}
.skin-purple .treeview-menu > li > a {
  color: #c8c5ff;
}
.skin-purple .treeview-menu > li.active > a,
.skin-purple .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-purple .sidebar-form {
  border-radius: 3px;
  border: 1px solid #807dba;
  background-color: #807dba;
  margin: 10px 10px;
}
.skin-purple .sidebar-form input[type="text"],
.skin-purple .sidebar-form .btn {
  box-shadow: none;
  background-color: #807dba;
  border: 1px solid transparent;
  height: 35px;
}
.skin-purple .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-purple .sidebar-form input[type="text"]:focus,
.skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-purple .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-purple .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-purple .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-purple.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-purple .sidebar-form input[type="text"]::-moz-placeholder {
  color: #fff;
  opacity: 1;
}
.skin-purple .sidebar-form input[type="text"]:-ms-input-placeholder {
  color: #fff;
}
.skin-purple .sidebar-form input[type="text"]::-webkit-input-placeholder {
  color: #fff;
}
.skin-purple .sidebar-form input[type="text"],
.skin-purple .sidebar-form .btn {
  color: #fff;
}
@media (max-width: 767px) {
  .skin-purple.multiplenav .main-header .navbar {
    background-color: #605ca8;
  }
  .skin-purple.multiplenav .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-purple.multiplenav .main-header .navbar .nav > li > a:hover,
  .skin-purple.multiplenav .main-header .navbar .nav > li > a:active,
  .skin-purple.multiplenav .main-header .navbar .nav > li > a:focus,
  .skin-purple.multiplenav .main-header .navbar .nav .open > a,
  .skin-purple.multiplenav .main-header .navbar .nav .open > a:hover,
  .skin-purple.multiplenav .main-header .navbar .nav .open > a:focus,
  .skin-purple.multiplenav .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-purple.multiplenav .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-purple.multiplenav .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-purple.multiplenav .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
  .skin-purple.multiplenav .main-header > .logo {
    background-color: #605ca8;
    color: #fff;
    border-bottom: 0 solid transparent;
  }
  .skin-purple.multiplenav .main-header > .logo:hover {
    background-color: #5d59a6;
  }
  .skin-purple.multiplenav .sidebar .mobilenav a.btn-app {
    background: #807dba;
    color: #fff;
  }
  .skin-purple.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #fff;
    color: #807dba;
  }
}
/*# sourceMappingURL=skin-purple.css.map */