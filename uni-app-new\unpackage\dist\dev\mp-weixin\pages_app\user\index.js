"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "UserIndex",
  data() {
    return {
      config: {
        iSzgm: false
        // 根据实际配置设置
      },
      userInfo: {
        avatar: "",
        nickName: "",
        sex: "",
        phonenumber: ""
      },
      allFunctionList: [
        {
          icon: "/static/img/user/app_getbalance.png",
          name: "管理员码",
          path: "/pages_app/user/oneqrcode",
          key: "code",
          showBadge: false,
          badgeText: "",
          badgeClass: "",
          platforms: ["all"]
          // 所有平台都显示
        },
        {
          icon: "/static/img/user/app_getbalance.png",
          name: "订阅通知",
          path: "noticeSubcribe",
          key: "notice",
          showBadge: false,
          badgeText: "",
          badgeClass: "",
          platforms: ["mp-weixin"]
          // 仅微信小程序显示
        }
      ],
      bookingList: [
        {
          icon: "/static/img/user/venue.png",
          name: "参观预约",
          path: "/pages_app/user/venuescheme",
          key: "venue",
          showBadge: false,
          badgeText: "",
          badgeClass: "badge-blue"
        },
        {
          icon: "/static/img/user/film.png",
          name: "观影预约",
          path: "/pages_app/user/filmscheme",
          key: "film",
          showBadge: false,
          badgeText: "",
          badgeClass: "badge-orange"
        },
        {
          icon: "/static/img/user/course.png",
          name: "课程预约",
          path: "/pages_app/user/curriculumscheme",
          key: "course",
          showBadge: false,
          badgeText: "",
          badgeClass: "badge-purple"
        }
      ]
    };
  },
  computed: {
    // 根据当前平台过滤功能列表
    functionList() {
      return this.allFunctionList.filter((item) => {
        if (item.platforms.includes("all")) {
          return true;
        }
        return item.platforms.includes("mp-weixin");
      });
    }
  },
  onShow() {
    this.getUserInfo();
    this.updateBadges();
  },
  methods: {
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await this.$myRequest({
          url: "/web/common/getUserInfo",
          method: "get"
        });
        if (res.code === 200) {
          this.userInfo = res.data.data || {};
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/index.vue:188", "获取用户信息失败:", error);
      }
    },
    // 更新徽章数量
    async updateBadges() {
    },
    // 更新预约徽章
    updateBookingBadge(key, count) {
      const item = this.bookingList.find((item2) => item2.key === key);
      if (item && count > 0) {
        item.showBadge = true;
        item.badgeText = count > 99 ? "99+" : count.toString();
      } else if (item) {
        item.showBadge = false;
      }
    },
    // 处理功能点击
    handleFunctionClick(item) {
      if (item.path === "noticeSubcribe") {
        this.handleNoticeSubscribe();
      } else if (item.path) {
        common_vendor.index.navigateTo({
          url: item.path
        });
      }
    },
    // 处理订阅通知
    handleNoticeSubscribe() {
      const tmplIds = [
        "sWn1mSByjsKEuiD-QOg48VlKvbjhcp_XfZpUmMJjt5g",
        "4FDnApuDczeYIFSYDNGBw9FWwZG3Fr6J6Sq8PqWE6j0"
      ];
      common_vendor.index.requestSubscribeMessage({
        tmplIds,
        success: (res) => {
          if (res[tmplIds[0]] === "accept" || res[tmplIds[1]] === "accept") {
            common_vendor.index.showToast({
              title: "订阅成功",
              icon: "success",
              duration: 2e3
            });
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "订阅失败",
            icon: "error",
            duration: 2e3
          });
        }
      });
    },
    // 处理预约点击
    handleBookingClick(item) {
      if (item.path) {
        common_vendor.index.navigateTo({
          url: item.path
        });
      }
    },
    // 跳转到联系人管理
    goToContacts() {
      common_vendor.index.navigateTo({
        url: "/pages_app/contacts/index"
      });
    },
    // 退出登录
    logout() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            this.performLogout();
          }
        }
      });
    },
    // 执行退出登录
    async performLogout() {
      try {
        common_vendor.index.showLoading({
          title: "退出中..."
        });
        await this.$myRequest({
          url: "/auth/logout",
          method: "post"
        });
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("userInfo");
        common_vendor.index.hideLoading();
        common_vendor.index.reLaunch({
          url: "/pages/login/index"
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages_app/user/index.vue:328", "退出登录失败:", error);
        common_vendor.index.showToast({
          title: "退出失败",
          icon: "error"
        });
      }
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.config.iSzgm
  }, !$data.config.iSzgm ? {
    b: common_vendor.p({
      title: "个人中心",
      isBack: false,
      isShowHome: false,
      background: "transparent"
    })
  } : {}, {
    c: $data.userInfo.avatar || "/static/img/user/default-avatar.png",
    d: common_vendor.t($data.userInfo.nickName || "未设置昵称"),
    e: common_assets._imports_0$1,
    f: common_vendor.t($data.userInfo.phonenumber || "未绑定手机"),
    g: common_vendor.o((...args) => $options.logout && $options.logout(...args)),
    h: common_assets._imports_1,
    i: common_assets._imports_2,
    j: common_vendor.o((...args) => $options.goToContacts && $options.goToContacts(...args)),
    k: common_vendor.f($options.functionList, (item, index, i0) => {
      return common_vendor.e({
        a: item.icon,
        b: item.showBadge
      }, item.showBadge ? {
        c: common_vendor.t(item.badgeText),
        d: common_vendor.n(item.badgeClass)
      } : {}, {
        e: common_vendor.t(item.name),
        f: index,
        g: common_vendor.o(($event) => $options.handleFunctionClick(item), index)
      });
    }),
    l: common_vendor.f($data.bookingList, (item, index, i0) => {
      return common_vendor.e({
        a: item.icon,
        b: item.showBadge
      }, item.showBadge ? {
        c: common_vendor.t(item.badgeText),
        d: common_vendor.n(item.badgeClass)
      } : {}, {
        e: common_vendor.t(item.name),
        f: index,
        g: common_vendor.o(($event) => $options.handleBookingClick(item), index)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ae029d5f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/user/index.js.map
