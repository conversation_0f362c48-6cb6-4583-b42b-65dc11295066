"use strict";
const utils_request = require("../utils/request.js");
function getCodeImg() {
  return utils_request.request({
    url: "/captchaImage",
    headers: {
      isToken: false
    },
    method: "GET",
    timeout: 2e4
  });
}
function getInfo() {
  return utils_request.request({
    url: "/getInfo",
    method: "GET"
  });
}
function login(tenantId, username, password, code, uuid) {
  const data = {
    tenantId,
    username,
    password,
    code,
    uuid
  };
  return utils_request.request({
    url: "/login",
    headers: {
      isToken: false,
      isEncrypt: false
    },
    method: "POST",
    data
  });
}
function logout() {
  return utils_request.request({
    url: "/logout",
    method: "POST"
  });
}
function registerWithPhone(userData) {
  return utils_request.request({
    url: "/wx/user/register",
    headers: {
      isToken: false,
      isEncrypt: false
    },
    method: "POST",
    data: userData
  });
}
exports.getCodeImg = getCodeImg;
exports.getInfo = getInfo;
exports.login = login;
exports.logout = logout;
exports.registerWithPhone = registerWithPhone;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/login.js.map
