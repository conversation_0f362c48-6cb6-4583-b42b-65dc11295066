<template>
  <view class="entervenue">
    <view class="content">
      <my-header
        :isBack="true"
        :isShowHome="true"
        title="参观预约"
        background="transparent"
      />
      <view class="venue_content">
        <view class="venue_top">
          <view class="calendar">
            <uni-calendar
              :date="timeSection.start"
              :insert="true"
              :lunar="false"
              :showMonth="false"
              :start-date="timeSection.start"
              :end-date="timeSection.end"
              :selected="selected"
              @change="getVenueDetail"
            />
          </view>
        </view>
        <view :class="['venue_bottom', isChooseVenue ? 'isChooseVenue' : '']">
          <view class="show_fixed_box">
            <view class="title">预约场次</view>
            <view class="venue_list">
              <view class="venue_picker">
                <uni-data-picker
                  :localdata="localdata"
                  placeholder="点击选择入馆时间"
                  popup-title="请选择时间"
                  v-model="venueId"
                  @change="bindPickerChange"
                />
              </view>
            </view>

            <view class="line">
              <view class="line_item" v-for="(item, index) in 20" :key="index"></view>
            </view>

            <view class="title">预约人数</view>
            <view class="chooseNum">
              <view
                :class="['checkItem', checkIndex === index ? 'isCheck' : '']"
                v-for="(item, index) in checkItem"
                :key="index"
                @click="checkNum(item.value)"
              >
                {{ item.label }}
              </view>
            </view>

            <view class="isChoose" v-if="isChooseVenue">
              <view class="title">人员名单</view>
              <view class="contactList">
                <view
                  class="contactItem"
                  v-for="(item, index) in contactList"
                  :key="item.linkId"
                >
                  <view class="left">
                    <view class="name">{{ item.linkmanName }}</view>
                    <view class="phone">{{ item.linkmanPhone }}</view>
                    <view class="idcard">{{ hideIdCard(item.linkmanCertificate) }}</view>
                  </view>
                  <view class="right">
                    <view
                      :class="['checkbox', item.linkCheck ? 'checked' : '']"
                      @click="toggleContact(index)"
                    >
                      <text v-if="item.linkCheck" class="check-icon">✓</text>
                    </view>
                  </view>
                </view>
              </view>

              <view class="addContact" @click="addContact">
                <text class="add-icon">+</text>
                <text>添加联系人</text>
              </view>
            </view>

            <view class="submit_btn" v-if="isChooseVenue">
              <button
                class="submit"
                @click="submitReservation"
                :disabled="isSubmitting"
              >
                {{ isSubmitting ? '提交中...' : '确认预约' }}
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import MyHeader from '@/components/my-header/my-header.vue'

export default {
  name: 'EnterVenue',
  components: {
    MyHeader
  },
  data() {
    return {
      // 时间选择相关
      timeSection: {
        start: '',
        end: ''
      },
      selected: [],
      checkDate: '',

      // 场次选择相关
      localdata: [],
      venueId: '',
      venueInfo: [],
      isChooseVenue: false,

      // 人数选择相关
      checkItem: [
        { label: '1人', value: 1 },
        { label: '2人', value: 2 },
        { label: '3人', value: 3 },
        { label: '4人', value: 4 },
        { label: '5人', value: 5 }
      ],
      checkIndex: -1,
      selectedNum: 0,

      // 联系人相关
      contactList: [],

      // 提交状态
      isSubmitting: false
    }
  },

  onLoad() {
    this.initTimeSection()
    this.getContactList()
  },

  onShow() {
    this.getContactList()
  },

  methods: {
    // 初始化时间范围
    initTimeSection() {
      const today = new Date()
      const endDate = new Date()
      endDate.setDate(today.getDate() + 30) // 30天内可预约

      this.timeSection = {
        start: this.formatDate(today),
        end: this.formatDate(endDate)
      }

      // 默认选择今天
      this.checkDate = this.formatDate(today)
      this.selected = [{
        date: this.formatDate(today),
        info: '今天'
      }]

      this.getVenueDetail({ fulldate: this.formatDate(today) })
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 获取场次详情
    async getVenueDetail(e) {
      try {
        this.checkDate = e.fulldate

        const res = await this.$myRequest({
          url: '/web/venue/getVenueByDate',
          method: 'get',
          data: {
            venueDate: e.fulldate
          }
        })

        if (res.code === 200 && res.data.data) {
          this.venueInfo = res.data.data
          this.localdata = res.data.data.map(item => ({
            value: item.id,
            text: `${item.venueStartTime}-${item.venueEndTime} (剩余${item.inventoryVotes})`
          }))
        } else {
          this.venueInfo = []
          this.localdata = []
          uni.showToast({
            title: '该日期暂无可预约场次',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取场次失败:', error)
        uni.showToast({
          title: '获取场次失败',
          icon: 'error'
        })
      }
    },

    // 选择场次
    bindPickerChange(e) {
      this.venueId = e.detail.value
      this.isChooseVenue = true
    },

    // 选择人数
    checkNum(num) {
      this.selectedNum = num
      this.checkIndex = this.checkItem.findIndex(item => item.value === num)
    },

    // 获取联系人列表
    async getContactList() {
      try {
        const res = await this.$myRequest({
          url: '/web/linkman/getLinkmanList',
          method: 'get'
        })

        if (res.code === 200) {
          this.contactList = (res.data.data || []).map(item => ({
            ...item,
            linkCheck: false
          }))
        }
      } catch (error) {
        console.error('获取联系人失败:', error)
      }
    },

    // 切换联系人选择状态
    toggleContact(index) {
      const selectedCount = this.contactList.filter(item => item.linkCheck).length

      if (!this.contactList[index].linkCheck && selectedCount >= this.selectedNum) {
        uni.showToast({
          title: `最多只能选择${this.selectedNum}人`,
          icon: 'none'
        })
        return
      }

      this.contactList[index].linkCheck = !this.contactList[index].linkCheck
    },

    // 添加联系人
    addContact() {
      uni.navigateTo({
        url: '/pages_app/contacts/addcontact'
      })
    },

    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      return idCard.replace(idCard.substring(4, 15), '*******')
    },

    // 提交预约
    async submitReservation() {
      if (this.isSubmitting) return

      // 验证选择
      if (!this.venueId) {
        uni.showToast({
          title: '请选择预约场次',
          icon: 'none'
        })
        return
      }

      if (this.selectedNum === 0) {
        uni.showToast({
          title: '请选择预约人数',
          icon: 'none'
        })
        return
      }

      const selectedContacts = this.contactList.filter(item => item.linkCheck)
      if (selectedContacts.length === 0) {
        uni.showToast({
          title: '请选择联系人',
          icon: 'none'
        })
        return
      }

      if (selectedContacts.length !== this.selectedNum) {
        uni.showToast({
          title: `请选择${this.selectedNum}位联系人`,
          icon: 'none'
        })
        return
      }

      try {
        this.isSubmitting = true

        const res = await this.$myRequest({
          url: '/web/venue/createVenueOrder',
          method: 'post',
          data: {
            venueId: this.venueId,
            venueDate: this.checkDate,
            linkmanIds: selectedContacts.map(item => item.linkId).join(','),
            peopleNum: this.selectedNum
          }
        })

        if (res.code === 200) {
          uni.showToast({
            title: '预约成功',
            icon: 'success',
            duration: 2000
          })

          setTimeout(() => {
            uni.navigateTo({
              url: `/pages_app/schemesuccess/venuesuccess?orderId=${res.data.data.orderId}`
            })
          }, 2000)
        } else {
          throw new Error(res.msg || '预约失败')
        }
      } catch (error) {
        console.error('提交预约失败:', error)
        uni.showToast({
          title: error.message || '预约失败',
          icon: 'error'
        })
      } finally {
        this.isSubmitting = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.entervenue {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding-top: 120rpx; // 为头部留出空间

    .venue_content {
      .venue_top {
        background: rgba(255, 255, 255, 0.95);
        margin: 20rpx;
        border-radius: 20rpx;
        overflow: hidden;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

        .calendar {
          padding: 20rpx;
        }
      }

      .venue_bottom {
        background: rgba(255, 255, 255, 0.95);
        margin: 20rpx;
        border-radius: 20rpx;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &.isChooseVenue {
          margin-bottom: 120rpx;
        }

        .show_fixed_box {
          padding: 30rpx;

          .title {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 20rpx;
          }

          .venue_list {
            margin-bottom: 30rpx;

            .venue_picker {
              border: 2rpx solid #e5e5e5;
              border-radius: 12rpx;
              overflow: hidden;
              transition: border-color 0.3s ease;

              &:focus-within {
                border-color: #667eea;
              }
            }
          }

          .line {
            display: flex;
            justify-content: center;
            margin: 40rpx 0;

            .line_item {
              width: 8rpx;
              height: 8rpx;
              background: #ddd;
              border-radius: 50%;
              margin: 0 4rpx;

              &:nth-child(odd) {
                background: #667eea;
              }
            }
          }

          .chooseNum {
            display: flex;
            flex-wrap: wrap;
            gap: 20rpx;
            margin-bottom: 30rpx;

            .checkItem {
              flex: 1;
              min-width: 120rpx;
              height: 80rpx;
              border: 2rpx solid #e5e5e5;
              border-radius: 12rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 28rpx;
              color: #666;
              transition: all 0.3s ease;

              &.isCheck {
                border-color: #667eea;
                background: #667eea;
                color: white;
              }
            }
          }

          .isChoose {
            .contactList {
              margin-bottom: 30rpx;

              .contactItem {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20rpx 0;
                border-bottom: 1rpx solid #f0f0f0;

                &:last-child {
                  border-bottom: none;
                }

                .left {
                  flex: 1;

                  .name {
                    font-size: 30rpx;
                    font-weight: 500;
                    color: #333;
                    margin-bottom: 8rpx;
                  }

                  .phone {
                    font-size: 26rpx;
                    color: #666;
                    margin-bottom: 6rpx;
                  }

                  .idcard {
                    font-size: 24rpx;
                    color: #999;
                  }
                }

                .right {
                  .checkbox {
                    width: 48rpx;
                    height: 48rpx;
                    border: 2rpx solid #ddd;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;

                    &.checked {
                      background: linear-gradient(135deg, #667eea, #764ba2);
                      border-color: #667eea;

                      .check-icon {
                        color: white;
                        font-size: 28rpx;
                        font-weight: bold;
                      }
                    }
                  }
                }
              }
            }

            .addContact {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 20rpx;
              border: 2rpx dashed #ddd;
              border-radius: 12rpx;
              color: #666;
              font-size: 28rpx;
              margin-bottom: 30rpx;
              transition: all 0.3s ease;

              &:active {
                background: #f8f8f8;
              }

              .add-icon {
                font-size: 36rpx;
                margin-right: 10rpx;
              }
            }
          }

          .submit_btn {
            .submit {
              width: 100%;
              height: 88rpx;
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              border-radius: 44rpx;
              font-size: 32rpx;
              font-weight: 500;
              border: none;

              &::after {
                border: none;
              }

              &:disabled {
                background: #ccc;
                color: #999;
              }
            }
          }
        }
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.entervenue {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>