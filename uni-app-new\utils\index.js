const Utils = {
	changeTime: function(time, isYear) {
		let times = new Date(time);
		let y = times.getFullYear();
		let m = (times.getMonth() + 1) < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
		let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();

		let h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();
		let mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();
		let s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();
		if (isYear) {
			return y + '-' + m + '-' + d;
		} else {
			return h + ':' + mm
		}
	},
	changeTime2: function(time) {
		let times = new Date(time);
		let y = times.getFullYear();
		let m = (times.getMonth() + 1) < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
		let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();

		let h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();
		let mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();
		let s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();
		return y + '-' + m + '-' + d +'  '+ h + ':' + mm + ':' + s;
	},	
	formatTime: function(time) {
		let times = new Date(time);
		let y = times.getFullYear();
		let m = (times.getMonth() + 1) < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
		let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();

		let h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();
		let mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();
		let s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();
		return h + ':' + mm + ':' + s;
	},	
	checkMobile(str) {
		var reg = /^1[34578]\d{9}$/
		if (reg.test(str)) {
			return true;
		} else {
			return false;
		}
	},
	distance: function(la1, lo1, la2, lo2) {
		var La1 = la1 * Math.PI / 180.0;
		var La2 = la2 * Math.PI / 180.0;
		var La3 = La1 - La2;
		var Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
		var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math
			.pow(Math.sin(Lb3 / 2), 2)));
		s = s * 6378.137;
		s = Math.round(s * 10000) / 10000;
		return s;
	},
	verifyIdCard: function(str, isLand = true) {
		/**
		 * isLand 是否为大陆身份证
		 */
		var Land = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/; //大陆
		var Hongkong = /([A-Za-z](\d{6})\d)|(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/; //香港、澳门
		var Taiwan = /^[a-zA-Z][0-9]{9}$/; //台湾
		var Macao = /^[1|5|7][0-9]{6}[09Aa]$/; //澳门

		if (isLand) {
			return Boolean(Land.test(str));
		} else {
			return Boolean(Hongkong.test(str) || Taiwan.test(str) || Macao.test(str));
		}

	},
	timeThenNow: function(times) {
		//判断传入的时间是否超过了当前的时间
		let now = new Date();
		return (new Date(times) > now);
	},
	// 节流
	throttle: function(fn, interval) {
		var enterTime = 0; //触发的时间
		var gapTime = interval || 3000; //间隔时间，如果interval不传，则默认300ms
		return function() {
			var context = this;
			var backTime = new Date(); //第一次函数return即触发的时间
			if (backTime - enterTime > gapTime) {
				fn.call(context, arguments);
				enterTime = backTime; //赋值给第一次触发的时间，这样就保存了第二次触发的时间
			}
		};
	},
	// 防抖
	debounce: function(fn, time) {
		//初始化一个定时器的时间
		var timer
		var lastTime = 0;
		return function(path) {
			//每次触发事件都先清除上一次的定时器，重新开始
			clearTimeout(timer);
			//获取当前的时间
			var nowTime = Date.now();
			//保存this指向的对象oBox，因为在定时器中直接使用this，那么this指向的定时器
			var _this = this;
			//arguments[0]就是当前事件对象的event对象
			//保存在定时器中直接使用arguments[0]，那么arguments[0]指向的定时器的参数实例
			var e = arguments[0];
			timer = setTimeout(function() {
				if (nowTime - lastTime < time) {
					return;
				}
				//使用call来改变fn函数的指向，并给fn传递this指向oBox
				fn.call(_this, e, path);
			}, time)
		}
	}
}


export default Utils;
