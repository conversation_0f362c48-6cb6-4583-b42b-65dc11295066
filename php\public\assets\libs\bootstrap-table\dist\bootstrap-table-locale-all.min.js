/*
* bootstrap-table - v1.11.11 - 2024-03-14
https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(t){"use strict";t.fn.bootstrapTable.locales["af-ZA"]={formatLoadingMessage:function(){return"Besig om te laai, wag asseblief ..."},formatRecordsPerPage:function(t){return t+" rekords per bladsy"},formatShowingRows:function(t,r,n){return"Resultate "+t+" tot "+r+" van "+n+" rye"},formatSearch:function(){return"Soek"},formatNoMatches:function(){return"Geen rekords gevind nie"},formatPaginationSwitch:function(){return"Wys/verberg bladsy nummering"},formatRefresh:function(){return"Herlaai"},formatToggle:function(){return"Wissel"},formatColumns:function(){return"Kolomme"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["af-ZA"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ar-SA"]={formatLoadingMessage:function(){return"جاري التحميل, يرجى الإنتظار..."},formatRecordsPerPage:function(t){return t+" سجل لكل صفحة"},formatShowingRows:function(t,r,n){return"الظاهر "+t+" إلى "+r+" من "+n+" سجل"},formatSearch:function(){return"بحث"},formatNoMatches:function(){return"لا توجد نتائج مطابقة للبحث"},formatPaginationSwitch:function(){return"إخفاءإظهار ترقيم الصفحات"},formatRefresh:function(){return"تحديث"},formatToggle:function(){return"تغيير"},formatColumns:function(){return"أعمدة"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ar-SA"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ca-ES"]={formatLoadingMessage:function(){return"Espereu, si us plau..."},formatRecordsPerPage:function(t){return t+" resultats per pàgina"},formatShowingRows:function(t,r,n){return"Mostrant de "+t+" fins "+r+" - total "+n+" resultats"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"No s'han trobat resultats"},formatPaginationSwitch:function(){return"Amaga/Mostra paginació"},formatRefresh:function(){return"Refresca"},formatToggle:function(){return"Alterna formatació"},formatColumns:function(){return"Columnes"},formatAllRows:function(){return"Tots"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ca-ES"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["cs-CZ"]={formatLoadingMessage:function(){return"Čekejte, prosím..."},formatRecordsPerPage:function(t){return t+" položek na stránku"},formatShowingRows:function(t,r,n){return"Zobrazena "+t+". - "+r+". položka z celkových "+n},formatSearch:function(){return"Vyhledávání"},formatNoMatches:function(){return"Nenalezena žádná vyhovující položka"},formatPaginationSwitch:function(){return"Skrýt/Zobrazit stránkování"},formatRefresh:function(){return"Aktualizovat"},formatToggle:function(){return"Přepni"},formatColumns:function(){return"Sloupce"},formatAllRows:function(){return"Vše"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["cs-CZ"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["da-DK"]={formatLoadingMessage:function(){return"Indlæser, vent venligst..."},formatRecordsPerPage:function(t){return t+" poster pr side"},formatShowingRows:function(t,r,n){return"Viser "+t+" til "+r+" af "+n+" rækker"},formatSearch:function(){return"Søg"},formatNoMatches:function(){return"Ingen poster fundet"},formatRefresh:function(){return"Opdater"},formatToggle:function(){return"Skift"},formatColumns:function(){return"Kolonner"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["da-DK"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["de-DE"]={formatLoadingMessage:function(){return"Lade, bitte warten..."},formatRecordsPerPage:function(t){return t+" Einträge pro Seite."},formatShowingRows:function(t,r,n){return"Zeige Zeile "+t+" bis "+r+" von "+n+" Zeile"+(1<n?"n":"")+"."},formatDetailPagination:function(t){return"Zeige "+t+" Zeile"+(1<t?"n":"")+"."},formatSearch:function(){return"Suchen ..."},formatNoMatches:function(){return"Keine passenden Ergebnisse gefunden."},formatRefresh:function(){return"Neu laden"},formatToggle:function(){return"Umschalten"},formatColumns:function(){return"Spalten"},formatAllRows:function(){return"Alle"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["de-DE"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["el-GR"]={formatLoadingMessage:function(){return"Φορτώνει, παρακαλώ περιμένετε..."},formatRecordsPerPage:function(t){return t+" αποτελέσματα ανά σελίδα"},formatShowingRows:function(t,r,n){return"Εμφανίζονται από την "+t+" ως την "+r+" από σύνολο "+n+" σειρών"},formatSearch:function(){return"Αναζητήστε"},formatNoMatches:function(){return"Δεν βρέθηκαν αποτελέσματα"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["el-GR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["en-US"]={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(t){return t+" rows per page"},formatShowingRows:function(t,r,n){return"Showing "+t+" to "+r+" of "+n+" rows"},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"},formatExport:function(){return"Export data"},formatClearFilters:function(){return"Clear filters"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["en-US"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-AR"]={formatLoadingMessage:function(){return"Cargando, espere por favor..."},formatRecordsPerPage:function(t){return t+" registros por página"},formatShowingRows:function(t,r,n){return"Mostrando "+t+" a "+r+" de "+n+" filas"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatAllRows:function(){return"Todo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-AR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-CL"]={formatLoadingMessage:function(){return"Cargando, espere por favor..."},formatRecordsPerPage:function(t){return t+" filas por página"},formatShowingRows:function(t,r,n){return"Mostrando "+t+" a "+r+" de "+n+" filas"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Cambiar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-CL"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-CR"]={formatLoadingMessage:function(){return"Cargando, por favor espere..."},formatRecordsPerPage:function(t){return t+" registros por página"},formatShowingRows:function(t,r,n){return"Mostrando de "+t+" a "+r+" registros de "+n+" registros en total"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-CR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-ES"]={formatLoadingMessage:function(){return"Por favor espere..."},formatRecordsPerPage:function(t){return t+" resultados por página"},formatShowingRows:function(t,r,n){return"Mostrando desde "+t+" hasta "+r+" - En total "+n+" resultados"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron resultados"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Ocultar/Mostrar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todos"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-ES"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-MX"]={formatLoadingMessage:function(){return"Cargando, espere por favor..."},formatRecordsPerPage:function(t){return t+" registros por página"},formatShowingRows:function(t,r,n){return"Mostrando "+t+" a "+r+" de "+n+" filas"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatAllRows:function(){return"Todo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-MX"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-NI"]={formatLoadingMessage:function(){return"Cargando, por favor espere..."},formatRecordsPerPage:function(t){return t+" registros por página"},formatShowingRows:function(t,r,n){return"Mostrando de "+t+" a "+r+" registros de "+n+" registros en total"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron registros"},formatRefresh:function(){return"Refrescar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-NI"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["es-SP"]={formatLoadingMessage:function(){return"Cargando, por favor espera..."},formatRecordsPerPage:function(t){return t+" registros por p&#225;gina."},formatShowingRows:function(t,r,n){return t+" - "+r+" de "+n+" registros."},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se han encontrado registros."},formatRefresh:function(){return"Actualizar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Columnas"},formatAllRows:function(){return"Todo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["es-SP"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["et-EE"]={formatLoadingMessage:function(){return"Päring käib, palun oota..."},formatRecordsPerPage:function(t){return t+" rida lehe kohta"},formatShowingRows:function(t,r,n){return"Näitan tulemusi "+t+" kuni "+r+" - kokku "+n+" tulemust"},formatSearch:function(){return"Otsi"},formatNoMatches:function(){return"Päringu tingimustele ei vastanud ühtegi tulemust"},formatPaginationSwitch:function(){return"Näita/Peida lehtedeks jagamine"},formatRefresh:function(){return"Värskenda"},formatToggle:function(){return"Lülita"},formatColumns:function(){return"Veerud"},formatAllRows:function(){return"Kõik"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["et-EE"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["fa-IR"]={formatLoadingMessage:function(){return"در حال بارگذاری, لطفا صبر کنید..."},formatRecordsPerPage:function(t){return t+" رکورد در صفحه"},formatShowingRows:function(t,r,n){return"نمایش "+t+" تا "+r+" از "+n+" ردیف"},formatSearch:function(){return"جستجو"},formatNoMatches:function(){return"رکوردی یافت نشد."},formatPaginationSwitch:function(){return"نمایش/مخفی صفحه بندی"},formatRefresh:function(){return"به روز رسانی"},formatToggle:function(){return"تغییر نمایش"},formatColumns:function(){return"سطر ها"},formatAllRows:function(){return"همه"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["fa-IR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["fr-BE"]={formatLoadingMessage:function(){return"Chargement en cours..."},formatRecordsPerPage:function(t){return t+" entrées par page"},formatShowingRows:function(t,r,n){return"Affiche de"+t+" à "+r+" sur "+n+" lignes"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Pas de fichiers trouvés"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["fr-BE"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["fr-FR"]={formatLoadingMessage:function(){return"Chargement en cours, patientez, s´il vous plaît ..."},formatRecordsPerPage:function(t){return t+" lignes par page"},formatShowingRows:function(t,r,n){return"Affichage des lignes "+t+" à "+r+" sur "+n+" lignes au total"},formatSearch:function(){return"Rechercher"},formatNoMatches:function(){return"Aucun résultat trouvé"},formatRefresh:function(){return"Rafraîchir"},formatToggle:function(){return"Alterner"},formatColumns:function(){return"Colonnes"},formatAllRows:function(){return"Tous"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["fr-FR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["he-IL"]={formatLoadingMessage:function(){return"טוען, נא להמתין..."},formatRecordsPerPage:function(t){return t+" שורות בעמוד"},formatShowingRows:function(t,r,n){return"מציג "+t+" עד "+r+" מ-"+n+" שורות"},formatSearch:function(){return"חיפוש"},formatNoMatches:function(){return"לא נמצאו רשומות תואמות"},formatPaginationSwitch:function(){return"הסתר/הצג מספור דפים"},formatRefresh:function(){return"רענן"},formatToggle:function(){return"החלף תצוגה"},formatColumns:function(){return"עמודות"},formatAllRows:function(){return"הכל"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["he-IL"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["hr-HR"]={formatLoadingMessage:function(){return"Molimo pričekajte ..."},formatRecordsPerPage:function(t){return t+" broj zapisa po stranici"},formatShowingRows:function(t,r,n){return"Prikazujem "+t+". - "+r+". od ukupnog broja zapisa "+n},formatSearch:function(){return"Pretraži"},formatNoMatches:function(){return"Nije pronađen niti jedan zapis"},formatPaginationSwitch:function(){return"Prikaži/sakrij stranice"},formatRefresh:function(){return"Osvježi"},formatToggle:function(){return"Promijeni prikaz"},formatColumns:function(){return"Kolone"},formatAllRows:function(){return"Sve"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["hr-HR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["hu-HU"]={formatLoadingMessage:function(){return"Betöltés, kérem várjon..."},formatRecordsPerPage:function(t){return t+" rekord per oldal"},formatShowingRows:function(t,r,n){return"Megjelenítve "+t+" - "+r+" / "+n+" összesen"},formatSearch:function(){return"Keresés"},formatNoMatches:function(){return"Nincs találat"},formatPaginationSwitch:function(){return"Lapozó elrejtése/megjelenítése"},formatRefresh:function(){return"Frissítés"},formatToggle:function(){return"Összecsuk/Kinyit"},formatColumns:function(){return"Oszlopok"},formatAllRows:function(){return"Összes"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["hu-HU"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["id-ID"]={formatLoadingMessage:function(){return"Memuat, mohon tunggu..."},formatRecordsPerPage:function(t){return t+" baris per halaman"},formatShowingRows:function(t,r,n){return"Menampilkan "+t+" sampai "+r+" dari "+n+" baris"},formatSearch:function(){return"Pencarian"},formatNoMatches:function(){return"Tidak ditemukan data yang cocok"},formatPaginationSwitch:function(){return"Sembunyikan/Tampilkan halaman"},formatRefresh:function(){return"Muat ulang"},formatToggle:function(){return"Beralih"},formatColumns:function(){return"kolom"},formatAllRows:function(){return"Semua"},formatExport:function(){return"Ekspor data"},formatClearFilters:function(){return"Bersihkan filter"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["id-ID"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["it-IT"]={formatLoadingMessage:function(){return"Caricamento in corso..."},formatRecordsPerPage:function(t){return t+" elementi per pagina"},formatShowingRows:function(t,r,n){return"Elementi mostrati da "+t+" a "+r+" (Numero totali di elementi "+n+")"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun elemento trovato"},formatPaginationSwitch:function(){return"Nascondi/Mostra paginazione"},formatRefresh:function(){return"Aggiorna"},formatToggle:function(){return"Attiva/Disattiva"},formatColumns:function(){return"Colonne"},formatAllRows:function(){return"Tutto"},formatExport:function(){return"Esporta dati"},formatClearFilters:function(){return"Pulisci filtri"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["it-IT"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ja-JP"]={formatLoadingMessage:function(){return"読み込み中です。少々お待ちください。"},formatRecordsPerPage:function(t){return"ページ当たり最大"+t+"件"},formatShowingRows:function(t,r,n){return"全"+n+"件から、"+t+"から"+r+"件目まで表示しています"},formatSearch:function(){return"検索"},formatNoMatches:function(){return"該当するレコードが見つかりません"},formatPaginationSwitch:function(){return"ページ数を表示・非表示"},formatRefresh:function(){return"更新"},formatToggle:function(){return"トグル"},formatColumns:function(){return"列"},formatAllRows:function(){return"すべて"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ja-JP"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ka-GE"]={formatLoadingMessage:function(){return"იტვირთება, გთხოვთ მოიცადოთ..."},formatRecordsPerPage:function(t){return t+" ჩანაწერი თითო გვერდზე"},formatShowingRows:function(t,r,n){return"ნაჩვენებია "+t+"-დან "+r+"-მდე ჩანაწერი ჯამური "+n+"-დან"},formatSearch:function(){return"ძებნა"},formatNoMatches:function(){return"მონაცემები არ არის"},formatPaginationSwitch:function(){return"გვერდების გადამრთველის დამალვა/გამოჩენა"},formatRefresh:function(){return"განახლება"},formatToggle:function(){return"ჩართვა/გამორთვა"},formatColumns:function(){return"სვეტები"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ka-GE"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ko-KR"]={formatLoadingMessage:function(){return"데이터를 불러오는 중입니다..."},formatRecordsPerPage:function(t){return"페이지 당 "+t+"개 데이터 출력"},formatShowingRows:function(t,r,n){return"전체 "+n+"개 중 "+t+"~"+r+"번째 데이터 출력,"},formatSearch:function(){return"검색"},formatNoMatches:function(){return"조회된 데이터가 없습니다."},formatRefresh:function(){return"새로 고침"},formatToggle:function(){return"전환"},formatColumns:function(){return"컬럼 필터링"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ko-KR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ms-MY"]={formatLoadingMessage:function(){return"Permintaan sedang dimuatkan. Sila tunggu sebentar..."},formatRecordsPerPage:function(t){return t+" rekod setiap muka surat"},formatShowingRows:function(t,r,n){return"Sedang memaparkan rekod "+t+" hingga "+r+" daripada jumlah "+n+" rekod"},formatSearch:function(){return"Cari"},formatNoMatches:function(){return"Tiada rekod yang menyamai permintaan"},formatPaginationSwitch:function(){return"Tunjuk/sembunyi muka surat"},formatRefresh:function(){return"Muatsemula"},formatToggle:function(){return"Tukar"},formatColumns:function(){return"Lajur"},formatAllRows:function(){return"Semua"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ms-MY"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["nb-NO"]={formatLoadingMessage:function(){return"Oppdaterer, vennligst vent..."},formatRecordsPerPage:function(t){return t+" poster pr side"},formatShowingRows:function(t,r,n){return"Viser "+t+" til "+r+" av "+n+" rekker"},formatSearch:function(){return"Søk"},formatNoMatches:function(){return"Ingen poster funnet"},formatRefresh:function(){return"Oppdater"},formatToggle:function(){return"Endre"},formatColumns:function(){return"Kolonner"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["nb-NO"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["nl-NL"]={formatLoadingMessage:function(){return"Laden, even geduld..."},formatRecordsPerPage:function(t){return t+" records per pagina"},formatShowingRows:function(t,r,n){return"Toon "+t+" tot "+r+" van "+n+" record"+(1<n?"s":"")},formatDetailPagination:function(t){return"Toon "+t+" record"+(1<t?"s":"")},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatRefresh:function(){return"Vernieuwen"},formatToggle:function(){return"Omschakelen"},formatColumns:function(){return"Kolommen"},formatAllRows:function(){return"Alle"},formatPaginationSwitch:function(){return"Verberg/Toon paginatie"},formatExport:function(){return"Exporteer data"},formatClearFilters:function(){return"Verwijder filters"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["nl-NL"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["pl-PL"]={formatLoadingMessage:function(){return"Ładowanie, proszę czekać..."},formatRecordsPerPage:function(t){return t+" rekordów na stronę"},formatShowingRows:function(t,r,n){return"Wyświetlanie rekordów od "+t+" do "+r+" z "+n},formatSearch:function(){return"Szukaj"},formatNoMatches:function(){return"Niestety, nic nie znaleziono"},formatRefresh:function(){return"Odśwież"},formatToggle:function(){return"Przełącz"},formatColumns:function(){return"Kolumny"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["pl-PL"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["pt-BR"]={formatLoadingMessage:function(){return"Carregando, aguarde..."},formatRecordsPerPage:function(t){return t+" registros por página"},formatShowingRows:function(t,r,n){return"Exibindo "+t+" até "+r+" de "+n+" linhas"},formatSearch:function(){return"Pesquisar"},formatRefresh:function(){return"Recarregar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Colunas"},formatPaginationSwitch:function(){return"Ocultar/Exibir paginação"},formatNoMatches:function(){return"Nenhum registro encontrado"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["pt-BR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["pt-PT"]={formatLoadingMessage:function(){return"A carregar, por favor aguarde..."},formatRecordsPerPage:function(t){return t+" registos por p&aacute;gina"},formatShowingRows:function(t,r,n){return"A mostrar "+t+" at&eacute; "+r+" de "+n+" linhas"},formatSearch:function(){return"Pesquisa"},formatNoMatches:function(){return"Nenhum registo encontrado"},formatPaginationSwitch:function(){return"Esconder/Mostrar pagina&ccedil&atilde;o"},formatRefresh:function(){return"Atualizar"},formatToggle:function(){return"Alternar"},formatColumns:function(){return"Colunas"},formatAllRows:function(){return"Tudo"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["pt-PT"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ro-RO"]={formatLoadingMessage:function(){return"Se incarca, va rugam asteptati..."},formatRecordsPerPage:function(t){return t+" inregistrari pe pagina"},formatShowingRows:function(t,r,n){return"Arata de la "+t+" pana la "+r+" din "+n+" randuri"},formatSearch:function(){return"Cauta"},formatNoMatches:function(){return"Nu au fost gasite inregistrari"},formatPaginationSwitch:function(){return"Ascunde/Arata paginatia"},formatRefresh:function(){return"Reincarca"},formatToggle:function(){return"Comuta"},formatColumns:function(){return"Coloane"},formatAllRows:function(){return"Toate"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ro-RO"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ru-RU"]={formatLoadingMessage:function(){return"Пожалуйста, подождите, идёт загрузка..."},formatRecordsPerPage:function(t){return t+" записей на страницу"},formatShowingRows:function(t,r,n){return"Записи с "+t+" по "+r+" из "+n},formatSearch:function(){return"Поиск"},formatNoMatches:function(){return"Ничего не найдено"},formatRefresh:function(){return"Обновить"},formatToggle:function(){return"Переключить"},formatColumns:function(){return"Колонки"},formatClearFilters:function(){return"Очистить фильтры"},formatMultipleSort:function(){return"Множественная сортировка"},formatAddLevel:function(){return"Добавить уровень"},formatDeleteLevel:function(){return"Удалить уровень"},formatColumn:function(){return"Колонка"},formatOrder:function(){return"Порядок"},formatSortBy:function(){return"Сортировать по"},formatThenBy:function(){return"затем по"},formatSort:function(){return"Сортировать"},formatCancel:function(){return"Отмена"},formatDuplicateAlertTitle:function(){return"Дублирование колонок!"},formatDuplicateAlertDescription:function(){return"Удалите, пожалуйста, дублирующую колонку, или замените ее на другую."}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ru-RU"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["sk-SK"]={formatLoadingMessage:function(){return"Prosím čakajte ..."},formatRecordsPerPage:function(t){return t+" záznamov na stranu"},formatShowingRows:function(t,r,n){return"Zobrazená "+t+". - "+r+". položka z celkových "+n},formatSearch:function(){return"Vyhľadávanie"},formatNoMatches:function(){return"Nenájdená žiadna vyhovujúca položka"},formatRefresh:function(){return"Obnoviť"},formatToggle:function(){return"Prepni"},formatColumns:function(){return"Stĺpce"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["sk-SK"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["sv-SE"]={formatLoadingMessage:function(){return"Laddar, vänligen vänta..."},formatRecordsPerPage:function(t){return t+" rader per sida"},formatShowingRows:function(t,r,n){return"Visa "+t+" till "+r+" av "+n+" rader"},formatSearch:function(){return"Sök"},formatNoMatches:function(){return"Inga matchande resultat funna."},formatRefresh:function(){return"Uppdatera"},formatToggle:function(){return"Skifta"},formatColumns:function(){return"kolumn"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["sv-SE"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["th-TH"]={formatLoadingMessage:function(){return"กำลังโหลดข้อมูล, กรุณารอสักครู่..."},formatRecordsPerPage:function(t){return t+" รายการต่อหน้า"},formatShowingRows:function(t,r,n){return"รายการที่ "+t+" ถึง "+r+" จากทั้งหมด "+n+" รายการ"},formatSearch:function(){return"ค้นหา"},formatNoMatches:function(){return"ไม่พบรายการที่ค้นหา !"},formatRefresh:function(){return"รีเฟรส"},formatToggle:function(){return"สลับมุมมอง"},formatColumns:function(){return"คอลัมน์"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["th-TH"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["tr-TR"]={formatLoadingMessage:function(){return"Yükleniyor, lütfen bekleyin..."},formatRecordsPerPage:function(t){return"Sayfa başına "+t+" kayıt."},formatShowingRows:function(t,r,n){return n+" kayıttan "+t+"-"+r+" arası gösteriliyor."},formatSearch:function(){return"Ara"},formatNoMatches:function(){return"Eşleşen kayıt bulunamadı."},formatRefresh:function(){return"Yenile"},formatToggle:function(){return"Değiştir"},formatColumns:function(){return"Sütunlar"},formatAllRows:function(){return"Tüm Satırlar"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["tr-TR"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["uk-UA"]={formatLoadingMessage:function(){return"Завантаження, будь ласка, зачекайте..."},formatRecordsPerPage:function(t){return t+" записів на сторінку"},formatShowingRows:function(t,r,n){return"Показано з "+t+" по "+r+". Всього: "+n},formatSearch:function(){return"Пошук"},formatNoMatches:function(){return"Не знайдено жодного запису"},formatRefresh:function(){return"Оновити"},formatToggle:function(){return"Змінити"},formatColumns:function(){return"Стовпці"},formatClearFilters:function(){return"Очистити фільтри"},formatMultipleSort:function(){return"Сортування за кількома стовпцями"},formatAddLevel:function(){return"Додати рівень"},formatDeleteLevel:function(){return"Видалити рівень"},formatColumn:function(){return"Стовпець"},formatOrder:function(){return"Порядок"},formatSortBy:function(){return"Сортувати за"},formatThenBy:function(){return"потім за"},formatSort:function(){return"Сортувати"},formatCancel:function(){return"Скасувати"},formatDuplicateAlertTitle:function(){return"Дублювання стовпців!"},formatDuplicateAlertDescription:function(){return"Видаліть, будь ласка, дублюючий стовпець, або замініть його на інший."}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["uk-UA"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["ur-PK"]={formatLoadingMessage:function(){return"براۓ مہربانی انتظار کیجئے"},formatRecordsPerPage:function(t){return t+" ریکارڈز فی صفہ "},formatShowingRows:function(t,r,n){return"دیکھیں "+t+" سے "+r+" کے "+n+"ریکارڈز"},formatSearch:function(){return"تلاش"},formatNoMatches:function(){return"کوئی ریکارڈ نہیں ملا"},formatRefresh:function(){return"تازہ کریں"},formatToggle:function(){return"تبدیل کریں"},formatColumns:function(){return"کالم"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["ur-PK"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["uz-Latn-UZ"]={formatLoadingMessage:function(){return"Yuklanyapti, iltimos kuting..."},formatRecordsPerPage:function(t){return t+" qator har sahifada"},formatShowingRows:function(t,r,n){return"Ko'rsatypati "+t+" dan "+r+" gacha "+n+" qatorlarni"},formatSearch:function(){return"Qidirish"},formatNoMatches:function(){return"Hech narsa topilmadi"},formatPaginationSwitch:function(){return"Sahifalashni yashirish/ko'rsatish"},formatRefresh:function(){return"Yangilash"},formatToggle:function(){return"Ko'rinish"},formatColumns:function(){return"Ustunlar"},formatAllRows:function(){return"Hammasi"},formatExport:function(){return"Eksport"},formatClearFilters:function(){return"Filtrlarni tozalash"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["uz-Latn-UZ"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["vi-VN"]={formatLoadingMessage:function(){return"Đang tải..."},formatRecordsPerPage:function(t){return t+" bản ghi mỗi trang"},formatShowingRows:function(t,r,n){return"Hiển thị từ trang "+t+" đến "+r+" của "+n+" bảng ghi"},formatSearch:function(){return"Tìm kiếm"},formatNoMatches:function(){return"Không có dữ liệu"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["vi-VN"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["zh-CN"]={formatLoadingMessage:function(){return"正在努力地加载数据中，请稍候……"},formatRecordsPerPage:function(t){return"每页显示 "+t+" 条记录"},formatShowingRows:function(t,r,n){return"显示第 "+t+" 到第 "+r+" 条记录，总共 "+n+" 条记录"},formatDetailPagination:function(t){return"总共 "+t+" 条记录"},formatSearch:function(){return"搜索"},formatNoMatches:function(){return"没有找到匹配的记录"},formatPaginationSwitch:function(){return"隐藏/显示分页"},formatRefresh:function(){return"刷新"},formatToggle:function(){return"切换"},formatColumns:function(){return"列"},formatExport:function(){return"导出数据"},formatClearFilters:function(){return"清空过滤"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-CN"])}(jQuery),function(t){"use strict";t.fn.bootstrapTable.locales["zh-TW"]={formatLoadingMessage:function(){return"正在努力地載入資料，請稍候……"},formatRecordsPerPage:function(t){return"每頁顯示 "+t+" 項記錄"},formatShowingRows:function(t,r,n){return"顯示第 "+t+" 到第 "+r+" 項記錄，總共 "+n+" 項記錄"},formatDetailPagination:function(t,r,n){return"總共 "+n+" 項記錄"},formatSearch:function(){return"搜尋"},formatNoMatches:function(){return"沒有找到符合的結果"},formatPaginationSwitch:function(){return"隱藏/顯示分頁"},formatRefresh:function(){return"重新整理"},formatToggle:function(){return"切換"},formatColumns:function(){return"列"}},t.extend(t.fn.bootstrapTable.defaults,t.fn.bootstrapTable.locales["zh-TW"])}(jQuery);