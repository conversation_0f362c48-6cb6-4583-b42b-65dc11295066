{"version": 3, "file": "index.js", "sources": ["pages_app/entervenue/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGVudGVydmVudWVcaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"entervenue\">\r\n    <view class=\"content\">\r\n      <my-header\r\n        :isBack=\"true\"\r\n        :isShowHome=\"true\"\r\n        title=\"参观预约\"\r\n        background=\"transparent\"\r\n      />\r\n      <view class=\"venue_content\">\r\n        <view class=\"venue_top\">\r\n          <view class=\"calendar\">\r\n            <uni-calendar\r\n              :date=\"timeSection.start\"\r\n              :insert=\"true\"\r\n              :lunar=\"false\"\r\n              :showMonth=\"false\"\r\n              :start-date=\"timeSection.start\"\r\n              :end-date=\"timeSection.end\"\r\n              :selected=\"selected\"\r\n              @change=\"getVenueDetail\"\r\n            />\r\n          </view>\r\n        </view>\r\n        <view :class=\"['venue_bottom', isChooseVenue ? 'isChooseVenue' : '']\">\r\n          <view class=\"show_fixed_box\">\r\n            <view class=\"title\">预约场次</view>\r\n            <view class=\"venue_list\">\r\n              <view class=\"venue_picker\">\r\n                <uni-data-picker\r\n                  :localdata=\"localdata\"\r\n                  placeholder=\"点击选择入馆时间\"\r\n                  popup-title=\"请选择时间\"\r\n                  v-model=\"venueId\"\r\n                  @change=\"bindPickerChange\"\r\n                />\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"line\">\r\n              <view class=\"line_item\" v-for=\"(item, index) in 20\" :key=\"index\"></view>\r\n            </view>\r\n\r\n            <view class=\"title\">预约人数</view>\r\n            <view class=\"chooseNum\">\r\n              <view\r\n                :class=\"['checkItem', checkIndex === index ? 'isCheck' : '']\"\r\n                v-for=\"(item, index) in checkItem\"\r\n                :key=\"index\"\r\n                @click=\"checkNum(item.value)\"\r\n              >\r\n                {{ item.label }}\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"isChoose\" v-if=\"isChooseVenue\">\r\n              <view class=\"title\">人员名单</view>\r\n              <view class=\"contactList\">\r\n                <view\r\n                  class=\"contactItem\"\r\n                  v-for=\"(item, index) in contactList\"\r\n                  :key=\"item.linkId\"\r\n                >\r\n                  <view class=\"left\">\r\n                    <view class=\"name\">{{ item.linkmanName }}</view>\r\n                    <view class=\"phone\">{{ item.linkmanPhone }}</view>\r\n                    <view class=\"idcard\">{{ hideIdCard(item.linkmanCertificate) }}</view>\r\n                  </view>\r\n                  <view class=\"right\">\r\n                    <view\r\n                      :class=\"['checkbox', item.linkCheck ? 'checked' : '']\"\r\n                      @click=\"toggleContact(index)\"\r\n                    >\r\n                      <text v-if=\"item.linkCheck\" class=\"check-icon\">✓</text>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n\r\n              <view class=\"addContact\" @click=\"addContact\">\r\n                <text class=\"add-icon\">+</text>\r\n                <text>添加联系人</text>\r\n              </view>\r\n            </view>\r\n\r\n            <view class=\"submit_btn\" v-if=\"isChooseVenue\">\r\n              <button\r\n                class=\"submit\"\r\n                @click=\"submitReservation\"\r\n                :disabled=\"isSubmitting\"\r\n              >\r\n                {{ isSubmitting ? '提交中...' : '确认预约' }}\r\n              </button>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport MyHeader from '@/components/my-header/my-header.vue'\r\n\r\nexport default {\r\n  name: 'EnterVenue',\r\n  components: {\r\n    MyHeader\r\n  },\r\n  data() {\r\n    return {\r\n      // 时间选择相关\r\n      timeSection: {\r\n        start: '',\r\n        end: ''\r\n      },\r\n      selected: [],\r\n      checkDate: '',\r\n\r\n      // 场次选择相关\r\n      localdata: [],\r\n      venueId: '',\r\n      venueInfo: [],\r\n      isChooseVenue: false,\r\n\r\n      // 人数选择相关\r\n      checkItem: [\r\n        { label: '1人', value: 1 },\r\n        { label: '2人', value: 2 },\r\n        { label: '3人', value: 3 },\r\n        { label: '4人', value: 4 },\r\n        { label: '5人', value: 5 }\r\n      ],\r\n      checkIndex: -1,\r\n      selectedNum: 0,\r\n\r\n      // 联系人相关\r\n      contactList: [],\r\n\r\n      // 提交状态\r\n      isSubmitting: false\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.initTimeSection()\r\n    this.getContactList()\r\n  },\r\n\r\n  onShow() {\r\n    this.getContactList()\r\n  },\r\n\r\n  methods: {\r\n    // 初始化时间范围\r\n    initTimeSection() {\r\n      const today = new Date()\r\n      const endDate = new Date()\r\n      endDate.setDate(today.getDate() + 30) // 30天内可预约\r\n\r\n      this.timeSection = {\r\n        start: this.formatDate(today),\r\n        end: this.formatDate(endDate)\r\n      }\r\n\r\n      // 默认选择今天\r\n      this.checkDate = this.formatDate(today)\r\n      this.selected = [{\r\n        date: this.formatDate(today),\r\n        info: '今天'\r\n      }]\r\n\r\n      this.getVenueDetail({ fulldate: this.formatDate(today) })\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(date) {\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      return `${year}-${month}-${day}`\r\n    },\r\n\r\n    // 获取场次详情\r\n    async getVenueDetail(e) {\r\n      try {\r\n        this.checkDate = e.fulldate\r\n\r\n        const res = await this.$myRequest({\r\n          url: '/web/venue/getVenueByDate',\r\n          method: 'get',\r\n          data: {\r\n            venueDate: e.fulldate\r\n          }\r\n        })\r\n\r\n        if (res.code === 200 && res.data.data) {\r\n          this.venueInfo = res.data.data\r\n          this.localdata = res.data.data.map(item => ({\r\n            value: item.id,\r\n            text: `${item.venueStartTime}-${item.venueEndTime} (剩余${item.inventoryVotes})`\r\n          }))\r\n        } else {\r\n          this.venueInfo = []\r\n          this.localdata = []\r\n          uni.showToast({\r\n            title: '该日期暂无可预约场次',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('获取场次失败:', error)\r\n        uni.showToast({\r\n          title: '获取场次失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 选择场次\r\n    bindPickerChange(e) {\r\n      this.venueId = e.detail.value\r\n      this.isChooseVenue = true\r\n    },\r\n\r\n    // 选择人数\r\n    checkNum(num) {\r\n      this.selectedNum = num\r\n      this.checkIndex = this.checkItem.findIndex(item => item.value === num)\r\n    },\r\n\r\n    // 获取联系人列表\r\n    async getContactList() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/linkman/getLinkmanList',\r\n          method: 'get'\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          this.contactList = (res.data.data || []).map(item => ({\r\n            ...item,\r\n            linkCheck: false\r\n          }))\r\n        }\r\n      } catch (error) {\r\n        console.error('获取联系人失败:', error)\r\n      }\r\n    },\r\n\r\n    // 切换联系人选择状态\r\n    toggleContact(index) {\r\n      const selectedCount = this.contactList.filter(item => item.linkCheck).length\r\n\r\n      if (!this.contactList[index].linkCheck && selectedCount >= this.selectedNum) {\r\n        uni.showToast({\r\n          title: `最多只能选择${this.selectedNum}人`,\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      this.contactList[index].linkCheck = !this.contactList[index].linkCheck\r\n    },\r\n\r\n    // 添加联系人\r\n    addContact() {\r\n      uni.navigateTo({\r\n        url: '/pages_app/contacts/addcontact'\r\n      })\r\n    },\r\n\r\n    // 隐藏身份证号中间部分\r\n    hideIdCard(idCard) {\r\n      if (!idCard || idCard.length < 8) return idCard\r\n      return idCard.replace(idCard.substring(4, 15), '*******')\r\n    },\r\n\r\n    // 提交预约\r\n    async submitReservation() {\r\n      if (this.isSubmitting) return\r\n\r\n      // 验证选择\r\n      if (!this.venueId) {\r\n        uni.showToast({\r\n          title: '请选择预约场次',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.selectedNum === 0) {\r\n        uni.showToast({\r\n          title: '请选择预约人数',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      const selectedContacts = this.contactList.filter(item => item.linkCheck)\r\n      if (selectedContacts.length === 0) {\r\n        uni.showToast({\r\n          title: '请选择联系人',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (selectedContacts.length !== this.selectedNum) {\r\n        uni.showToast({\r\n          title: `请选择${this.selectedNum}位联系人`,\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.isSubmitting = true\r\n\r\n        const res = await this.$myRequest({\r\n          url: '/web/venue/createVenueOrder',\r\n          method: 'post',\r\n          data: {\r\n            venueId: this.venueId,\r\n            venueDate: this.checkDate,\r\n            linkmanIds: selectedContacts.map(item => item.linkId).join(','),\r\n            peopleNum: this.selectedNum\r\n          }\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          uni.showToast({\r\n            title: '预约成功',\r\n            icon: 'success',\r\n            duration: 2000\r\n          })\r\n\r\n          setTimeout(() => {\r\n            uni.navigateTo({\r\n              url: `/pages_app/schemesuccess/venuesuccess?orderId=${res.data.data.orderId}`\r\n            })\r\n          }, 2000)\r\n        } else {\r\n          throw new Error(res.msg || '预约失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('提交预约失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '预约失败',\r\n          icon: 'error'\r\n        })\r\n      } finally {\r\n        this.isSubmitting = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.entervenue {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\r\n  .content {\r\n    padding-top: 120rpx; // 为头部留出空间\r\n\r\n    .venue_content {\r\n      .venue_top {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        margin: 20rpx;\r\n        border-radius: 20rpx;\r\n        overflow: hidden;\r\n        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n        .calendar {\r\n          padding: 20rpx;\r\n        }\r\n      }\r\n\r\n      .venue_bottom {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        margin: 20rpx;\r\n        border-radius: 20rpx;\r\n        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n        transition: all 0.3s ease;\r\n\r\n        &.isChooseVenue {\r\n          margin-bottom: 120rpx;\r\n        }\r\n\r\n        .show_fixed_box {\r\n          padding: 30rpx;\r\n\r\n          .title {\r\n            font-size: 32rpx;\r\n            font-weight: 600;\r\n            color: #333;\r\n            margin-bottom: 20rpx;\r\n          }\r\n\r\n          .venue_list {\r\n            margin-bottom: 30rpx;\r\n\r\n            .venue_picker {\r\n              border: 2rpx solid #e5e5e5;\r\n              border-radius: 12rpx;\r\n              overflow: hidden;\r\n              transition: border-color 0.3s ease;\r\n\r\n              &:focus-within {\r\n                border-color: #667eea;\r\n              }\r\n            }\r\n          }\r\n\r\n          .line {\r\n            display: flex;\r\n            justify-content: center;\r\n            margin: 40rpx 0;\r\n\r\n            .line_item {\r\n              width: 8rpx;\r\n              height: 8rpx;\r\n              background: #ddd;\r\n              border-radius: 50%;\r\n              margin: 0 4rpx;\r\n\r\n              &:nth-child(odd) {\r\n                background: #667eea;\r\n              }\r\n            }\r\n          }\r\n\r\n          .chooseNum {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            gap: 20rpx;\r\n            margin-bottom: 30rpx;\r\n\r\n            .checkItem {\r\n              flex: 1;\r\n              min-width: 120rpx;\r\n              height: 80rpx;\r\n              border: 2rpx solid #e5e5e5;\r\n              border-radius: 12rpx;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              font-size: 28rpx;\r\n              color: #666;\r\n              transition: all 0.3s ease;\r\n\r\n              &.isCheck {\r\n                border-color: #667eea;\r\n                background: #667eea;\r\n                color: white;\r\n              }\r\n            }\r\n          }\r\n\r\n          .isChoose {\r\n            .contactList {\r\n              margin-bottom: 30rpx;\r\n\r\n              .contactItem {\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: space-between;\r\n                padding: 20rpx 0;\r\n                border-bottom: 1rpx solid #f0f0f0;\r\n\r\n                &:last-child {\r\n                  border-bottom: none;\r\n                }\r\n\r\n                .left {\r\n                  flex: 1;\r\n\r\n                  .name {\r\n                    font-size: 30rpx;\r\n                    font-weight: 500;\r\n                    color: #333;\r\n                    margin-bottom: 8rpx;\r\n                  }\r\n\r\n                  .phone {\r\n                    font-size: 26rpx;\r\n                    color: #666;\r\n                    margin-bottom: 6rpx;\r\n                  }\r\n\r\n                  .idcard {\r\n                    font-size: 24rpx;\r\n                    color: #999;\r\n                  }\r\n                }\r\n\r\n                .right {\r\n                  .checkbox {\r\n                    width: 48rpx;\r\n                    height: 48rpx;\r\n                    border: 2rpx solid #ddd;\r\n                    border-radius: 50%;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                    transition: all 0.3s ease;\r\n\r\n                    &.checked {\r\n                      background: linear-gradient(135deg, #667eea, #764ba2);\r\n                      border-color: #667eea;\r\n\r\n                      .check-icon {\r\n                        color: white;\r\n                        font-size: 28rpx;\r\n                        font-weight: bold;\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            .addContact {\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              padding: 20rpx;\r\n              border: 2rpx dashed #ddd;\r\n              border-radius: 12rpx;\r\n              color: #666;\r\n              font-size: 28rpx;\r\n              margin-bottom: 30rpx;\r\n              transition: all 0.3s ease;\r\n\r\n              &:active {\r\n                background: #f8f8f8;\r\n              }\r\n\r\n              .add-icon {\r\n                font-size: 36rpx;\r\n                margin-right: 10rpx;\r\n              }\r\n            }\r\n          }\r\n\r\n          .submit_btn {\r\n            .submit {\r\n              width: 100%;\r\n              height: 88rpx;\r\n              background: linear-gradient(135deg, #667eea, #764ba2);\r\n              color: white;\r\n              border-radius: 44rpx;\r\n              font-size: 32rpx;\r\n              font-weight: 500;\r\n              border: none;\r\n\r\n              &::after {\r\n                border: none;\r\n              }\r\n\r\n              &:disabled {\r\n                background: #ccc;\r\n                color: #999;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.entervenue {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/entervenue/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAsGA,MAAK,WAAY,MAAW;AAE5B,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA;AAAA,MAEL,aAAa;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACN;AAAA,MACD,UAAU,CAAE;AAAA,MACZ,WAAW;AAAA;AAAA,MAGX,WAAW,CAAE;AAAA,MACb,SAAS;AAAA,MACT,WAAW,CAAE;AAAA,MACb,eAAe;AAAA;AAAA,MAGf,WAAW;AAAA,QACT,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,QACzB,EAAE,OAAO,MAAM,OAAO,EAAE;AAAA,MACzB;AAAA,MACD,YAAY;AAAA,MACZ,aAAa;AAAA;AAAA,MAGb,aAAa,CAAE;AAAA;AAAA,MAGf,cAAc;AAAA,IAChB;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,gBAAgB;AACrB,SAAK,eAAe;AAAA,EACrB;AAAA,EAED,SAAS;AACP,SAAK,eAAe;AAAA,EACrB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,kBAAkB;AAChB,YAAM,QAAQ,oBAAI,KAAK;AACvB,YAAM,UAAU,oBAAI,KAAK;AACzB,cAAQ,QAAQ,MAAM,QAAO,IAAK,EAAE;AAEpC,WAAK,cAAc;AAAA,QACjB,OAAO,KAAK,WAAW,KAAK;AAAA,QAC5B,KAAK,KAAK,WAAW,OAAO;AAAA,MAC9B;AAGA,WAAK,YAAY,KAAK,WAAW,KAAK;AACtC,WAAK,WAAW,CAAC;AAAA,QACf,MAAM,KAAK,WAAW,KAAK;AAAA,QAC3B,MAAM;AAAA,OACP;AAED,WAAK,eAAe,EAAE,UAAU,KAAK,WAAW,KAAK,GAAG;AAAA,IACzD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,YAAM,OAAO,KAAK,YAAY;AAC9B,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC/B;AAAA;AAAA,IAGD,MAAM,eAAe,GAAG;AACtB,UAAI;AACF,aAAK,YAAY,EAAE;AAEnB,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,WAAW,EAAE;AAAA,UACf;AAAA,SACD;AAED,YAAI,IAAI,SAAS,OAAO,IAAI,KAAK,MAAM;AACrC,eAAK,YAAY,IAAI,KAAK;AAC1B,eAAK,YAAY,IAAI,KAAK,KAAK,IAAI,WAAS;AAAA,YAC1C,OAAO,KAAK;AAAA,YACZ,MAAM,GAAG,KAAK,cAAc,IAAI,KAAK,YAAY,OAAO,KAAK,cAAc;AAAA,UAC7E,EAAE;AAAA,eACG;AACL,eAAK,YAAY,CAAC;AAClB,eAAK,YAAY,CAAC;AAClBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,GAAG;AAClB,WAAK,UAAU,EAAE,OAAO;AACxB,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,SAAS,KAAK;AACZ,WAAK,cAAc;AACnB,WAAK,aAAa,KAAK,UAAU,UAAU,UAAQ,KAAK,UAAU,GAAG;AAAA,IACtE;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,eAAe,IAAI,KAAK,QAAQ,CAAE,GAAE,IAAI,WAAS;AAAA,YACpD,GAAG;AAAA,YACH,WAAW;AAAA,UACb,EAAE;AAAA,QACJ;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,yCAAA,YAAY,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,YAAM,gBAAgB,KAAK,YAAY,OAAO,UAAQ,KAAK,SAAS,EAAE;AAEtE,UAAI,CAAC,KAAK,YAAY,KAAK,EAAE,aAAa,iBAAiB,KAAK,aAAa;AAC3EA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,SAAS,KAAK,WAAW;AAAA,UAChC,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,WAAK,YAAY,KAAK,EAAE,YAAY,CAAC,KAAK,YAAY,KAAK,EAAE;AAAA,IAC9D;AAAA;AAAA,IAGD,aAAa;AACXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,OACN;AAAA,IACF;AAAA;AAAA,IAGD,WAAW,QAAQ;AACjB,UAAI,CAAC,UAAU,OAAO,SAAS;AAAG,eAAO;AACzC,aAAO,OAAO,QAAQ,OAAO,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,IACzD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI,KAAK;AAAc;AAGvB,UAAI,CAAC,KAAK,SAAS;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,KAAK,gBAAgB,GAAG;AAC1BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,YAAM,mBAAmB,KAAK,YAAY,OAAO,UAAQ,KAAK,SAAS;AACvE,UAAI,iBAAiB,WAAW,GAAG;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI,iBAAiB,WAAW,KAAK,aAAa;AAChDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,KAAK,WAAW;AAAA,UAC7B,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEA,UAAI;AACF,aAAK,eAAe;AAEpB,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,SAAS,KAAK;AAAA,YACd,WAAW,KAAK;AAAA,YAChB,YAAY,iBAAiB,IAAI,UAAQ,KAAK,MAAM,EAAE,KAAK,GAAG;AAAA,YAC9D,WAAW,KAAK;AAAA,UAClB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAED,qBAAW,MAAM;AACfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK,iDAAiD,IAAI,KAAK,KAAK,OAAO;AAAA,aAC5E;AAAA,UACF,GAAE,GAAI;AAAA,eACF;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,QACnC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnWA,GAAG,WAAW,eAAe;"}