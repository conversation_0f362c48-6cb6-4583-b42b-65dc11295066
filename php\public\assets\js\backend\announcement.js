define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        index: function () {

            // 获取公告内容数据
            $.get('announcement/edit', function (res) {
                if (res.code === 1 && res.data) {
                    var content = res.data.announcement.content || '';
                    $('#notice_announcement').val(content);

                    // entrance 渲染
                    if (Array.isArray(res.data.entrance)) {
                        res.data.entrance.forEach(item => {
                            switch (item.id) {
                                case 1: // 入馆预约须知
                                    $('#entrance_1_object').val(item.entrance_object || '');
                                    $('#entrance_1_register').val(item.entrance_register || '');
                                    $('#entrance_1_audit').val(item.entrance_audit || '');
                                    break;
                                case 2: // 观影预约须知
                                    $('#entrance_2_object').val(item.entrance_object || '');
                                    $('#entrance_2_register').val(item.entrance_register || '');
                                    $('#entrance_2_audit').val(item.entrance_audit || '');
                                    break;
                                case 3: // 观影预约成功（注意事项、开播条件）
                                    $('#beiyong_three').val(item.beiyong_three || '');
                                    $('#beiyong_four').val(item.beiyong_four || '');
                                    break;
                            }
                        });
                    }
                    Toastr.success("公告内容加载成功");
                } else {
                    Toastr.error(res.msg || "获取数据失败");
                }
            }, 'json');




            // 公告发布
            $(document).on('click', '#btn-announcement', function () {
                var content = $('#notice_announcement').val();
                $.post('announcement/edit', {
                    type: 'announcement',
                    content: content
                }, function (res) {
                    res.code === 1 ? Toastr.success("发布成功") : Toastr.error(res.msg || "发布失败");
                }, 'json');
            });

            // 入馆预约
            $(document).on('click', '#btn-entry', function () {
                var data = {
                    type: 'entry',
                    entrance_object: $('#entrance_1_object').val().trim(),
                    entrance_register: $('#entrance_1_register').val().trim(),
                    entrance_audit: $('#entrance_1_audit').val().trim(),
                };
                $.post('announcement/edit', data, function (res) {
                    res.code === 1 ? Toastr.success("发布成功") : Toastr.error(res.msg || "发布失败");
                }, 'json');
            });

            // 观影预约
            $(document).on('click', '#btn-movie', function () {
                var data = {
                    type: 'movie',
                    entrance_object: $('#entrance_2_object').val().trim(),
                    entrance_register: $('#entrance_2_register').val().trim(),
                    entrance_audit: $('#entrance_2_audit').val().trim()
                };
                $.post('announcement/edit', data, function (res) {
                    res.code === 1 ? Toastr.success("发布成功") : Toastr.error(res.msg || "发布失败");
                }, 'json');
            });

            // 观影成功提示
            $(document).on('click', '#btn-success', function () {
                var data = {
                    type: 'movie-success',
                    beiyong_three: $('#beiyong_three').val().trim(),
                    beiyong_four: $('#beiyong_four').val().trim()
                };
                $.post('announcement/edit', data, function (res) {
                    res.code === 1 ? Toastr.success("发布成功") : Toastr.error(res.msg || "发布失败");
                }, 'json');
            });
        }
    };
    return Controller;
});
