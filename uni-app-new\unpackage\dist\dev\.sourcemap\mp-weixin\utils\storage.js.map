{"version": 3, "file": "storage.js", "sources": ["utils/storage.js"], "sourcesContent": ["import { constant } from './constant.js'\r\n\r\nconst STORAGE_KEY = 'storage_data'\r\nconst allowedKeys = [\r\n  constant.avatar,\r\n  constant.username,\r\n  constant.password,\r\n  constant.roles,\r\n  constant.tenantId,\r\n  constant.rememberMe,\r\n  constant.permissions\r\n]\r\n\r\n// 本地存储 - 关键适配点：uni.setStorageSync 替代 wx.setStorageSync\r\nexport const storage = {\r\n  set(key, value) {\r\n    if (allowedKeys.indexOf(key) !== -1) {\r\n      try {\r\n        let storageData = uni.getStorageSync(STORAGE_KEY) || {}\r\n        storageData[key] = value\r\n        uni.setStorageSync(STORAGE_KEY, storageData)\r\n        return true\r\n      } catch (error) {\r\n        console.error('存储数据失败:', error)\r\n        return false\r\n      }\r\n    }\r\n    return false\r\n  },\r\n\r\n  get(key) {\r\n    try {\r\n      const storageData = uni.getStorageSync(STORAGE_KEY) || {}\r\n      return storageData[key] || ''\r\n    } catch (error) {\r\n      console.error('读取存储数据失败:', error)\r\n      return ''\r\n    }\r\n  },\r\n\r\n  remove(key) {\r\n    try {\r\n      let storageData = uni.getStorageSync(STORAGE_KEY) || {}\r\n      delete storageData[key]\r\n      uni.setStorageSync(STORAGE_KEY, storageData)\r\n      return true\r\n    } catch (error) {\r\n      console.error('删除存储数据失败:', error)\r\n      return false\r\n    }\r\n  },\r\n\r\n  clean() {\r\n    try {\r\n      uni.removeStorageSync(STORAGE_KEY)\r\n      return true\r\n    } catch (error) {\r\n      console.error('清空存储数据失败:', error)\r\n      return false\r\n    }\r\n  },\r\n\r\n  // 检查key是否被允许\r\n  isAllowedKey(key) {\r\n    return allowedKeys.indexOf(key) !== -1\r\n  },\r\n\r\n  // 获取所有存储的数据\r\n  getAll() {\r\n    try {\r\n      return uni.getStorageSync(STORAGE_KEY) || {}\r\n    } catch (error) {\r\n      console.error('获取所有存储数据失败:', error)\r\n      return {}\r\n    }\r\n  }\r\n}\r\n\r\n// 通用存储方法 - 跨平台兼容\r\nexport function setStorage(key, value) {\r\n  try {\r\n    // 平台兼容性处理\r\n    // #ifdef H5\r\n    // H5平台可能有存储限制，需要检查\r\n    if (typeof value === 'object') {\r\n      const jsonStr = JSON.stringify(value)\r\n      if (jsonStr.length > 5 * 1024 * 1024) { // 5MB限制\r\n        console.warn('存储数据过大，可能在某些平台失败')\r\n      }\r\n    }\r\n    // #endif\r\n    \r\n    uni.setStorageSync(key, value)\r\n    return true\r\n  } catch (error) {\r\n    console.error('存储失败:', error)\r\n    \r\n    // #ifdef H5\r\n    // H5平台存储失败时的降级处理\r\n    if (error.name === 'QuotaExceededError') {\r\n      console.error('存储空间不足，尝试清理旧数据')\r\n      // 可以在这里实现清理逻辑\r\n    }\r\n    // #endif\r\n    \r\n    return false\r\n  }\r\n}\r\n\r\nexport function getStorage(key) {\r\n  try {\r\n    const value = uni.getStorageSync(key)\r\n    \r\n    // #ifdef APP-PLUS\r\n    // App平台可能需要特殊处理\r\n    if (value === undefined || value === null) {\r\n      return null\r\n    }\r\n    // #endif\r\n    \r\n    return value\r\n  } catch (error) {\r\n    console.error('读取存储失败:', error)\r\n    return null\r\n  }\r\n}\r\n\r\nexport function removeStorage(key) {\r\n  try {\r\n    uni.removeStorageSync(key)\r\n    return true\r\n  } catch (error) {\r\n    console.error('删除存储失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\nexport function clearStorage() {\r\n  try {\r\n    uni.clearStorageSync()\r\n    return true\r\n  } catch (error) {\r\n    console.error('清空存储失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\n// 异步存储方法（推荐在不阻塞UI的场景使用）\r\nexport function setStorageAsync(key, value) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.setStorage({\r\n      key,\r\n      data: value,\r\n      success: () => resolve(true),\r\n      fail: (error) => {\r\n        console.error('异步存储失败:', error)\r\n        reject(error)\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\nexport function getStorageAsync(key) {\r\n  return new Promise((resolve, reject) => {\r\n    uni.getStorage({\r\n      key,\r\n      success: (res) => resolve(res.data),\r\n      fail: (error) => {\r\n        console.error('异步读取存储失败:', error)\r\n        resolve(null)\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\n// 获取存储信息\r\nexport function getStorageInfo() {\r\n  return new Promise((resolve, reject) => {\r\n    uni.getStorageInfo({\r\n      success: (res) => resolve(res),\r\n      fail: (error) => {\r\n        console.error('获取存储信息失败:', error)\r\n        reject(error)\r\n      }\r\n    })\r\n  })\r\n}"], "names": ["constant", "uni"], "mappings": ";;;AAEA,MAAM,cAAc;AACpB,MAAM,cAAc;AAAA,EAClBA,eAAAA,SAAS;AAAA,EACTA,eAAAA,SAAS;AAAA,EACTA,eAAAA,SAAS;AAAA,EACTA,eAAAA,SAAS;AAAA,EACTA,eAAAA,SAAS;AAAA,EACTA,eAAAA,SAAS;AAAA,EACTA,eAAAA,SAAS;AACX;AAGY,MAAC,UAAU;AAAA,EACrB,IAAI,KAAK,OAAO;AACd,QAAI,YAAY,QAAQ,GAAG,MAAM,IAAI;AACnC,UAAI;AACF,YAAI,cAAcC,cAAG,MAAC,eAAe,WAAW,KAAK,CAAE;AACvD,oBAAY,GAAG,IAAI;AACnBA,4BAAI,eAAe,aAAa,WAAW;AAC3C,eAAO;AAAA,MACR,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,KAAK;AAC9B,eAAO;AAAA,MACR;AAAA,IACF;AACD,WAAO;AAAA,EACR;AAAA,EAED,IAAI,KAAK;AACP,QAAI;AACF,YAAM,cAAcA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAE;AACzD,aAAO,YAAY,GAAG,KAAK;AAAA,IAC5B,SAAQ,OAAO;AACdA,oBAAAA,+CAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA,EAED,OAAO,KAAK;AACV,QAAI;AACF,UAAI,cAAcA,cAAG,MAAC,eAAe,WAAW,KAAK,CAAE;AACvD,aAAO,YAAY,GAAG;AACtBA,0BAAI,eAAe,aAAa,WAAW;AAC3C,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,+CAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA,EAED,QAAQ;AACN,QAAI;AACFA,oBAAG,MAAC,kBAAkB,WAAW;AACjC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdA,oBAAAA,+CAAc,aAAa,KAAK;AAChC,aAAO;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGD,aAAa,KAAK;AAChB,WAAO,YAAY,QAAQ,GAAG,MAAM;AAAA,EACrC;AAAA;AAAA,EAGD,SAAS;AACP,QAAI;AACF,aAAOA,oBAAI,eAAe,WAAW,KAAK,CAAE;AAAA,IAC7C,SAAQ,OAAO;AACdA,oBAAAA,MAAc,MAAA,SAAA,0BAAA,eAAe,KAAK;AAClC,aAAO,CAAE;AAAA,IACV;AAAA,EACF;AACH;;"}