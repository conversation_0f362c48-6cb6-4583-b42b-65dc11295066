{"id": "uni-popup", "displayName": "uni-popup 弹出层", "version": "1.9.10", "description": " Popup 组件，提供常用的弹层", "keywords": ["uni-ui", "弹出层", "弹窗", "popup", "弹框"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": "", "uni-app": "^4.06", "uni-app-x": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["uni-scss", "uni-transition"], "encrypt": [], "platforms": {"cloud": {"tcb": "x", "aliyun": "x", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "√", "lark": "-"}, "quickapp": {"huawei": "-", "union": "-"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}}