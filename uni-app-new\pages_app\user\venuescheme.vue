<template>
  <view class="venue-scheme">
    <!-- 自定义头部 -->
    <my-header 
      title="参观记录" 
      :isBack="true" 
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
    />
    
    <!-- 记录列表 -->
    <scroll-view 
      class="scheme-list" 
      scroll-y 
      @scrolltolower="loadMore"
      :style="{ paddingBottom: safeAreaBottom + 'px' }"
    >
      <view class="list-container">
        <!-- 空状态 -->
        <view v-if="venueList.length === 0 && !loading" class="empty-state">
          <image class="empty-icon" src="/static/img/common/empty.png" mode="aspectFit" />
          <text class="empty-text">暂无参观记录</text>
        </view>
        
        <!-- 记录项 -->
        <view 
          v-for="(item, index) in venueList" 
          :key="item.venueSessionId || index"
          class="scheme-item"
        >
          <!-- 标题 -->
          <view class="item-header">
            <text class="venue-name">宝安科技馆 入馆预约</text>
            <view :class="['status-badge', getStatusClass(item.subscribeState)]">
              {{ getStatusText(item.subscribeState) }}
            </view>
          </view>
          
          <!-- 预约信息 -->
          <view class="venue-info">
            <view class="info-row">
              <text class="label">预约日期：</text>
              <text class="value important">{{ item.date }}</text>
            </view>
            <view class="info-row">
              <text class="label">预约时段：</text>
              <text class="value">{{ item.venueStartTime }} - {{ item.venueEndTime }}</text>
            </view>
            <view class="info-row">
              <text class="label">预约人数：</text>
              <text class="value">{{ item.subscribeType }}人</text>
            </view>
            <view class="info-row" v-if="item.linkmanName">
              <text class="label">联系人：</text>
              <text class="value">{{ item.linkmanName }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="action-buttons" v-if="showActionButtons(item.subscribeState)">
            <button 
              v-if="canCancel(item.subscribeState)"
              class="cancel-btn"
              @tap="cancelBooking(item)"
            >
              取消预约
            </button>
            <button 
              v-if="canViewQRCode(item.subscribeState)"
              class="qrcode-btn"
              @tap="viewQRCode(item)"
            >
              查看凭证
            </button>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="loading" class="loading-more">
          <text class="loading-text">加载中...</text>
        </view>
        
        <view v-if="!hasMore && venueList.length > 0" class="no-more">
          <text class="no-more-text">没有更多记录了</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'VenueScheme',
  data() {
    return {
      venueList: [],
      statusTextList: ['去签到', '已过期(未检票)', '已取消', '去检票', '已完成', '已过期(未签到)', '场次取消'],
      
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      
      // 安全区域
      safeAreaBottom: 0
    }
  },
  
  onLoad() {
    // 获取安全区域信息
    const systemInfo = uni.getSystemInfoSync()
    this.safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0
  },
  
  onShow() {
    this.resetData()
    this.getVenueList()
  },
  
  methods: {
    // 重置数据
    resetData() {
      this.venueList = []
      this.pageNum = 1
      this.total = 0
      this.hasMore = true
    },
    
    // 获取参观记录列表
    async getVenueList() {
      if (this.loading || !this.hasMore) return
      
      this.loading = true
      
      try {
        const res = await this.$myRequest({
          url: '/web/venueSession/personalCenterVenue',
          method: 'get',
          data: {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        })
        
        if (res.code === 200) {
          const dataList = res.data.data.rows || []
          this.total = res.data.data.total || 0
          
          // 处理数据
          const processedList = dataList.map(item => ({
            ...item,
            date: this.formatDate(item.venueArrangedDate),
            venueStartTime: this.formatTime(item.venueStartTime),
            venueEndTime: this.formatTime(item.venueEndTime)
          }))
          
          if (this.pageNum === 1) {
            this.venueList = processedList
          } else {
            this.venueList.push(...processedList)
          }
          
          // 检查是否还有更多数据
          this.hasMore = this.venueList.length < this.total
        } else {
          throw new Error(res.msg || '获取记录失败')
        }
      } catch (error) {
        console.error('获取参观记录失败:', error)
        uni.showToast({
          title: error.message || '获取记录失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return
      
      this.pageNum++
      this.getVenueList()
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      
      try {
        const date = new Date(dateStr.replace(/-/g, '/'))
        const weekList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        const weekDay = weekList[date.getDay()]
        return `${dateStr} ${weekDay}`
      } catch (error) {
        return dateStr
      }
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      
      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },
    
    // 获取状态文本
    getStatusText(state) {
      if (state >= 0 && state < this.statusTextList.length) {
        return this.statusTextList[state]
      }
      return '未知状态'
    },
    
    // 获取状态样式类
    getStatusClass(state) {
      const statusMap = {
        0: 'status-pending',    // 去签到
        1: 'status-expired',    // 已过期(未检票)
        2: 'status-cancelled',  // 已取消
        3: 'status-checkin',    // 去检票
        4: 'status-completed',  // 已完成
        5: 'status-expired',    // 已过期(未签到)
        6: 'status-cancelled'   // 场次取消
      }
      return statusMap[state] || 'status-unknown'
    },
    
    // 是否显示操作按钮
    showActionButtons(state) {
      return state === 1 || state === 4 || this.canCancel(state)
    },
    
    // 是否可以取消
    canCancel(state) {
      return state === 1 || state === 4
    },
    
    // 是否可以查看二维码
    canViewQRCode(state) {
      return state === 1 || state === 4
    },
    
    // 取消预约
    cancelBooking(item) {
      uni.navigateTo({
        url: `/pages_app/schemesuccess/venuecancel?vote=${item.subscribeType}&batchNumber=${item.batchNumber}&venueSessionId=${item.venueSessionId}`
      })
    },
    
    // 查看二维码
    viewQRCode(item) {
      uni.navigateTo({
        url: `/pages_app/schemesuccess/venuesuccess?batchNumber=${item.batchNumber}&venueSessionId=${item.venueSessionId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.venue-scheme {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .scheme-list {
    height: calc(100vh - 88rpx);

    .list-container {
      padding: 20rpx;
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-icon {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 30rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  // 记录项
  .scheme-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 30rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;

      .venue-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-pending {
          background: #e3f2fd;
          color: #1976d2;
        }

        &.status-expired {
          background: #fce4ec;
          color: #c2185b;
        }

        &.status-cancelled {
          background: #f3e5f5;
          color: #7b1fa2;
        }

        &.status-checkin {
          background: #e8f5e8;
          color: #388e3c;
        }

        &.status-completed {
          background: #e8f5e8;
          color: #388e3c;
        }

        &.status-unknown {
          background: #f5f5f5;
          color: #666;
        }
      }
    }

    .venue-info {
      margin-bottom: 20rpx;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 28rpx;
          color: #666;
          min-width: 140rpx;
        }

        .value {
          font-size: 28rpx;
          color: #333;
          flex: 1;

          &.important {
            color: #1976d2;
            font-weight: 600;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 20rpx;

      .cancel-btn,
      .qrcode-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        font-weight: 500;
        border: none;

        &::after {
          border: none;
        }
      }

      .cancel-btn {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
      }

      .qrcode-btn {
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
      }
    }
  }

  // 加载状态
  .loading-more {
    display: flex;
    justify-content: center;
    padding: 40rpx;

    .loading-text {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .no-more {
    display: flex;
    justify-content: center;
    padding: 40rpx;

    .no-more-text {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.venue-scheme {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.scheme-list {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
