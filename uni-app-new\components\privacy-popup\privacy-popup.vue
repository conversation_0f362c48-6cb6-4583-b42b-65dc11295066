<template>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="privacy-popup">
      <view class="privacy-header">
        <text class="privacy-title">隐私保护指引</text>
      </view>
      <view class="privacy-content">
        <text class="privacy-text">
          感谢您使用宝安科技馆预约服务平台！我们非常重视您的隐私保护和个人信息安全。
          在您使用我们的服务前，请您仔细阅读并充分理解《隐私政策》的各项条款。
        </text>
        <text class="privacy-text">
          我们会严格按照法律法规要求和隐私政策使用您的个人信息，为您提供更好的服务体验。
        </text>
      </view>
      <view class="privacy-buttons">
        <button class="privacy-btn reject-btn" @tap="handleReject">拒绝</button>
        <button class="privacy-btn agree-btn" @tap="handleAgree">同意</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'PrivacyPopup',
  props: {
    id: {
      type: String,
      default: 'privacy-popup'
    }
  },
  data() {
    return {
      isShow: false
    }
  },
  mounted() {
    // 检查是否需要显示隐私政策弹窗
    this.checkPrivacyStatus()
  },
  watch: {
    // 监听父组件传入的显示状态
    '$parent.showPrivacy'(newVal) {
      if (newVal) {
        this.show()
      } else {
        this.hide()
      }
    }
  },
  methods: {
    checkPrivacyStatus() {
      // 检查用户是否已经同意隐私政策
      try {
        const privacyAgreed = uni.getStorageSync('privacy_agreed')
        if (!privacyAgreed) {
          // 延迟显示，确保页面渲染完成
          this.$nextTick(() => {
            this.show()
          })
        }
      } catch (error) {
        console.error('检查隐私状态失败:', error)
        // 出错时默认显示隐私政策
        this.show()
      }
    },
    show() {
      this.isShow = true
      this.$refs.popup.open()
    },
    hide() {
      this.isShow = false
      this.$refs.popup.close()
    },
    handleAgree() {
      try {
        // 用户同意隐私政策
        uni.setStorageSync('privacy_agreed', true)
        uni.setStorageSync('privacy_agreed_time', Date.now())
        
        this.hide()
        
        // 触发同意事件
        this.$emit('agree')
        
        // 全局事件通知
        uni.$emit && uni.$emit('privacyAgree', true)
        
        uni.showToast({
          title: '感谢您的信任',
          icon: 'success',
          duration: 1500
        })
      } catch (error) {
        console.error('保存隐私政策同意状态失败:', error)
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        })
      }
    },
    handleReject() {
      // 用户拒绝隐私政策
      this.hide()
      
      // 触发拒绝事件
      this.$emit('reject')
      
      // 全局事件通知
      uni.$emit('privacyReject', false)
      
      uni.showModal({
        title: '提示',
        content: '拒绝隐私政策将无法使用相关功能，您可以稍后在设置中重新选择。',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }
}
</script>

<style scoped>
.privacy-popup {
  width: 600rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
}

.privacy-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.privacy-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.privacy-content {
  margin-bottom: 40rpx;
}

.privacy-text {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.privacy-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.privacy-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reject-btn {
  background: #f5f5f5;
  color: #666666;
}

.agree-btn {
  background: #007aff;
  color: #ffffff;
}

.privacy-btn:active {
  opacity: 0.8;
}
</style>