"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../common/vendor.js");
const TOKEN_KEY = "token";
const REQUEST_CODE_KEY = "requestCode";
function getToken() {
  try {
    return common_vendor.index.getStorageSync(TOKEN_KEY);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:9", "获取token失败:", error);
    return "";
  }
}
function setToken(token) {
  try {
    return common_vendor.index.setStorageSync(TOKEN_KEY, token);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:19", "设置token失败:", error);
    return false;
  }
}
function removeToken() {
  try {
    common_vendor.index.removeStorageSync(REQUEST_CODE_KEY);
    common_vendor.index.removeStorageSync(TOKEN_KEY);
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:31", "移除token失败:", error);
    return false;
  }
}
function hasToken() {
  const token = getToken();
  return token && token.length > 0;
}
function clearAuth() {
  try {
    removeToken();
    common_vendor.index.removeStorageSync("userInfo");
    common_vendor.index.removeStorageSync("openid");
    return true;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:51", "清除认证数据失败:", error);
    return false;
  }
}
function getOpenId() {
  try {
    return common_vendor.index.getStorageSync("openid");
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:67", "获取openid失败:", error);
    return "";
  }
}
function setOpenId(openid) {
  try {
    return common_vendor.index.setStorageSync("openid", openid);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/auth.js:82", "设置openid失败:", error);
    return false;
  }
}
exports.clearAuth = clearAuth;
exports.getOpenId = getOpenId;
exports.getToken = getToken;
exports.hasToken = hasToken;
exports.removeToken = removeToken;
exports.setOpenId = setOpenId;
exports.setToken = setToken;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/auth.js.map
