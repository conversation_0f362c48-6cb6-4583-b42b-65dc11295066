<?php

namespace app\admin\model\film;

use think\Model;


class Session extends Model
{

    

    

    // 表名
    protected $table = 'film_session';
    
    // 定义时间戳字段名
    protected $autoWriteTimestamp = 'datetime'; // 改为 'datetime'
    protected $dateFormat = 'Y-m-d H:i:s'; // 定义输出格式
    protected $createTime = 'create_time'; // 创建时间字段名
    protected $updateTime = 'update_time'; // 更新时间字段名

    // 追加属性
    protected $append = [

    ];
    
    
    public function film()
    {
        return $this->belongsTo(\app\common\model\film\Base::class, 'film_id', 'id')
        ->field('id, film_name,film_cover ,  film_type, film_time');
    }
    






}
