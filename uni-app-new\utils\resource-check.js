/**
 * 资源路径验证工具
 * 用于验证静态资源路径的正确性
 */

// 静态资源路径配置
export const STATIC_PATHS = {
  // 图标和Logo
  favicon: '/static/favicon.ico',
  logo: '/static/logo.png',
  
  // 字体图标
  iconfont: '/static/config/iconfont.css',
  
  // 配置文件
  webconfig: '/static/config/webconfig.json',
  
  // 通用图片
  common: {
    line: '/static/img/common/line.png'
  },
  
  // 首页图片
  home: {
    bg: '/static/img/home/<USER>',
    book: '/static/img/home/<USER>',
    cItem: '/static/img/home/<USER>',
    info: '/static/img/home/<USER>',
    lItem: '/static/img/home/<USER>',
    new: '/static/img/home/<USER>',
    qItem: '/static/img/home/<USER>',
    rili: '/static/img/home/<USER>',
    scalc: '/static/img/home/<USER>',
    zItem: '/static/img/home/<USER>'
  },
  
  // 用户相关图片
  user: '/static/img/user/',
  
  // 预约相关图片
  entervenue: '/static/img/entervenue/',
  vieworder: '/static/img/vieworder/',
  curriculum: '/static/img/curriculum/',
  
  // 成功页面图片
  schemesuccess: '/static/img/schemesuccess/',
  
  // 其他图片目录
  black: '/static/img/black/',
  filmdes: '/static/img/filmdes/',
  ordersate: '/static/img/ordersate/'
}

/**
 * 获取静态资源完整路径
 * @param {string} path 相对路径
 * @returns {string} 完整路径
 */
export function getStaticPath(path) {
  // 确保路径以 / 开头
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  
  // 在不同平台下处理路径
  // #ifdef H5
  return path
  // #endif
  
  // #ifdef MP-WEIXIN
  return path
  // #endif
  
  // #ifdef APP-PLUS
  return path
  // #endif
  
  return path
}

/**
 * 验证资源是否存在（仅在开发环境使用）
 * @param {string} path 资源路径
 * @returns {Promise<boolean>} 是否存在
 */
export function checkResourceExists(path) {
  return new Promise((resolve) => {
    // 在实际项目中，这里可以添加资源存在性检查逻辑
    // 目前只是简单返回 true
    resolve(true)
  })
}

/**
 * 批量验证资源路径
 * @param {Array<string>} paths 路径数组
 * @returns {Promise<Object>} 验证结果
 */
export async function batchCheckResources(paths) {
  const results = {}
  
  for (const path of paths) {
    try {
      const exists = await checkResourceExists(path)
      results[path] = exists
    } catch (error) {
      console.error(`检查资源失败: ${path}`, error)
      results[path] = false
    }
  }
  
  return results
}

/**
 * 获取图片资源路径（带平台适配）
 * @param {string} category 图片分类
 * @param {string} name 图片名称
 * @returns {string} 图片路径
 */
export function getImagePath(category, name) {
  const basePath = `/static/img/${category}/`
  
  // 确保文件扩展名
  if (!name.includes('.')) {
    name += '.png'
  }
  
  return getStaticPath(basePath + name)
}

// 导出常用的资源路径获取函数
export const getHomePath = (name) => getImagePath('home', name)
export const getUserPath = (name) => getImagePath('user', name)
export const getCommonPath = (name) => getImagePath('common', name)
export const getEnterVenuePath = (name) => getImagePath('entervenue', name)
export const getViewOrderPath = (name) => getImagePath('vieworder', name)
export const getCurriculumPath = (name) => getImagePath('curriculum', name)
export const getSchemeSuccessPath = (name) => getImagePath('schemesuccess', name)