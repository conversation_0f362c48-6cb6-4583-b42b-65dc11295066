{"version": 3, "file": "permission.js", "sources": ["permission.js"], "sourcesContent": ["// permission.js - 路由权限拦截\r\nimport { getToken } from './utils/auth.js'\r\n\r\n// 白名单路由 - 无需登录即可访问\r\nconst whiteList = [\r\n  '/pages_app/login/index',\r\n  '/pages_app/register/index', \r\n  '/pages/index/index',\r\n  '/pages_app/entervenue/index',\r\n  '/pages_app/vieworder/index',\r\n  '/pages_app/curriculum/index',\r\n  '/pages_app/user/index'\r\n]\r\n\r\n// 路由拦截器\r\nconst routerInterceptor = {\r\n  invoke(to) {\r\n    const token = getToken()\r\n    \r\n    if (token) {\r\n      // 已登录用户访问登录页，重定向到首页\r\n      if (to.url === '/pages_app/login/index') {\r\n        uni.reLaunch({\r\n          url: '/pages/index/index'\r\n        })\r\n        return false\r\n      }\r\n      return true\r\n    } else {\r\n      // 未登录用户\r\n      const path = to.url.split('?')[0]\r\n      if (whiteList.includes(path)) {\r\n        return true\r\n      } else {\r\n        // 重定向到首页\r\n        uni.reLaunch({\r\n          url: '/pages/index/index'\r\n        })\r\n        return false\r\n      }\r\n    }\r\n  },\r\n  fail(err) {\r\n    console.error('路由拦截失败:', err)\r\n  }\r\n}\r\n\r\n// 注册路由拦截器\r\nconst routerMethods = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']\r\nrouterMethods.forEach(method => {\r\n  uni.addInterceptor(method, routerInterceptor)\r\n})\r\n"], "names": ["getToken", "uni"], "mappings": ";;;AAIA,MAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,MAAM,oBAAoB;AAAA,EACxB,OAAO,IAAI;AACT,UAAM,QAAQA,WAAAA,SAAU;AAExB,QAAI,OAAO;AAET,UAAI,GAAG,QAAQ,0BAA0B;AACvCC,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA,QACf,CAAS;AACD,eAAO;AAAA,MACR;AACD,aAAO;AAAA,IACb,OAAW;AAEL,YAAM,OAAO,GAAG,IAAI,MAAM,GAAG,EAAE,CAAC;AAChC,UAAI,UAAU,SAAS,IAAI,GAAG;AAC5B,eAAO;AAAA,MACf,OAAa;AAELA,sBAAAA,MAAI,SAAS;AAAA,UACX,KAAK;AAAA,QACf,CAAS;AACD,eAAO;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA,EACD,KAAK,KAAK;AACRA,kBAAAA,4CAAc,WAAW,GAAG;AAAA,EAC7B;AACH;AAGA,MAAM,gBAAgB,CAAC,cAAc,cAAc,YAAY,WAAW;AAC1E,cAAc,QAAQ,YAAU;AAC9BA,sBAAI,eAAe,QAAQ,iBAAiB;AAC9C,CAAC;"}