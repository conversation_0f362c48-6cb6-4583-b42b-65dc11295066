define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'sys/user/index' + location.search,
                    // add_url: 'sys/user/add',
                    // edit_url: 'sys/user/edit',
                    del_url: 'sys/user/del',
                    multi_url: 'sys/user/multi',
                    import_url: 'sys/user/import',
                    table: 'sys_user',

                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                            search:false,
                            searchFormVisible: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('id')},
                        // {field: 'dept_id', title: __('Dept_id')},
                        {field: 'user_name', title: __('User_name'), operate: 'LIKE'},
                        {field: 'nick_name', title: __('Nick_name'), operate: 'LIKE'},
                        // {field: 'user_type', title: __('User_type'), operate: 'LIKE'},
                        // {field: 'email', title: __('Email'), operate: 'LIKE'},
                        // {field: 'name', title: __('Name'), operate: 'LIKE'},
                        {field: 'phonenumber', title: __('Phonenumber'), operate: 'LIKE'},
                        // {field: 'sex', title: __('Sex')},
                        {field: 'avatar', title: __('Avatar'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                        // {field: 'password', title: __('Password'), operate: 'LIKE'},

                        {
                            field: 'status',
                            title: __('Status'),
                            formatter: Table.api.formatter.toggle,
                            events: Table.api.events.toggle,
                            searchList: { "0": "启用", "1": "禁用" },
                            custom: { "0": "success", "1": "grey" }, // 控制颜色
                            yes: 0, // ✅ 反转 yes 和 no 的含义
                            no: 1
                        },
                        // {field: 'openid', title: __('Openid'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'session_key', title: __('Session_key'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'del_flag', title: __('Del_flag'), formatter: Table.api.formatter.flag},
                        // {field: 'login_ip', title: __('Login_ip'), operate: 'LIKE'},
                        // {field: 'login_date', title: __('Login_date'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'create_by', title: __('Create_by'), operate: 'LIKE'},
                        {field: 'create_time', title: __('Create_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'update_by', title: __('Update_by'), operate: 'LIKE'},
                        // {field: 'update_time', title: __('Update_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'remark', title: __('Remark'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'del',
                                    title: __('Delete'),
                                    icon: 'fa fa-trash',
                                    classname: 'btn btn-xs btn-danger btn-delone'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
