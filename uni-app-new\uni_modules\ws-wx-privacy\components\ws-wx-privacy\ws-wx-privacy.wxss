.ws-privacy-popup.data-v-5705b407 {
    background: linear-gradient(180deg,#e5edff,#fff);
    border-radius: 24rpx;
    box-sizing: border-box;
    overflow: hidden;
    padding: 48rpx;
    width: 560rpx
}

.ws-privacy-popup__header.data-v-5705b407 {
    align-items: center;
    color: #1a1a1a;
    display: flex;
    font-family: PingFangSC-Medium,PingFang SC;
    font-size: 36rpx;
    font-weight: 550;
    height: 52rpx;
    justify-content: center;
    line-height: 52rpx;
    margin-bottom: 48rpx;
    width: 100%
}

.ws-privacy-popup__container.data-v-5705b407 {
    box-sizing: border-box;
    color: #333;
    font-family: PingFangSC-Regular,PingFang SC;
    font-size: 28rpx;
    font-weight: 400;
    line-height: 48rpx;
    margin-bottom: 48rpx;
    width: 100%
}

.ws-privacy-popup__container-protocol.data-v-5705b407 {
    color: #4d80f0;
    font-weight: 550
}

.ws-privacy-popup__footer.data-v-5705b407 {
    display: flex;
    flex-direction: column
}

.ws-privacy-popup__footer .is-agree.data-v-5705b407,.ws-privacy-popup__footer .is-disagree.data-v-5705b407 {
    background: #fff;
    border-radius: 44rpx;
    color: #666;
    font-family: PingFangSC-Regular,PingFang SC;
    font-size: 32rpx;
    font-weight: 400;
    height: 88rpx;
    width: 100%
}

.ws-privacy-popup__footer .is-agree.data-v-5705b407 {
    background: #4d80f0;
    color: #fff;
    margin-bottom: 18rpx
}

.ws-privacy-popup__footer wx-button.data-v-5705b407 {
    border: none;
    outline: none
}

.ws-privacy-popup__footer wx-button.data-v-5705b407:after {
    border: none
}
