<template>
  <view class="register-container">
    <!-- 自定义头部 -->
    <my-header 
      title="用户注册" 
      :isBack="true" 
      background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      color="#ffffff"
    />
    
    <view class="register-content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit" />
        <text class="app-name">宝安科技馆</text>
        <text class="subtitle">用户注册</text>
      </view>
      
      <!-- 注册表单 -->
      <view class="form-section">
        <!-- 用户名输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon-user"></text>
            </view>
            <input 
              class="form-input" 
              placeholder="请输入用户名" 
              type="text" 
              v-model="registerForm.username"
              maxlength="20"
              @blur="validateUsername"
            />
          </view>
          <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
        </view>
        
        <!-- 手机号输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon-phone"></text>
            </view>
            <input 
              class="form-input" 
              placeholder="请输入手机号" 
              type="number" 
              v-model="registerForm.phone"
              maxlength="11"
              @blur="validatePhone"
            />
          </view>
          <text v-if="errors.phone" class="error-text">{{ errors.phone }}</text>
        </view>
        
        <!-- 短信验证码 -->
        <view class="input-group">
          <view class="input-wrapper verification-wrapper">
            <view class="input-icon">
              <text class="icon-code"></text>
            </view>
            <input 
              class="form-input verification-input" 
              placeholder="请输入短信验证码" 
              type="number" 
              v-model="registerForm.smsCode"
              maxlength="6"
            />
            <button 
              class="verification-btn"
              :class="{ disabled: !canSendSms || countdown > 0 }"
              :disabled="!canSendSms || countdown > 0"
              @tap="sendSmsCode"
            >
              {{ countdown > 0 ? `${countdown}s后重发` : '获取验证码' }}
            </button>
          </view>
          <text v-if="errors.smsCode" class="error-text">{{ errors.smsCode }}</text>
        </view>
        
        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon-password"></text>
            </view>
            <input 
              class="form-input" 
              placeholder="请输入密码" 
              type="password" 
              v-model="registerForm.password"
              maxlength="20"
              @blur="validatePassword"
            />
          </view>
          <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
          <text class="password-hint">密码至少8位，必须包含字母、数字、特殊符号</text>
        </view>
        
        <!-- 确认密码 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon-password"></text>
            </view>
            <input 
              class="form-input" 
              placeholder="请再次输入密码" 
              type="password" 
              v-model="registerForm.confirmPassword"
              maxlength="20"
              @blur="validateConfirmPassword"
            />
          </view>
          <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
        </view>
        
        <!-- 图形验证码 -->
        <view class="input-group" v-if="captchaEnabled">
          <view class="input-wrapper captcha-wrapper">
            <view class="input-icon">
              <text class="icon-shield"></text>
            </view>
            <input 
              class="form-input captcha-input" 
              placeholder="请输入图形验证码" 
              type="text" 
              v-model="registerForm.captcha"
              maxlength="4"
            />
            <view class="captcha-image" @tap="refreshCaptcha">
              <image v-if="codeUrl" class="captcha-img" :src="codeUrl" mode="aspectFit" />
              <text v-else class="refresh-text">点击刷新</text>
            </view>
          </view>
          <text v-if="errors.captcha" class="error-text">{{ errors.captcha }}</text>
        </view>
        
        <!-- 用户协议 -->
        <view class="agreement-section">
          <label class="agreement-checkbox">
            <checkbox 
              :checked="agreeToTerms" 
              @change="onAgreementChange"
              color="#667eea"
            />
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @tap="showUserAgreement">《用户协议》</text>
              和
              <text class="link-text" @tap="showPrivacyPolicy">《隐私政策》</text>
            </text>
          </label>
        </view>
        
        <!-- 注册按钮 -->
        <view class="button-section">
          <button 
            class="register-button"
            :class="{ disabled: !canRegister }"
            :disabled="!canRegister"
            @tap="handleRegister"
          >
            {{ isRegistering ? '注册中...' : '立即注册' }}
          </button>
        </view>
        
        <!-- 登录链接 -->
        <view class="login-link-section">
          <text class="login-link" @tap="goToLogin">已有账号？立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getCodeImg, register, registerWithPhone } from '@/api/login.js'
import { sendSmsCode, verifySmsCode, getUserAgreement, getPrivacyPolicy } from '@/api/common.js'
import MyHeader from '@/components/my-header/my-header.vue'

export default {
  components: {
    MyHeader
  },
  data() {
    return {
      // 验证码相关
      codeUrl: '',
      captchaEnabled: true,
      countdown: 0,
      timer: null,
      
      // 注册状态
      isRegistering: false,
      agreeToTerms: false,
      
      // 表单数据
      registerForm: {
        username: '',
        phone: '',
        password: '',
        confirmPassword: '',
        smsCode: '',
        captcha: '',
        uuid: ''
      },
      
      // 表单验证错误
      errors: {
        username: '',
        phone: '',
        password: '',
        confirmPassword: '',
        smsCode: '',
        captcha: ''
      }
    }
  },
  
  computed: {
    // 是否可以发送短信验证码
    canSendSms() {
      return this.isValidPhone(this.registerForm.phone) && this.countdown === 0
    },
    
    // 是否可以注册
    canRegister() {
      return this.agreeToTerms && 
             !this.isRegistering &&
             this.registerForm.username &&
             this.registerForm.phone &&
             this.registerForm.password &&
             this.registerForm.confirmPassword &&
             this.registerForm.smsCode &&
             (!this.captchaEnabled || this.registerForm.captcha) &&
             !Object.values(this.errors).some(error => error)
    }
  },
  
  created() {
    this.getCaptcha()
  },
  
  onUnload() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  
  methods: {
    // 获取图形验证码
    async getCaptcha() {
      try {
        const res = await getCodeImg()
        if (res.code === 200) {
          this.captchaEnabled = res.captchaEnabled !== false
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.registerForm.uuid = res.uuid
          }
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
      }
    },
    
    // 刷新图形验证码
    refreshCaptcha() {
      this.registerForm.captcha = ''
      this.errors.captcha = ''
      this.getCaptcha()
    },
    
    // 发送短信验证码
    async sendSmsCode() {
      if (!this.canSendSms) return
      
      try {
        uni.showLoading({ title: '发送中...' })
        
        const res = await sendSmsCode(this.registerForm.phone, 'register')
        
        if (res.code === 200) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'success'
          })
          
          // 开始倒计时
          this.startCountdown()
        } else {
          uni.showToast({
            title: res.msg || '发送失败',
            icon: 'none'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },
    
    // 表单验证方法
    validateUsername() {
      const username = this.registerForm.username.trim()
      if (!username) {
        this.errors.username = '请输入用户名'
      } else if (username.length < 3) {
        this.errors.username = '用户名至少3个字符'
      } else if (username.length > 20) {
        this.errors.username = '用户名不能超过20个字符'
      } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
        this.errors.username = '用户名只能包含字母、数字、下划线和中文'
      } else {
        this.errors.username = ''
      }
    },
    
    validatePhone() {
      const phone = this.registerForm.phone.trim()
      if (!phone) {
        this.errors.phone = '请输入手机号'
      } else if (!this.isValidPhone(phone)) {
        this.errors.phone = '请输入正确的手机号'
      } else {
        this.errors.phone = ''
      }
    },
    
    validatePassword() {
      const password = this.registerForm.password
      if (!password) {
        this.errors.password = '请输入密码'
      } else if (password.length < 8) {
        this.errors.password = '密码至少8位'
      } else if (!/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).{8,}$/.test(password)) {
        this.errors.password = '密码必须包含字母、数字、特殊符号'
      } else {
        this.errors.password = ''
      }
    },
    
    validateConfirmPassword() {
      const confirmPassword = this.registerForm.confirmPassword
      if (!confirmPassword) {
        this.errors.confirmPassword = '请确认密码'
      } else if (confirmPassword !== this.registerForm.password) {
        this.errors.confirmPassword = '两次输入的密码不一致'
      } else {
        this.errors.confirmPassword = ''
      }
    },
    
    // 验证手机号格式
    isValidPhone(phone) {
      return /^1[3-9]\d{9}$/.test(phone)
    },
    
    // 全面表单验证
    validateForm() {
      this.validateUsername()
      this.validatePhone()
      this.validatePassword()
      this.validateConfirmPassword()
      
      // 验证短信验证码
      if (!this.registerForm.smsCode) {
        this.errors.smsCode = '请输入短信验证码'
      } else if (this.registerForm.smsCode.length !== 6) {
        this.errors.smsCode = '验证码应为6位数字'
      } else {
        this.errors.smsCode = ''
      }
      
      // 验证图形验证码
      if (this.captchaEnabled) {
        if (!this.registerForm.captcha) {
          this.errors.captcha = '请输入图形验证码'
        } else if (this.registerForm.captcha.length !== 4) {
          this.errors.captcha = '验证码应为4位'
        } else {
          this.errors.captcha = ''
        }
      }
      
      return !Object.values(this.errors).some(error => error)
    },
    
    // 用户协议变更
    onAgreementChange(e) {
      this.agreeToTerms = e.detail.value.length > 0
    },
    
    // 显示用户协议
    async showUserAgreement() {
      try {
        const res = await getUserAgreement()
        if (res.code === 200) {
          uni.showModal({
            title: '用户协议',
            content: res.data.content || '用户协议内容',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '获取协议失败',
          icon: 'none'
        })
      }
    },
    
    // 显示隐私政策
    async showPrivacyPolicy() {
      try {
        const res = await getPrivacyPolicy()
        if (res.code === 200) {
          uni.showModal({
            title: '隐私政策',
            content: res.data.content || '隐私政策内容',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      } catch (error) {
        uni.showToast({
          title: '获取政策失败',
          icon: 'none'
        })
      }
    },
    
    // 注册处理
    async handleRegister() {
      if (!this.validateForm()) {
        return
      }
      
      if (!this.agreeToTerms) {
        uni.showToast({
          title: '请先同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }
      
      this.isRegistering = true
      
      try {
        // 先验证短信验证码
        const verifyRes = await verifySmsCode(
          this.registerForm.phone, 
          this.registerForm.smsCode, 
          'register'
        )
        
        if (verifyRes.code !== 200) {
          uni.showToast({
            title: '短信验证码错误',
            icon: 'none'
          })
          return
        }
        
        // 准备注册数据
        const registerData = {
          username: this.registerForm.username.trim(),
          phone: this.registerForm.phone.trim(),
          password: this.registerForm.password,
          smsCode: this.registerForm.smsCode
        }
        
        // 如果启用了图形验证码，添加相关字段
        if (this.captchaEnabled) {
          registerData.code = this.registerForm.captcha
          registerData.uuid = this.registerForm.uuid
        }
        
        const res = await registerWithPhone(registerData)
        
        if (res.code === 200) {
          uni.showModal({
            title: '注册成功',
            content: `恭喜您，账号 ${this.registerForm.username} 注册成功！`,
            showCancel: false,
            confirmText: '立即登录',
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.goToLogin()
              }
            }
          })
        } else {
          uni.showToast({
            title: res.msg || '注册失败',
            icon: 'none'
          })
          
          // 刷新图形验证码
          if (this.captchaEnabled) {
            this.refreshCaptcha()
          }
        }
      } catch (error) {
        console.error('注册失败:', error)
        uni.showToast({
          title: '注册失败，请重试',
          icon: 'none'
        })
        
        // 刷新图形验证码
        if (this.captchaEnabled) {
          this.refreshCaptcha()
        }
      } finally {
        this.isRegistering = false
      }
    },
    
    // 跳转到登录页面
    goToLogin() {
      uni.navigateTo({
        url: '/pages_app/login/index'
      })
    }
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.register-content {
  padding: 40rpx;
  min-height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
  margin-top: 40rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单区域 */
.form-section {
  flex: 1;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 输入组 */
.input-group {
  margin-bottom: 40rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 32rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
}

.form-input::placeholder {
  color: #adb5bd;
}

/* 验证码相关 */
.verification-wrapper {
  padding-right: 20rpx;
}

.verification-input {
  flex: 1;
}

.verification-btn {
  padding: 0 24rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}

.verification-btn.disabled {
  background: #adb5bd;
  color: #ffffff;
}

.verification-btn:not(.disabled):active {
  background: #5a67d8;
}

/* 图形验证码 */
.captcha-wrapper {
  padding-right: 20rpx;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 120rpx;
  height: 60rpx;
  margin-left: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e9ecef;
}

.captcha-img {
  width: 100%;
  height: 100%;
}

.refresh-text {
  font-size: 20rpx;
  color: #6c757d;
}

/* 错误提示 */
.error-text {
  display: block;
  color: #dc3545;
  font-size: 24rpx;
  margin-top: 10rpx;
  margin-left: 80rpx;
}

/* 密码提示 */
.password-hint {
  display: block;
  color: #6c757d;
  font-size: 22rpx;
  margin-top: 10rpx;
  margin-left: 80rpx;
}

/* 用户协议 */
.agreement-section {
  margin-bottom: 40rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
}

.agreement-text {
  margin-left: 20rpx;
  flex: 1;
}

.link-text {
  color: #667eea;
  text-decoration: underline;
}

/* 按钮区域 */
.button-section {
  margin-bottom: 40rpx;
}

.register-button {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}

.register-button.disabled {
  background: #adb5bd;
  color: #ffffff;
}

.register-button:not(.disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 登录链接 */
.login-link-section {
  text-align: center;
}

.login-link {
  color: #667eea;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .register-content {
    padding: 30rpx;
  }
  
  .form-section {
    padding: 40rpx 30rpx;
  }
  
  .logo {
    width: 100rpx;
    height: 100rpx;
  }
  
  .app-name {
    font-size: 42rpx;
  }
}

/* 平台兼容性 */
/* #ifdef H5 */
.register-container {
  max-width: 750rpx;
  margin: 0 auto;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.input-wrapper:focus-within {
  border-color: #667eea;
}
/* #endif */

/* #ifdef APP-PLUS */
.form-input {
  padding-left: 20rpx;
}
/* #endif */

/* 通用工具类 */
.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

/* 图标字体 - 使用简单文本图标 */
.icon-user::before {
  content: '👤';
}

.icon-phone::before {
  content: '📱';
}

.icon-code::before {
  content: '🔢';
}

.icon-password::before {
  content: '🔒';
}

.icon-shield::before {
  content: '🛡️';
}
</style>