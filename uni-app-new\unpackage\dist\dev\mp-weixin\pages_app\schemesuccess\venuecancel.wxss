/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.venue-cancel.data-v-b8f7c080 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.venue-cancel .content.data-v-b8f7c080 {
  padding: 20rpx;
  padding-top: 120rpx;
}
.venue-cancel .content .venue-info.data-v-b8f7c080 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.venue-cancel .content .venue-info .info-header.data-v-b8f7c080 {
  margin-bottom: 20rpx;
}
.venue-cancel .content .venue-info .info-header .venue-name.data-v-b8f7c080 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.venue-cancel .content .venue-info .venue-details .detail-row.data-v-b8f7c080 {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.venue-cancel .content .venue-info .venue-details .detail-row.data-v-b8f7c080:last-child {
  margin-bottom: 0;
}
.venue-cancel .content .venue-info .venue-details .detail-row .label.data-v-b8f7c080 {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}
.venue-cancel .content .venue-info .venue-details .detail-row .value.data-v-b8f7c080 {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.venue-cancel .content .venue-info .venue-details .detail-row .value.important.data-v-b8f7c080 {
  color: #1976d2;
  font-weight: 600;
}
.venue-cancel .content .contacts-section.data-v-b8f7c080 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.venue-cancel .content .contacts-section .section-title.data-v-b8f7c080 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item.data-v-b8f7c080 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item.data-v-b8f7c080:last-child {
  border-bottom: none;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .contact-info.data-v-b8f7c080 {
  flex: 1;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-name.data-v-b8f7c080 {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-phone.data-v-b8f7c080 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .contact-info .contact-id.data-v-b8f7c080 {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .checkbox.data-v-b8f7c080 {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .checkbox.checked.data-v-b8f7c080 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}
.venue-cancel .content .contacts-section .contacts-list .contact-item .checkbox.checked .check-icon.data-v-b8f7c080 {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}
.venue-cancel .content .action-section .cancel-btn.data-v-b8f7c080 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.venue-cancel .content .action-section .cancel-btn.data-v-b8f7c080::after {
  border: none;
}
.venue-cancel .content .action-section .cancel-btn.data-v-b8f7c080:disabled {
  background: #ccc;
  color: #999;
}

/* 平台特定样式 */
.content.data-v-b8f7c080 {
  padding-bottom: env(safe-area-inset-bottom);
}