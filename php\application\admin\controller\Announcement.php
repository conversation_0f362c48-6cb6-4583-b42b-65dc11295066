<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Cache;

/**
 * 公告管理
 *
 * @icon fa fa-circle-o
 */
class Announcement extends Backend
{
    /**
     * Announcement模型对象
     * @var \app\common\model\Announcement
     */
    protected $model = null;
    protected $model_entrance = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Announcement;
        $this->model_entrance = new \app\common\model\EntranceAnnouncement;
    }
    public function index()
    {
        return $this->view->fetch();
    }
    public function edit($ids = null)
    {
        if ($this->request->isPost()) {


            $type = $this->request->post('type');
            if (!$type) {
                return  $this->error(['code' => 0, 'msg' => '参数不完整']);
            }

            $params = $this->request->post();
            switch ($type) {
                case 'announcement':
                    $this->model->where('id', 1)->update(['content' => $params['content']]);
                    Cache::store('redis')->set('announcement', $params['content']);
                    break;
                case 'entry':
                    $data = [
                        'entrance_object' => $params['entrance_object'],
                        'entrance_register' => $params['entrance_register'],
                        'entrance_audit' => $params['entrance_audit']
                    ];
                    $this->model_entrance->where('id', 1)->update($data);
                    Cache::store('redis')->set('entry', $data);
                    break;
                case 'movie':
                    $data = [
                        'entrance_object' => $params['entrance_object'],
                        'entrance_register' => $params['entrance_register'],
                        'entrance_audit' => $params['entrance_audit']
                    ];
                    $this->model_entrance->where('id', 2)->update($data);
                    Cache::store('redis')->set('movie', $data);
                    break;
                case 'movie-success':
                    $data = [
                        'beiyong_three' => $params['beiyong_three'],
                        'beiyong_four' => $params['beiyong_four']
                    ];
                    $this->model_entrance->where('id', 3)->update($data);
                    Cache::store('redis')->set('movie-success', $data);
                    break;
                default:
                    return json(['code' => 0, 'msg' => '不支持的类型']);
            }
            return $this->success("保存成功");
        } else {

            $row = $this->model->get(1)->toArray();
            if (!$row) {
                return $this->error('公告未找到');
            }

            $row_entrance = $this->model_entrance
                ->where('id', 'in', [1, 2, 3])
                ->order('id', 'asc') // 确保顺序一致
                ->select();
            // 将 $row_entrance 转为数组
            // 将 $row_entrance 转为以 id 为键的数组
            $row_entrance = collection($row_entrance)->toArray();
            // 合并结构
            $data = [
                'announcement' => $row,
                'entrance' => $row_entrance,
            ];

            return $this->success("获取成功", null, $data);
        }
    }




    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    // public function index(){}
    public function add() {}
    // public function edit($ids = null){}
    public function del($ids = null) {}
    public function multi($ids = null) {}
    public function destroy($ids = null) {}
    public function restore($ids = null) {}
    public function recyclebin() {}
}
