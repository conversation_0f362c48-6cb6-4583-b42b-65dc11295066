{"name": "overtrue/socialite", "description": "A collection of OAuth 2 packages that extracts from laravel/socialite.", "keywords": ["OAuth", "social", "login", "Weibo", "WeChat", "QQ"], "autoload": {"psr-4": {"Overtrue\\Socialite\\": "src/"}}, "require": {"php": ">=5.6", "guzzlehttp/guzzle": "^5.0|^6.0|^7.0", "symfony/http-foundation": "^2.7|^3.0|^4.0|^5.0", "ext-json": "*"}, "require-dev": {"mockery/mockery": "~1.2", "phpunit/phpunit": "^6.0|^7.0|^8.0|^9.0"}, "license": "MIT", "authors": [{"name": "overtrue", "email": "<EMAIL>"}]}