"use strict";
const common_vendor = require("../../common/vendor.js");
const api_api = require("../../api/api.js");
const config = require("../../config.js");
const MyHeader = () => "../../components/my-header/my-header.js";
const PrivacyPopup = () => "../../components/privacy-popup/privacy-popup.js";
const _sfc_main = {
  components: {
    MyHeader,
    PrivacyPopup
  },
  data() {
    return {
      iSzgm: config.config.iSzgm,
      appId: "",
      initCode: "",
      jumpList: [
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/entervenue/index",
          name: "参观预约",
          id: 0
        },
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/vieworder/index",
          name: "观影预约",
          id: 1
        },
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/curriculum/index",
          name: "课程预约",
          id: 2
        },
        {
          bg: "/static/img/home/<USER>",
          icon: "/static/img/home/<USER>",
          path: "/pages_app/user/index",
          name: "个人中心",
          id: 3
        }
      ],
      notice: "",
      path: null,
      privacyAllow: false,
      showHeader: true,
      showPrivacy: false,
      isNavigating: false
      // 防止重复导航
    };
  },
  onShow() {
    this.getNotice();
  },
  onLoad() {
    this.initPrivacyCheck();
  },
  onReady() {
    this.checkPrivacyDisplay();
  },
  onUnload() {
    this.notice = "";
    this.path = null;
  },
  onHide() {
  },
  methods: {
    initPrivacyCheck() {
      const privacyAgreed = common_vendor.index.getStorageSync("privacy_agreed");
      if (privacyAgreed) {
        this.privacyAllow = true;
        this.showPrivacy = false;
      }
      try {
        common_vendor.index.requirePrivacyAuthorize({
          success: () => {
            this.privacyAllow = true;
            common_vendor.index.__f__("log", "at pages/index/index.vue:135", "微信隐私授权成功");
          },
          fail: (error) => {
            common_vendor.index.__f__("log", "at pages/index/index.vue:138", "微信隐私授权失败:", error);
            this.showPrivacy = true;
          },
          complete: () => {
          }
        });
      } catch (error) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:145", "requirePrivacyAuthorize API不可用:", error);
        if (!privacyAgreed) {
          this.showPrivacy = true;
        }
      }
    },
    async jumpTo(path, id) {
      if (this.isNavigating) {
        return;
      }
      if (!this.privacyAllow) {
        common_vendor.index.showToast({
          title: "请先同意隐私政策",
          icon: "none",
          duration: 3e3
        });
        return;
      }
      try {
        this.isNavigating = true;
        this.path = path;
        await common_vendor.index.navigateTo({
          url: this.path
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:187", "页面跳转失败:", error);
        common_vendor.index.showToast({
          title: "页面跳转失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        setTimeout(() => {
          this.isNavigating = false;
        }, 500);
      }
    },
    getNotice() {
      api_api.myRequest({
        url: "/apitp/announcement/getInfo",
        noLoadingFlag: true
        // 不显示加载提示，避免影响用户体验
      }).then((res) => {
        if (res.data && res.data.code === 200) {
          this.notice = res.data.msg || res.data.data;
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages/index/index.vue:209", "获取公告信息失败:", error);
      });
    },
    onPrivacyAgree() {
      this.privacyAllow = true;
      this.showPrivacy = false;
      common_vendor.index.__f__("log", "at pages/index/index.vue:216", "用户同意隐私政策");
      common_vendor.index.setStorageSync("privacy_agreed", true);
    },
    onPrivacyReject() {
      this.privacyAllow = false;
      this.showPrivacy = false;
      common_vendor.index.__f__("log", "at pages/index/index.vue:224", "用户拒绝隐私政策");
      common_vendor.index.showModal({
        title: "提示",
        content: "需要同意隐私政策才能使用应用功能",
        showCancel: false,
        confirmText: "我知道了"
      });
    },
    checkPrivacyDisplay() {
      const privacyAgreed = common_vendor.index.getStorageSync("privacy_agreed");
      if (!privacyAgreed) {
        this.showPrivacy = true;
      }
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  const _component_uni_notice_bar = common_vendor.resolveComponent("uni-notice-bar");
  const _easycom_privacy_popup2 = common_vendor.resolveComponent("privacy-popup");
  (_easycom_my_header2 + _component_uni_notice_bar + _easycom_privacy_popup2)();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
const _easycom_privacy_popup = () => "../../components/privacy-popup/privacy-popup.js";
if (!Math) {
  (_easycom_my_header + _easycom_privacy_popup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showHeader
  }, $data.showHeader ? {
    b: common_vendor.p({
      isBack: false,
      isShowHome: false,
      title: "宝安科技馆",
      color: "#fff",
      background: "transparent"
    })
  } : {}, {
    c: common_vendor.f($data.jumpList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: item.icon,
        c: index,
        d: `url(${item.bg}) no-repeat center center`,
        e: common_vendor.o(($event) => $options.jumpTo(item.path, item.id), index)
      };
    }),
    d: $data.notice
  }, $data.notice ? {
    e: common_vendor.p({
      scrollable: true,
      speed: 41.8,
      single: true,
      ["background-color"]: "#ffffff",
      text: $data.notice
    })
  } : {}, {
    f: $data.showPrivacy
  }, $data.showPrivacy ? {
    g: common_vendor.o($options.onPrivacyAgree),
    h: common_vendor.o($options.onPrivacyReject),
    i: common_vendor.p({
      id: "privacy-popup"
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
