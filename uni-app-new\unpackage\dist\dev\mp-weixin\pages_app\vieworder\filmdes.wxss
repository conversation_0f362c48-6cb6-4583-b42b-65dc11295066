/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.filmDes.data-v-3a54848b {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-family: "PingFang SC", sans-serif;
  min-height: 100vh;
  overflow: auto;
  position: relative;
  width: 100%;
}
.filmInfoContent.data-v-3a54848b {
  min-height: 100vh;
  width: 100%;
}
.content.data-v-3a54848b {
  width: 100%;
  padding-top: 120rpx;
}
.titlebar.data-v-3a54848b {
  background: url("data:image/png;base64,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") no-repeat top;
  background-size: cover;
  height: 96rpx;
  margin: 81rpx auto 0;
  width: calc(100% - 30rpx);
}
.main.data-v-3a54848b {
  height: 729rpx;
  margin: -50rpx auto 150rpx;
  position: relative;
  width: 654rpx;
}
.main.isChooseContact.data-v-3a54848b {
  height: auto;
  min-height: 729rpx;
}
.show_fixed.data-v-3a54848b {
  background: url("data:image/png;base64,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") no-repeat 50%;
  background-size: 100% 100%;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx;
}
.filmNew.data-v-3a54848b {
  margin-bottom: 40rpx;
}
.filmItem.data-v-3a54848b {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}
.itemLeft.data-v-3a54848b {
  display: flex;
  flex: 1;
}
.cover.data-v-3a54848b {
  width: 160rpx;
  height: 220rpx;
  border-radius: 12rpx;
  margin-right: 30rpx;
}
.fileInfo.data-v-3a54848b {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.fileInfo .filmName.data-v-3a54848b {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}
.fileInfo .filmType.data-v-3a54848b,
.fileInfo .filmTime.data-v-3a54848b,
.fileInfo .filmDate.data-v-3a54848b {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
  line-height: 1.3;
}
.itemRight.data-v-3a54848b {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}
.ticketType.data-v-3a54848b {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  padding: 10rpx 15rpx;
  margin-bottom: 15rpx;
  text-align: center;
}
.ticketType.hidden.data-v-3a54848b {
  display: none;
}
.ticketType .n_text.data-v-3a54848b {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 5rpx;
}
.ticketType .t_text.data-v-3a54848b {
  display: block;
  font-size: 28rpx;
  color: #ff4757;
  font-weight: bold;
}
.order_button.data-v-3a54848b {
  background: #ff6b6b;
  color: #ffffff;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  font-weight: bold;
  text-align: center;
  min-width: 120rpx;
}
.order_button.green.data-v-3a54848b {
  background: #2ed573;
}
.order_button.gray.data-v-3a54848b {
  background: #95a5a6;
}
.filmText.data-v-3a54848b {
  font-size: 28rpx;
  color: #555555;
  line-height: 1.6;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
}
.line.data-v-3a54848b {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}
.line .line_item.data-v-3a54848b {
  width: 8rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}
.line.lineshow.data-v-3a54848b {
  margin: 40rpx 0;
}
.orderNum.data-v-3a54848b {
  margin-bottom: 40rpx;
}
.orderTitle.data-v-3a54848b {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 25rpx;
  text-align: center;
}
.chooseNum.data-v-3a54848b {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 20rpx;
}
.checkItem.data-v-3a54848b {
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: #666666;
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  text-align: center;
  min-width: 100rpx;
  transition: all 0.3s ease;
}
.checkItem.isCheck.data-v-3a54848b {
  background: #2ed573;
  border-color: #2ed573;
  color: #ffffff;
  transform: scale(1.05);
}
.contact.data-v-3a54848b {
  margin-bottom: 40rpx;
}
.contactTitle.data-v-3a54848b {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 25rpx;
  text-align: center;
}
.contactList.data-v-3a54848b {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12rpx;
  padding: 20rpx;
}
.contactItem.data-v-3a54848b {
  display: flex;
  align-items: center;
  padding: 25rpx 20rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}
.contactItem.data-v-3a54848b:last-child {
  border-bottom: none;
}
.left.data-v-3a54848b {
  flex: 1;
}
.peopleName.data-v-3a54848b {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}
.peopleCard.data-v-3a54848b,
.peopleMablie.data-v-3a54848b {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
}
.peopleCard text.data-v-3a54848b,
.peopleMablie text.data-v-3a54848b {
  margin-right: 20rpx;
}
.right.data-v-3a54848b {
  margin-left: 20rpx;
}
.checkBtn.data-v-3a54848b {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}
.checkBtn.isCheck.data-v-3a54848b {
  background: #2ed573;
  border-color: #2ed573;
}
.checkBtn.isCheck.data-v-3a54848b::after {
  content: "";
  position: absolute;
  left: 12rpx;
  top: 6rpx;
  width: 12rpx;
  height: 20rpx;
  border: 3rpx solid #ffffff;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}
.show_foot.data-v-3a54848b {
  height: 120rpx;
}
.filmInfofont.data-v-3a54848b {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 20rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}
.submitBtn.data-v-3a54848b {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
}
.submitBtn.data-v-3a54848b:active {
  transform: translateY(2rpx);
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
}
.mask.data-v-3a54848b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.blackDefault.data-v-3a54848b {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 0 40rpx;
  text-align: center;
  max-width: 600rpx;
  width: 100%;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}
.blackStateIcon.data-v-3a54848b,
.locationStateIcon.data-v-3a54848b {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
}
.blackStateIcon.data-v-3a54848b {
  background: #ff4757;
}
.blackStateIcon.data-v-3a54848b::before {
  content: "⚠️";
}
.locationStateIcon.data-v-3a54848b {
  background: #ffa502;
}
.locationStateIcon.data-v-3a54848b::before {
  content: "📍";
}
.blackStateT.data-v-3a54848b {
  margin-bottom: 40rpx;
}
.blackStateT text.data-v-3a54848b {
  display: block;
  font-size: 32rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 15rpx;
}
.blackDate.data-v-3a54848b {
  color: #ff4757;
  font-weight: bold;
}
.blackBtn.data-v-3a54848b {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 20rpx 60rpx;
  border-radius: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}
.blackBtn.data-v-3a54848b:active {
  transform: translateY(2rpx);
  opacity: 0.8;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.main.data-v-3a54848b {
    width: calc(100% - 40rpx);
    margin-left: 20rpx;
    margin-right: 20rpx;
}
.show_fixed.data-v-3a54848b {
    padding: 30rpx;
}
.filmItem.data-v-3a54848b {
    flex-direction: column;
    align-items: center;
    text-align: center;
}
.itemLeft.data-v-3a54848b {
    flex-direction: column;
    align-items: center;
    margin-bottom: 20rpx;
}
.cover.data-v-3a54848b {
    margin-right: 0;
    margin-bottom: 20rpx;
}
.chooseNum.data-v-3a54848b {
    justify-content: center;
}
}
/* 平台特定样式 */
.filmInfofont.data-v-3a54848b {
  padding-bottom: env(safe-area-inset-bottom);
}