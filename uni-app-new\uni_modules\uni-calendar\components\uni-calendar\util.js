var e = require("../../../../@babel/runtime/helpers/typeof"),
  t = require("../../../../@babel/runtime/helpers/classCallCheck"),
  a = require("../../../../@babel/runtime/helpers/createClass"),
  n = require("./calendar.js"),
  r = function() {
    function r() {
      var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
        a = (e.date, e.selected),
        n = e.startDate,
        l = e.endDate,
        s = e.range;
      t(this, r), this.date = this.getDate(new Date), this.selected = a || [], this.startDate = n, this.endDate = l, this.range = s, this.cleanMultipleStatus(), this.weeks = {}
    }
    return a(r, [{
      key: "setDate",
      value: function(e) {
        this.selectDate = this.getDate(e), this._getWeek(this.selectDate.fullDate)
      }
    }, {
      key: "cleanMultipleStatus",
      value: function() {
        this.multipleStatus = {
          before: "",
          after: "",
          data: []
        }
      }
    }, {
      key: "resetSatrtDate",
      value: function(e) {
        this.startDate = e
      }
    }, {
      key: "resetEndDate",
      value: function(e) {
        this.endDate = e
      }
    }, {
      key: "getDate",
      value: function(t) {
        var a = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0,
          n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "day";
        t || (t = new Date), "object" != e(t) && (t = t.replace(/-/g, "/"));
        var r = new Date(t);
        switch (n) {
          case "day":
            r.setDate(r.getDate() + a);
            break;
          case "month":
            31 === r.getDate() ? r.setDate(r.getDate() + a) : r.setMonth(r.getMonth() + a);
            break;
          case "year":
            r.setFullYear(r.getFullYear() + a)
        }
        var l = r.getFullYear(),
          s = r.getMonth() + 1 < 10 ? "0" + (r.getMonth() + 1) : r.getMonth() + 1,
          u = r.getDate() < 10 ? "0" + r.getDate() : r.getDate();
        return {
          fullDate: l + "-" + s + "-" + u,
          year: l,
          month: s,
          date: u,
          day: r.getDay()
        }
      }
    }, {
      key: "_getLastMonthDays",
      value: function(e, t) {
        for (var a = [], n = e; n > 0; n--) {
          var r = new Date(t.year, t.month - 1, 1 - n).getDate();
          a.push({
            date: r,
            month: t.month - 1,
            lunar: this.getlunar(t.year, t.month - 1, r),
            disable: !0
          })
        }
        return a
      }
    }, {
      key: "_currentMonthDys",
      value: function(e, t) {
        for (var a = this, n = [], r = this.date.fullDate, l = function() {
            var e = t.year + "-" + (t.month, t.month + "-") + (s < 10 ? "0" + s : s),
              l = r === e,
              u = a.selected && a.selected.find((function(t) {
                if (a.dateEqual(e, t.date)) return t
              })),
              i = !0,
              h = !0;
            a.startDate && (i = a.dateCompare(a.startDate, e)), a.endDate && (h = a.dateCompare(e, a.endDate));
            var o = a.multipleStatus.data,
              D = !1,
              c = -1;
            a.range && (o && (c = o.findIndex((function(t) {
              return a.dateEqual(t, e)
            }))), -1 !== c && (D = !0));
            var f = {
              fullDate: e,
              year: t.year,
              date: s,
              multiple: !!a.range && D,
              beforeMultiple: a.dateEqual(a.multipleStatus.before, e),
              afterMultiple: a.dateEqual(a.multipleStatus.after, e),
              month: t.month,
              lunar: a.getlunar(t.year, t.month, s),
              disable: !(i && h),
              isDay: l
            };
            u && (f.extraInfo = u), n.push(f)
          }, s = 1; s <= e; s++) l();
        return n
      }
    }, {
      key: "_getNextMonthDays",
      value: function(e, t) {
        for (var a = [], n = 1; n < e + 1; n++) a.push({
          date: n,
          month: Number(t.month) + 1,
          lunar: this.getlunar(t.year, Number(t.month) + 1, n),
          disable: !0
        });
        return a
      }
    }, {
      key: "getInfo",
      value: function(e) {
        var t = this;
        return e || (e = new Date), this.canlender.find((function(a) {
          return a.fullDate === t.getDate(e).fullDate
        }))
      }
    }, {
      key: "dateCompare",
      value: function(e, t) {
        return (e = new Date(e.replace("-", "/").replace("-", "/"))) <= (t = new Date(t.replace("-", "/").replace("-", "/")))
      }
    }, {
      key: "dateEqual",
      value: function(e, t) {
        return e = new Date(e.replace("-", "/").replace("-", "/")), t = new Date(t.replace("-", "/").replace("-", "/")), e.getTime() - t.getTime() == 0
      }
    }, {
      key: "geDateAll",
      value: function(e, t) {
        var a = [],
          n = e.split("-"),
          r = t.split("-"),
          l = new Date;
        l.setFullYear(n[0], n[1] - 1, n[2]);
        var s = new Date;
        s.setFullYear(r[0], r[1] - 1, r[2]);
        for (var u = l.getTime() - 864e5, i = s.getTime() - 864e5, h = u; h <= i;) h += 864e5, a.push(this.getDate(new Date(parseInt(h))).fullDate);
        return a
      }
    }, {
      key: "getlunar",
      value: function(e, t, a) {
        return n.calendar.solar2lunar(e, t, a)
      }
    }, {
      key: "setSelectInfo",
      value: function(e, t) {
        this.selected = t, this._getWeek(e)
      }
    }, {
      key: "setMultiple",
      value: function(e) {
        var t = this.multipleStatus,
          a = t.before,
          n = t.after;
        this.range && (a && n ? (this.multipleStatus.before = "", this.multipleStatus.after = "", this.multipleStatus.data = []) : a ? (this.multipleStatus.after = e, this.dateCompare(this.multipleStatus.before, this.multipleStatus.after) ? this.multipleStatus.data = this.geDateAll(this.multipleStatus.before, this.multipleStatus.after) : this.multipleStatus.data = this.geDateAll(this.multipleStatus.after, this.multipleStatus.before)) : this.multipleStatus.before = e, this._getWeek(e))
      }
    }, {
      key: "_getWeek",
      value: function(e) {
        var t = this.getDate(e),
          a = (t.fullDate, t.year),
          n = t.month,
          r = (t.date, t.day, new Date(a, n - 1, 1).getDay()),
          l = new Date(a, n, 0).getDate(),
          s = {
            lastMonthDays: this._getLastMonthDays(r, this.getDate(e)),
            currentMonthDys: this._currentMonthDys(l, this.getDate(e)),
            nextMonthDays: [],
            weeks: []
          },
          u = [],
          i = 42 - (s.lastMonthDays.length + s.currentMonthDys.length);
        s.nextMonthDays = this._getNextMonthDays(i, this.getDate(e)), u = u.concat(s.lastMonthDays, s.currentMonthDys, []);
        for (var h = {}, o = 0; o < u.length; o++) o % 7 == 0 && (h[parseInt(o / 7)] = new Array(7)), h[parseInt(o / 7)][o % 7] = u[o];
        this.canlender = u, this.weeks = h
      }
    }]), r
  }();
exports.Calendar = r;