"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_storage = require("../../utils/storage.js");
const utils_constant = require("../../utils/constant.js");
const api_login = require("../../api/login.js");
const utils_auth = require("../../utils/auth.js");
const state = () => ({
  token: utils_auth.getToken(),
  tenantId: utils_storage.storage.get(utils_constant.constant.tenantId),
  rememberMe: utils_storage.storage.get(utils_constant.constant.rememberMe),
  username: utils_storage.storage.get(utils_constant.constant.username),
  password: utils_storage.storage.get(utils_constant.constant.password),
  avatar: utils_storage.storage.get(utils_constant.constant.avatar),
  roles: utils_storage.storage.get(utils_constant.constant.roles),
  permissions: utils_storage.storage.get(utils_constant.constant.permissions),
  // 新增状态
  openid: "",
  // 微信openid
  userInfo: null,
  // 完整用户信息
  loginTime: null
  // 登录时间
});
const mutations = {
  SET_TOKEN(state2, token) {
    state2.token = token;
    if (token) {
      utils_auth.setToken(token);
      state2.loginTime = (/* @__PURE__ */ new Date()).getTime();
    }
  },
  SET_TENANTID(state2, tenantId) {
    state2.tenantId = tenantId;
    if (tenantId === "" || tenantId === null) {
      utils_storage.storage.remove(utils_constant.constant.tenantId);
    } else {
      utils_storage.storage.set(utils_constant.constant.tenantId, tenantId);
    }
  },
  SET_REMEMBERME(state2, rememberMe) {
    state2.rememberMe = rememberMe;
    if (rememberMe === "" || rememberMe === null || rememberMe === false) {
      utils_storage.storage.remove(utils_constant.constant.rememberMe);
    } else {
      utils_storage.storage.set(utils_constant.constant.rememberMe, rememberMe);
    }
  },
  SET_USERNAME(state2, username) {
    state2.username = username;
    if (username === "" || username === null) {
      utils_storage.storage.remove(utils_constant.constant.username);
    } else {
      utils_storage.storage.set(utils_constant.constant.username, username);
    }
  },
  SET_PASSWORD(state2, password) {
    state2.password = password;
    if (password === "" || password === null) {
      utils_storage.storage.remove(utils_constant.constant.password);
    } else {
      utils_storage.storage.set(utils_constant.constant.password, password);
    }
  },
  SET_AVATAR(state2, avatar) {
    state2.avatar = avatar;
    if (avatar === "" || avatar === null) {
      utils_storage.storage.remove(utils_constant.constant.avatar);
    } else {
      utils_storage.storage.set(utils_constant.constant.avatar, avatar);
    }
  },
  SET_ROLES(state2, roles) {
    state2.roles = roles;
    if (roles === "" || roles === null || Array.isArray(roles) && roles.length === 0) {
      utils_storage.storage.remove(utils_constant.constant.roles);
    } else {
      utils_storage.storage.set(utils_constant.constant.roles, roles);
    }
  },
  SET_PERMISSIONS(state2, permissions) {
    state2.permissions = permissions;
    if (permissions === "" || permissions === null || Array.isArray(permissions) && permissions.length === 0) {
      utils_storage.storage.remove(utils_constant.constant.permissions);
    } else {
      utils_storage.storage.set(utils_constant.constant.permissions, permissions);
    }
  },
  // 新增mutations
  SET_OPENID(state2, openid) {
    state2.openid = openid;
  },
  SET_USER_INFO(state2, userInfo) {
    state2.userInfo = userInfo;
  },
  CLEAR_USER_DATA(state2) {
    state2.token = "";
    state2.tenantId = "";
    state2.rememberMe = false;
    state2.username = "";
    state2.password = "";
    state2.avatar = "";
    state2.roles = [];
    state2.permissions = [];
    state2.openid = "";
    state2.userInfo = null;
    state2.loginTime = null;
  }
};
const actions = {
  // 登录action
  async Login({ commit }, loginForm) {
    const { tenantId, username, password, code, uuid, rememberMe } = loginForm;
    const trimmedUsername = username.trim();
    try {
      if (rememberMe) {
        commit("SET_TENANTID", tenantId);
        commit("SET_REMEMBERME", rememberMe);
        commit("SET_USERNAME", trimmedUsername);
        commit("SET_PASSWORD", password);
      } else {
        commit("SET_TENANTID", "");
        commit("SET_REMEMBERME", false);
        commit("SET_USERNAME", "");
        commit("SET_PASSWORD", "");
      }
      const response = await api_login.login(tenantId, trimmedUsername, password, code);
      const { access_token } = response.data;
      commit("SET_TOKEN", access_token);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:148", "登录失败:", error);
      throw error;
    }
  },
  // 获取用户信息
  async GetInfo({ commit, state: state2 }) {
    try {
      const response = await api_login.getInfo();
      const { user: user2, roles, permissions } = response.data;
      const avatar = user2 && user2.avatarUrl && user2.avatarUrl !== null ? user2.avatarUrl : "@/static/images/profile.jpg";
      const username = user2 && user2.userName && user2.userName !== null ? user2.userName : "";
      if (roles && roles.length > 0) {
        commit("SET_ROLES", roles);
        commit("SET_PERMISSIONS", permissions);
      } else {
        commit("SET_ROLES", ["ROLE_DEFAULT"]);
      }
      commit("SET_USERNAME", username);
      commit("SET_AVATAR", avatar);
      commit("SET_USER_INFO", user2);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:184", "获取用户信息失败:", error);
      throw error;
    }
  },
  // 登出action
  async LogOut({ commit, state: state2 }) {
    try {
      const response = await api_login.logout();
      commit("CLEAR_USER_DATA");
      utils_auth.removeToken();
      utils_storage.storage.clean();
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:203", "登出失败:", error);
      commit("CLEAR_USER_DATA");
      utils_auth.removeToken();
      utils_storage.storage.clean();
      throw error;
    }
  },
  // 微信登录action
  async WxLogin({ commit }, { code, openid }) {
    try {
      const response = await api_login.login("", "", "", code);
      const { access_token } = response.data;
      commit("SET_TOKEN", access_token);
      commit("SET_OPENID", openid);
      return response;
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:224", "微信登录失败:", error);
      throw error;
    }
  },
  // 刷新token
  async RefreshToken({ commit, state: state2 }) {
    try {
      return { data: { access_token: state2.token } };
    } catch (error) {
      common_vendor.index.__f__("error", "at store/modules/user.js:240", "刷新token失败:", error);
      throw error;
    }
  }
};
const user = {
  namespaced: true,
  state,
  mutations,
  actions
};
exports.user = user;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/store/modules/user.js.map
