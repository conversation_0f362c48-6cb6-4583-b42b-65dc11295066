{"version": 3, "file": "index.js", "sources": ["utils/index.js"], "sourcesContent": ["const Utils = {\r\n\tchangeTime: function(time, isYear) {\r\n\t\tlet times = new Date(time);\r\n\t\tlet y = times.getFullYear();\r\n\t\tlet m = (times.getMonth() + 1) < 10 ? \"0\" + (times.getMonth() + 1) : times.getMonth() + 1;\r\n\t\tlet d = times.getDate() < 10 ? \"0\" + times.getDate() : times.getDate();\r\n\r\n\t\tlet h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();\r\n\t\tlet mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();\r\n\t\tlet s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();\r\n\t\tif (isYear) {\r\n\t\t\treturn y + '-' + m + '-' + d;\r\n\t\t} else {\r\n\t\t\treturn h + ':' + mm\r\n\t\t}\r\n\t},\r\n\tchangeTime2: function(time) {\r\n\t\tlet times = new Date(time);\r\n\t\tlet y = times.getFullYear();\r\n\t\tlet m = (times.getMonth() + 1) < 10 ? \"0\" + (times.getMonth() + 1) : times.getMonth() + 1;\r\n\t\tlet d = times.getDate() < 10 ? \"0\" + times.getDate() : times.getDate();\r\n\r\n\t\tlet h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();\r\n\t\tlet mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();\r\n\t\tlet s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();\r\n\t\treturn y + '-' + m + '-' + d +'  '+ h + ':' + mm + ':' + s;\r\n\t},\t\r\n\tformatTime: function(time) {\r\n\t\tlet times = new Date(time);\r\n\t\tlet y = times.getFullYear();\r\n\t\tlet m = (times.getMonth() + 1) < 10 ? \"0\" + (times.getMonth() + 1) : times.getMonth() + 1;\r\n\t\tlet d = times.getDate() < 10 ? \"0\" + times.getDate() : times.getDate();\r\n\r\n\t\tlet h = times.getHours() < 10 ? '0' + times.getHours() : times.getHours();\r\n\t\tlet mm = times.getMinutes() < 10 ? '0' + times.getMinutes() : times.getMinutes();\r\n\t\tlet s = times.getSeconds() < 10 ? '0' + times.getSeconds() : times.getSeconds();\r\n\t\treturn h + ':' + mm + ':' + s;\r\n\t},\t\r\n\tcheckMobile(str) {\r\n\t\tvar reg = /^1[34578]\\d{9}$/\r\n\t\tif (reg.test(str)) {\r\n\t\t\treturn true;\r\n\t\t} else {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t},\r\n\tdistance: function(la1, lo1, la2, lo2) {\r\n\t\tvar La1 = la1 * Math.PI / 180.0;\r\n\t\tvar La2 = la2 * Math.PI / 180.0;\r\n\t\tvar La3 = La1 - La2;\r\n\t\tvar Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;\r\n\t\tvar s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math\r\n\t\t\t.pow(Math.sin(Lb3 / 2), 2)));\r\n\t\ts = s * 6378.137;\r\n\t\ts = Math.round(s * 10000) / 10000;\r\n\t\treturn s;\r\n\t},\r\n\tverifyIdCard: function(str, isLand = true) {\r\n\t\t/**\r\n\t\t * isLand 是否为大陆身份证\r\n\t\t */\r\n\t\tvar Land = /^\\d{6}(18|19|20)?\\d{2}(0[1-9]|1[012])(0[1-9]|[12]\\d|3[01])\\d{3}(\\d|[xX])$/; //大陆\r\n\t\tvar Hongkong = /([A-Za-z](\\d{6})\\d)|(\\d{6})(\\d{4})(\\d{2})(\\d{2})(\\d{3})([0-9]|X|x)$/; //香港、澳门\r\n\t\tvar Taiwan = /^[a-zA-Z][0-9]{9}$/; //台湾\r\n\t\tvar Macao = /^[1|5|7][0-9]{6}[09Aa]$/; //澳门\r\n\r\n\t\tif (isLand) {\r\n\t\t\treturn Boolean(Land.test(str));\r\n\t\t} else {\r\n\t\t\treturn Boolean(Hongkong.test(str) || Taiwan.test(str) || Macao.test(str));\r\n\t\t}\r\n\r\n\t},\r\n\ttimeThenNow: function(times) {\r\n\t\t//判断传入的时间是否超过了当前的时间\r\n\t\tlet now = new Date();\r\n\t\treturn (new Date(times) > now);\r\n\t},\r\n\t// 节流\r\n\tthrottle: function(fn, interval) {\r\n\t\tvar enterTime = 0; //触发的时间\r\n\t\tvar gapTime = interval || 3000; //间隔时间，如果interval不传，则默认300ms\r\n\t\treturn function() {\r\n\t\t\tvar context = this;\r\n\t\t\tvar backTime = new Date(); //第一次函数return即触发的时间\r\n\t\t\tif (backTime - enterTime > gapTime) {\r\n\t\t\t\tfn.call(context, arguments);\r\n\t\t\t\tenterTime = backTime; //赋值给第一次触发的时间，这样就保存了第二次触发的时间\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\t// 防抖\r\n\tdebounce: function(fn, time) {\r\n\t\t//初始化一个定时器的时间\r\n\t\tvar timer\r\n\t\tvar lastTime = 0;\r\n\t\treturn function(path) {\r\n\t\t\t//每次触发事件都先清除上一次的定时器，重新开始\r\n\t\t\tclearTimeout(timer);\r\n\t\t\t//获取当前的时间\r\n\t\t\tvar nowTime = Date.now();\r\n\t\t\t//保存this指向的对象oBox，因为在定时器中直接使用this，那么this指向的定时器\r\n\t\t\tvar _this = this;\r\n\t\t\t//arguments[0]就是当前事件对象的event对象\r\n\t\t\t//保存在定时器中直接使用arguments[0]，那么arguments[0]指向的定时器的参数实例\r\n\t\t\tvar e = arguments[0];\r\n\t\t\ttimer = setTimeout(function() {\r\n\t\t\t\tif (nowTime - lastTime < time) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t//使用call来改变fn函数的指向，并给fn传递this指向oBox\r\n\t\t\t\tfn.call(_this, e, path);\r\n\t\t\t}, time)\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\nexport default Utils;\r\n"], "names": [], "mappings": ";AAAK,MAAC,QAAQ;AAAA,EACb,YAAY,SAAS,MAAM,QAAQ;AAClC,QAAI,QAAQ,IAAI,KAAK,IAAI;AACzB,QAAI,IAAI,MAAM;AACd,QAAI,IAAK,MAAM,SAAQ,IAAK,IAAK,KAAK,OAAO,MAAM,SAAU,IAAG,KAAK,MAAM,SAAU,IAAG;AACxF,QAAI,IAAI,MAAM,QAAO,IAAK,KAAK,MAAM,MAAM,QAAO,IAAK,MAAM,QAAO;AAEpE,QAAI,IAAI,MAAM,SAAQ,IAAK,KAAK,MAAM,MAAM,SAAQ,IAAK,MAAM,SAAQ;AACvE,QAAI,KAAK,MAAM,WAAU,IAAK,KAAK,MAAM,MAAM,WAAU,IAAK,MAAM,WAAU;AACtE,UAAM,WAAY,IAAG,KAAK,MAAM,MAAM,WAAU,IAAK,MAAM,WAAa;AAChF,QAAI,QAAQ;AACX,aAAO,IAAI,MAAM,IAAI,MAAM;AAAA,IAC9B,OAAS;AACN,aAAO,IAAI,MAAM;AAAA,IACjB;AAAA,EACD;AAAA,EACD,aAAa,SAAS,MAAM;AAC3B,QAAI,QAAQ,IAAI,KAAK,IAAI;AACzB,QAAI,IAAI,MAAM;AACd,QAAI,IAAK,MAAM,SAAQ,IAAK,IAAK,KAAK,OAAO,MAAM,SAAU,IAAG,KAAK,MAAM,SAAU,IAAG;AACxF,QAAI,IAAI,MAAM,QAAO,IAAK,KAAK,MAAM,MAAM,QAAO,IAAK,MAAM,QAAO;AAEpE,QAAI,IAAI,MAAM,SAAQ,IAAK,KAAK,MAAM,MAAM,SAAQ,IAAK,MAAM,SAAQ;AACvE,QAAI,KAAK,MAAM,WAAU,IAAK,KAAK,MAAM,MAAM,WAAU,IAAK,MAAM,WAAU;AAC9E,QAAI,IAAI,MAAM,WAAU,IAAK,KAAK,MAAM,MAAM,WAAU,IAAK,MAAM,WAAU;AAC7E,WAAO,IAAI,MAAM,IAAI,MAAM,IAAG,OAAM,IAAI,MAAM,KAAK,MAAM;AAAA,EACzD;AAAA,EACD,YAAY,SAAS,MAAM;AAC1B,QAAI,QAAQ,IAAI,KAAK,IAAI;AACjB,UAAM,YAAc;AACpB,IAAC,MAAM,SAAQ,IAAK,IAAK,KAAK,OAAO,MAAM,SAAU,IAAG,KAAK,MAAM,SAAU,IAAG;AAChF,UAAM,QAAS,IAAG,KAAK,MAAM,MAAM,QAAO,IAAK,MAAM,QAAU;AAEvE,QAAI,IAAI,MAAM,SAAQ,IAAK,KAAK,MAAM,MAAM,SAAQ,IAAK,MAAM,SAAQ;AACvE,QAAI,KAAK,MAAM,WAAU,IAAK,KAAK,MAAM,MAAM,WAAU,IAAK,MAAM,WAAU;AAC9E,QAAI,IAAI,MAAM,WAAU,IAAK,KAAK,MAAM,MAAM,WAAU,IAAK,MAAM,WAAU;AAC7E,WAAO,IAAI,MAAM,KAAK,MAAM;AAAA,EAC5B;AAAA,EACD,YAAY,KAAK;AAChB,QAAI,MAAM;AACV,QAAI,IAAI,KAAK,GAAG,GAAG;AAClB,aAAO;AAAA,IACV,OAAS;AACN,aAAO;AAAA,IACP;AAAA,EACD;AAAA,EACD,UAAU,SAAS,KAAK,KAAK,KAAK,KAAK;AACtC,QAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,QAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,QAAI,MAAM,MAAM;AAChB,QAAI,MAAM,MAAM,KAAK,KAAK,MAAQ,MAAM,KAAK,KAAK;AAClD,QAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,KAC/F,IAAI,KAAK,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5B,QAAI,IAAI;AACR,QAAI,KAAK,MAAM,IAAI,GAAK,IAAI;AAC5B,WAAO;AAAA,EACP;AAAA,EACD,cAAc,SAAS,KAAK,SAAS,MAAM;AAI1C,QAAI,OAAO;AACX,QAAI,WAAW;AACf,QAAI,SAAS;AACb,QAAI,QAAQ;AAEZ,QAAI,QAAQ;AACX,aAAO,QAAQ,KAAK,KAAK,GAAG,CAAC;AAAA,IAChC,OAAS;AACN,aAAO,QAAQ,SAAS,KAAK,GAAG,KAAK,OAAO,KAAK,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC;AAAA,IACxE;AAAA,EAED;AAAA,EACD,aAAa,SAAS,OAAO;AAE5B,QAAI,MAAM,oBAAI;AACd,WAAQ,IAAI,KAAK,KAAK,IAAI;AAAA,EAC1B;AAAA;AAAA,EAED,UAAU,SAAS,IAAI,UAAU;AAChC,QAAI,YAAY;AAChB,QAAI,UAAU,YAAY;AAC1B,WAAO,WAAW;AACjB,UAAI,UAAU;AACd,UAAI,WAAW,oBAAI;AACnB,UAAI,WAAW,YAAY,SAAS;AACnC,WAAG,KAAK,SAAS,SAAS;AAC1B,oBAAY;AAAA,MACZ;AAAA,IACJ;AAAA,EACE;AAAA;AAAA,EAED,UAAU,SAAS,IAAI,MAAM;AAE5B,QAAI;AACJ,QAAI,WAAW;AACf,WAAO,SAAS,MAAM;AAErB,mBAAa,KAAK;AAElB,UAAI,UAAU,KAAK;AAEnB,UAAI,QAAQ;AAGZ,UAAI,IAAI,UAAU,CAAC;AACnB,cAAQ,WAAW,WAAW;AAC7B,YAAI,UAAU,WAAW,MAAM;AAC9B;AAAA,QACA;AAED,WAAG,KAAK,OAAO,GAAG,IAAI;AAAA,MACtB,GAAE,IAAI;AAAA,IACP;AAAA,EACD;AACF;;"}