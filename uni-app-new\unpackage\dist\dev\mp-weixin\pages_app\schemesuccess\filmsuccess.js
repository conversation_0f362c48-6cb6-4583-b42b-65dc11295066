"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "FilmSuccess",
  data() {
    return {
      filmInfo: {},
      filmSessionId: null,
      batchNumber: null,
      qrcodeList: [],
      qrcodeIndex: 0,
      qrcodeBtns: ["票码一", "票码二", "票码三", "票码四", "票码五"],
      filmTypeList: ["球幕电影", "4D电影"],
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      // 状态控制
      isShowCountDown: false,
      countDown: "",
      countDownTimer: null,
      isShowLocationMask: false,
      isSigningUp: false,
      showQrcode: false,
      isPlayFilm: true,
      // 提示信息
      tipsList: [
        "1.请按时到达宝安科技馆，凭二维码检票入场",
        "2.请提前10分钟签到获取二维码，否则该预约失效，需重新预约",
        "3.入场后请保持安静，配合工作人员安排"
      ],
      // 定时器
      refreshTimer: null,
      filmInfoTimer: null
    };
  },
  onLoad(options) {
    this.batchNumber = options.batchNumber;
    this.filmSessionId = options.filmSessionId;
    this.initPage();
  },
  onShow() {
    this.getFilmInfo();
    this.getTipsList();
  },
  onHide() {
    this.clearTimers();
  },
  onUnload() {
    this.clearTimers();
  },
  methods: {
    // 初始化页面
    initPage() {
      this.getFilmInfo();
      this.getTipsList();
      this.startCountDown();
    },
    // 获取电影信息
    async getFilmInfo() {
      try {
        const res = await this.$myRequest({
          url: "/web/fileSession/personalCenterFilmByFilmSessionId",
          method: "get",
          data: {
            filmSessionId: this.filmSessionId
          }
        });
        if (res.code === 200) {
          this.filmInfo = {
            ...res.data.data,
            filmStartTime: this.formatTime(res.data.data.filmStartTime),
            filmEndTime: this.formatTime(res.data.data.filmEndTime),
            orderNum: res.data.data.filmPoll - res.data.data.inventoryVotes
          };
          this.startFilmInfoRefresh();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/filmsuccess.vue:240", "获取电影信息失败:", error);
        common_vendor.index.showToast({
          title: "获取信息失败",
          icon: "error"
        });
      }
    },
    // 获取提示信息
    async getTipsList() {
      try {
        const res = await this.$myRequest({
          url: "/admin/entrance_announcement/3",
          method: "get"
        });
        if (res.code === 200) {
          const data = res.data.data;
          if (data.beiyongThree) {
            this.tipsList = data.beiyongThree.split("\n").filter((tip) => tip.trim());
          }
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages_app/schemesuccess/filmsuccess.vue:263", "获取提示信息失败:", error);
      }
    },
    // 切换二维码
    changeQrcodeIndex(index) {
      this.qrcodeIndex = index;
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 获取星期
    getWeekDay(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr.replace(/-/g, "/"));
        return this.weekList[date.getDay()];
      } catch (error) {
        return "";
      }
    },
    // 签到获取二维码
    async signUp() {
      if (this.isSigningUp)
        return;
      this.isSigningUp = true;
      try {
        const location = await this.getCurrentLocation();
        const locationRes = await this.$myRequest({
          url: "/web/common/checkLocation",
          method: "post",
          data: {
            latitude: location.latitude,
            longitude: location.longitude
          }
        });
        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {
          this.isShowLocationMask = true;
          return;
        }
        const signRes = await this.$myRequest({
          url: "/web/fileSession/filmSign",
          method: "post",
          data: {
            batchNumber: this.batchNumber,
            filmSessionId: this.filmSessionId
          }
        });
        if (signRes.code === 200) {
          this.showQrcode = true;
          this.getQrcode();
          common_vendor.index.showToast({
            title: "签到成功",
            icon: "success"
          });
        } else {
          throw new Error(signRes.msg || "签到失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/filmsuccess.vue:345", "签到失败:", error);
        if (error.message && error.message.includes("定位")) {
          this.isShowLocationMask = true;
        } else {
          common_vendor.index.showToast({
            title: error.message || "签到失败",
            icon: "error"
          });
        }
      } finally {
        this.isSigningUp = false;
      }
    },
    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getLocation({
          type: "gcj02",
          success: resolve,
          fail: reject
        });
      });
    },
    // 获取二维码
    async getQrcode() {
      try {
        const res = await this.$myRequest({
          url: "/web/fileSession/qrCodeInfo",
          method: "get",
          data: {
            batchNumber: this.batchNumber,
            filmType: this.filmInfo.filmType
          }
        });
        if (res.code === 200) {
          this.qrcodeList = res.data.data || [];
          if (this.qrcodeList.length === 0) {
            common_vendor.index.showModal({
              title: "提示",
              content: "状态已更改, 返回上一页",
              showCancel: false,
              success: () => {
                common_vendor.index.navigateBack();
              }
            });
          } else {
            this.startQrcodeRefresh();
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/filmsuccess.vue:401", "获取二维码失败:", error);
        common_vendor.index.showToast({
          title: "获取二维码失败",
          icon: "error"
        });
      }
    },
    // 刷新二维码
    refreshQrcode() {
      this.getQrcode();
    },
    // 开始倒计时
    startCountDown() {
      if (!this.filmInfo.filmStartTime)
        return;
      const startTime = (/* @__PURE__ */ new Date(this.filmInfo.filmArrangedDate + " " + this.filmInfo.filmStartTime)).getTime();
      const now = (/* @__PURE__ */ new Date()).getTime();
      if (startTime - now > 0 && startTime - now < 30 * 60 * 1e3) {
        this.isShowCountDown = true;
        this.countDownTimer = setInterval(() => {
          const now2 = (/* @__PURE__ */ new Date()).getTime();
          const diff = startTime - now2;
          if (diff <= 0) {
            this.isShowCountDown = false;
            clearInterval(this.countDownTimer);
            return;
          }
          const minutes = Math.floor(diff / 6e4);
          const seconds = Math.floor(diff % 6e4 / 1e3);
          this.countDown = `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        }, 1e3);
      }
    },
    // 开始电影信息刷新
    startFilmInfoRefresh() {
      this.filmInfoTimer = setTimeout(() => {
        this.getFilmInfo();
      }, 1500);
    },
    // 开始二维码刷新
    startQrcodeRefresh() {
      this.refreshTimer = setTimeout(() => {
        this.getQrcode();
      }, 3e4);
    },
    // 清除所有定时器
    clearTimers() {
      if (this.countDownTimer) {
        clearInterval(this.countDownTimer);
        this.countDownTimer = null;
      }
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
      if (this.filmInfoTimer) {
        clearTimeout(this.filmInfoTimer);
        this.filmInfoTimer = null;
      }
    },
    // 关闭定位弹窗
    closeLocationMask() {
      this.isShowLocationMask = false;
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "观影凭证",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333",
      isFixed: true
    }),
    b: common_assets._imports_0$3,
    c: common_vendor.f($data.tipsList, (tip, index, i0) => {
      return {
        a: common_vendor.t(tip),
        b: index
      };
    }),
    d: $data.filmInfo.filmCover,
    e: common_vendor.t($data.filmInfo.filmName),
    f: common_vendor.t($data.filmTypeList[$data.filmInfo.filmType - 1] || "未知"),
    g: common_vendor.t($data.filmInfo.filmStartTime),
    h: common_vendor.t($data.filmInfo.filmEndTime),
    i: common_vendor.t($data.filmInfo.filmArrangedDate),
    j: common_vendor.t($options.getWeekDay($data.filmInfo.filmArrangedDate)),
    k: common_vendor.t($data.filmInfo.orderNum),
    l: $data.qrcodeList.length > 1
  }, $data.qrcodeList.length > 1 ? {
    m: common_vendor.f($data.qrcodeBtns.slice(0, $data.qrcodeList.length), (btn, index, i0) => {
      return {
        a: common_vendor.t(btn),
        b: index,
        c: common_vendor.n({
          active: $data.qrcodeIndex === index
        }),
        d: common_vendor.o(($event) => $options.changeQrcodeIndex(index), index)
      };
    })
  } : {}, {
    n: $data.qrcodeList.length > 0 && $data.showQrcode
  }, $data.qrcodeList.length > 0 && $data.showQrcode ? {
    o: $data.qrcodeList[$data.qrcodeIndex].qrcodeUrl,
    p: common_vendor.o((...args) => $options.refreshQrcode && $options.refreshQrcode(...args))
  } : {
    q: common_assets._imports_1$1
  }, {
    r: !$data.showQrcode || $data.filmInfo.subscribeState === 1 || !$data.isPlayFilm
  }, !$data.showQrcode || $data.filmInfo.subscribeState === 1 || !$data.isPlayFilm ? common_vendor.e({
    s: !$data.isPlayFilm && ($data.filmInfo.subscribeState === 4 || $data.filmInfo.subscribeState === 1)
  }, !$data.isPlayFilm && ($data.filmInfo.subscribeState === 4 || $data.filmInfo.subscribeState === 1) ? {} : !$data.showQrcode && $data.filmInfo.subscribeState === 4 ? {} : {
    v: common_vendor.t($data.isSigningUp ? "签到中..." : "签到获取二维码"),
    w: common_vendor.o((...args) => $options.signUp && $options.signUp(...args)),
    x: $data.isSigningUp
  }, {
    t: !$data.showQrcode && $data.filmInfo.subscribeState === 4
  }) : {}, {
    y: $data.isShowCountDown
  }, $data.isShowCountDown ? {
    z: common_vendor.t($data.countDown)
  } : {}, {
    A: $data.isShowLocationMask
  }, $data.isShowLocationMask ? {
    B: common_vendor.o((...args) => $options.closeLocationMask && $options.closeLocationMask(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8ad8d244"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/schemesuccess/filmsuccess.js.map
