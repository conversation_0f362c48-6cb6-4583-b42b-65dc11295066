"use strict";
const common_vendor = require("../common/vendor.js");
const utils_constant = require("./constant.js");
const STORAGE_KEY = "storage_data";
const allowedKeys = [
  utils_constant.constant.avatar,
  utils_constant.constant.username,
  utils_constant.constant.password,
  utils_constant.constant.roles,
  utils_constant.constant.tenantId,
  utils_constant.constant.rememberMe,
  utils_constant.constant.permissions
];
const storage = {
  set(key, value) {
    if (allowedKeys.indexOf(key) !== -1) {
      try {
        let storageData = common_vendor.index.getStorageSync(STORAGE_KEY) || {};
        storageData[key] = value;
        common_vendor.index.setStorageSync(STORAGE_KEY, storageData);
        return true;
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/storage.js:24", "存储数据失败:", error);
        return false;
      }
    }
    return false;
  },
  get(key) {
    try {
      const storageData = common_vendor.index.getStorageSync(STORAGE_KEY) || {};
      return storageData[key] || "";
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:36", "读取存储数据失败:", error);
      return "";
    }
  },
  remove(key) {
    try {
      let storageData = common_vendor.index.getStorageSync(STORAGE_KEY) || {};
      delete storageData[key];
      common_vendor.index.setStorageSync(STORAGE_KEY, storageData);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:48", "删除存储数据失败:", error);
      return false;
    }
  },
  clean() {
    try {
      common_vendor.index.removeStorageSync(STORAGE_KEY);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:58", "清空存储数据失败:", error);
      return false;
    }
  },
  // 检查key是否被允许
  isAllowedKey(key) {
    return allowedKeys.indexOf(key) !== -1;
  },
  // 获取所有存储的数据
  getAll() {
    try {
      return common_vendor.index.getStorageSync(STORAGE_KEY) || {};
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/storage.js:73", "获取所有存储数据失败:", error);
      return {};
    }
  }
};
exports.storage = storage;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/storage.js.map
