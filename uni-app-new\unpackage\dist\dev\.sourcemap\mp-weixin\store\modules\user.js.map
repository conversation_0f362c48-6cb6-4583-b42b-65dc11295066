{"version": 3, "file": "user.js", "sources": ["store/modules/user.js"], "sourcesContent": ["import { storage } from '../../utils/storage.js'\r\nimport { constant } from '../../utils/constant.js'\r\nimport { login, getInfo, logout } from '../../api/login.js'\r\nimport { getToken, setToken, removeToken } from '../../utils/auth.js'\r\n\r\nconst state = () => ({\r\n  token: getToken(),\r\n  tenantId: storage.get(constant.tenantId),\r\n  rememberMe: storage.get(constant.rememberMe),\r\n  username: storage.get(constant.username),\r\n  password: storage.get(constant.password),\r\n  avatar: storage.get(constant.avatar),\r\n  roles: storage.get(constant.roles),\r\n  permissions: storage.get(constant.permissions),\r\n  // 新增状态\r\n  openid: '', // 微信openid\r\n  userInfo: null, // 完整用户信息\r\n  loginTime: null // 登录时间\r\n})\r\n\r\nconst\r\nmutations = {\r\n  SET_TOKEN(state, token) {\r\n    state.token = token\r\n    if (token) {\r\n      setToken(token)\r\n      state.loginTime = new Date().getTime()\r\n    }\r\n  },\r\n  \r\n  SET_TENANTID(state, tenantId) {\r\n    state.tenantId = tenantId\r\n    if (tenantId === '' || tenantId === null) {\r\n      storage.remove(constant.tenantId)\r\n    } else {\r\n      storage.set(constant.tenantId, tenantId)\r\n    }\r\n  },\r\n  \r\n  SET_REMEMBERME(state, rememberMe) {\r\n    state.rememberMe = rememberMe\r\n    if (rememberMe === '' || rememberMe === null || rememberMe === false) {\r\n      storage.remove(constant.rememberMe)\r\n    } else {\r\n      storage.set(constant.rememberMe, rememberMe)\r\n    }\r\n  },\r\n  \r\n  SET_USERNAME(state, username) {\r\n    state.username = username\r\n    if (username === '' || username === null) {\r\n      storage.remove(constant.username)\r\n    } else {\r\n      storage.set(constant.username, username)\r\n    }\r\n  },\r\n  \r\n  SET_PASSWORD(state, password) {\r\n    state.password = password\r\n    if (password === '' || password === null) {\r\n      storage.remove(constant.password)\r\n    } else {\r\n      storage.set(constant.password, password)\r\n    }\r\n  },\r\n  \r\n  SET_AVATAR(state, avatar) {\r\n    state.avatar = avatar\r\n    if (avatar === '' || avatar === null) {\r\n      storage.remove(constant.avatar)\r\n    } else {\r\n      storage.set(constant.avatar, avatar)\r\n    }\r\n  },\r\n  \r\n  SET_ROLES(state, roles) {\r\n    state.roles = roles\r\n    if (roles === '' || roles === null || (Array.isArray(roles) && roles.length === 0)) {\r\n      storage.remove(constant.roles)\r\n    } else {\r\n      storage.set(constant.roles, roles)\r\n    }\r\n  },\r\n  \r\n  SET_PERMISSIONS(state, permissions) {\r\n    state.permissions = permissions\r\n    if (permissions === '' || permissions === null || (Array.isArray(permissions) && permissions.length === 0)) {\r\n      storage.remove(constant.permissions)\r\n    } else {\r\n      storage.set(constant.permissions, permissions)\r\n    }\r\n  },\r\n  \r\n  // 新增mutations\r\n  SET_OPENID(state, openid) {\r\n    state.openid = openid\r\n  },\r\n  \r\n  SET_USER_INFO(state, userInfo) {\r\n    state.userInfo = userInfo\r\n  },\r\n  \r\n  CLEAR_USER_DATA(state) {\r\n    state.token = ''\r\n    state.tenantId = ''\r\n    state.rememberMe = false\r\n    state.username = ''\r\n    state.password = ''\r\n    state.avatar = ''\r\n    state.roles = []\r\n    state.permissions = []\r\n    state.openid = ''\r\n    state.userInfo = null\r\n    state.loginTime = null\r\n  }\r\n}\r\n\r\nconst\r\nactions = {\r\n  // 登录action\r\n  async Login({ commit }, loginForm) {\r\n    const { tenantId, username, password, code, uuid, rememberMe } = loginForm\r\n    const trimmedUsername = username.trim()\r\n    \r\n    try {\r\n      // 处理记住密码\r\n      if (rememberMe) {\r\n        commit('SET_TENANTID', tenantId)\r\n        commit('SET_REMEMBERME', rememberMe)\r\n        commit('SET_USERNAME', trimmedUsername)\r\n        commit('SET_PASSWORD', password)\r\n      } else {\r\n        commit('SET_TENANTID', '')\r\n        commit('SET_REMEMBERME', false)\r\n        commit('SET_USERNAME', '')\r\n        commit('SET_PASSWORD', '')\r\n      }\r\n      \r\n      // 调用登录接口\r\n      const response = await login(tenantId, trimmedUsername, password, code)\r\n      const { access_token } = response.data\r\n      \r\n      // 设置token\r\n      commit('SET_TOKEN', access_token)\r\n      \r\n      return response\r\n    } catch (error) {\r\n      console.error('登录失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n  \r\n  // 获取用户信息\r\n  async GetInfo({ commit, state }) {\r\n    try {\r\n      const response = await getInfo()\r\n      const { user, roles, permissions } = response.data\r\n      \r\n      // 处理头像\r\n      const avatar = (user && user.avatarUrl && user.avatarUrl !== null) \r\n        ? user.avatarUrl \r\n        : '@/static/images/profile.jpg'\r\n      \r\n      // 处理用户名\r\n      const username = (user && user.userName && user.userName !== null) \r\n        ? user.userName \r\n        : ''\r\n      \r\n      // 设置角色和权限\r\n      if (roles && roles.length > 0) {\r\n        commit('SET_ROLES', roles)\r\n        commit('SET_PERMISSIONS', permissions)\r\n      } else {\r\n        commit('SET_ROLES', ['ROLE_DEFAULT'])\r\n      }\r\n      \r\n      // 设置用户信息\r\n      commit('SET_USERNAME', username)\r\n      commit('SET_AVATAR', avatar)\r\n      commit('SET_USER_INFO', user)\r\n      \r\n      return response\r\n    } catch (error) {\r\n      console.error('获取用户信息失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n  \r\n  // 登出action\r\n  async LogOut({ commit, state }) {\r\n    try {\r\n      const response = await logout()\r\n      \r\n      // 清除所有用户数据\r\n      commit('CLEAR_USER_DATA')\r\n      \r\n      // 清除token和存储\r\n      removeToken()\r\n      storage.clean()\r\n      \r\n      return response\r\n    } catch (error) {\r\n      console.error('登出失败:', error)\r\n      // 即使接口失败也要清除本地数据\r\n      commit('CLEAR_USER_DATA')\r\n      removeToken()\r\n      storage.clean()\r\n      throw error\r\n    }\r\n  },\r\n  \r\n  // 微信登录action\r\n  async WxLogin({ commit }, { code, openid }) {\r\n    try {\r\n      // 这里需要根据实际的微信登录接口调整\r\n      const response = await login('', '', '', code)\r\n      const { access_token } = response.data\r\n      \r\n      commit('SET_TOKEN', access_token)\r\n      commit('SET_OPENID', openid)\r\n      \r\n      return response\r\n    } catch (error) {\r\n      console.error('微信登录失败:', error)\r\n      throw error\r\n    }\r\n  },\r\n  \r\n  // 刷新token\r\n  async RefreshToken({ commit, state }) {\r\n    try {\r\n      // 这里需要根据实际的刷新token接口调整\r\n      // const response = await refreshToken()\r\n      // commit('SET_TOKEN', response.data.access_token)\r\n      // return response\r\n      \r\n      // 暂时返回当前token\r\n      return { data: { access_token: state.token } }\r\n    } catch (error) {\r\n      console.error('刷新token失败:', error)\r\n      throw error\r\n    }\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}"], "names": ["getToken", "storage", "constant", "state", "setToken", "login", "uni", "getInfo", "user", "logout", "removeToken"], "mappings": ";;;;;;AAKA,MAAM,QAAQ,OAAO;AAAA,EACnB,OAAOA,WAAAA,SAAU;AAAA,EACjB,UAAUC,cAAO,QAAC,IAAIC,eAAAA,SAAS,QAAQ;AAAA,EACvC,YAAYD,cAAO,QAAC,IAAIC,eAAAA,SAAS,UAAU;AAAA,EAC3C,UAAUD,cAAO,QAAC,IAAIC,eAAAA,SAAS,QAAQ;AAAA,EACvC,UAAUD,cAAO,QAAC,IAAIC,eAAAA,SAAS,QAAQ;AAAA,EACvC,QAAQD,cAAO,QAAC,IAAIC,eAAAA,SAAS,MAAM;AAAA,EACnC,OAAOD,cAAO,QAAC,IAAIC,eAAAA,SAAS,KAAK;AAAA,EACjC,aAAaD,cAAO,QAAC,IAAIC,eAAAA,SAAS,WAAW;AAAA;AAAA,EAE7C,QAAQ;AAAA;AAAA,EACR,UAAU;AAAA;AAAA,EACV,WAAW;AAAA;AACb;AAEA,MACA,YAAY;AAAA,EACV,UAAUC,QAAO,OAAO;AACtB,IAAAA,OAAM,QAAQ;AACd,QAAI,OAAO;AACTC,iBAAAA,SAAS,KAAK;AACd,MAAAD,OAAM,aAAY,oBAAI,KAAI,GAAG,QAAS;AAAA,IACvC;AAAA,EACF;AAAA,EAED,aAAaA,QAAO,UAAU;AAC5B,IAAAA,OAAM,WAAW;AACjB,QAAI,aAAa,MAAM,aAAa,MAAM;AACxCF,4BAAQ,OAAOC,eAAQ,SAAC,QAAQ;AAAA,IACtC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,UAAU,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA,EAED,eAAeC,QAAO,YAAY;AAChC,IAAAA,OAAM,aAAa;AACnB,QAAI,eAAe,MAAM,eAAe,QAAQ,eAAe,OAAO;AACpEF,4BAAQ,OAAOC,eAAQ,SAAC,UAAU;AAAA,IACxC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,YAAY,UAAU;AAAA,IAC5C;AAAA,EACF;AAAA,EAED,aAAaC,QAAO,UAAU;AAC5B,IAAAA,OAAM,WAAW;AACjB,QAAI,aAAa,MAAM,aAAa,MAAM;AACxCF,4BAAQ,OAAOC,eAAQ,SAAC,QAAQ;AAAA,IACtC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,UAAU,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA,EAED,aAAaC,QAAO,UAAU;AAC5B,IAAAA,OAAM,WAAW;AACjB,QAAI,aAAa,MAAM,aAAa,MAAM;AACxCF,4BAAQ,OAAOC,eAAQ,SAAC,QAAQ;AAAA,IACtC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,UAAU,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA,EAED,WAAWC,QAAO,QAAQ;AACxB,IAAAA,OAAM,SAAS;AACf,QAAI,WAAW,MAAM,WAAW,MAAM;AACpCF,4BAAQ,OAAOC,eAAQ,SAAC,MAAM;AAAA,IACpC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,QAAQ,MAAM;AAAA,IACpC;AAAA,EACF;AAAA,EAED,UAAUC,QAAO,OAAO;AACtB,IAAAA,OAAM,QAAQ;AACd,QAAI,UAAU,MAAM,UAAU,QAAS,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAI;AAClFF,4BAAQ,OAAOC,eAAQ,SAAC,KAAK;AAAA,IACnC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,OAAO,KAAK;AAAA,IAClC;AAAA,EACF;AAAA,EAED,gBAAgBC,QAAO,aAAa;AAClC,IAAAA,OAAM,cAAc;AACpB,QAAI,gBAAgB,MAAM,gBAAgB,QAAS,MAAM,QAAQ,WAAW,KAAK,YAAY,WAAW,GAAI;AAC1GF,4BAAQ,OAAOC,eAAQ,SAAC,WAAW;AAAA,IACzC,OAAW;AACLD,oBAAAA,QAAQ,IAAIC,wBAAS,aAAa,WAAW;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA,EAGD,WAAWC,QAAO,QAAQ;AACxB,IAAAA,OAAM,SAAS;AAAA,EAChB;AAAA,EAED,cAAcA,QAAO,UAAU;AAC7B,IAAAA,OAAM,WAAW;AAAA,EAClB;AAAA,EAED,gBAAgBA,QAAO;AACrB,IAAAA,OAAM,QAAQ;AACd,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,aAAa;AACnB,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,SAAS;AACf,IAAAA,OAAM,QAAQ,CAAE;AAChB,IAAAA,OAAM,cAAc,CAAE;AACtB,IAAAA,OAAM,SAAS;AACf,IAAAA,OAAM,WAAW;AACjB,IAAAA,OAAM,YAAY;AAAA,EACnB;AACH;AAEA,MACA,UAAU;AAAA;AAAA,EAER,MAAM,MAAM,EAAE,OAAQ,GAAE,WAAW;AACjC,UAAM,EAAE,UAAU,UAAU,UAAU,MAAM,MAAM,WAAU,IAAK;AACjE,UAAM,kBAAkB,SAAS,KAAM;AAEvC,QAAI;AAEF,UAAI,YAAY;AACd,eAAO,gBAAgB,QAAQ;AAC/B,eAAO,kBAAkB,UAAU;AACnC,eAAO,gBAAgB,eAAe;AACtC,eAAO,gBAAgB,QAAQ;AAAA,MACvC,OAAa;AACL,eAAO,gBAAgB,EAAE;AACzB,eAAO,kBAAkB,KAAK;AAC9B,eAAO,gBAAgB,EAAE;AACzB,eAAO,gBAAgB,EAAE;AAAA,MAC1B;AAGD,YAAM,WAAW,MAAME,UAAK,MAAC,UAAU,iBAAiB,UAAU,IAAI;AACtE,YAAM,EAAE,iBAAiB,SAAS;AAGlC,aAAO,aAAa,YAAY;AAEhC,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,gCAAA,SAAS,KAAK;AAC5B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,QAAQ,EAAE,QAAQ,OAAAH,UAAS;AAC/B,QAAI;AACF,YAAM,WAAW,MAAMI,kBAAS;AAChC,YAAM,EAAE,MAAAC,OAAM,OAAO,YAAa,IAAG,SAAS;AAG9C,YAAM,SAAUA,SAAQA,MAAK,aAAaA,MAAK,cAAc,OACzDA,MAAK,YACL;AAGJ,YAAM,WAAYA,SAAQA,MAAK,YAAYA,MAAK,aAAa,OACzDA,MAAK,WACL;AAGJ,UAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,eAAO,aAAa,KAAK;AACzB,eAAO,mBAAmB,WAAW;AAAA,MAC7C,OAAa;AACL,eAAO,aAAa,CAAC,cAAc,CAAC;AAAA,MACrC;AAGD,aAAO,gBAAgB,QAAQ;AAC/B,aAAO,cAAc,MAAM;AAC3B,aAAO,iBAAiBA,KAAI;AAE5B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdF,oBAAAA,qDAAc,aAAa,KAAK;AAChC,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,OAAO,EAAE,QAAQ,OAAAH,UAAS;AAC9B,QAAI;AACF,YAAM,WAAW,MAAMM,iBAAQ;AAG/B,aAAO,iBAAiB;AAGxBC,6BAAa;AACbT,oBAAAA,QAAQ,MAAO;AAEf,aAAO;AAAA,IACR,SAAQ,OAAO;AACdK,oBAAAA,MAAc,MAAA,SAAA,gCAAA,SAAS,KAAK;AAE5B,aAAO,iBAAiB;AACxBI,6BAAa;AACbT,oBAAAA,QAAQ,MAAO;AACf,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,QAAQ,EAAE,OAAM,GAAI,EAAE,MAAM,OAAM,GAAI;AAC1C,QAAI;AAEF,YAAM,WAAW,MAAMI,UAAK,MAAC,IAAI,IAAI,IAAI,IAAI;AAC7C,YAAM,EAAE,iBAAiB,SAAS;AAElC,aAAO,aAAa,YAAY;AAChC,aAAO,cAAc,MAAM;AAE3B,aAAO;AAAA,IACR,SAAQ,OAAO;AACdC,oBAAAA,MAAc,MAAA,SAAA,gCAAA,WAAW,KAAK;AAC9B,YAAM;AAAA,IACP;AAAA,EACF;AAAA;AAAA,EAGD,MAAM,aAAa,EAAE,QAAQ,OAAAH,UAAS;AACpC,QAAI;AAOF,aAAO,EAAE,MAAM,EAAE,cAAcA,OAAM,MAAK,EAAI;AAAA,IAC/C,SAAQ,OAAO;AACdG,oBAAAA,MAAc,MAAA,SAAA,gCAAA,cAAc,KAAK;AACjC,YAAM;AAAA,IACP;AAAA,EACF;AACH;AAEA,MAAe,OAAA;AAAA,EACb,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AACF;;"}