/**
 * 通用工具函数
 * 适配uni-app环境
 */

/**
 * 转换参数为URL查询字符串
 * @param {Object} params 参数对象
 * @returns {string} 查询字符串
 */
export function tansParams(params) {
  if (!params || typeof params !== 'object') {
    return ''
  }

  let result = ''

  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      const value = params[key]
      const encodedKey = encodeURIComponent(key) + '='

      if (value !== null && value !== '' && value !== undefined) {
        if (typeof value === 'object') {
          // 处理对象类型的参数
          for (const subKey in value) {
            if (value.hasOwnProperty(subKey)) {
              const subValue = value[subKey]
              if (subValue !== null && subValue !== '' && subValue !== undefined) {
                result += encodeURIComponent(key + '[' + subKey + ']') + '=' + encodeURIComponent(subValue) + '&'
              }
            }
          }
        } else {
          result += encodedKey + encodeURIComponent(value) + '&'
        }
      }
    }
  }

  return result
}

/**
 * 显示提示信息
 * @param {string} message 提示信息
 * @param {string} icon 图标类型
 */
export function toast(message, icon = 'none') {
  uni.showToast({
    icon: icon,
    title: message,
    duration: 2000
  })
}

/**
 * 显示加载提示
 * @param {string} title 加载提示文字
 */
export function showLoading(title = '加载中...') {
  uni.showLoading({
    title: title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
export function hideLoading() {
  uni.hideLoading()
}

/**
 * 显示确认对话框
 * @param {string} content 对话框内容
 * @param {string} title 对话框标题
 * @returns {Promise} Promise对象
 */
export function showConfirm(content, title = '提示') {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title: title,
      content: content,
      success: (res) => {
        if (res.confirm) {
          resolve(true)
        } else {
          resolve(false)
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 格式化日期
 * @param {Date|string} date 日期对象或字符串
 * @param {string} format 格式化模板
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''

  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let timer = null
  return function(...args) {
    if (!timer) {
      timer = setTimeout(() => {
        func.apply(this, args)
        timer = null
      }, delay)
    }
  }
}

// 默认导出
export default {
  tansParams,
  toast,
  showLoading,
  hideLoading,
  showConfirm,
  formatDate,
  debounce,
  throttle
}