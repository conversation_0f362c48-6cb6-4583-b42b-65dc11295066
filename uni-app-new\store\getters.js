const getters = {
  token: (state) => state.user.token,
  tenantId: (state) => state.user.tenantId,
  rememberMe: (state) => state.user.rememberMe,
  avatar: (state) => state.user.avatar,
  username: (state) => state.user.username,
  password: (state) => state.user.password,
  roles: (state) => state.user.roles,
  permissions: (state) => state.user.permissions,
  
  // 新增一些有用的getters
  isLoggedIn: (state) => !!state.user.token,
  hasRole: (state) => (role) => {
    return state.user.roles && state.user.roles.includes(role)
  },
  hasPermission: (state) => (permission) => {
    return state.user.permissions && state.user.permissions.includes(permission)
  },
  userInfo: (state) => ({
    username: state.user.username,
    avatar: state.user.avatar,
    roles: state.user.roles,
    permissions: state.user.permissions
  })
}

export default getters