define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'moment'], function ($, undefined, Backend, Table, Form, moment) {
  var calendar = null;                // FullCalendar 实例
  let selectedDate = new Date(); // 默认当前日期
  var currentStatus = null;          // 当前选中日期的开闭馆状态 ('1' = 开馆, '0' = 闭馆)




  var Controller = {
    index: function () {
      var calendarEl = document.getElementById('holiday-calendar');

      $(function () {
        var calendar = new FullCalendar.Calendar(calendarEl, {
          locale: 'zh-cn',
          initialView: 'dayGridMonth',
          height: 'auto',
          headerToolbar: {
            left: 'prev today next',
            center: 'title',
            right: ''
          },
          selectable: true,
          customButtons: {

            today: {
              text: '今天',
              click: function () {
                selectedDate = new Date(); // 今天
                calendar.today();
                highlightSelectedDate();
                Controller.api.fetchSessionData(selectedDate);
              }
            },
            prev: {
              text: '上个月',
              click: function () {
                calendar.prev();

                setTimeout(() => {
                  const firstOfMonth = calendar.view.currentStart;
                  selectedDate = firstOfMonth;
                  highlightSelectedDate();
                  Controller.api.fetchSessionData(selectedDate);
                });
              }
            },
            next: {
              text: '下个月',
              click: function () {
                calendar.next();

                setTimeout(() => {
                  const firstOfMonth = calendar.view.currentStart;
                  console.log('firstOfMonth', firstOfMonth)
                  selectedDate = firstOfMonth;
                  highlightSelectedDate();
                  Controller.api.fetchSessionData(selectedDate);
                });
              }
            },

          },

          events: function (info, successCallback, failureCallback) {
            // 加载指定月份的数据
            const middle = new Date(info.start.getTime());
            middle.setDate(middle.getDate() + 15);

            const year = middle.getFullYear();
            const month = String(middle.getMonth() + 1).padStart(2, '0');
            const ym = `${year}-${month}`;

            $.get('functioncontrol/holidays/getlist', {
              month: ym
            }, function (res) {
              if (res.code === 1) {
                successCallback(res.data);
              } else {
                failureCallback(res.msg);
              }
            });
          },
          eventContent: function (arg) {
            const isClose = arg.event.extendedProps.is_close === '0'; // 0 = 闭馆

            const el = document.createElement('div');
            el.innerHTML = `<span class="${isClose ? 'calendar-close' : 'calendar-open'}">
                    ${isClose ? '闭馆日' : '开馆日'}
                  </span>`;

            return { domNodes: [el] };
          },
          dateClick: function (info) {

            //这一段是处理，点击了 非本月的日子，自动跳到对应月
            const clickedDate = info.date;
            const calendar = info.view.calendar;
            const currentMonth = calendar.getDate().getMonth(); // 当前中心月（0-11）
            const clickedMonth = clickedDate.getMonth();

            if (clickedMonth < currentMonth) {
              calendar.prev(); // 上个月
            } else if (clickedMonth > currentMonth) {
              calendar.next(); // 下个月
            } else {
              // 本月日，正常处理选中
              $('.fc-daygrid-day').removeClass('selected-date');
              $('.fc-daygrid-day[data-date="' + info.dateStr + '"]').addClass('selected-date');
            }

            selectedDate = info.date;
            highlightSelectedDate();
            Controller.api.fetchSessionData(info.date);

          },
          // 每次视图变更都会触发
          datesSet: function (info) {

            highlightSelectedDate(); // 高亮打钩
            // console.log(info.date)
            console.log(selectedDate)
            Controller.api.fetchSessionData(selectedDate); //  初始化时加载一次右侧数据

          },
        });

        calendar.render();
        setTimeout(() => {
          $('.fc-prev-button').text('上个月');
          $('.fc-next-button').text('下个月');
          $('.fc-closeButton-button').css({
            'background-color': 'red',
            'border-color': 'red',
            'color': 'white'
          });
          $('.fc-openButton-button').css({
            'background-color': '#1890ff',
            'border-color': '#1890ff',
            'color': 'white'
          });
        }, 100);

        $('#btn-add-session').on('click', function () {
          console.log('添加课程场次', formatDateToYMD(selectedDate));
          const dateStr = formatDateToYMD(selectedDate);
          Fast.api.open('course/session/add?selectedDate=' + dateStr, '添加课程场次', {
            callback: function () {
              // ✅ 此时页面已关闭，主页面上下文安全
              Controller.api.fetchSessionData(window.currentSelectedDate);
            }
          });

        });

        $(document).on('click', '.btn-edit', function () {
          const id = $(this).data('id');
          console.log('编辑课程场次', id);
          Fast.api.open('course/session/edit?ids=' + id, '编辑课程', {
            callback: function () {
              Controller.api.fetchSessionData(window.currentSelectedDate);
            }
          });
        });

        $(document).on('click', '.btn-del', function () {
          var id = $(this).data('id');
          Layer.confirm('确定要删除该课程场次吗？', { icon: 3, title: '提示' }, function (index) {
            $.ajax({
              url: 'course/session/del', // FastAdmin默认路由
              type: 'POST',
              data: { ids: id },  // 注意是 ids，不是 id（符合 FastAdmin 规范）
              success: function (res) {
                if (res.code === 1) {
                  Toastr.success("删除成功");
                  Controller.api.fetchSessionData(window.currentSelectedDate);
                } else {
                  Toastr.error(res.msg || "删除失败");
                }
              },
              error: function () {
                Toastr.error("请求失败");
              }
            });
            Layer.close(index);
          });
        });

        $(document).on('click', '.btn-toggle-state', function () {
          const id = $(this).data('id');
          const currentState = $(this).data('state'); // '1' or '0'
          const newState = currentState == '1' ? '0' : '1';
          const confirmText = newState == '1' ? '确定要开启预约？' : '确定要停止预约？';

          Layer.confirm(confirmText, function (index) {

            // 发送请求修改状态
            $.post('course/session/set_state', { id: id, state: newState }, function (res) {
              if (res.code === 1) {
                Toastr.success(res.msg || '操作成功');
                Controller.api.fetchSessionData(window.currentSelectedDate); // 刷新列表
              } else {
                Toastr.error(res.msg || '操作失败');
              }
              Layer.close(index); // ✅ 关闭弹窗
            });
          });
        });


        $('#btn-import').on('click', function () {
          const dateStr = formatDateToYMD(selectedDate);
          Fast.api.open('course/session/import?selectedDate=' + dateStr, '一键导入课程', {
            callback: function () {
              Controller.api.fetchSessionData(window.currentSelectedDate);
            }
          });
        });


        // ======= 辅助函数放这里 =======
        // 自定义函数，从事件中判断当天状态 （开馆 闭馆)
        function getDateStatusFromEvent(dateStr) {
          const events = calendar.getEvents();
          const event = events.find(ev => ev.startStr === dateStr);
          return event ? event.extendedProps.is_close : null;
        }
        //、创建高亮函数
        function highlightSelectedDate() {
          $('.fc-daygrid-day').removeClass('fc-selected');
          const dateObj = new Date(selectedDate); // 转为标准 Date 对象
          const dateStr = formatDateToYMD(dateObj);

          console.log('高亮函数：');
          console.log('[highl] selectedDate:', selectedDate);
          console.log('[highl] formatted:', dateStr);


          $(`.fc-daygrid-day[data-date="${dateStr}"]`).addClass('fc-selected');

          $('.fc-prev-button').text('上个月');
          $('.fc-next-button').text('下个月');

          //每次选中后，需要把当前内容展示在右边，在这里 弄比较好



        }
        //、创建高亮函数2
        function formatDateToYMD(date) {
          const y = date.getFullYear();
          const m = String(date.getMonth() + 1).padStart(2, '0');
          const d = String(date.getDate()).padStart(2, '0');
          return `${y}-${m}-${d}`;
        }

      });


    },
    add: function () {

      Controller.api.bindevent();
    },
    edit: function () {
      Controller.api.bindevent();
    },
    api: {
      bindevent: function () {

        Form.api.bindevent($("form[role=form]"), function (data, ret) {
          if (typeof parent.Toastr !== 'undefined') {
            parent.Toastr.success(ret.msg || "成功");
          } else {
            Toastr.success(ret.msg || "成功");
          }
          Fast.api.close(data); // ✅ index 页 callback 会收到
          return false; // 阻止默认行为（避免重复关闭）
        });

      },


      //查询课程，请求接口
      fetchSessionData: function (dateObj) {
        window.currentSelectedDate = dateObj; // 设置全局当前日期
        const year = dateObj.getFullYear();
        const month = String(dateObj.getMonth() + 1).padStart(2, '0');
        const day = dateObj.getDate();
        const dateStr = `${year}-${month}-${day}`;

        $.ajax({
          url: 'course/session/list',
          data: { queryDate: dateStr },
          success: function (res) {
            if (res.code === 200 && res.data) {
              Controller.api.renderCourseCards(res.data);
            } else {
              $('#course-list').html('<p class="text-muted">暂无课程</p>');
              layer.msg(res.msg, { icon: 2 });

            }
          },
          error: function () {
            $('#course-list').html('<p class="text-danger">请求失败</p>');
          }
        });
      },
      // 查询课程， 渲染课程卡片
      renderCourseCards: function (data) {
        const html = data.map(row => {
          const startTime =  moment(row.courseStartTime).format('HH:mm');
          const endTime = moment(row.courseEndTime).format('HH:mm');     
          const remain = row.inventoryVotes;
          const total = row.coursePoll;
          const stateBtn = `
  <button class="btn btn-${row.courseState === '1' ? 'danger' : 'success'} btn-xs btn-toggle-state"
    data-id="${row.id}"
    data-state="${row.courseState}">
    ${row.courseState === '1' ? '停止预约' : '开启预约'}
  </button>`;


          return `
            <div class="panel panel-default" style="padding: 15px; margin-bottom: 15px;">
              <div class="media">
                <div class="media-left">
                  <img src="${row.courseCover}" width="90" height="120" style="object-fit: cover;" onerror="this.style.display='none'" />
                </div>
                <div class="media-body" style="padding-left: 15px;">
                  <strong style="font-size: 18px;">${row.courseName}</strong>
                  <div style="margin: 5px 0;">时间: ${startTime} - ${endTime}</div>
                  <div>总预约量: ${total}　剩余预约量: ${remain}</div>
                </div>
                <div class="media-right text-right" style="min-width: 80px;">
                  <div class="btn-group-vertical">
                    <button class="btn btn-success btn-xs btn-edit" data-id="${row.id}">编辑</button>
                    <button class="btn btn-danger btn-xs btn-del" data-id="${row.id}">删除</button>
                    ${stateBtn}
                  </div>
                </div>
              </div>
            </div>
            `;
        }).join('');

        $('#course-list').html(html);
      },


    },

    import: function () {
      let importedData = [];

      $('#btn-choose-file').on('click', function () {
        $('#excelFile').click();
      });

      $('#excelFile').on('change', function (e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function (event) {
          const workbook = XLSX.read(event.target.result, { type: 'binary' });
          const sheet = workbook.Sheets[workbook.SheetNames[0]];
          importedData = XLSX.utils.sheet_to_json(sheet);
          // console.log("✅ 解析后的数据：", importedData); // <== 关键调试点
          renderPreview(importedData);
          injectHiddenInputs(importedData);
        };
        reader.readAsBinaryString(file);
      });

      // 预览卡片
      function renderPreview(data) {
        const html = data.map(item => `
    <div class="panel panel-default" style="padding:15px;margin-bottom:10px;">
      <strong style="font-size:16px;">${item['课程名称'] || '未命名课程'}</strong><br>
      <div style="color:#555;margin:5px 0;">课程简介：${item['课程简介'] || ''}</div>
      <div>时间：${item['开始时间(24小时制)']} - ${item['结束时间(24小时制)']}</div>
      <div>适龄范围：${item['适龄范围'] || '-'}</div>
      <div>地点：${item['地点'] || '-'}</div>
      <div>总预约量：${item['票数'] || 0}</div>
    </div>
  `).join('');
        $('#preview-list').html(html);
      }


      // 注入隐藏表单字段
      function injectHiddenInputs(data) {
        const container = $('#import-hidden-fields');
        container.empty();

        data.forEach((item, index) => {
          // 构建完整字段名 → 字段值映射
          const row = {
            course_name: item['课程名称'] || '课程名称',
            course_introduce: item['课程简介'] || '课程简介',
            course_age_prop: item['适龄范围'] || '适龄范围',
            course_address: item['地点'] || '地点',
            course_start_time: item['开始时间(24小时制)'] || '00:00',
            course_end_time: item['结束时间(24小时制)'] || '23:59',
            course_poll: item['票数'] || 0,
          };

          for (const [field, value] of Object.entries(row)) {
            const input = `<input type="hidden" name="rows[${index}][${field}]" value="${value}">`;
            container.append(input);
          }
        });
      }




      Controller.api.bindevent();
    },

  };
  return Controller;
});
