/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.venue-success.data-v-8101f867 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.venue-success .content.data-v-8101f867 {
  padding: 20rpx;
  padding-top: 120rpx;
}
.venue-success .content .success-state.data-v-8101f867 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.venue-success .content .success-state .success-header.data-v-8101f867 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.venue-success .content .success-state .success-header .success-icon.data-v-8101f867 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.venue-success .content .success-state .success-header .success-text.data-v-8101f867 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.venue-success .content .venue-info.data-v-8101f867 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.venue-success .content .venue-info .info-header.data-v-8101f867 {
  margin-bottom: 20rpx;
}
.venue-success .content .venue-info .info-header .venue-name.data-v-8101f867 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.venue-success .content .venue-info .venue-details.data-v-8101f867 {
  margin-bottom: 30rpx;
}
.venue-success .content .venue-info .venue-details .detail-row.data-v-8101f867 {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.venue-success .content .venue-info .venue-details .detail-row.data-v-8101f867:last-child {
  margin-bottom: 0;
}
.venue-success .content .venue-info .venue-details .detail-row .label.data-v-8101f867 {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
}
.venue-success .content .venue-info .venue-details .detail-row .value.data-v-8101f867 {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.venue-success .content .venue-info .venue-details .detail-row .value.important.data-v-8101f867 {
  color: #1976d2;
  font-weight: 600;
}
.venue-success .content .venue-info .contacts-list.data-v-8101f867 {
  margin-bottom: 30rpx;
}
.venue-success .content .venue-info .contacts-list .contacts-title.data-v-8101f867 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.venue-success .content .venue-info .contacts-list .contact-item.data-v-8101f867 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.venue-success .content .venue-info .contacts-list .contact-item.data-v-8101f867:last-child {
  border-bottom: none;
}
.venue-success .content .venue-info .contacts-list .contact-item .contact-name.data-v-8101f867 {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.venue-success .content .venue-info .contacts-list .contact-item .contact-id.data-v-8101f867 {
  font-size: 24rpx;
  color: #999;
}
.venue-success .content .venue-info .tips-section.data-v-8101f867 {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.venue-success .content .venue-info .tips-section .tips-icon.data-v-8101f867 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  margin-top: 4rpx;
  background: url("data:image/png;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMiIgaGVpZ2h0PSIzMiIgdmlld0JveD0iMCAwIDMyIDMyIj4KICA8cGF0aCBmaWxsPSIjRkZBMDAwIiBkPSJNMTYsMiBMMzAsMjggTDIsMjggTDE2LDIgWiIvPgogIDxwYXRoIGZpbGw9IiNGRkZGRkYiIGQ9Ik0xNCwyMiBMMTgsMjIgTDE4LDI2IEwxNCwyNiBMMTQsMjIgWiBNMTQsMTAgTDE4LDEwIEwxOCwyMCBMMTQsMjAgTDE0LDEwIFoiLz4KPC9zdmc+") center/contain no-repeat;
}
.venue-success .content .venue-info .tips-section .tips-text.data-v-8101f867 {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.venue-success .content .venue-info .tips-section .tips-text .highlight.data-v-8101f867 {
  color: #1976d2;
  font-weight: 500;
}
.venue-success .content .signin-section .signin-btn.data-v-8101f867 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
}
.venue-success .content .signin-section .signin-btn.data-v-8101f867::after {
  border: none;
}
.venue-success .content .signin-section .signin-btn.signed.data-v-8101f867 {
  background: #e8f5e8;
  color: #388e3c;
}
.venue-success .content .signin-section .signin-btn.data-v-8101f867:disabled {
  opacity: 0.6;
}
.venue-success .sign-modal.data-v-8101f867 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.venue-success .sign-modal .modal-dialog.data-v-8101f867 {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  text-align: center;
}
.venue-success .sign-modal .modal-dialog.fail .modal-icon.data-v-8101f867 {
  background: url("data:image/png;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTIwIDEyMCI+CiAgPGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iNTAiIGZpbGw9IiNGNDQzMzYiIC8+CiAgPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTYwLDMwIEM0OC45NTQsMzAgNDAsMzguOTU0IDQwLDUwIEM0MCw2NSA2MCw5MCA2MCw5MCBDNjAsOTAgODAsNjUgODAsNTAgQzgwLDM4Ljk1NCA3MS4wNDYsMzAgNjAsMzAgWiBNNjAsNTggQzU1LjU4Miw1OCA1Miw1NC40MTggNTIsNTAgQzUyLDQ1LjU4MiA1NS41ODIsNDIgNjAsNDIgQzY0LjQxOCw0MiA2OCw0NS41ODIgNjgsNTAgQzY4LDU0LjQxOCA2NC40MTgsNTggNjAsNTggWiIgLz4KICA8cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNODQuMjQyLDg0LjI0MiBDODEuODk1LDg2LjU4OSA3OS4wMzksODguMzkzIDc1Ljg1OCw4OS41IEw4OS41LDEwMy4xNDIgQzkwLjYwNyw5OS45NjEgOTIuNDExLDk3LjEwNSA5NC43NTgsOTQuNzU4IEw4NC4yNDIsODQuMjQyIFoiIC8+CiAgPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTM1Ljc1OCwzNS43NTggQzM4LjEwNSwzMy40MTEgNDAuOTYxLDMxLjYwNyA0NC4xNDIsMzAuNSBMMzAuNSwxNi44NTggQzI5LjM5MywyMC4wMzkgMjcuNTg5LDIyLjg5NSAyNS4yNDIsMjUuMjQyIEwzNS43NTgsMzUuNzU4IFoiIC8+Cjwvc3ZnPg==") center/contain no-repeat;
}
.venue-success .sign-modal .modal-dialog .modal-icon.data-v-8101f867 {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  background: url("data:image/png;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTIwIDEyMCI+CiAgPGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iNTAiIGZpbGw9IiM0Q0FGNTAiIC8+CiAgPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTUwLDcwLjggTDM1LjYsNTYuNCBMMzAsNjIgTDUwLDgyIEw5MCw0MiBMODQuNCwzNi40IEw1MCw3MC44IFoiIC8+Cjwvc3ZnPg==") center/contain no-repeat;
}
.venue-success .sign-modal .modal-dialog .modal-text.data-v-8101f867 {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 40rpx;
}
.venue-success .sign-modal .modal-dialog .modal-text .highlight.data-v-8101f867 {
  color: #1976d2;
  font-weight: 500;
}
.venue-success .sign-modal .modal-dialog .modal-btn.data-v-8101f867 {
  padding: 20rpx 60rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 平台特定样式 */
.content.data-v-8101f867 {
  padding-bottom: env(safe-area-inset-bottom);
}