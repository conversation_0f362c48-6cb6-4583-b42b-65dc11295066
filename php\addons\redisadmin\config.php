<?php

return [
    [
        'name'    => 'host',
        'title'   => 'Redis 主机',
        'type'    => 'string',
        'value'   => '127.0.0.1',
        'rule'    => 'required',
        'tip'     => 'Redis 服务器地址，例如 127.0.0.1',
        'ok'      => '',
        'extend'  => ''
    ],
    [
        'name'    => 'port',
        'title'   => 'Redis 端口',
        'type'    => 'number',
        'value'   => '6379',
        'rule'    => 'required',
        'tip'     => '默认端口 6379',
        'ok'      => '',
        'extend'  => ''
    ],
    [
        'name'    => 'password',
        'title'   => '密码',
        'type'    => 'string',
        'value'   => '',
        'rule'    => '',
        'tip'     => '如未设置密码可留空',
        'ok'      => '',
        'extend'  => ''
    ],
    [
        'name'    => 'select',
        'title'   => '数据库编号',
        'type'    => 'number',
        'value'   => '0',
        'rule'    => '',
        'tip'     => 'Redis 默认使用第 0 个库',
        'ok'      => '',
        'extend'  => ''
    ],    [
        'name'    => 'timeout',
        'title'   => '超时',
        'type'    => 'number',
        'value'   => '0',
        'rule'    => '',
        'tip'     => '连接 Redis 的超时时间，单位：秒',
        'ok'      => '',
        'extend'  => ''
    ],
    [
        'name'    => '__tips__',
        'title'   => '温馨提示',
        'type'    => 'string',
        'content' => [],
        'value'   => '支持配置 Redis 连接参数，如无特殊需求默认即可',
        'rule'    => '',
        'msg'     => '',
        'tip'     => '',
        'ok'      => '',
        'extend'  => '',
    ],
];
