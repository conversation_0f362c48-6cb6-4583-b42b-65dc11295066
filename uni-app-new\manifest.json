{"name": "宝安科技馆预约服务平台", "appid": "__UNI__BAOANQUESTACON", "description": "宝安科技馆预约服务平台 - 支持场馆预约、观影预约、课程预约等功能的跨平台应用", "versionName": "2.0.0", "versionCode": "200", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}, "Geolocation": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>"]}, "ios": {"privacyDescription": {"NSLocationWhenInUseUsageDescription": "此应用需要获取您的位置信息用于场馆导航和位置相关服务", "NSCameraUsageDescription": "此应用需要使用相机功能用于扫描二维码"}}, "sdkConfigs": {}}}, "quickapp": {}, "h5": {"title": "宝安科技馆预约服务平台", "template": "index.html", "router": {"mode": "hash", "base": "./"}, "optimization": {"treeShaking": {"enable": true}}, "publicPath": "./", "sdkConfigs": {}}, "mp-weixin": {"appid": "wx7fdbf0566b7e1707", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": false, "enhance": false, "minifyWXSS": true, "minifyWXML": true, "bigPackageSizeSupport": true}, "usingComponents": true, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "__usePrivacyCheck__": true, "lazyCodeLoading": "requiredComponents", "renderer": "webview", "navigateToMiniProgramAppIdList": [], "networkTimeout": {"request": 60000, "downloadFile": 60000}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3"}