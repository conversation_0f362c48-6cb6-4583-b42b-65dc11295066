.uni-data-pickerview {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    overflow: hidden
}

.error-text {
    color: #dd524d
}

.loading-cover {
    align-items: center;
    background-color: hsla(0,0%,100%,.5);
    bottom: 0;
    display: flex;
    flex-direction: column;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1001
}

.load-more {
    margin: auto
}

.error-message {
    background-color: #fff;
    bottom: 0;
    left: 0;
    opacity: .9;
    padding: 15px;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 102
}

.selected-list {
    border-bottom: 1px solid #f8f8f8;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    padding: 0 5px
}

.selected-item {
    margin-left: 10px;
    margin-right: 10px;
    padding: 12px 0;
    text-align: center;
    white-space: nowrap
}

.selected-item-text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    width: 168px;
    width: 6em
}

.selected-item-active {
    border-bottom: 2px solid #007aff
}

.selected-item-text {
    color: #007aff
}

.tab-c {
    display: flex;
    flex: 1;
    flex-direction: row;
    overflow: hidden;
    position: relative
}

.list {
    flex: 1
}

.item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 12px 15px
}

.is-disabled {
    opacity: .5
}

.item-text {
    color: #333
}

.item-text-overflow {
    overflow: hidden;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    white-space: nowrap;
    width: 280px;
    width: 20em
}

.check {
    border: 2px solid #007aff;
    border-left: 0;
    border-top: 0;
    height: 12px;
    margin-right: 5px;
    transform: rotate(45deg);
    transform-origin: center;
    transition: all .3s;
    width: 6px
}
