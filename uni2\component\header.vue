<template>
	<view class="header" :style="{
			background:background,
			color:color,
			paddingTop:menuButtonInfo.top + 'px',
			height:menuButtonInfo.height+ 'px',
			paddingBottom: 10 + 'px',
			position:isFixed ? 'fixed' : 'static',
			zIndex:isFixed ? 999 : 3
		}">
		<view class="header_content">
			<view :class="['header_btns',menuClass]">
				<view class="back icon_back" v-show="isBack" @tap="goBack" :style="{
					lineHeight:menuButtonInfo.height+ 'px'
				}"></view>
				<view class="home icon_home" v-show="isShowHome" @tap="goHome" :style="{
					lineHeight:menuButtonInfo.height+ 'px'
				}"></view>
			</view>
			<view class="title">{{title}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'header',
		props: {
			title: {
				type: String,
				default: ""
			},
			background: {
				type: String,
				default: "#ffffff"
			},
			isBack: {
				type: [Boolean, String],
				default: false
			},
			isShowHome: {
				type: [Boolean, String],
				default: false
			},
			isFixed: {
				type: [Boolean, String],
				default: false
			},
			color: {
				type: String,
				default: "#ffffff"
			},
			menuClass: {
				type: String,
				default: ""
			}
		},
		data() {
			return {
				menuButtonInfo: {}
			}
		},
		created() {
			this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			goHome() {
				uni.reLaunch({
					url: `/pages/index/index`
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.header {
		width: 100%;
		color: #333;
		font-family: 'PingFang SC';
		font-size: 35upx;

		.header_content {
			width: 100%;
			height: 100%;
			position: relative;

			.header_btns {
				width: 160upx;
				height: 100%;
				display: flex;
				position: relative;
				margin-left: 29upx;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				border-radius: 100upx;
				text-align: center;
				background-color: transparent;

				//默认的按钮样式
				&.df {
					background: #8EC7FF;

					.back {
						&::after {
							background-color: rgba(255, 255, 255, 0.2);
						}
					}
				}

				//带边框的样式
				&.bor {
					border: 1upx solid rgba(0, 0, 0, 0.2);

					.back {
						&::after {
							background-color: rgba(0, 0, 0, 0.2);
						}
					}
				}

				.back {
					width: 50%;
					height: 100%;
					font-size: 33upx;
					position: relative;

					&::after {
						content: '';
						display: inline-block;
						width: 1upx;
						height: 36upx;
						background-color: transparent;
						position: absolute;
						top: 50%;
						right: 1upx;
						transform: translateY(-50%);
					}
				}

				.home {
					width: 50%;
					height: 100%;
					font-size: 33upx;
				}
			}

			.title {
				display: inline-block;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-weight: 700;
			}


		}

	}
</style>
