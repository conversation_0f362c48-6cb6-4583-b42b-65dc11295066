############################################################
##
## PhpSpreadsheet - function name translations
##
## Dansk (Danish)
##
############################################################


##
## Kubefunktioner (Cube Functions)
##
CUBEKPIMEMBER = KUBE.KPI.MEDLEM
CUBEMEMBER = KUBEMEDLEM
CUBEMEMBERPROPERTY = KUBEMEDLEM.EGENSKAB
CUBERANKEDMEMBER = KUBERANGERET.MEDLEM
CUBESET = KUBESÆT
CUBESETCOUNT = KUBESÆT.ANTAL
CUBEVALUE = KUBEVÆRDI

##
## Databasefunktioner (Database Functions)
##
DAVERAGE = DMIDDEL
DCOUNT = DTÆL
DCOUNTA = DTÆLV
DGET = DHENT
DMAX = DMAKS
DMIN = DMIN
DPRODUCT = DPRODUKT
DSTDEV = DSTDAFV
DSTDEVP = DSTDAFVP
DSUM = DSUM
DVAR = DVARIANS
DVARP = DVARIANSP

##
## Dato- og klokkeslætfunktioner (Date & Time Functions)
##
DATE = DATO
DATEDIF = DATO.FORSKEL
DATESTRING = DATOSTRENG
DATEVALUE = DATOVÆRDI
DAY = DAG
DAYS = DAGE
DAYS360 = DAGE360
EDATE = EDATO
EOMONTH = SLUT.PÅ.MÅNED
HOUR = TIME
ISOWEEKNUM = ISOUGE.NR
MINUTE = MINUT
MONTH = MÅNED
NETWORKDAYS = ANTAL.ARBEJDSDAGE
NETWORKDAYS.INTL = ANTAL.ARBEJDSDAGE.INTL
NOW = NU
SECOND = SEKUND
THAIDAYOFWEEK = THAILANDSKUGEDAG
THAIMONTHOFYEAR = THAILANDSKMÅNED
THAIYEAR = THAILANDSKÅR
TIME = TID
TIMEVALUE = TIDSVÆRDI
TODAY = IDAG
WEEKDAY = UGEDAG
WEEKNUM = UGE.NR
WORKDAY = ARBEJDSDAG
WORKDAY.INTL = ARBEJDSDAG.INTL
YEAR = ÅR
YEARFRAC = ÅR.BRØK

##
## Tekniske funktioner (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BIN.TIL.DEC
BIN2HEX = BIN.TIL.HEX
BIN2OCT = BIN.TIL.OKT
BITAND = BITOG
BITLSHIFT = BITLSKIFT
BITOR = BITELLER
BITRSHIFT = BITRSKIFT
BITXOR = BITXELLER
COMPLEX = KOMPLEKS
CONVERT = KONVERTER
DEC2BIN = DEC.TIL.BIN
DEC2HEX = DEC.TIL.HEX
DEC2OCT = DEC.TIL.OKT
DELTA = DELTA
ERF = FEJLFUNK
ERF.PRECISE = ERF.PRECISE
ERFC = FEJLFUNK.KOMP
ERFC.PRECISE = ERFC.PRECISE
GESTEP = GETRIN
HEX2BIN = HEX.TIL.BIN
HEX2DEC = HEX.TIL.DEC
HEX2OCT = HEX.TIL.OKT
IMABS = IMAGABS
IMAGINARY = IMAGINÆR
IMARGUMENT = IMAGARGUMENT
IMCONJUGATE = IMAGKONJUGERE
IMCOS = IMAGCOS
IMCOSH = IMAGCOSH
IMCOT = IMAGCOT
IMCSC = IMAGCSC
IMCSCH = IMAGCSCH
IMDIV = IMAGDIV
IMEXP = IMAGEKSP
IMLN = IMAGLN
IMLOG10 = IMAGLOG10
IMLOG2 = IMAGLOG2
IMPOWER = IMAGPOTENS
IMPRODUCT = IMAGPRODUKT
IMREAL = IMAGREELT
IMSEC = IMAGSEC
IMSECH = IMAGSECH
IMSIN = IMAGSIN
IMSINH = IMAGSINH
IMSQRT = IMAGKVROD
IMSUB = IMAGSUB
IMSUM = IMAGSUM
IMTAN = IMAGTAN
OCT2BIN = OKT.TIL.BIN
OCT2DEC = OKT.TIL.DEC
OCT2HEX = OKT.TIL.HEX

##
## Finansielle funktioner (Financial Functions)
##
ACCRINT = PÅLØBRENTE
ACCRINTM = PÅLØBRENTE.UDLØB
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = KUPONDAGE.SA
COUPDAYS = KUPONDAGE.A
COUPDAYSNC = KUPONDAGE.ANK
COUPNCD = KUPONDAG.NÆSTE
COUPNUM = KUPONBETALINGER
COUPPCD = KUPONDAG.FORRIGE
CUMIPMT = AKKUM.RENTE
CUMPRINC = AKKUM.HOVEDSTOL
DB = DB
DDB = DSA
DISC = DISKONTO
DOLLARDE = KR.DECIMAL
DOLLARFR = KR.BRØK
DURATION = VARIGHED
EFFECT = EFFEKTIV.RENTE
FV = FV
FVSCHEDULE = FVTABEL
INTRATE = RENTEFOD
IPMT = R.YDELSE
IRR = IA
ISPMT = ISPMT
MDURATION = MVARIGHED
MIRR = MIA
NOMINAL = NOMINEL
NPER = NPER
NPV = NUTIDSVÆRDI
ODDFPRICE = ULIGE.KURS.PÅLYDENDE
ODDFYIELD = ULIGE.FØRSTE.AFKAST
ODDLPRICE = ULIGE.SIDSTE.KURS
ODDLYIELD = ULIGE.SIDSTE.AFKAST
PDURATION = PVARIGHED
PMT = YDELSE
PPMT = H.YDELSE
PRICE = KURS
PRICEDISC = KURS.DISKONTO
PRICEMAT = KURS.UDLØB
PV = NV
RATE = RENTE
RECEIVED = MODTAGET.VED.UDLØB
RRI = RRI
SLN = LA
SYD = ÅRSAFSKRIVNING
TBILLEQ = STATSOBLIGATION
TBILLPRICE = STATSOBLIGATION.KURS
TBILLYIELD = STATSOBLIGATION.AFKAST
VDB = VSA
XIRR = INTERN.RENTE
XNPV = NETTO.NUTIDSVÆRDI
YIELD = AFKAST
YIELDDISC = AFKAST.DISKONTO
YIELDMAT = AFKAST.UDLØBSDATO

##
## Informationsfunktioner (Information Functions)
##
CELL = CELLE
ERROR.TYPE = FEJLTYPE
INFO = INFO
ISBLANK = ER.TOM
ISERR = ER.FJL
ISERROR = ER.FEJL
ISEVEN = ER.LIGE
ISFORMULA = ER.FORMEL
ISLOGICAL = ER.LOGISK
ISNA = ER.IKKE.TILGÆNGELIG
ISNONTEXT = ER.IKKE.TEKST
ISNUMBER = ER.TAL
ISODD = ER.ULIGE
ISREF = ER.REFERENCE
ISTEXT = ER.TEKST
N = TAL
NA = IKKE.TILGÆNGELIG
SHEET = ARK
SHEETS = ARK.FLERE
TYPE = VÆRDITYPE

##
## Logiske funktioner (Logical Functions)
##
AND = OG
FALSE = FALSK
IF = HVIS
IFERROR = HVIS.FEJL
IFNA = HVISIT
IFS = HVISER
NOT = IKKE
OR = ELLER
SWITCH = SKIFT
TRUE = SAND
XOR = XELLER

##
## Opslags- og referencefunktioner (Lookup & Reference Functions)
##
ADDRESS = ADRESSE
AREAS = OMRÅDER
CHOOSE = VÆLG
COLUMN = KOLONNE
COLUMNS = KOLONNER
FORMULATEXT = FORMELTEKST
GETPIVOTDATA = GETPIVOTDATA
HLOOKUP = VOPSLAG
HYPERLINK = HYPERLINK
INDEX = INDEKS
INDIRECT = INDIREKTE
LOOKUP = SLÅ.OP
MATCH = SAMMENLIGN
OFFSET = FORSKYDNING
ROW = RÆKKE
ROWS = RÆKKER
RTD = RTD
TRANSPOSE = TRANSPONER
VLOOKUP = LOPSLAG
*RC = RK

##
## Matematiske og trigonometriske funktioner (Math & Trig Functions)
##
ABS = ABS
ACOS = ARCCOS
ACOSH = ARCCOSH
ACOT = ARCCOT
ACOTH = ARCCOTH
AGGREGATE = SAMLING
ARABIC = ARABISK
ASIN = ARCSIN
ASINH = ARCSINH
ATAN = ARCTAN
ATAN2 = ARCTAN2
ATANH = ARCTANH
BASE = BASIS
CEILING.MATH = LOFT.MAT
CEILING.PRECISE = LOFT.PRECISE
COMBIN = KOMBIN
COMBINA = KOMBINA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DECIMAL
DEGREES = GRADER
ECMA.CEILING = ECMA.LOFT
EVEN = LIGE
EXP = EKSP
FACT = FAKULTET
FACTDOUBLE = DOBBELT.FAKULTET
FLOOR.MATH = AFRUND.BUND.MAT
FLOOR.PRECISE = AFRUND.GULV.PRECISE
GCD = STØRSTE.FÆLLES.DIVISOR
INT = HELTAL
ISO.CEILING = ISO.LOFT
LCM = MINDSTE.FÆLLES.MULTIPLUM
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MDETERM
MINVERSE = MINVERT
MMULT = MPRODUKT
MOD = REST
MROUND = MAFRUND
MULTINOMIAL = MULTINOMIAL
MUNIT = MENHED
ODD = ULIGE
PI = PI
POWER = POTENS
PRODUCT = PRODUKT
QUOTIENT = KVOTIENT
RADIANS = RADIANER
RAND = SLUMP
RANDBETWEEN = SLUMPMELLEM
ROMAN = ROMERTAL
ROUND = AFRUND
ROUNDBAHTDOWN = RUNDBAHTNED
ROUNDBAHTUP = RUNDBAHTOP
ROUNDDOWN = RUND.NED
ROUNDUP = RUND.OP
SEC = SEC
SECH = SECH
SERIESSUM = SERIESUM
SIGN = FORTEGN
SIN = SIN
SINH = SINH
SQRT = KVROD
SQRTPI = KVRODPI
SUBTOTAL = SUBTOTAL
SUM = SUM
SUMIF = SUM.HVIS
SUMIFS = SUM.HVISER
SUMPRODUCT = SUMPRODUKT
SUMSQ = SUMKV
SUMX2MY2 = SUMX2MY2
SUMX2PY2 = SUMX2PY2
SUMXMY2 = SUMXMY2
TAN = TAN
TANH = TANH
TRUNC = AFKORT

##
## Statistiske funktioner (Statistical Functions)
##
AVEDEV = MAD
AVERAGE = MIDDEL
AVERAGEA = MIDDELV
AVERAGEIF = MIDDEL.HVIS
AVERAGEIFS = MIDDEL.HVISER
BETA.DIST = BETA.FORDELING
BETA.INV = BETA.INV
BINOM.DIST = BINOMIAL.FORDELING
BINOM.DIST.RANGE = BINOMIAL.DIST.INTERVAL
BINOM.INV = BINOMIAL.INV
CHISQ.DIST = CHI2.FORDELING
CHISQ.DIST.RT = CHI2.FORD.RT
CHISQ.INV = CHI2.INV
CHISQ.INV.RT = CHI2.INV.RT
CHISQ.TEST = CHI2.TEST
CONFIDENCE.NORM = KONFIDENS.NORM
CONFIDENCE.T = KONFIDENST
CORREL = KORRELATION
COUNT = TÆL
COUNTA = TÆLV
COUNTBLANK = ANTAL.BLANKE
COUNTIF = TÆL.HVIS
COUNTIFS = TÆL.HVISER
COVARIANCE.P = KOVARIANS.P
COVARIANCE.S = KOVARIANS.S
DEVSQ = SAK
EXPON.DIST = EKSP.FORDELING
F.DIST = F.FORDELING
F.DIST.RT = F.FORDELING.RT
F.INV = F.INV
F.INV.RT = F.INV.RT
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = PROGNOSE.ETS
FORECAST.ETS.CONFINT = PROGNOSE.ETS.CONFINT
FORECAST.ETS.SEASONALITY = PROGNOSE.ETS.SÆSONUDSVING
FORECAST.ETS.STAT = PROGNOSE.ETS.STAT
FORECAST.LINEAR = PROGNOSE.LINEÆR
FREQUENCY = FREKVENS
GAMMA = GAMMA
GAMMA.DIST = GAMMA.FORDELING
GAMMA.INV = GAMMA.INV
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.PRECISE
GAUSS = GAUSS
GEOMEAN = GEOMIDDELVÆRDI
GROWTH = FORØGELSE
HARMEAN = HARMIDDELVÆRDI
HYPGEOM.DIST = HYPGEO.FORDELING
INTERCEPT = SKÆRING
KURT = TOPSTEJL
LARGE = STØRSTE
LINEST = LINREGR
LOGEST = LOGREGR
LOGNORM.DIST = LOGNORM.FORDELING
LOGNORM.INV = LOGNORM.INV
MAX = MAKS
MAXA = MAKSV
MAXIFS = MAKSHVISER
MEDIAN = MEDIAN
MIN = MIN
MINA = MINV
MINIFS = MINHVISER
MODE.MULT = HYPPIGST.FLERE
MODE.SNGL = HYPPIGST.ENKELT
NEGBINOM.DIST = NEGBINOM.FORDELING
NORM.DIST = NORMAL.FORDELING
NORM.INV = NORM.INV
NORM.S.DIST = STANDARD.NORM.FORDELING
NORM.S.INV = STANDARD.NORM.INV
PEARSON = PEARSON
PERCENTILE.EXC = FRAKTIL.UDELAD
PERCENTILE.INC = FRAKTIL.MEDTAG
PERCENTRANK.EXC = PROCENTPLADS.UDELAD
PERCENTRANK.INC = PROCENTPLADS.MEDTAG
PERMUT = PERMUT
PERMUTATIONA = PERMUTATIONA
PHI = PHI
POISSON.DIST = POISSON.FORDELING
PROB = SANDSYNLIGHED
QUARTILE.EXC = KVARTIL.UDELAD
QUARTILE.INC = KVARTIL.MEDTAG
RANK.AVG = PLADS.GNSN
RANK.EQ = PLADS.LIGE
RSQ = FORKLARINGSGRAD
SKEW = SKÆVHED
SKEW.P = SKÆVHED.P
SLOPE = STIGNING
SMALL = MINDSTE
STANDARDIZE = STANDARDISER
STDEV.P = STDAFV.P
STDEV.S = STDAFV.S
STDEVA = STDAFVV
STDEVPA = STDAFVPV
STEYX = STFYX
T.DIST = T.FORDELING
T.DIST.2T = T.FORDELING.2T
T.DIST.RT = T.FORDELING.RT
T.INV = T.INV
T.INV.2T = T.INV.2T
T.TEST = T.TEST
TREND = TENDENS
TRIMMEAN = TRIMMIDDELVÆRDI
VAR.P = VARIANS.P
VAR.S = VARIANS.S
VARA = VARIANSV
VARPA = VARIANSPV
WEIBULL.DIST = WEIBULL.FORDELING
Z.TEST = Z.TEST

##
## Tekstfunktioner (Text Functions)
##
BAHTTEXT = BAHTTEKST
CHAR = TEGN
CLEAN = RENS
CODE = KODE
CONCAT = CONCAT
DOLLAR = KR
EXACT = EKSAKT
FIND = FIND
FIXED = FAST
ISTHAIDIGIT = ERTHAILANDSKCIFFER
LEFT = VENSTRE
LEN = LÆNGDE
LOWER = SMÅ.BOGSTAVER
MID = MIDT
NUMBERSTRING = TALSTRENG
NUMBERVALUE = TALVÆRDI
PHONETIC = FONETISK
PROPER = STORT.FORBOGSTAV
REPLACE = ERSTAT
REPT = GENTAG
RIGHT = HØJRE
SEARCH = SØG
SUBSTITUTE = UDSKIFT
T = T
TEXT = TEKST
TEXTJOIN = TEKST.KOMBINER
THAIDIGIT = THAILANDSKCIFFER
THAINUMSOUND = THAILANDSKNUMLYD
THAINUMSTRING = THAILANDSKNUMSTRENG
THAISTRINGLENGTH = THAILANDSKSTRENGLÆNGDE
TRIM = FJERN.OVERFLØDIGE.BLANKE
UNICHAR = UNICHAR
UNICODE = UNICODE
UPPER = STORE.BOGSTAVER
VALUE = VÆRDI

##
## Webfunktioner (Web Functions)
##
ENCODEURL = KODNINGSURL
FILTERXML = FILTRERXML
WEBSERVICE = WEBTJENESTE

##
## Kompatibilitetsfunktioner (Compatibility Functions)
##
BETADIST = BETAFORDELING
BETAINV = BETAINV
BINOMDIST = BINOMIALFORDELING
CEILING = AFRUND.LOFT
CHIDIST = CHIFORDELING
CHIINV = CHIINV
CHITEST = CHITEST
CONCATENATE = SAMMENKÆDNING
CONFIDENCE = KONFIDENSINTERVAL
COVAR = KOVARIANS
CRITBINOM = KRITBINOM
EXPONDIST = EKSPFORDELING
FDIST = FFORDELING
FINV = FINV
FLOOR = AFRUND.GULV
FORECAST = PROGNOSE
FTEST = FTEST
GAMMADIST = GAMMAFORDELING
GAMMAINV = GAMMAINV
HYPGEOMDIST = HYPGEOFORDELING
LOGINV = LOGINV
LOGNORMDIST = LOGNORMFORDELING
MODE = HYPPIGST
NEGBINOMDIST = NEGBINOMFORDELING
NORMDIST = NORMFORDELING
NORMINV = NORMINV
NORMSDIST = STANDARDNORMFORDELING
NORMSINV = STANDARDNORMINV
PERCENTILE = FRAKTIL
PERCENTRANK = PROCENTPLADS
POISSON = POISSON
QUARTILE = KVARTIL
RANK = PLADS
STDEV = STDAFV
STDEVP = STDAFVP
TDIST = TFORDELING
TINV = TINV
TTEST = TTEST
VAR = VARIANS
VARP = VARIANSP
WEIBULL = WEIBULL
ZTEST = ZTEST
