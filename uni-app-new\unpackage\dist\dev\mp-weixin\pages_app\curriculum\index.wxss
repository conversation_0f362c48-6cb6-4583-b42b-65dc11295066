
.curriculum {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.curriculum_order_box {
  padding-top: 20rpx;
}
.calendar_content {
  background-color: #ffffff;
  margin: 0 30rpx 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}
.calendar {
  padding: 20rpx;
}
.curriculum_list_content {
  background-color: #ffffff;
  margin: 0 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;
}
.curriculum_title {
  width: 100%;
  height: 80rpx;
  margin-bottom: 30rpx;
}
.curriculum_list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.curriculum_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}
.curriculum_item:active {
  background-color: #e9ecef;
}
.curriculum_item.disNone {
  display: none;
}
.itemLeft {
  display: flex;
  flex: 1;
  align-items: center;
}
.cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  margin-right: 30rpx;
  object-fit: cover;
}
.curriculumInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8rpx;
}
.curriculumName {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}
.curriculumType,
.curriculumTime,
.curriculumPlace {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.3;
}
.itemRight {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20rpx;
}
.ticketType {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}
.n_text {
  font-size: 22rpx;
  color: #999999;
}
.t_text {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: 600;
}
.order_button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  min-width: 120rpx;
  transition: all 0.3s ease;
}
.order_button.gray {
  background: #cccccc;
  color: #999999;
}
.curriculum_tip {
  text-align: center;
  color: #999999;
  font-size: 28rpx;
  padding: 100rpx 0;
}

/* 弹窗样式 */
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.maskContent {
  background-color: #ffffff;
  width: 600rpx;
  max-height: 80vh;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}
.noticeTitle {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}
.noticeView {
  flex: 1;
  max-height: 500rpx;
  overflow-y: auto;
  margin-bottom: 30rpx;
}
.noticeItem {
  margin-bottom: 30rpx;
}
.itemTitle {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 15rpx;
}
.itemContent {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}
.agreeBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 25rpx;
  border-radius: 15rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.agreeBtn.gray {
  background: #cccccc;
  color: #999999;
}
.agreeBtn:active {
  transform: scale(0.98);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
.curriculum_item {
    padding: 25rpx;
}
.cover {
    width: 100rpx;
    height: 100rpx;
    margin-right: 25rpx;
}
.curriculumName {
    font-size: 30rpx;
}
.curriculumType,
  .curriculumTime,
  .curriculumPlace {
    font-size: 24rpx;
}
}
