/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.choose-curriculum.data-v-110d03ff {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}
.curriculum-container.data-v-110d03ff {
  padding: 20rpx 30rpx;
}
.curriculum-header.data-v-110d03ff {
  position: relative;
  height: 360rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}
.curriculum-header .curriculum-bg.data-v-110d03ff {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.curriculum-header .curriculum-info.data-v-110d03ff {
  position: relative;
  z-index: 2;
  display: flex;
  padding: 40rpx;
  height: 100%;
  box-sizing: border-box;
  align-items: center;
}
.curriculum-header .curriculum-info .course-cover.data-v-110d03ff {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  margin-right: 30rpx;
  background-color: rgba(255, 255, 255, 0.2);
}
.curriculum-header .curriculum-info .course-details.data-v-110d03ff {
  flex: 1;
  color: #ffffff;
}
.curriculum-header .curriculum-info .course-details .course-name.data-v-110d03ff {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  display: block;
}
.curriculum-header .curriculum-info .course-details .course-meta .meta-item.data-v-110d03ff {
  font-size: 26rpx;
  display: block;
  margin-bottom: 10rpx;
  opacity: 0.9;
}
.curriculum-content.data-v-110d03ff {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.curriculum-content .content-section.data-v-110d03ff {
  margin-bottom: 40rpx;
}
.curriculum-content .content-section.data-v-110d03ff:last-child {
  margin-bottom: 0;
}
.curriculum-content .content-section .section-title.data-v-110d03ff {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.curriculum-content .content-section .section-title .title-icon.data-v-110d03ff {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.curriculum-content .content-section .section-title .title-text.data-v-110d03ff {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.curriculum-content .content-section .section-content.data-v-110d03ff {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
.curriculum-content .content-section .notice-content .notice-item.data-v-110d03ff {
  margin-bottom: 30rpx;
}
.curriculum-content .content-section .notice-content .notice-item.data-v-110d03ff:last-child {
  margin-bottom: 0;
}
.curriculum-content .content-section .notice-content .notice-item .notice-title.data-v-110d03ff {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}
.curriculum-content .content-section .notice-content .notice-item .notice-list .notice-text.data-v-110d03ff {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}
.curriculum-content .content-section .notice-content .notice-item .notice-list .notice-text.data-v-110d03ff:last-child {
  margin-bottom: 0;
}
.contacts-section.data-v-110d03ff {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
}
.contacts-section .section-title.data-v-110d03ff {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.contacts-section .section-title .title-icon.data-v-110d03ff {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.contacts-section .section-title .title-text.data-v-110d03ff {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.contacts-section .contacts-list .contact-item.data-v-110d03ff {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.contacts-section .contacts-list .contact-item.data-v-110d03ff:last-child {
  border-bottom: none;
}
.contacts-section .contacts-list .contact-item .contact-info .contact-name.data-v-110d03ff {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 6rpx;
  display: block;
}
.contacts-section .contacts-list .contact-item .contact-info .contact-id.data-v-110d03ff {
  font-size: 24rpx;
  color: #999999;
  display: block;
}
.contacts-section .contacts-list .contact-item .contact-select .select-circle.data-v-110d03ff {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #dddddd;
  position: relative;
}
.contacts-section .contacts-list .contact-item .contact-select .select-circle.selected.data-v-110d03ff {
  border-color: #1976d2;
  background-color: #1976d2;
}
.contacts-section .contacts-list .contact-item .contact-select .select-circle.selected.data-v-110d03ff:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 12rpx;
  border-left: 4rpx solid #ffffff;
  border-bottom: 4rpx solid #ffffff;
  transform: translate(-50%, -60%) rotate(-45deg);
}
.contacts-section .no-contacts.data-v-110d03ff {
  padding: 40rpx 0;
  text-align: center;
}
.contacts-section .no-contacts .no-contacts-text.data-v-110d03ff {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 20rpx;
  display: block;
}
.contacts-section .no-contacts .add-contact-btn.data-v-110d03ff {
  display: inline-block;
  background-color: #1976d2;
  color: #ffffff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  border: none;
}
.contacts-section .no-contacts .add-contact-btn.data-v-110d03ff::after {
  border: none;
}
.bottom-bar.data-v-110d03ff {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.bottom-bar .submit-btn.data-v-110d03ff {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  border: none;
}
.bottom-bar .submit-btn.data-v-110d03ff::after {
  border: none;
}
.bottom-bar .submit-btn.data-v-110d03ff:disabled {
  opacity: 0.6;
}