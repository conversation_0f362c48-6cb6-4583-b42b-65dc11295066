{"version": 3, "file": "filmsuccess.js", "sources": ["pages_app/schemesuccess/filmsuccess.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHNjaGVtZXN1Y2Nlc3NcZmlsbXN1Y2Nlc3MudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"film-success\">\r\n    <!-- 自定义头部 -->\r\n    <my-header\r\n      title=\"观影凭证\"\r\n      :isBack=\"true\"\r\n      :isShowHome=\"true\"\r\n      background=\"#ffffff\"\r\n      color=\"#333333\"\r\n      :isFixed=\"true\"\r\n    />\r\n\r\n    <view class=\"content\">\r\n      <!-- 成功状态 -->\r\n      <view class=\"success-state\">\r\n        <view class=\"success-header\">\r\n          <image\r\n            src=\"/static/img/schemesuccess/schemestate_course_film.png\"\r\n            mode=\"aspectFit\"\r\n            class=\"success-icon\"\r\n          />\r\n          <text class=\"success-text\">预约成功</text>\r\n        </view>\r\n\r\n        <!-- 注意事项 -->\r\n        <view class=\"tips-section\">\r\n          <view class=\"tips-title\">注意:</view>\r\n          <view\r\n            v-for=\"(tip, index) in tipsList\"\r\n            :key=\"index\"\r\n            class=\"tips-item\"\r\n          >\r\n            {{ tip }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 电影信息 -->\r\n      <view class=\"film-info\">\r\n        <view class=\"info-header\">\r\n          <text class=\"venue-name\">宝安科技馆 观影预约</text>\r\n        </view>\r\n\r\n        <view class=\"film-details\">\r\n          <view class=\"film-poster\">\r\n            <image\r\n              :src=\"filmInfo.filmCover\"\r\n              mode=\"aspectFill\"\r\n              class=\"poster-image\"\r\n            />\r\n          </view>\r\n          <view class=\"film-meta\">\r\n            <text class=\"film-name\">{{ filmInfo.filmName }}</text>\r\n            <text class=\"film-type\">类型：{{ filmTypeList[filmInfo.filmType - 1] || '未知' }}</text>\r\n            <text class=\"film-time\">时间：{{ filmInfo.filmStartTime }} - {{ filmInfo.filmEndTime }}</text>\r\n            <text class=\"film-date\">日期：{{ filmInfo.filmArrangedDate }} {{ getWeekDay(filmInfo.filmArrangedDate) }}</text>\r\n            <text class=\"film-count\">预约人数：{{ filmInfo.orderNum }}人</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 二维码区域 -->\r\n      <view class=\"qrcode-section\">\r\n        <!-- 二维码切换按钮 -->\r\n        <view v-if=\"qrcodeList.length > 1\" class=\"qrcode-tabs\">\r\n          <view\r\n            v-for=\"(btn, index) in qrcodeBtns.slice(0, qrcodeList.length)\"\r\n            :key=\"index\"\r\n            :class=\"['tab-btn', { active: qrcodeIndex === index }]\"\r\n            @tap=\"changeQrcodeIndex(index)\"\r\n          >\r\n            {{ btn }}\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 二维码显示 -->\r\n        <view class=\"qrcode-container\">\r\n          <view class=\"qrcode-wrapper\">\r\n            <image\r\n              v-if=\"qrcodeList.length > 0 && showQrcode\"\r\n              :src=\"qrcodeList[qrcodeIndex].qrcodeUrl\"\r\n              class=\"qrcode-image\"\r\n              mode=\"aspectFit\"\r\n              @tap=\"refreshQrcode\"\r\n            />\r\n            <image\r\n              v-else\r\n              src=\"/static/img/schemesuccess/qrcode.png\"\r\n              class=\"qrcode-placeholder\"\r\n              mode=\"aspectFit\"\r\n            />\r\n\r\n            <!-- 二维码遮罩 -->\r\n            <view\r\n              v-if=\"!showQrcode || filmInfo.subscribeState === 1 || !isPlayFilm\"\r\n              class=\"qrcode-mask\"\r\n            >\r\n              <view class=\"mask-content\">\r\n                <button\r\n                  v-if=\"!isPlayFilm && (filmInfo.subscribeState === 4 || filmInfo.subscribeState === 1)\"\r\n                  class=\"cancel-btn\"\r\n                  disabled\r\n                >\r\n                  电影因人数不足取消放映\r\n                </button>\r\n                <button\r\n                  v-else-if=\"!showQrcode && filmInfo.subscribeState === 4\"\r\n                  class=\"waiting-btn\"\r\n                  disabled\r\n                >\r\n                  <view>影片未开始</view>\r\n                  <view>提前十分钟入场</view>\r\n                </button>\r\n                <button\r\n                  v-else\r\n                  class=\"signin-btn\"\r\n                  @tap=\"signUp\"\r\n                  :disabled=\"isSigningUp\"\r\n                >\r\n                  {{ isSigningUp ? '签到中...' : '签到获取二维码' }}\r\n                </button>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 倒计时 -->\r\n        <view v-if=\"isShowCountDown\" class=\"countdown\">\r\n          <text class=\"countdown-text\">距离开场还有：{{ countDown }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 底部提示 -->\r\n      <view class=\"bottom-tips\">\r\n        在首页<text class=\"highlight\">个人中心—观影登记—查看凭证</text>中查看此凭证，提前10分钟签到获取二维码，扫码进场\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 定位弹窗 -->\r\n    <view v-if=\"isShowLocationMask\" class=\"location-mask\">\r\n      <view class=\"mask-dialog\">\r\n        <view class=\"location-icon\"></view>\r\n        <view class=\"mask-text\">\r\n          <text>您的定位较远</text>\r\n          <br />\r\n          <text>请移步至宝安科技馆进行<text class=\"highlight\">现场签到</text></text>\r\n        </view>\r\n        <view class=\"mask-btn\" @tap=\"closeLocationMask\">返回</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'FilmSuccess',\r\n  data() {\r\n    return {\r\n      filmInfo: {},\r\n      filmSessionId: null,\r\n      batchNumber: null,\r\n      qrcodeList: [],\r\n      qrcodeIndex: 0,\r\n      qrcodeBtns: ['票码一', '票码二', '票码三', '票码四', '票码五'],\r\n      filmTypeList: ['球幕电影', '4D电影'],\r\n      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\r\n\r\n      // 状态控制\r\n      isShowCountDown: false,\r\n      countDown: '',\r\n      countDownTimer: null,\r\n      isShowLocationMask: false,\r\n      isSigningUp: false,\r\n      showQrcode: false,\r\n      isPlayFilm: true,\r\n\r\n      // 提示信息\r\n      tipsList: [\r\n        '1.请按时到达宝安科技馆，凭二维码检票入场',\r\n        '2.请提前10分钟签到获取二维码，否则该预约失效，需重新预约',\r\n        '3.入场后请保持安静，配合工作人员安排'\r\n      ],\r\n\r\n      // 定时器\r\n      refreshTimer: null,\r\n      filmInfoTimer: null\r\n    }\r\n  },\r\n\r\n  onLoad(options) {\r\n    this.batchNumber = options.batchNumber\r\n    this.filmSessionId = options.filmSessionId\r\n    this.initPage()\r\n  },\r\n\r\n  onShow() {\r\n    this.getFilmInfo()\r\n    this.getTipsList()\r\n  },\r\n\r\n  onHide() {\r\n    this.clearTimers()\r\n  },\r\n\r\n  onUnload() {\r\n    this.clearTimers()\r\n  },\r\n\r\n  methods: {\r\n    // 初始化页面\r\n    initPage() {\r\n      this.getFilmInfo()\r\n      this.getTipsList()\r\n      this.startCountDown()\r\n    },\r\n\r\n    // 获取电影信息\r\n    async getFilmInfo() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/fileSession/personalCenterFilmByFilmSessionId',\r\n          method: 'get',\r\n          data: {\r\n            filmSessionId: this.filmSessionId\r\n          }\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          this.filmInfo = {\r\n            ...res.data.data,\r\n            filmStartTime: this.formatTime(res.data.data.filmStartTime),\r\n            filmEndTime: this.formatTime(res.data.data.filmEndTime),\r\n            orderNum: res.data.data.filmPoll - res.data.data.inventoryVotes\r\n          }\r\n\r\n          // 开始定时刷新电影信息\r\n          this.startFilmInfoRefresh()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取电影信息失败:', error)\r\n        uni.showToast({\r\n          title: '获取信息失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 获取提示信息\r\n    async getTipsList() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/admin/entrance_announcement/3',\r\n          method: 'get'\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          const data = res.data.data\r\n          if (data.beiyongThree) {\r\n            this.tipsList = data.beiyongThree.split('\\n').filter(tip => tip.trim())\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.warn('获取提示信息失败:', error)\r\n      }\r\n    },\r\n\r\n    // 切换二维码\r\n    changeQrcodeIndex(index) {\r\n      this.qrcodeIndex = index\r\n    },\r\n\r\n    // 格式化时间\r\n    formatTime(timeStr) {\r\n      if (!timeStr) return ''\r\n\r\n      try {\r\n        const date = new Date(timeStr.replace(/-/g, '/'))\r\n        const hours = date.getHours().toString().padStart(2, '0')\r\n        const minutes = date.getMinutes().toString().padStart(2, '0')\r\n        return `${hours}:${minutes}`\r\n      } catch (error) {\r\n        return timeStr\r\n      }\r\n    },\r\n\r\n    // 获取星期\r\n    getWeekDay(dateStr) {\r\n      if (!dateStr) return ''\r\n\r\n      try {\r\n        const date = new Date(dateStr.replace(/-/g, '/'))\r\n        return this.weekList[date.getDay()]\r\n      } catch (error) {\r\n        return ''\r\n      }\r\n    },\r\n\r\n    // 签到获取二维码\r\n    async signUp() {\r\n      if (this.isSigningUp) return\r\n\r\n      this.isSigningUp = true\r\n\r\n      try {\r\n        // 获取位置信息\r\n        const location = await this.getCurrentLocation()\r\n\r\n        // 验证位置\r\n        const locationRes = await this.$myRequest({\r\n          url: '/web/common/checkLocation',\r\n          method: 'post',\r\n          data: {\r\n            latitude: location.latitude,\r\n            longitude: location.longitude\r\n          }\r\n        })\r\n\r\n        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {\r\n          this.isShowLocationMask = true\r\n          return\r\n        }\r\n\r\n        // 签到获取二维码\r\n        const signRes = await this.$myRequest({\r\n          url: '/web/fileSession/filmSign',\r\n          method: 'post',\r\n          data: {\r\n            batchNumber: this.batchNumber,\r\n            filmSessionId: this.filmSessionId\r\n          }\r\n        })\r\n\r\n        if (signRes.code === 200) {\r\n          this.showQrcode = true\r\n          this.getQrcode()\r\n\r\n          uni.showToast({\r\n            title: '签到成功',\r\n            icon: 'success'\r\n          })\r\n        } else {\r\n          throw new Error(signRes.msg || '签到失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('签到失败:', error)\r\n\r\n        if (error.message && error.message.includes('定位')) {\r\n          this.isShowLocationMask = true\r\n        } else {\r\n          uni.showToast({\r\n            title: error.message || '签到失败',\r\n            icon: 'error'\r\n          })\r\n        }\r\n      } finally {\r\n        this.isSigningUp = false\r\n      }\r\n    },\r\n\r\n    // 获取当前位置\r\n    getCurrentLocation() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.getLocation({\r\n          type: 'gcj02',\r\n          success: resolve,\r\n          fail: reject\r\n        })\r\n      })\r\n    },\r\n\r\n    // 获取二维码\r\n    async getQrcode() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/fileSession/qrCodeInfo',\r\n          method: 'get',\r\n          data: {\r\n            batchNumber: this.batchNumber,\r\n            filmType: this.filmInfo.filmType\r\n          }\r\n        })\r\n\r\n        if (res.code === 200) {\r\n          this.qrcodeList = res.data.data || []\r\n\r\n          if (this.qrcodeList.length === 0) {\r\n            uni.showModal({\r\n              title: '提示',\r\n              content: '状态已更改, 返回上一页',\r\n              showCancel: false,\r\n              success: () => {\r\n                uni.navigateBack()\r\n              }\r\n            })\r\n          } else {\r\n            // 设置定时刷新二维码\r\n            this.startQrcodeRefresh()\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取二维码失败:', error)\r\n        uni.showToast({\r\n          title: '获取二维码失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n\r\n    // 刷新二维码\r\n    refreshQrcode() {\r\n      this.getQrcode()\r\n    },\r\n\r\n    // 开始倒计时\r\n    startCountDown() {\r\n      if (!this.filmInfo.filmStartTime) return\r\n\r\n      const startTime = new Date(this.filmInfo.filmArrangedDate + ' ' + this.filmInfo.filmStartTime).getTime()\r\n      const now = new Date().getTime()\r\n\r\n      // 如果距离开场时间在30分钟内，显示倒计时\r\n      if (startTime - now > 0 && startTime - now < 30 * 60 * 1000) {\r\n        this.isShowCountDown = true\r\n\r\n        this.countDownTimer = setInterval(() => {\r\n          const now = new Date().getTime()\r\n          const diff = startTime - now\r\n\r\n          if (diff <= 0) {\r\n            this.isShowCountDown = false\r\n            clearInterval(this.countDownTimer)\r\n            return\r\n          }\r\n\r\n          const minutes = Math.floor(diff / 60000)\r\n          const seconds = Math.floor((diff % 60000) / 1000)\r\n          this.countDown = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`\r\n        }, 1000)\r\n      }\r\n    },\r\n\r\n    // 开始电影信息刷新\r\n    startFilmInfoRefresh() {\r\n      this.filmInfoTimer = setTimeout(() => {\r\n        this.getFilmInfo()\r\n      }, 1500)\r\n    },\r\n\r\n    // 开始二维码刷新\r\n    startQrcodeRefresh() {\r\n      this.refreshTimer = setTimeout(() => {\r\n        this.getQrcode()\r\n      }, 30000) // 30秒刷新一次\r\n    },\r\n\r\n    // 清除所有定时器\r\n    clearTimers() {\r\n      if (this.countDownTimer) {\r\n        clearInterval(this.countDownTimer)\r\n        this.countDownTimer = null\r\n      }\r\n      if (this.refreshTimer) {\r\n        clearTimeout(this.refreshTimer)\r\n        this.refreshTimer = null\r\n      }\r\n      if (this.filmInfoTimer) {\r\n        clearTimeout(this.filmInfoTimer)\r\n        this.filmInfoTimer = null\r\n      }\r\n    },\r\n\r\n    // 关闭定位弹窗\r\n    closeLocationMask() {\r\n      this.isShowLocationMask = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.film-success {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\r\n  .content {\r\n    padding: 20rpx;\r\n    padding-top: 120rpx; // 为固定头部留出空间\r\n\r\n    // 成功状态\r\n    .success-state {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 40rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .success-header {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        margin-bottom: 30rpx;\r\n\r\n        .success-icon {\r\n          width: 120rpx;\r\n          height: 120rpx;\r\n          margin-bottom: 20rpx;\r\n        }\r\n\r\n        .success-text {\r\n          font-size: 36rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .tips-section {\r\n        .tips-title {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n          margin-bottom: 20rpx;\r\n        }\r\n\r\n        .tips-item {\r\n          font-size: 28rpx;\r\n          color: #666;\r\n          line-height: 1.6;\r\n          margin-bottom: 12rpx;\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 电影信息\r\n    .film-info {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .info-header {\r\n        margin-bottom: 20rpx;\r\n\r\n        .venue-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .film-details {\r\n        display: flex;\r\n\r\n        .film-poster {\r\n          width: 120rpx;\r\n          height: 160rpx;\r\n          margin-right: 20rpx;\r\n          border-radius: 12rpx;\r\n          overflow: hidden;\r\n\r\n          .poster-image {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n\r\n        .film-meta {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-between;\r\n\r\n          .film-name {\r\n            font-size: 32rpx;\r\n            font-weight: 600;\r\n            color: #333;\r\n            margin-bottom: 8rpx;\r\n            line-height: 1.4;\r\n          }\r\n\r\n          .film-type,\r\n          .film-time,\r\n          .film-date,\r\n          .film-count {\r\n            font-size: 26rpx;\r\n            color: #666;\r\n            margin-bottom: 6rpx;\r\n            line-height: 1.3;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 二维码区域\r\n    .qrcode-section {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .qrcode-tabs {\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-bottom: 30rpx;\r\n        gap: 20rpx;\r\n\r\n        .tab-btn {\r\n          padding: 12rpx 24rpx;\r\n          border-radius: 20rpx;\r\n          font-size: 26rpx;\r\n          background: #f5f5f5;\r\n          color: #666;\r\n\r\n          &.active {\r\n            background: linear-gradient(135deg, #667eea, #764ba2);\r\n            color: white;\r\n          }\r\n        }\r\n      }\r\n\r\n      .qrcode-container {\r\n        display: flex;\r\n        justify-content: center;\r\n\r\n        .qrcode-wrapper {\r\n          position: relative;\r\n          width: 400rpx;\r\n          height: 400rpx;\r\n\r\n          .qrcode-image,\r\n          .qrcode-placeholder {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 12rpx;\r\n          }\r\n\r\n          .qrcode-mask {\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            background: rgba(0, 0, 0, 0.7);\r\n            border-radius: 12rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n\r\n            .mask-content {\r\n              text-align: center;\r\n\r\n              .cancel-btn,\r\n              .waiting-btn,\r\n              .signin-btn {\r\n                padding: 20rpx 40rpx;\r\n                border-radius: 40rpx;\r\n                font-size: 28rpx;\r\n                font-weight: 500;\r\n                border: none;\r\n\r\n                &::after {\r\n                  border: none;\r\n                }\r\n              }\r\n\r\n              .cancel-btn {\r\n                background: #ff6b6b;\r\n                color: white;\r\n              }\r\n\r\n              .waiting-btn {\r\n                background: #ffa726;\r\n                color: white;\r\n\r\n                view {\r\n                  line-height: 1.3;\r\n                }\r\n              }\r\n\r\n              .signin-btn {\r\n                background: linear-gradient(135deg, #4ecdc4, #44a08d);\r\n                color: white;\r\n\r\n                &:disabled {\r\n                  opacity: 0.6;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .countdown {\r\n        text-align: center;\r\n        margin-top: 20rpx;\r\n\r\n        .countdown-text {\r\n          font-size: 28rpx;\r\n          color: #666;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 底部提示\r\n    .bottom-tips {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      line-height: 1.6;\r\n      text-align: center;\r\n\r\n      .highlight {\r\n        color: #1976d2;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 定位弹窗\r\n  .location-mask {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n\r\n    .mask-dialog {\r\n      background: white;\r\n      border-radius: 20rpx;\r\n      padding: 60rpx 40rpx;\r\n      margin: 40rpx;\r\n      text-align: center;\r\n\r\n      .location-icon {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        margin: 0 auto 30rpx;\r\n        background: url('/static/img/common/location-error.png') center/contain no-repeat;\r\n      }\r\n\r\n      .mask-text {\r\n        font-size: 32rpx;\r\n        color: #333;\r\n        line-height: 1.6;\r\n        margin-bottom: 40rpx;\r\n\r\n        .highlight {\r\n          color: #1976d2;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      .mask-btn {\r\n        padding: 20rpx 60rpx;\r\n        background: linear-gradient(135deg, #667eea, #764ba2);\r\n        color: white;\r\n        border-radius: 40rpx;\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.film-success {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/schemesuccess/filmsuccess.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "now"], "mappings": ";;;AA0JA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,UAAU,CAAE;AAAA,MACZ,eAAe;AAAA,MACf,aAAa;AAAA,MACb,YAAY,CAAE;AAAA,MACd,aAAa;AAAA,MACb,YAAY,CAAC,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,MAC9C,cAAc,CAAC,QAAQ,MAAM;AAAA,MAC7B,UAAU,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA;AAAA,MAGnD,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MAGZ,UAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA;AAAA,MAGD,cAAc;AAAA,MACd,eAAe;AAAA,IACjB;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACd,SAAK,cAAc,QAAQ;AAC3B,SAAK,gBAAgB,QAAQ;AAC7B,SAAK,SAAS;AAAA,EACf;AAAA,EAED,SAAS;AACP,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AACP,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,WAAW;AACT,SAAK,YAAY;AAAA,EAClB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,WAAW;AACT,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,eAAe,KAAK;AAAA,UACtB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,WAAW;AAAA,YACd,GAAG,IAAI,KAAK;AAAA,YACZ,eAAe,KAAK,WAAW,IAAI,KAAK,KAAK,aAAa;AAAA,YAC1D,aAAa,KAAK,WAAW,IAAI,KAAK,KAAK,WAAW;AAAA,YACtD,UAAU,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,KAAK;AAAA,UACnD;AAGA,eAAK,qBAAqB;AAAA,QAC5B;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,cAAc;AAClB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,OAAO,IAAI,KAAK;AACtB,cAAI,KAAK,cAAc;AACrB,iBAAK,WAAW,KAAK,aAAa,MAAM,IAAI,EAAE,OAAO,SAAO,IAAI,KAAI,CAAE;AAAA,UACxE;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,kDAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACvB,WAAK,cAAc;AAAA,IACpB;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC1B,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,eAAO,KAAK,SAAS,KAAK,OAAM,CAAE;AAAA,MAClC,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,SAAS;AACb,UAAI,KAAK;AAAa;AAEtB,WAAK,cAAc;AAEnB,UAAI;AAEF,cAAM,WAAW,MAAM,KAAK,mBAAmB;AAG/C,cAAM,cAAc,MAAM,KAAK,WAAW;AAAA,UACxC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,UAAU,SAAS;AAAA,YACnB,WAAW,SAAS;AAAA,UACtB;AAAA,SACD;AAED,YAAI,YAAY,SAAS,OAAO,CAAC,YAAY,KAAK,KAAK,UAAU;AAC/D,eAAK,qBAAqB;AAC1B;AAAA,QACF;AAGA,cAAM,UAAU,MAAM,KAAK,WAAW;AAAA,UACpC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,aAAa,KAAK;AAAA,YAClB,eAAe,KAAK;AAAA,UACtB;AAAA,SACD;AAED,YAAI,QAAQ,SAAS,KAAK;AACxB,eAAK,aAAa;AAClB,eAAK,UAAU;AAEfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,WACP;AAAA,eACI;AACL,gBAAM,IAAI,MAAM,QAAQ,OAAO,MAAM;AAAA,QACvC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kDAAA,SAAS,KAAK;AAE5B,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjD,eAAK,qBAAqB;AAAA,eACrB;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF,UAAU;AACR,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,YAAY;AAAA,UACd,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,YAAY;AAChB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,aAAa,KAAK;AAAA,YAClB,UAAU,KAAK,SAAS;AAAA,UAC1B;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,aAAa,IAAI,KAAK,QAAQ,CAAC;AAEpC,cAAI,KAAK,WAAW,WAAW,GAAG;AAChCA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,SAAS,MAAM;AACbA,8BAAAA,MAAI,aAAa;AAAA,cACnB;AAAA,aACD;AAAA,iBACI;AAEL,iBAAK,mBAAmB;AAAA,UAC1B;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kDAAA,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,UAAU;AAAA,IAChB;AAAA;AAAA,IAGD,iBAAiB;AACf,UAAI,CAAC,KAAK,SAAS;AAAe;AAElC,YAAM,aAAY,oBAAI,KAAK,KAAK,SAAS,mBAAmB,MAAM,KAAK,SAAS,aAAa,GAAE,QAAQ;AACvG,YAAM,OAAM,oBAAI,KAAM,GAAC,QAAQ;AAG/B,UAAI,YAAY,MAAM,KAAK,YAAY,MAAM,KAAK,KAAK,KAAM;AAC3D,aAAK,kBAAkB;AAEvB,aAAK,iBAAiB,YAAY,MAAM;AACtC,gBAAMC,QAAM,oBAAI,KAAM,GAAC,QAAQ;AAC/B,gBAAM,OAAO,YAAYA;AAEzB,cAAI,QAAQ,GAAG;AACb,iBAAK,kBAAkB;AACvB,0BAAc,KAAK,cAAc;AACjC;AAAA,UACF;AAEA,gBAAM,UAAU,KAAK,MAAM,OAAO,GAAK;AACvC,gBAAM,UAAU,KAAK,MAAO,OAAO,MAAS,GAAI;AAChD,eAAK,YAAY,GAAG,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC,IAAI,QAAQ,SAAU,EAAC,SAAS,GAAG,GAAG,CAAC;AAAA,QAC/F,GAAE,GAAI;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,uBAAuB;AACrB,WAAK,gBAAgB,WAAW,MAAM;AACpC,aAAK,YAAY;AAAA,MAClB,GAAE,IAAI;AAAA,IACR;AAAA;AAAA,IAGD,qBAAqB;AACnB,WAAK,eAAe,WAAW,MAAM;AACnC,aAAK,UAAU;AAAA,MAChB,GAAE,GAAK;AAAA,IACT;AAAA;AAAA,IAGD,cAAc;AACZ,UAAI,KAAK,gBAAgB;AACvB,sBAAc,KAAK,cAAc;AACjC,aAAK,iBAAiB;AAAA,MACxB;AACA,UAAI,KAAK,cAAc;AACrB,qBAAa,KAAK,YAAY;AAC9B,aAAK,eAAe;AAAA,MACtB;AACA,UAAI,KAAK,eAAe;AACtB,qBAAa,KAAK,aAAa;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB;AAClB,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3dA,GAAG,WAAW,eAAe;"}