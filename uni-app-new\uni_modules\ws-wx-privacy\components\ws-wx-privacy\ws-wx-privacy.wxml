<uni-popup bind:__l="__l" class="r data-v-5705b407" id="privacy" uI="5705b407-0" uP="{{n}}" uR="privacyPopup" uS="{{['d']}}" wx:if="{{n}}">
    <view class="ws-privacy-popup data-v-5705b407" style="{{l}}">
        <view class="ws-privacy-popup__header data-v-5705b407">
            <view class="ws-picker__title data-v-5705b407">{{a}}</view>
        </view>
        <view class="ws-privacy-popup__container data-v-5705b407">
            <text class="data-v-5705b407">{{b}}</text>
            <text bindtap="{{e}}" class="ws-privacy-popup__container-protocol data-v-5705b407" style="{{d}}">{{c}}</text>
            <text class="data-v-5705b407">{{f}}</text>
        </view>
        <view class="ws-privacy-popup__footer data-v-5705b407">
            <button bindagreeprivacyauthorization="{{i}}" class="is-agree data-v-5705b407" id="agree-btn" openType="agreePrivacyAuthorization" style="{{h}}">{{g}}</button>
            <button bindtap="{{k}}" class="is-disagree data-v-5705b407" id="disagree-btn">{{j}}</button>
        </view>
    </view>
</uni-popup>
