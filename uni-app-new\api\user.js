import { request } from '../utils/request.js'

// 获取用户个人信息
export function getUserProfile() {
  return request({
    url: '/apitp/user/profile',
    method: 'GET'
  })
}

// 更新用户个人信息
export function updateUserProfile(userInfo) {
  return request({
    url: '/apitp/user/profile',
    method: 'PUT',
    data: userInfo
  })
}

// 上传用户头像
export function uploadAvatar(avatarFile) {
  return request({
    url: '/apitp/user/avatar',
    method: 'POST',
    data: avatarFile
  })
}

// 绑定手机号
export function bindPhone(phone, code) {
  return request({
    url: '/apitp/user/bindPhone',
    method: 'POST',
    data: { phone, code }
  })
}

// 解绑手机号
export function unbindPhone(code) {
  return request({
    url: '/apitp/user/unbindPhone',
    method: 'POST',
    data: { code }
  })
}

// 获取用户预约历史
export function getUserReservationHistory(params) {
  return request({
    url: '/apitp/user/reservations',
    method: 'GET',
    params
  })
}

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: '/apitp/user/stats',
    method: 'GET'
  })
}

// 用户反馈
export function submitFeedback(feedbackData) {
  return request({
    url: '/apitp/user/feedback',
    method: 'POST',
    data: feedbackData
  })
}

// 获取用户消息
export function getUserMessages(params) {
  return request({
    url: '/apitp/user/messages',
    method: 'GET',
    params
  })
}

// 标记消息为已读
export function markMessageAsRead(messageId) {
  return request({
    url: `/apitp/user/message/read/${messageId}`,
    method: 'PUT'
  })
}

// 删除用户消息
export function deleteUserMessage(messageId) {
  return request({
    url: `/apitp/user/message/${messageId}`,
    method: 'DELETE'
  })
}

// 获取用户设置
export function getUserSettings() {
  return request({
    url: '/apitp/user/settings',
    method: 'GET'
  })
}

// 更新用户设置
export function updateUserSettings(settings) {
  return request({
    url: '/apitp/user/settings',
    method: 'PUT',
    data: settings
  })
}

// 注销账户
export function deleteAccount(password) {
  return request({
    url: '/apitp/user/delete',
    method: 'DELETE',
    data: { password }
  })
}