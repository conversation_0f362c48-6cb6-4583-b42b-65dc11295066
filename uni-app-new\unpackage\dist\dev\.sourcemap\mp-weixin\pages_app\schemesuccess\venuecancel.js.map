{"version": 3, "file": "venuecancel.js", "sources": ["pages_app/schemesuccess/venuecancel.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHNjaGVtZXN1Y2Nlc3NcdmVudWVjYW5jZWwudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"venue-cancel\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      title=\"取消预约\" \r\n      :isBack=\"true\" \r\n      :isShowHome=\"true\"\r\n      background=\"#ffffff\"\r\n      color=\"#333333\"\r\n    />\r\n    \r\n    <view class=\"content\">\r\n      <!-- 参观信息 -->\r\n      <view class=\"venue-info\">\r\n        <view class=\"info-header\">\r\n          <text class=\"venue-name\">宝安科技馆 入馆预约</text>\r\n        </view>\r\n        \r\n        <view class=\"venue-details\">\r\n          <view class=\"detail-row\">\r\n            <text class=\"label\">预约日期：</text>\r\n            <text class=\"value important\">{{ venueInfo.date }}</text>\r\n          </view>\r\n          <view class=\"detail-row\">\r\n            <text class=\"label\">入馆时间：</text>\r\n            <text class=\"value\">{{ venueInfo.venueStartTime }} - {{ venueInfo.venueEndTime }}</text>\r\n          </view>\r\n          <view class=\"detail-row\">\r\n            <text class=\"label\">预约人数：</text>\r\n            <text class=\"value\">{{ venueInfo.subscribeType }}人</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 联系人选择 -->\r\n      <view class=\"contacts-section\">\r\n        <view class=\"section-title\">选择要取消的联系人</view>\r\n        <view class=\"contacts-list\">\r\n          <view \r\n            v-for=\"(contact, index) in contactsList\" \r\n            :key=\"contact.linkId\"\r\n            class=\"contact-item\"\r\n            @tap=\"toggleContact(index)\"\r\n          >\r\n            <view class=\"contact-info\">\r\n              <text class=\"contact-name\">{{ contact.linkmanName }}</text>\r\n              <text class=\"contact-phone\">{{ contact.linkmanPhone }}</text>\r\n              <text class=\"contact-id\">{{ hideIdCard(contact.linkmanCertificate) }}</text>\r\n            </view>\r\n            <view :class=\"['checkbox', { checked: contact.linkCheck }]\">\r\n              <text v-if=\"contact.linkCheck\" class=\"check-icon\">✓</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 取消按钮 -->\r\n      <view class=\"action-section\">\r\n        <button \r\n          class=\"cancel-btn\"\r\n          @tap=\"confirmCancel\"\r\n          :disabled=\"!hasSelectedContacts || isCancelling\"\r\n        >\r\n          {{ isCancelling ? '取消中...' : '确认取消预约' }}\r\n        </button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'VenueCancel',\r\n  data() {\r\n    return {\r\n      venueInfo: {},\r\n      contactsList: [],\r\n      \r\n      // 参数\r\n      venueSessionId: null,\r\n      batchNumber: null,\r\n      vote: null,\r\n      \r\n      // 状态\r\n      isCancelling: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 是否有选中的联系人\r\n    hasSelectedContacts() {\r\n      return this.contactsList.some(contact => contact.linkCheck)\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    this.venueSessionId = options.venueSessionId\r\n    this.batchNumber = options.batchNumber\r\n    this.vote = options.vote\r\n    \r\n    this.getVenueInfo()\r\n    this.getContactsList()\r\n  },\r\n  \r\n  methods: {\r\n    // 获取参观信息\r\n    async getVenueInfo() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/venueSession/personalCenterVenueByVenueSessionId',\r\n          method: 'get',\r\n          data: {\r\n            venueSessionId: this.venueSessionId\r\n          }\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          const data = res.data.data\r\n          this.venueInfo = {\r\n            ...data,\r\n            date: this.formatDate(data.venueArrangedDate),\r\n            venueStartTime: this.formatTime(data.venueStartTime),\r\n            venueEndTime: this.formatTime(data.venueEndTime)\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取参观信息失败:', error)\r\n        uni.showToast({\r\n          title: '获取信息失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 获取联系人列表\r\n    async getContactsList() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/venueSession/getVenueSubscribePeoples',\r\n          method: 'get',\r\n          data: {\r\n            batchNumber: this.batchNumber\r\n          }\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          this.contactsList = (res.data.data || []).map(contact => ({\r\n            ...contact,\r\n            linkCheck: false\r\n          }))\r\n        }\r\n      } catch (error) {\r\n        console.error('获取联系人失败:', error)\r\n        uni.showToast({\r\n          title: '获取联系人失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 切换联系人选择状态\r\n    toggleContact(index) {\r\n      this.contactsList[index].linkCheck = !this.contactsList[index].linkCheck\r\n    },\r\n    \r\n    // 确认取消预约\r\n    confirmCancel() {\r\n      if (!this.hasSelectedContacts) {\r\n        uni.showToast({\r\n          title: '请选择要取消的联系人',\r\n          icon: 'error'\r\n        })\r\n        return\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '确认取消',\r\n        content: '确定要取消选中联系人的预约吗？',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            this.performCancel()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 执行取消操作\r\n    async performCancel() {\r\n      if (this.isCancelling) return\r\n      \r\n      this.isCancelling = true\r\n      \r\n      try {\r\n        const selectedIds = this.contactsList\r\n          .filter(contact => contact.linkCheck)\r\n          .map(contact => contact.linkId)\r\n        \r\n        const res = await this.$myRequest({\r\n          url: '/web/venueSession/cancelVenueSession',\r\n          method: 'get',\r\n          data: {\r\n            vote: this.vote,\r\n            venueSessionId: this.venueSessionId,\r\n            batchNumber: this.batchNumber,\r\n            peopleIds: selectedIds.join(',')\r\n          }\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          uni.showToast({\r\n            title: '取消预约成功',\r\n            icon: 'success',\r\n            duration: 2000\r\n          })\r\n          \r\n          setTimeout(() => {\r\n            uni.navigateTo({\r\n              url: '/pages_app/user/venuescheme'\r\n            })\r\n          }, 2000)\r\n        } else {\r\n          throw new Error(res.msg || '取消失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('取消预约失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '取消失败',\r\n          icon: 'error'\r\n        })\r\n      } finally {\r\n        this.isCancelling = false\r\n      }\r\n    },\r\n    \r\n    // 格式化日期\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return ''\r\n      \r\n      try {\r\n        const date = new Date(dateStr.replace(/-/g, '/'))\r\n        const weekList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n        const weekDay = weekList[date.getDay()]\r\n        return `${dateStr} ${weekDay}`\r\n      } catch (error) {\r\n        return dateStr\r\n      }\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime(timeStr) {\r\n      if (!timeStr) return ''\r\n      \r\n      try {\r\n        const date = new Date(timeStr.replace(/-/g, '/'))\r\n        const hours = date.getHours().toString().padStart(2, '0')\r\n        const minutes = date.getMinutes().toString().padStart(2, '0')\r\n        return `${hours}:${minutes}`\r\n      } catch (error) {\r\n        return timeStr\r\n      }\r\n    },\r\n    \r\n    // 隐藏身份证号中间部分\r\n    hideIdCard(idCard) {\r\n      if (!idCard || idCard.length < 8) return idCard\r\n      \r\n      return idCard.replace(idCard.substring(4, 15), '*******')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.venue-cancel {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\r\n  .content {\r\n    padding: 20rpx;\r\n    padding-top: 120rpx; // 为头部留出空间\r\n\r\n    // 参观信息\r\n    .venue-info {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .info-header {\r\n        margin-bottom: 20rpx;\r\n\r\n        .venue-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .venue-details {\r\n        .detail-row {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 12rpx;\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .label {\r\n            font-size: 28rpx;\r\n            color: #666;\r\n            min-width: 140rpx;\r\n          }\r\n\r\n          .value {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            flex: 1;\r\n\r\n            &.important {\r\n              color: #1976d2;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 联系人选择\r\n    .contacts-section {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .section-title {\r\n        font-size: 32rpx;\r\n        font-weight: 600;\r\n        color: #333;\r\n        margin-bottom: 30rpx;\r\n      }\r\n\r\n      .contacts-list {\r\n        .contact-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 20rpx 0;\r\n          border-bottom: 1rpx solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .contact-info {\r\n            flex: 1;\r\n\r\n            .contact-name {\r\n              display: block;\r\n              font-size: 30rpx;\r\n              font-weight: 500;\r\n              color: #333;\r\n              margin-bottom: 8rpx;\r\n            }\r\n\r\n            .contact-phone {\r\n              display: block;\r\n              font-size: 26rpx;\r\n              color: #666;\r\n              margin-bottom: 6rpx;\r\n            }\r\n\r\n            .contact-id {\r\n              display: block;\r\n              font-size: 24rpx;\r\n              color: #999;\r\n            }\r\n          }\r\n\r\n          .checkbox {\r\n            width: 48rpx;\r\n            height: 48rpx;\r\n            border: 2rpx solid #ddd;\r\n            border-radius: 50%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.3s ease;\r\n\r\n            &.checked {\r\n              background: linear-gradient(135deg, #667eea, #764ba2);\r\n              border-color: #667eea;\r\n\r\n              .check-icon {\r\n                color: white;\r\n                font-size: 28rpx;\r\n                font-weight: bold;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 操作区域\r\n    .action-section {\r\n      .cancel-btn {\r\n        width: 100%;\r\n        height: 88rpx;\r\n        background: linear-gradient(135deg, #ff6b6b, #ee5a52);\r\n        color: white;\r\n        border-radius: 44rpx;\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n        border: none;\r\n\r\n        &::after {\r\n          border: none;\r\n        }\r\n\r\n        &:disabled {\r\n          background: #ccc;\r\n          color: #999;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.venue-cancel {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>\r\n", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/schemesuccess/venuecancel.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAuEA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,WAAW,CAAE;AAAA,MACb,cAAc,CAAE;AAAA;AAAA,MAGhB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,MAAM;AAAA;AAAA,MAGN,cAAc;AAAA,IAChB;AAAA,EACD;AAAA,EAED,UAAU;AAAA;AAAA,IAER,sBAAsB;AACpB,aAAO,KAAK,aAAa,KAAK,aAAW,QAAQ,SAAS;AAAA,IAC5D;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACd,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,cAAc,QAAQ;AAC3B,SAAK,OAAO,QAAQ;AAEpB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AAAA,EACtB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,eAAe;AACnB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,gBAAgB,KAAK;AAAA,UACvB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,OAAO,IAAI,KAAK;AACtB,eAAK,YAAY;AAAA,YACf,GAAG;AAAA,YACH,MAAM,KAAK,WAAW,KAAK,iBAAiB;AAAA,YAC5C,gBAAgB,KAAK,WAAW,KAAK,cAAc;AAAA,YACnD,cAAc,KAAK,WAAW,KAAK,YAAY;AAAA,UACjD;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,aAAa,KAAK;AAAA,UACpB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,gBAAgB,IAAI,KAAK,QAAQ,CAAE,GAAE,IAAI,cAAY;AAAA,YACxD,GAAG;AAAA,YACH,WAAW;AAAA,UACb,EAAE;AAAA,QACJ;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kDAAA,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,OAAO;AACnB,WAAK,aAAa,KAAK,EAAE,YAAY,CAAC,KAAK,aAAa,KAAK,EAAE;AAAA,IAChE;AAAA;AAAA,IAGD,gBAAgB;AACd,UAAI,CAAC,KAAK,qBAAqB;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AACf,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI,KAAK;AAAc;AAEvB,WAAK,eAAe;AAEpB,UAAI;AACF,cAAM,cAAc,KAAK,aACtB,OAAO,aAAW,QAAQ,SAAS,EACnC,IAAI,aAAW,QAAQ,MAAM;AAEhC,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,MAAM,KAAK;AAAA,YACX,gBAAgB,KAAK;AAAA,YACrB,aAAa,KAAK;AAAA,YAClB,WAAW,YAAY,KAAK,GAAG;AAAA,UACjC;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,WACX;AAED,qBAAW,MAAM;AACfA,0BAAAA,MAAI,WAAW;AAAA,cACb,KAAK;AAAA,aACN;AAAA,UACF,GAAE,GAAI;AAAA,eACF;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,QACnC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,kDAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,cAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,cAAM,UAAU,SAAS,KAAK,OAAM,CAAE;AACtC,eAAO,GAAG,OAAO,IAAI,OAAO;AAAA,MAC5B,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC1B,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,QAAQ;AACjB,UAAI,CAAC,UAAU,OAAO,SAAS;AAAG,eAAO;AAEzC,aAAO,OAAO,QAAQ,OAAO,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,IAC1D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5QA,GAAG,WAAW,eAAe;"}