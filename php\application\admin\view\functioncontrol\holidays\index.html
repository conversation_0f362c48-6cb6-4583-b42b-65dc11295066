<div class="panel panel-default panel-intro">
  {:build_heading()}
  <!-- 本地 FullCalendar CSS -->
  <link rel="stylesheet" href="/assets/libs/fullcalendar/main.min.css">

  <!-- 本地 FullCalendar JS -->
  <script src="/assets/libs/fullcalendar/main.min.js"></script>
  <script src="/assets/libs/fullcalendar/locales/zh-cn.min.js"></script>
  <style>
    /* 去掉事件默认背景色 */
    .fc-daygrid-event {
      background-color: transparent !important;
      padding: 0 !important;
      border: none !important;
    }

    /* 星期栏：周一、周二、周三…… */
    .fc .fc-col-header-cell-cushion {
      color: #000 !important;
    }

    /* 日期数字：1日、2日、3日…… */
    .fc .fc-daygrid-day-number {
      color: #000 !important;
      /* 日期数字居中 */
      display: block;
      text-align: center;
      width: 100%;
    }

    /* 开馆日 / 闭馆日 标签居中 */
    .fc .fc-event {
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
    }

    .fc-daygrid-event {
      pointer-events: none !important;
      /* 让事件不捕获点击 */
    }

    .calendar-open {
      color: #1b8c00;
      /*    background-color: #e8f5e9;*/
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
    }

    .calendar-close {
      color: #c62828;
      /*    background-color: #ffebee;*/
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      font-weight: bold;
      font-size: 14px;
    }

    /* 1. 去掉今日的背景颜色 */
    .fc-day-today {
      background-color: transparent !important;
    }

    /* 2. 把“今日”的日期文字改为蓝色 */
    .fc-day-today .fc-daygrid-day-number {
      color: #1890ff !important;
      font-weight: bold;
    }

    /*当前选中日期的样式*/
    .fc-selected {
      border: 2px solid #007bff !important;
      background-color: rgba(0, 123, 255, 0.1);
      border-radius: 4px;
    }

    /*带打钩图标）*/
    .fc-daygrid-day.fc-selected::after {
      content: "✓";
      color: #007bff;
      font-weight: bold;
      font-size: 16px;
      position: absolute;
      top: 2px;
      right: 4px;
      pointer-events: none;
    }

    .fc-daygrid-day.fc-selected {
      position: relative;
    }

    /*    让“2025年11月”看起来更小一些、更加紧凑。*/
    .fc-toolbar-title {
      font-size: 18px !important;
      /* 原本可能是 24px 或更大 */
      font-weight: 600;
      /* 可选：更紧凑一点 */
      line-height: 1.2;
    }
  </style>


  <div class="panel-body">
    <div id="myTabContent" class="tab-content">
      <div class="tab-pane fade active in" id="one">
        <div class="widget-body no-padding">

          <div class="row">
            <div class="col-md-6">

              <div id="holiday-calendar"></div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
<!-- 弹窗 HTML 模板 -->
<div class="modal fade" id="closeModal" tabindex="-1">
  <div class="modal-dialog" style="width: 500px;" role="document"> <!-- ✅ 控制宽度 -->
    <div class="modal-content">
      <div class="modal-header"><strong>闭馆</strong></div>
      <div class="modal-body">
        <p>以下内容填写时，所有预约当天的用户将在微信收到闭馆通知！</p>
        <div class="form-group">
          <label>通知标题</label>
          <input id="notice-title" maxlength="20" class="form-control" placeholder="关于XXXX年XX月XX日 闭馆通知公告！">
        </div>
        <div class="form-group">
          <label>通知内容</label>
          <input id="notice-content" maxlength="20" class="form-control" placeholder="因XX原因闭馆，敬请悉知！还望谅解！">
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-default" data-dismiss="modal">取消</button>
        <button class="btn btn-primary" id="confirm-close">确定</button>
      </div>
    </div>
  </div>
</div>