<template>
  <view class="curriculum-success">
    <!-- 自定义头部 -->
    <my-header 
      title="课程凭证" 
      :isBack="true" 
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
      :isFixed="true"
    />
    
    <view class="content">
      <!-- 成功状态 -->
      <view class="success-state">
        <view class="success-header">
          <image 
            src="/static/img/schemesuccess/schemestate_course.png" 
            mode="aspectFit"
            class="success-icon"
          />
          <text class="success-text">预约成功</text>
        </view>
      </view>
      
      <!-- 课程信息 -->
      <view class="course-info">
        <view class="info-header">
          <text class="venue-name">宝安科技馆 e宝课堂</text>
        </view>
        
        <view class="course-details">
          <view class="course-poster">
            <image 
              :src="courseInfo.courseCover" 
              mode="aspectFill"
              class="poster-image"
            />
          </view>
          <view class="course-meta">
            <text class="course-name">{{ courseInfo.courseName }}</text>
            <text class="course-teacher" v-if="courseInfo.courseTeacher">
              授课老师：{{ courseInfo.courseTeacher }}
            </text>
            <text class="course-location" v-if="courseInfo.courseLocation">
              上课地点：{{ courseInfo.courseLocation }}
            </text>
            <text class="course-time">
              上课时间：{{ courseInfo.courseStartTime }} - {{ courseInfo.courseEndTime }}
            </text>
            <text class="course-date">上课日期：{{ courseInfo.date }}</text>
          </view>
        </view>
        
        <!-- 联系人列表 -->
        <view v-if="contactsList.length > 0" class="contacts-list">
          <view class="contacts-title">预约人员</view>
          <view 
            v-for="(contact, index) in contactsList" 
            :key="contact.linkId"
            class="contact-item"
          >
            <text class="contact-name">{{ contact.linkmanName }}</text>
            <text class="contact-id">{{ hideIdCard(contact.linkmanCertificate) }}</text>
          </view>
        </view>
        
        <!-- 提示信息 -->
        <view class="tips-section">
          <view class="tips-icon"></view>
          <text class="tips-text">
            在首页<text class="highlight">个人中心—课程预约—查看凭证</text>中查看此凭证
          </text>
        </view>
      </view>
      
      <!-- 签到按钮 -->
      <view class="signin-section">
        <button 
          :class="['signin-btn', { signed: isSigned }]"
          @tap="signUp"
          :disabled="isSigningUp || isSigned"
        >
          {{ isSigned ? '已签到' : (isSigningUp ? '签到中...' : '课程签到') }}
        </button>
      </view>
    </view>
    
    <!-- 签到结果弹窗 -->
    <view v-if="showSignModal" class="sign-modal">
      <view :class="['modal-dialog', { fail: signResult === 'fail' }]">
        <view class="modal-icon"></view>
        <view class="modal-text">
          <template v-if="signResult === 'success'">
            恭喜您，签到成功！
          </template>
          <template v-else>
            您的定位较远<br />
            请移步至宝安科技馆进行<text class="highlight">现场签到</text>
          </template>
        </view>
        <view class="modal-btn" @tap="closeSignModal">
          {{ signResult === 'success' ? '确定' : '返回' }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CurriculumSuccess',
  data() {
    return {
      courseInfo: {},
      contactsList: [],
      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      
      // 参数
      courseId: null,
      
      // 状态
      isSigned: false,
      isSigningUp: false,
      showSignModal: false,
      signResult: 'success' // success | fail
    }
  },
  
  onLoad(options) {
    this.courseId = options.id
    this.getCourseInfo()
  },
  
  methods: {
    // 获取课程信息
    async getCourseInfo() {
      try {
        const res = await this.$myRequest({
          url: '/web/session/showVoucher',
          method: 'get',
          data: {
            courseId: this.courseId
          }
        })
        
        if (res.code === 200) {
          const data = res.data.data
          const startTime = data.courseStartTime
          const date = startTime.slice(0, 10).replace(/-/g, '.') + '  ' + 
                      this.weekList[new Date(startTime).getDay()]
          
          this.courseInfo = {
            ...data,
            date,
            courseStartTime: this.formatTime(data.courseStartTime),
            courseEndTime: this.formatTime(data.courseEndTime)
          }
          
          // 检查签到状态
          this.isSigned = data.signState === '1'
          
          // 获取联系人列表
          await this.getContactsList()
        }
      } catch (error) {
        console.error('获取课程信息失败:', error)
        uni.showToast({
          title: '获取信息失败',
          icon: 'error'
        })
      }
    },
    
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: '/web/session/getCourseSubscribePeoples',
          method: 'get',
          data: {
            courseId: this.courseId
          }
        })
        
        if (res.code === 200) {
          this.contactsList = res.data.data || []
        }
      } catch (error) {
        console.warn('获取联系人失败:', error)
      }
    },
    
    // 课程签到
    async signUp() {
      if (this.isSigningUp || this.isSigned) return
      
      this.isSigningUp = true
      
      try {
        uni.showLoading({
          title: '获取位置信息中',
          mask: true
        })
        
        // 获取位置信息
        const location = await this.getCurrentLocation()
        
        uni.hideLoading()
        
        // 验证位置
        const locationRes = await this.$myRequest({
          url: '/web/common/checkLocation',
          method: 'post',
          data: {
            latitude: location.latitude,
            longitude: location.longitude
          }
        })
        
        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {
          this.signResult = 'fail'
          this.showSignModal = true
          return
        }
        
        // 执行签到
        const signRes = await this.$myRequest({
          url: '/web/session/courseSign',
          method: 'post',
          data: {
            courseId: this.courseId
          }
        })
        
        if (signRes.code === 200) {
          this.isSigned = true
          this.signResult = 'success'
          this.showSignModal = true
        } else {
          throw new Error(signRes.msg || '签到失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('签到失败:', error)
        
        if (error.message && error.message.includes('定位')) {
          this.signResult = 'fail'
          this.showSignModal = true
        } else {
          uni.showToast({
            title: error.message || '签到失败',
            icon: 'error'
          })
        }
      } finally {
        this.isSigningUp = false
      }
    },
    
    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: resolve,
          fail: reject
        })
      })
    },
    
    // 关闭签到弹窗
    closeSignModal() {
      this.showSignModal = false
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      
      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },
    
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      
      return idCard.replace(idCard.substring(4, 15), '*******')
    }
  }
}
</script>

<style lang="scss" scoped>
.curriculum-success {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding: 20rpx;
    padding-top: 120rpx; // 为固定头部留出空间

    // 成功状态
    .success-state {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 40rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .success-header {
        display: flex;
        flex-direction: column;
        align-items: center;

        .success-icon {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 20rpx;
        }

        .success-text {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
        }
      }
    }

    // 课程信息
    .course-info {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .info-header {
        margin-bottom: 20rpx;

        .venue-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .course-details {
        display: flex;
        margin-bottom: 30rpx;

        .course-poster {
          width: 120rpx;
          height: 160rpx;
          margin-right: 20rpx;
          border-radius: 12rpx;
          overflow: hidden;

          .poster-image {
            width: 100%;
            height: 100%;
          }
        }

        .course-meta {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .course-name {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
            line-height: 1.4;
          }

          .course-teacher,
          .course-location,
          .course-time,
          .course-date {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 6rpx;
            line-height: 1.3;
          }
        }
      }

      .contacts-list {
        margin-bottom: 30rpx;

        .contacts-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
        }

        .contact-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .contact-name {
            font-size: 26rpx;
            color: #333;
            font-weight: 500;
          }

          .contact-id {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .tips-section {
        display: flex;
        align-items: flex-start;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;

        .tips-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
          margin-top: 4rpx;
          background: url('/static/img/common/warning.png') center/contain no-repeat;
        }

        .tips-text {
          flex: 1;
          font-size: 26rpx;
          color: #666;
          line-height: 1.5;

          .highlight {
            color: #1976d2;
            font-weight: 500;
          }
        }
      }
    }

    // 签到区域
    .signin-section {
      .signin-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #9c27b0, #7b1fa2);
        color: white;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;

        &::after {
          border: none;
        }

        &.signed {
          background: #e8f5e8;
          color: #388e3c;
        }

        &:disabled {
          opacity: 0.6;
        }
      }
    }
  }

  // 签到结果弹窗
  .sign-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-dialog {
      background: white;
      border-radius: 20rpx;
      padding: 60rpx 40rpx;
      margin: 40rpx;
      text-align: center;

      &.fail {
        .modal-icon {
          background: url('/static/img/common/location-error.png') center/contain no-repeat;
        }
      }

      .modal-icon {
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto 30rpx;
        background: url('/static/img/common/success.png') center/contain no-repeat;
      }

      .modal-text {
        font-size: 32rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 40rpx;

        .highlight {
          color: #1976d2;
          font-weight: 500;
        }
      }

      .modal-btn {
        padding: 20rpx 60rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 40rpx;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.curriculum-success {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
