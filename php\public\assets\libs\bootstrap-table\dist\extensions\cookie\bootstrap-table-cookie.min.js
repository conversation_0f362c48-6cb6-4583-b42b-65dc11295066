/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b={sortOrder:"bs.table.sortOrder",sortName:"bs.table.sortName",pageNumber:"bs.table.pageNumber",pageList:"bs.table.pageList",columns:"bs.table.columns",searchText:"bs.table.searchText",filterControl:"bs.table.filterControl"},c=function(a){var b=a.$header;return a.options.height&&(b=a.$tableHeader),b},d=function(a){var b="select, input";return a.options.height&&(b="table select, table input"),b},e=function(){return!!navigator.cookieEnabled},f=function(a,b){for(var c=-1,d=0;d<b.length;d++)if(a.toLowerCase()===b[d].toLowerCase()){c=d;break}return c},g=function(a,b,c){if(a.options.cookie&&e()&&""!==a.options.cookieIdTable&&-1!==f(b,a.options.cookiesEnabled)){switch(b=a.options.cookieIdTable+"."+b,a.options.cookieStorage){case"cookieStorage":document.cookie=[b,"=",c,"; expires="+a.options.cookieExpire,a.options.cookiePath?"; path="+a.options.cookiePath:"",a.options.cookieDomain?"; domain="+a.options.cookieDomain:"",a.options.cookieSecure?"; secure":""].join("");break;case"localStorage":localStorage.setItem(b,c);break;case"sessionStorage":sessionStorage.setItem(b,c);break;default:return!1}return!0}},h=function(a,b,c){if(!c)return null;if(-1===f(c,a.options.cookiesEnabled))return null;switch(c=b+"."+c,a.options.cookieStorage){case"cookieStorage":return decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(c).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null;case"localStorage":return localStorage.getItem(c);case"sessionStorage":return sessionStorage.getItem(c);default:return null}},i=function(a,b,c){switch(c=b+"."+c,a.options.cookieStorage){case"cookieStorage":document.cookie=[encodeURIComponent(c),"=","; expires=Thu, 01 Jan 1970 00:00:00 GMT",a.options.cookiePath?"; path="+a.options.cookiePath:"",a.options.cookieDomain?"; domain="+a.options.cookieDomain:""].join("");break;case"localStorage":localStorage.removeItem(c);break;case"sessionStorage":sessionStorage.removeItem(c)}return!0},j=function(e){setTimeout(function(){var f=JSON.parse(h(e,e.options.cookieIdTable,b.filterControl));if(!e.options.filterControlValuesLoaded&&f){e.options.filterControlValuesLoaded=!0;var g={},i=c(e),j=d(e),k=function(b,c){a(c).each(function(c,d){a(b).val(d.text),g[d.field]=d.text})};i.find(j).each(function(){var b=a(this).closest("[data-field]").data("field"),c=a.grep(f,function(a){return a.field===b});k(this,c)}),e.initColumnSearch(g)}},250)};a.extend(a.fn.bootstrapTable.defaults,{cookie:!1,cookieExpire:"2h",cookiePath:null,cookieDomain:null,cookieSecure:null,cookieIdTable:"",cookiesEnabled:["bs.table.sortOrder","bs.table.sortName","bs.table.pageNumber","bs.table.pageList","bs.table.columns","bs.table.searchText","bs.table.filterControl"],cookieStorage:"cookieStorage",filterControls:[],filterControlValuesLoaded:!1}),a.fn.bootstrapTable.methods.push("getCookies"),a.fn.bootstrapTable.methods.push("deleteCookie"),a.extend(a.fn.bootstrapTable.utils,{setCookie:g,getCookie:h});var k=a.fn.bootstrapTable.Constructor,l=k.prototype.init,m=k.prototype.initTable,n=k.prototype.initServer,o=k.prototype.onSort,p=k.prototype.onPageNumber,q=k.prototype.onPageListChange,r=k.prototype.onPageFirst,s=k.prototype.onPagePre,t=k.prototype.onPageNext,u=k.prototype.onPageLast,v=k.prototype.toggleColumn,w=k.prototype.selectPage,x=k.prototype.onSearch;k.prototype.init=function(){if(this.options.filterControls=[],this.options.filterControlValuesLoaded=!1,this.options.cookiesEnabled="string"==typeof this.options.cookiesEnabled?this.options.cookiesEnabled.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.cookiesEnabled,this.options.filterControl){var a=this;this.$el.on("column-search.bs.table",function(c,d,e){for(var f=!0,h=0;h<a.options.filterControls.length;h++)if(a.options.filterControls[h].field===d){a.options.filterControls[h].text=e,f=!1;break}f&&a.options.filterControls.push({field:d,text:e}),g(a,b.filterControl,JSON.stringify(a.options.filterControls))}).on("post-body.bs.table",j(a))}l.apply(this,Array.prototype.slice.apply(arguments))},k.prototype.initServer=function(){var c=this,d=[],e=function(a){return a.filterControl&&"select"===a.filterControl},f=function(a){return a.filterData&&"column"!==a.filterData},g=function(){var a=JSON.parse(h(c,c.options.cookieIdTable,b.filterControl));return c.options.cookie&&a};d=a.grep(c.columns,function(a){return e(a)&&!f(a)}),k.prototype.initServer=n,this.options.filterControl&&g()&&0===d.length||n.apply(this,Array.prototype.slice.apply(arguments))},k.prototype.initTable=function(){m.apply(this,Array.prototype.slice.apply(arguments)),this.initCookie()},k.prototype.initCookie=function(){if(this.options.cookie){if(""===this.options.cookieIdTable||""===this.options.cookieExpire||!e())throw new Error("Configuration error. Please review the cookieIdTable, cookieExpire properties, if those properties are ok, then this browser does not support the cookies");var c=h(this,this.options.cookieIdTable,b.sortOrder),d=h(this,this.options.cookieIdTable,b.sortName),f=h(this,this.options.cookieIdTable,b.pageNumber),g=h(this,this.options.cookieIdTable,b.pageList),i=JSON.parse(h(this,this.options.cookieIdTable,b.columns)),j=h(this,this.options.cookieIdTable,b.searchText);this.options.sortOrder=c?c:this.options.sortOrder,this.options.sortName=d?d:this.options.sortName,this.options.pageNumber=f?+f:this.options.pageNumber,this.options.pageSize=g?g===this.options.formatAllRows()?g:+g:this.options.pageSize,this.options.searchText=j?j:"",i&&a.each(this.columns,function(b,c){c.visible=-1!==a.inArray(c.field,i)})}},k.prototype.onSort=function(){o.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.sortOrder,this.options.sortOrder),g(this,b.sortName,this.options.sortName)},k.prototype.onPageNumber=function(){p.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},k.prototype.onPageListChange=function(){q.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageList,this.options.pageSize)},k.prototype.onPageFirst=function(){r.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},k.prototype.onPagePre=function(){s.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},k.prototype.onPageNext=function(){t.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},k.prototype.onPageLast=function(){u.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,this.options.pageNumber)},k.prototype.toggleColumn=function(){v.apply(this,Array.prototype.slice.apply(arguments));var c=[];a.each(this.columns,function(a,b){b.visible&&c.push(b.field)}),g(this,b.columns,JSON.stringify(c))},k.prototype.selectPage=function(a){w.apply(this,Array.prototype.slice.apply(arguments)),g(this,b.pageNumber,a)},k.prototype.onSearch=function(){var c=Array.prototype.slice.apply(arguments);x.apply(this,c),a(c[0].currentTarget).parent().hasClass("search")&&g(this,b.searchText,this.searchText)},k.prototype.getCookies=function(){var c=this,d={};return a.each(b,function(a,b){d[a]=h(c,c.options.cookieIdTable,b),"columns"===a&&(d[a]=JSON.parse(d[a]))}),d},k.prototype.deleteCookie=function(a){""!==a&&e()&&i(this,this.options.cookieIdTable,b[a])}}(jQuery);