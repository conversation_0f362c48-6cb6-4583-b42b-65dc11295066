<div class="panel panel-default">
    <div class="panel-heading">基本信息</div>
    <div class="panel-body">
        <div class="row">
            {foreach name="base" item="val" key="key"}
            <div class="col-md-3 col-sm-6 col-xs-12">
                <div class="box box-solid" style="border:1px solid #eee;padding:10px;margin-bottom:10px;">
                    <div style="font-size:13px;color:#666;">{$key}</div>
                    <div style="font-weight:bold;font-size:15px;">{$val}</div>
                </div>
            </div>
            {/foreach}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">Key 类型统计</div>
            <div class="panel-body">
                <canvas id="chartType" style="height: 300px;"></canvas>

            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">内存信息</div>
            <div class="panel-body">
                <div id="usedmemory" style="height: 300px;"></div>

            </div>
        </div>
    </div>
</div>




<!-- 引入 Chart.js 和 ECharts -->
<script src="/assets/js/chart.js"></script>
<!-- ECharts JS 引入 -->
<script src="/assets/js/echarts.min.js"></script>
<script src="/assets/js/echarts-theme.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/echarts@4.9.0/theme/macarons.js"></script> -->



<script>
    // Key 类型饼图
    const statData = {$stats_json};
      const ctx = document.getElementById('chartType').getContext('2d');
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: Object.keys(statData),
            datasets: [{
                data: Object.values(statData),
                borderWidth: 1
            }]
        },
        options: {
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });




    // 后端变量嵌入
    const usedMemory = {$used_memory ?? 0 };
    const maxMemory = {$max_memory ?? 100 * 1024 * 1024 }; // 默认100MB
    const usedMemoryHuman = "{$used_memory_human ?? '-' }";
    const maxMemoryHuman = "{$max_memory_human ?? '100MB' }";

    // 初始化图表
    const memChart = echarts.init(document.getElementById('usedmemory'), 'macarons');

    // 设置图表选项
    memChart.setOption({
        tooltip: {
            formatter: `当前内存: ${usedMemoryHuman}<br/>上限: ${maxMemoryHuman}`
        },
        series: [
            {
                name: '内存使用',
                type: 'gauge',
                min: 0,
                max: maxMemory / (1024 * 1024), // 转为MB
                detail: {
                    formatter: usedMemoryHuman,
                },
                data: [
                    {
                        value: (usedMemory / (1024 * 1024)).toFixed(2), // 转MB，保留两位小数
                        name: '内存消耗',
                    }
                ]
            }
        ]
    });

</script>
