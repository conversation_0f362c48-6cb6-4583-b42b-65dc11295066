/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.film-success.data-v-8ad8d244 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.film-success .content.data-v-8ad8d244 {
  padding: 20rpx;
  padding-top: 120rpx;
}
.film-success .content .success-state.data-v-8ad8d244 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.film-success .content .success-state .success-header.data-v-8ad8d244 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}
.film-success .content .success-state .success-header .success-icon.data-v-8ad8d244 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.film-success .content .success-state .success-header .success-text.data-v-8ad8d244 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.film-success .content .success-state .tips-section .tips-title.data-v-8ad8d244 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.film-success .content .success-state .tips-section .tips-item.data-v-8ad8d244 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.film-success .content .success-state .tips-section .tips-item.data-v-8ad8d244:last-child {
  margin-bottom: 0;
}
.film-success .content .film-info.data-v-8ad8d244 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.film-success .content .film-info .info-header.data-v-8ad8d244 {
  margin-bottom: 20rpx;
}
.film-success .content .film-info .info-header .venue-name.data-v-8ad8d244 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.film-success .content .film-info .film-details.data-v-8ad8d244 {
  display: flex;
}
.film-success .content .film-info .film-details .film-poster.data-v-8ad8d244 {
  width: 120rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.film-success .content .film-info .film-details .film-poster .poster-image.data-v-8ad8d244 {
  width: 100%;
  height: 100%;
}
.film-success .content .film-info .film-details .film-meta.data-v-8ad8d244 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.film-success .content .film-info .film-details .film-meta .film-name.data-v-8ad8d244 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.film-success .content .film-info .film-details .film-meta .film-type.data-v-8ad8d244,
.film-success .content .film-info .film-details .film-meta .film-time.data-v-8ad8d244,
.film-success .content .film-info .film-details .film-meta .film-date.data-v-8ad8d244,
.film-success .content .film-info .film-details .film-meta .film-count.data-v-8ad8d244 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
  line-height: 1.3;
}
.film-success .content .qrcode-section.data-v-8ad8d244 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.film-success .content .qrcode-section .qrcode-tabs.data-v-8ad8d244 {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
  gap: 20rpx;
}
.film-success .content .qrcode-section .qrcode-tabs .tab-btn.data-v-8ad8d244 {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  background: #f5f5f5;
  color: #666;
}
.film-success .content .qrcode-section .qrcode-tabs .tab-btn.active.data-v-8ad8d244 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}
.film-success .content .qrcode-section .qrcode-container.data-v-8ad8d244 {
  display: flex;
  justify-content: center;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper.data-v-8ad8d244 {
  position: relative;
  width: 400rpx;
  height: 400rpx;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-image.data-v-8ad8d244,
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-placeholder.data-v-8ad8d244 {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask.data-v-8ad8d244 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content.data-v-8ad8d244 {
  text-align: center;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .cancel-btn.data-v-8ad8d244,
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .waiting-btn.data-v-8ad8d244,
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .signin-btn.data-v-8ad8d244 {
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .cancel-btn.data-v-8ad8d244::after,
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .waiting-btn.data-v-8ad8d244::after,
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .signin-btn.data-v-8ad8d244::after {
  border: none;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .cancel-btn.data-v-8ad8d244 {
  background: #ff6b6b;
  color: white;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .waiting-btn.data-v-8ad8d244 {
  background: #ffa726;
  color: white;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .waiting-btn view.data-v-8ad8d244 {
  line-height: 1.3;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .signin-btn.data-v-8ad8d244 {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}
.film-success .content .qrcode-section .qrcode-container .qrcode-wrapper .qrcode-mask .mask-content .signin-btn.data-v-8ad8d244:disabled {
  opacity: 0.6;
}
.film-success .content .qrcode-section .countdown.data-v-8ad8d244 {
  text-align: center;
  margin-top: 20rpx;
}
.film-success .content .qrcode-section .countdown .countdown-text.data-v-8ad8d244 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.film-success .content .bottom-tips.data-v-8ad8d244 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
}
.film-success .content .bottom-tips .highlight.data-v-8ad8d244 {
  color: #1976d2;
  font-weight: 500;
}
.film-success .location-mask.data-v-8ad8d244 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.film-success .location-mask .mask-dialog.data-v-8ad8d244 {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin: 40rpx;
  text-align: center;
}
.film-success .location-mask .mask-dialog .location-icon.data-v-8ad8d244 {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 30rpx;
  background: url("data:image/png;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTIwIDEyMCI+CiAgPGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iNTAiIGZpbGw9IiNGNDQzMzYiIC8+CiAgPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTYwLDMwIEM0OC45NTQsMzAgNDAsMzguOTU0IDQwLDUwIEM0MCw2NSA2MCw5MCA2MCw5MCBDNjAsOTAgODAsNjUgODAsNTAgQzgwLDM4Ljk1NCA3MS4wNDYsMzAgNjAsMzAgWiBNNjAsNTggQzU1LjU4Miw1OCA1Miw1NC40MTggNTIsNTAgQzUyLDQ1LjU4MiA1NS41ODIsNDIgNjAsNDIgQzY0LjQxOCw0MiA2OCw0NS41ODIgNjgsNTAgQzY4LDU0LjQxOCA2NC40MTgsNTggNjAsNTggWiIgLz4KICA8cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNODQuMjQyLDg0LjI0MiBDODEuODk1LDg2LjU4OSA3OS4wMzksODguMzkzIDc1Ljg1OCw4OS41IEw4OS41LDEwMy4xNDIgQzkwLjYwNyw5OS45NjEgOTIuNDExLDk3LjEwNSA5NC43NTgsOTQuNzU4IEw4NC4yNDIsODQuMjQyIFoiIC8+CiAgPHBhdGggZmlsbD0iI0ZGRkZGRiIgZD0iTTM1Ljc1OCwzNS43NTggQzM4LjEwNSwzMy40MTEgNDAuOTYxLDMxLjYwNyA0NC4xNDIsMzAuNSBMMzAuNSwxNi44NTggQzI5LjM5MywyMC4wMzkgMjcuNTg5LDIyLjg5NSAyNS4yNDIsMjUuMjQyIEwzNS43NTgsMzUuNzU4IFoiIC8+Cjwvc3ZnPg==") center/contain no-repeat;
}
.film-success .location-mask .mask-dialog .mask-text.data-v-8ad8d244 {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 40rpx;
}
.film-success .location-mask .mask-dialog .mask-text .highlight.data-v-8ad8d244 {
  color: #1976d2;
  font-weight: 500;
}
.film-success .location-mask .mask-dialog .mask-btn.data-v-8ad8d244 {
  padding: 20rpx 60rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* 平台特定样式 */
.content.data-v-8ad8d244 {
  padding-bottom: env(safe-area-inset-bottom);
}