<template>
  <view class="film-success">
    <!-- 自定义头部 -->
    <my-header
      title="观影凭证"
      :isBack="true"
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
      :isFixed="true"
    />

    <view class="content">
      <!-- 成功状态 -->
      <view class="success-state">
        <view class="success-header">
          <image
            src="/static/img/schemesuccess/schemestate_course_film.png"
            mode="aspectFit"
            class="success-icon"
          />
          <text class="success-text">预约成功</text>
        </view>

        <!-- 注意事项 -->
        <view class="tips-section">
          <view class="tips-title">注意:</view>
          <view
            v-for="(tip, index) in tipsList"
            :key="index"
            class="tips-item"
          >
            {{ tip }}
          </view>
        </view>
      </view>

      <!-- 电影信息 -->
      <view class="film-info">
        <view class="info-header">
          <text class="venue-name">宝安科技馆 观影预约</text>
        </view>

        <view class="film-details">
          <view class="film-poster">
            <image
              :src="filmInfo.filmCover"
              mode="aspectFill"
              class="poster-image"
            />
          </view>
          <view class="film-meta">
            <text class="film-name">{{ filmInfo.filmName }}</text>
            <text class="film-type">类型：{{ filmTypeList[filmInfo.filmType - 1] || '未知' }}</text>
            <text class="film-time">时间：{{ filmInfo.filmStartTime }} - {{ filmInfo.filmEndTime }}</text>
            <text class="film-date">日期：{{ filmInfo.filmArrangedDate }} {{ getWeekDay(filmInfo.filmArrangedDate) }}</text>
            <text class="film-count">预约人数：{{ filmInfo.orderNum }}人</text>
          </view>
        </view>
      </view>

      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <!-- 二维码切换按钮 -->
        <view v-if="qrcodeList.length > 1" class="qrcode-tabs">
          <view
            v-for="(btn, index) in qrcodeBtns.slice(0, qrcodeList.length)"
            :key="index"
            :class="['tab-btn', { active: qrcodeIndex === index }]"
            @tap="changeQrcodeIndex(index)"
          >
            {{ btn }}
          </view>
        </view>

        <!-- 二维码显示 -->
        <view class="qrcode-container">
          <view class="qrcode-wrapper">
            <image
              v-if="qrcodeList.length > 0 && showQrcode"
              :src="qrcodeList[qrcodeIndex].qrcodeUrl"
              class="qrcode-image"
              mode="aspectFit"
              @tap="refreshQrcode"
            />
            <image
              v-else
              src="/static/img/schemesuccess/qrcode.png"
              class="qrcode-placeholder"
              mode="aspectFit"
            />

            <!-- 二维码遮罩 -->
            <view
              v-if="!showQrcode || filmInfo.subscribeState === 1 || !isPlayFilm"
              class="qrcode-mask"
            >
              <view class="mask-content">
                <button
                  v-if="!isPlayFilm && (filmInfo.subscribeState === 4 || filmInfo.subscribeState === 1)"
                  class="cancel-btn"
                  disabled
                >
                  电影因人数不足取消放映
                </button>
                <button
                  v-else-if="!showQrcode && filmInfo.subscribeState === 4"
                  class="waiting-btn"
                  disabled
                >
                  <view>影片未开始</view>
                  <view>提前十分钟入场</view>
                </button>
                <button
                  v-else
                  class="signin-btn"
                  @tap="signUp"
                  :disabled="isSigningUp"
                >
                  {{ isSigningUp ? '签到中...' : '签到获取二维码' }}
                </button>
              </view>
            </view>
          </view>
        </view>

        <!-- 倒计时 -->
        <view v-if="isShowCountDown" class="countdown">
          <text class="countdown-text">距离开场还有：{{ countDown }}</text>
        </view>
      </view>

      <!-- 底部提示 -->
      <view class="bottom-tips">
        在首页<text class="highlight">个人中心—观影登记—查看凭证</text>中查看此凭证，提前10分钟签到获取二维码，扫码进场
      </view>
    </view>

    <!-- 定位弹窗 -->
    <view v-if="isShowLocationMask" class="location-mask">
      <view class="mask-dialog">
        <view class="location-icon"></view>
        <view class="mask-text">
          <text>您的定位较远</text>
          <br />
          <text>请移步至宝安科技馆进行<text class="highlight">现场签到</text></text>
        </view>
        <view class="mask-btn" @tap="closeLocationMask">返回</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FilmSuccess',
  data() {
    return {
      filmInfo: {},
      filmSessionId: null,
      batchNumber: null,
      qrcodeList: [],
      qrcodeIndex: 0,
      qrcodeBtns: ['票码一', '票码二', '票码三', '票码四', '票码五'],
      filmTypeList: ['球幕电影', '4D电影'],
      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],

      // 状态控制
      isShowCountDown: false,
      countDown: '',
      countDownTimer: null,
      isShowLocationMask: false,
      isSigningUp: false,
      showQrcode: false,
      isPlayFilm: true,

      // 提示信息
      tipsList: [
        '1.请按时到达宝安科技馆，凭二维码检票入场',
        '2.请提前10分钟签到获取二维码，否则该预约失效，需重新预约',
        '3.入场后请保持安静，配合工作人员安排'
      ],

      // 定时器
      refreshTimer: null,
      filmInfoTimer: null
    }
  },

  onLoad(options) {
    this.batchNumber = options.batchNumber
    this.filmSessionId = options.filmSessionId
    this.initPage()
  },

  onShow() {
    this.getFilmInfo()
    this.getTipsList()
  },

  onHide() {
    this.clearTimers()
  },

  onUnload() {
    this.clearTimers()
  },

  methods: {
    // 初始化页面
    initPage() {
      this.getFilmInfo()
      this.getTipsList()
      this.startCountDown()
    },

    // 获取电影信息
    async getFilmInfo() {
      try {
        const res = await this.$myRequest({
          url: '/web/fileSession/personalCenterFilmByFilmSessionId',
          method: 'get',
          data: {
            filmSessionId: this.filmSessionId
          }
        })

        if (res.code === 200) {
          this.filmInfo = {
            ...res.data.data,
            filmStartTime: this.formatTime(res.data.data.filmStartTime),
            filmEndTime: this.formatTime(res.data.data.filmEndTime),
            orderNum: res.data.data.filmPoll - res.data.data.inventoryVotes
          }

          // 开始定时刷新电影信息
          this.startFilmInfoRefresh()
        }
      } catch (error) {
        console.error('获取电影信息失败:', error)
        uni.showToast({
          title: '获取信息失败',
          icon: 'error'
        })
      }
    },

    // 获取提示信息
    async getTipsList() {
      try {
        const res = await this.$myRequest({
          url: '/admin/entrance_announcement/3',
          method: 'get'
        })

        if (res.code === 200) {
          const data = res.data.data
          if (data.beiyongThree) {
            this.tipsList = data.beiyongThree.split('\n').filter(tip => tip.trim())
          }
        }
      } catch (error) {
        console.warn('获取提示信息失败:', error)
      }
    },

    // 切换二维码
    changeQrcodeIndex(index) {
      this.qrcodeIndex = index
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''

      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },

    // 获取星期
    getWeekDay(dateStr) {
      if (!dateStr) return ''

      try {
        const date = new Date(dateStr.replace(/-/g, '/'))
        return this.weekList[date.getDay()]
      } catch (error) {
        return ''
      }
    },

    // 签到获取二维码
    async signUp() {
      if (this.isSigningUp) return

      this.isSigningUp = true

      try {
        // 获取位置信息
        const location = await this.getCurrentLocation()

        // 验证位置
        const locationRes = await this.$myRequest({
          url: '/web/common/checkLocation',
          method: 'post',
          data: {
            latitude: location.latitude,
            longitude: location.longitude
          }
        })

        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {
          this.isShowLocationMask = true
          return
        }

        // 签到获取二维码
        const signRes = await this.$myRequest({
          url: '/web/fileSession/filmSign',
          method: 'post',
          data: {
            batchNumber: this.batchNumber,
            filmSessionId: this.filmSessionId
          }
        })

        if (signRes.code === 200) {
          this.showQrcode = true
          this.getQrcode()

          uni.showToast({
            title: '签到成功',
            icon: 'success'
          })
        } else {
          throw new Error(signRes.msg || '签到失败')
        }
      } catch (error) {
        console.error('签到失败:', error)

        if (error.message && error.message.includes('定位')) {
          this.isShowLocationMask = true
        } else {
          uni.showToast({
            title: error.message || '签到失败',
            icon: 'error'
          })
        }
      } finally {
        this.isSigningUp = false
      }
    },

    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: resolve,
          fail: reject
        })
      })
    },

    // 获取二维码
    async getQrcode() {
      try {
        const res = await this.$myRequest({
          url: '/web/fileSession/qrCodeInfo',
          method: 'get',
          data: {
            batchNumber: this.batchNumber,
            filmType: this.filmInfo.filmType
          }
        })

        if (res.code === 200) {
          this.qrcodeList = res.data.data || []

          if (this.qrcodeList.length === 0) {
            uni.showModal({
              title: '提示',
              content: '状态已更改, 返回上一页',
              showCancel: false,
              success: () => {
                uni.navigateBack()
              }
            })
          } else {
            // 设置定时刷新二维码
            this.startQrcodeRefresh()
          }
        }
      } catch (error) {
        console.error('获取二维码失败:', error)
        uni.showToast({
          title: '获取二维码失败',
          icon: 'error'
        })
      }
    },

    // 刷新二维码
    refreshQrcode() {
      this.getQrcode()
    },

    // 开始倒计时
    startCountDown() {
      if (!this.filmInfo.filmStartTime) return

      const startTime = new Date(this.filmInfo.filmArrangedDate + ' ' + this.filmInfo.filmStartTime).getTime()
      const now = new Date().getTime()

      // 如果距离开场时间在30分钟内，显示倒计时
      if (startTime - now > 0 && startTime - now < 30 * 60 * 1000) {
        this.isShowCountDown = true

        this.countDownTimer = setInterval(() => {
          const now = new Date().getTime()
          const diff = startTime - now

          if (diff <= 0) {
            this.isShowCountDown = false
            clearInterval(this.countDownTimer)
            return
          }

          const minutes = Math.floor(diff / 60000)
          const seconds = Math.floor((diff % 60000) / 1000)
          this.countDown = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        }, 1000)
      }
    },

    // 开始电影信息刷新
    startFilmInfoRefresh() {
      this.filmInfoTimer = setTimeout(() => {
        this.getFilmInfo()
      }, 1500)
    },

    // 开始二维码刷新
    startQrcodeRefresh() {
      this.refreshTimer = setTimeout(() => {
        this.getQrcode()
      }, 30000) // 30秒刷新一次
    },

    // 清除所有定时器
    clearTimers() {
      if (this.countDownTimer) {
        clearInterval(this.countDownTimer)
        this.countDownTimer = null
      }
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer)
        this.refreshTimer = null
      }
      if (this.filmInfoTimer) {
        clearTimeout(this.filmInfoTimer)
        this.filmInfoTimer = null
      }
    },

    // 关闭定位弹窗
    closeLocationMask() {
      this.isShowLocationMask = false
    }
  }
}
</script>

<style lang="scss" scoped>
.film-success {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding: 20rpx;
    padding-top: 120rpx; // 为固定头部留出空间

    // 成功状态
    .success-state {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 40rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .success-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 30rpx;

        .success-icon {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 20rpx;
        }

        .success-text {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .tips-section {
        .tips-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 20rpx;
        }

        .tips-item {
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    // 电影信息
    .film-info {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .info-header {
        margin-bottom: 20rpx;

        .venue-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .film-details {
        display: flex;

        .film-poster {
          width: 120rpx;
          height: 160rpx;
          margin-right: 20rpx;
          border-radius: 12rpx;
          overflow: hidden;

          .poster-image {
            width: 100%;
            height: 100%;
          }
        }

        .film-meta {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .film-name {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
            line-height: 1.4;
          }

          .film-type,
          .film-time,
          .film-date,
          .film-count {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 6rpx;
            line-height: 1.3;
          }
        }
      }
    }

    // 二维码区域
    .qrcode-section {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .qrcode-tabs {
        display: flex;
        justify-content: center;
        margin-bottom: 30rpx;
        gap: 20rpx;

        .tab-btn {
          padding: 12rpx 24rpx;
          border-radius: 20rpx;
          font-size: 26rpx;
          background: #f5f5f5;
          color: #666;

          &.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
          }
        }
      }

      .qrcode-container {
        display: flex;
        justify-content: center;

        .qrcode-wrapper {
          position: relative;
          width: 400rpx;
          height: 400rpx;

          .qrcode-image,
          .qrcode-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 12rpx;
          }

          .qrcode-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .mask-content {
              text-align: center;

              .cancel-btn,
              .waiting-btn,
              .signin-btn {
                padding: 20rpx 40rpx;
                border-radius: 40rpx;
                font-size: 28rpx;
                font-weight: 500;
                border: none;

                &::after {
                  border: none;
                }
              }

              .cancel-btn {
                background: #ff6b6b;
                color: white;
              }

              .waiting-btn {
                background: #ffa726;
                color: white;

                view {
                  line-height: 1.3;
                }
              }

              .signin-btn {
                background: linear-gradient(135deg, #4ecdc4, #44a08d);
                color: white;

                &:disabled {
                  opacity: 0.6;
                }
              }
            }
          }
        }
      }

      .countdown {
        text-align: center;
        margin-top: 20rpx;

        .countdown-text {
          font-size: 28rpx;
          color: #666;
          font-weight: 500;
        }
      }
    }

    // 底部提示
    .bottom-tips {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      text-align: center;

      .highlight {
        color: #1976d2;
        font-weight: 500;
      }
    }
  }

  // 定位弹窗
  .location-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .mask-dialog {
      background: white;
      border-radius: 20rpx;
      padding: 60rpx 40rpx;
      margin: 40rpx;
      text-align: center;

      .location-icon {
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto 30rpx;
        background: url('/static/img/common/location-error.png') center/contain no-repeat;
      }

      .mask-text {
        font-size: 32rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 40rpx;

        .highlight {
          color: #1976d2;
          font-weight: 500;
        }
      }

      .mask-btn {
        padding: 20rpx 60rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 40rpx;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.film-success {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>