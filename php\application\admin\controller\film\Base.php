<?php

namespace app\admin\controller\film;

use app\common\controller\Backend;

/**
 * 影片管理
 *
 * @icon fa fa-circle-o
 */
class Base extends Backend
{

    /**
     * Base模型对象
     * @var \app\common\model\film\Base
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\film\Base;

    }

    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->where($where)
            ->where('del_flag', '0')
            ->order($sort, $order)
            ->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }


    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");

            if ($params) {
                // 添加额外字段
                $params['del_flag']     = 0; // 逻辑有效
   


                $id = $this->model->save($params);
                $this->success("添加成功");
            }

            $this->error("提交数据无效");
        }


        // 渲染表单视图
        $selectedDate = $this->request->get("selectedDate");
        $this->assign('selectedDate', $selectedDate);
        return $this->view->fetch();
    }

    public function del($ids = "")
    {
        if ($this->request->isPost()) {
            if (!$ids) {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
    
            // 实例化模型
            $model = $this->model;
    
            // 查询要更新的记录
            $list = $model->where('id', 'in', $ids)->select();
            if (!$list) {
                $this->error(__('No rows were found'));
            }
    
            // 软删除处理
            foreach ($list as $row) {
                $row->del_flag = 2;
                $row->save();
            }
    
            $this->success();
        }
        $this->error(__('Invalid parameters'));
    }
    
        /**
     * 获取某日的影片排期统计
     */
    public function getstats()
    {
        $dateStr = $this->request->get("date"); // 例: 2025-07-27
        if (!$dateStr) {
            return json(['code' => 0, 'msg' => '缺少参数: date']);
        }

        // 格式化日期为 yyyyMMdd 用于 SQL 比较
        $date = date("Ymd", strtotime($dateStr));

        // 查询语句
        $list =  \app\admin\model\film\Session::table   ('film_session')
        ->alias('se')
            ->join(['film'=>'f'], 'f.id = se.film_id')
            ->field('
                se.id,
                f.film_type as filmType,
                f.film_name as filmName,
                se.film_poll as filmPoll,
                se.inventory_votes as inventoryVotes,
                se.film_state as filmState,
                se.film_start_time as filmStartTime,
                se.film_end_time as filmEndTime,
                (
                    select count(*) 
                    from film_subscribe 
                    where is_ticket = 1 and film_seeion_id = se.id
                ) as hadTicket
            ')
            ->where("se.del_flag", 0)
            ->whereRaw("DATE_FORMAT(se.film_start_time, '%Y%m%d') = ?", [$date])
            ->order('se.film_start_time asc')
            ->select();

        return json(['code' => 1, 'msg' => 'success', 'data' => $list]);
    }
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
