define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'moment'], function ($, undefined, Backend, Table, Form, moment) {
  var calendar = null;                // FullCalendar 实例
  let selectedDate = new Date(); // 默认当前日期
  var currentStatus = null;          // 当前选中日期的开闭馆状态 ('1' = 开馆, '0' = 闭馆)




  var Controller = {
    index: function () {
      var calendarEl = document.getElementById('holiday-calendar');

      $(function () {
        var calendar = new FullCalendar.Calendar(calendarEl, {
          locale: 'zh-cn',
          initialView: 'dayGridMonth',
          height: 'auto',
          headerToolbar: {
            left: 'prev today next',
            center: 'title',
            right: ''
          },
          selectable: true,
          customButtons: {

            today: {
              text: '今天',
              click: function () {
                selectedDate = new Date(); // 今天
                calendar.today();
                highlightSelectedDate();
                fetchVenueDetailByDate(selectedDate);
              }
            },
            prev: {
              text: '上个月',
              click: function () {
                calendar.prev();

                setTimeout(() => {
                  const firstOfMonth = calendar.view.currentStart;
                  selectedDate = firstOfMonth;
                  highlightSelectedDate();
                  fetchVenueDetailByDate(selectedDate);
                });
              }
            },
            next: {
              text: '下个月',
              click: function () {
                calendar.next();

                setTimeout(() => {
                  const firstOfMonth = calendar.view.currentStart;
                  console.log('firstOfMonth', firstOfMonth)
                  selectedDate = firstOfMonth;
                  highlightSelectedDate();
                  fetchVenueDetailByDate(selectedDate);
                });
              }
            },

          },

          events: function (info, successCallback, failureCallback) {
            // 加载指定月份的数据
            const middle = new Date(info.start.getTime());
            middle.setDate(middle.getDate() + 15);

            const year = middle.getFullYear();
            const month = String(middle.getMonth() + 1).padStart(2, '0');
            const ym = `${year}-${month}`;

            $.get('functioncontrol/holidays/getlist', {
              month: ym
            }, function (res) {
              if (res.code === 1) {
                successCallback(res.data);
              } else {
                failureCallback(res.msg);
              }
            });
          },
          eventContent: function (arg) {
            const isClose = arg.event.extendedProps.is_close === '0'; // 0 = 闭馆

            const el = document.createElement('div');
            el.innerHTML = `<span class="${isClose ? 'calendar-close' : 'calendar-open'}">
                    ${isClose ? '闭馆日' : '开馆日'}
                  </span>`;

            return { domNodes: [el] };
          },
          dateClick: function (info) {

            //这一段是处理，点击了 非本月的日子，自动跳到对应月
            const clickedDate = info.date;
            const calendar = info.view.calendar;
            const currentMonth = calendar.getDate().getMonth(); // 当前中心月（0-11）
            const clickedMonth = clickedDate.getMonth();

            if (clickedMonth < currentMonth) {
              calendar.prev(); // 上个月
            } else if (clickedMonth > currentMonth) {
              calendar.next(); // 下个月
            } else {
              // 本月日，正常处理选中
              $('.fc-daygrid-day').removeClass('selected-date');
              $('.fc-daygrid-day[data-date="' + info.dateStr + '"]').addClass('selected-date');
            }

            selectedDate = info.date;
            highlightSelectedDate();
            fetchVenueDetailByDate(info.date);

          },
          // 每次视图变更都会触发
          datesSet: function (info) {

            highlightSelectedDate(); // 高亮打钩
            // console.log(info.date)
            console.log(selectedDate)
            fetchVenueDetailByDate(selectedDate); // ✅ 初始化时加载一次右侧数据

          },
        });

        calendar.render();
        setTimeout(() => {
          $('.fc-prev-button').text('上个月');
          $('.fc-next-button').text('下个月');
          $('.fc-closeButton-button').css({
            'background-color': 'red',
            'border-color': 'red',
            'color': 'white'
          });
          $('.fc-openButton-button').css({
            'background-color': '#1890ff',
            'border-color': '#1890ff',
            'color': 'white'
          });
        }, 100);

        $(document).on('click', '.btn[data-id][data-status]', function () {
          const $btn = $(this);
          const id = $btn.data('id');       // 场次ID
          const currentStatus = $btn.data('status'); // 当前状态："1"表示开启，"0"表示关闭
          const newStatus = currentStatus == '1' ? '0' : '1';


          // 弹出确认框

          $.post('venue/changeStatus', {
            id: id,
            status: newStatus
          }, function (res) {
            if (res.code === 1) {
              Toastr.success('操作成功');
              // 重新加载场次信息
              fetchVenueDetailByDate(selectedDate);
            } else {
              Toastr.error(res.msg || '操作失败');
            }
          }, 'json');

        });


        let editingId = null;
        //点击 修改时间 弹窗
        $(document).on('click', '.btn-edit-time', function () {
          const id = $(this).data('id');
          const start = $(this).data('start');
          const end = $(this).data('end');

          editingId = id;
          $('#edit-start-time').val(start);
          $('#edit-end-time').val(end);

          $('#editTimeModal').modal('show');
        });
        //点击 修改时间 弹窗， 保存按钮
        $('#btn-save-time').on('click', function () {
          const start = $('#edit-start-time').val();
          const end = $('#edit-end-time').val();

          if (!start || !end || start >= end) {
            Toastr.error('时间格式不正确');
            return;
          }

          $.post('venue/updateTime', {
            id: editingId,
            start: start,
            end: end
          }, function (res) {
            if (res.code === 1) {
              Toastr.success('修改成功');
              $('#editTimeModal').modal('hide');
              // 重新加载场次信息
              fetchVenueDetailByDate(selectedDate);
            } else {
              Toastr.error(res.msg || '修改失败');
            }
          }, 'json');
        });


        // ======= 辅助函数放这里 =======
        // 自定义函数，从事件中判断当天状态 （开馆 闭馆)
        function getDateStatusFromEvent(dateStr) {
          const events = calendar.getEvents();
          const event = events.find(ev => ev.startStr === dateStr);
          return event ? event.extendedProps.is_close : null;
        }
        //、创建高亮函数
        function highlightSelectedDate() {
          $('.fc-daygrid-day').removeClass('fc-selected');
          const dateObj = new Date(selectedDate); // 转为标准 Date 对象
          const dateStr = formatDateToYMD(dateObj);

          console.log('高亮函数：');
          console.log('[highl] selectedDate:', selectedDate);
          console.log('[highl] formatted:', dateStr);


          $(`.fc-daygrid-day[data-date="${dateStr}"]`).addClass('fc-selected');

          $('.fc-prev-button').text('上个月');
          $('.fc-next-button').text('下个月');

          //每次选中后，需要把当前内容展示在右边，在这里 弄比较好



        }
        //、创建高亮函数2
        function formatDateToYMD(date) {
          const y = date.getFullYear();
          const m = String(date.getMonth() + 1).padStart(2, '0');
          const d = String(date.getDate()).padStart(2, '0');
          return `${y}-${m}-${d}`;
        }
        //查询场次
        function fetchVenueDetailByDate(dateObj) {
          const year = dateObj.getFullYear();
          const month = String(dateObj.getMonth() + 1).padStart(2, '0');
          const day = dateObj.getDate();
          const dateStr = `${year}-${month}-${day}`;

          $.get(`venue/getDetail`, { date: dateStr }, function (res) {
            if (res.code == 1) {
              renderVenueDetailTable(res.data); // 渲染右侧表格
            } else {
              Toastr.error(res.msg || '查询失败');
            }
          });
        }
        // 渲染右侧表格函数
        function renderVenueDetailTable(data) {
          const tbody = $('#timeslot-table-body');
          tbody.empty();

          if (!data || data.length === 0) {
            tbody.html('<tr><td colspan="4" class="text-center">暂无数据</td></tr>');
            return;
          }

          data.forEach((row, index) => {
            const name = index === 0 ? '上午场' : '下午场';
            const start = row.venue_start_time.slice(11, 16);
            const end = row.venue_end_time.slice(11, 16);
            const time = `${start}-${end}`;
            const total = row.venue_poll; //总票数
            const remain = row.inventory_votes;//剩余票数
            const used = total - remain;

            const isOpen = row.is_open === '1';

            const tr = $(`
            <tr>
                <td>${name}</td>
                <td>${time}</td>
                <td>
                    <span class="text-primary">可预约：${remain}</span><br>
                    <span class="text-success">已预约：${used}</span>
                </td>
                <td>
                    <button class="btn btn-xs ${isOpen ? 'btn-danger' : 'btn-success'}"
                            data-id="${row.id}"
                            data-status="${isOpen ? '1' : '0'}">
                            <i class="fa ${isOpen ? 'fa-ban' : 'fa-check-circle'}"></i>
                        ${isOpen ? '停止预约' : '开启预约'}
                    </button>

      <button class="btn btn-xs btn-primary btn-edit-time"
              data-id="${row.id}"
              data-start="${start}"
              data-end="${end}">
        <i class="fa fa-clock-o"></i> 修改时间
      </button>
                </td>
            </tr>
        `);
            tbody.append(tr);
          });
        }


      });


    }
  };
  return Controller;
});
