<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use Redis;
use think\Env;

class Redisadmin extends Backend
{
    protected $model = null;
    protected $redis = null;
    protected $options = [];
    public function _initialize($options = [])
    {
        parent::_initialize();

        if (!extension_loaded('redis')) {
            throw new \BadFunctionCallException('not support: redis');
        }

        $config = (get_addon_config('redisadmin'));

        $this->options['host']   = Env::get('redis.host', $config['host'] ?? '127.0.0.1');
        $this->options['port']   = Env::get('redis.port', $config['port'] ?? 6379);
        $this->options['password']   = Env::get('redis.auth', $config['password'] ?? '');
        $this->options['select'] = Env::get('redis.select', $config['select'] ?? 0);
        $this->options['timeout'] = Env::get('redis.timeout', $config['timeout'] ?? 0);


        if (!empty($options)) {
            $this->options = array_merge($this->options, $options);
        }
        $this->redis = new \Redis;

        $this->redis->connect($this->options['host'], $this->options['port'], $this->options['timeout']);


        if ('' != $this->options['password']) {
            $this->redis->auth($this->options['password']);
        }

        if (0 != $this->options['select']) {
            $this->redis->select($this->options['select']);
        }
    }

    public function manage()
    {
        $keys = $this->redis->keys('*');
        $list = [];
        foreach ($keys as $key) {
            $list[] = [
                'key' => $key,
                'type' => $this->redis->type($key),
                'ttl' => $this->redis->ttl($key),
            ];
        }

        $this->assign('list', $list);
        return $this->view->fetch('index');
    }

    public function dashboard()
    {
        $info = $this->redis->info();
        $dbSize = $this->redis->dbSize();

        // 基本信息字段映射
        $baseInfo = [
            'Redis版本'     => $info['redis_version'] ?? '-',
            '运行模式'     => $info['redis_mode'] ?? '-',
            '端口'         => $info['tcp_port'] ?? '-',
            '客户端数'     => $info['connected_clients'] ?? '-',
            '运行时间(天)' => floor(($info['uptime_in_seconds'] ?? 0) / 86400),
            '使用内存'     => $info['used_memory_human'] ?? '-',
            '使用CPU'     => round($info['used_cpu_sys'] ?? 0, 2),
            '内存配置'     => $info['maxmemory_human'] ?? '0B',
            'AOF是否开启'  => empty($info['aof_enabled']) ? '否' : '是',
            'RDB是否成功'  => $info['rdb_last_bgsave_status'] ?? '-',
            'Key数量'     => $dbSize,
            '网络入口/出口' => sprintf("%.2fkps / %.2fkps", $info['instantaneous_input_kbps'] ?? 0, $info['instantaneous_output_kbps'] ?? 0),
        ];

        // Key 类型统计
        $types = [
            Redis::REDIS_STRING => 'String',
            Redis::REDIS_LIST => 'List',
            Redis::REDIS_SET => 'Set',
            Redis::REDIS_ZSET => 'ZSet',
            Redis::REDIS_HASH => 'Hash'
        ];
        $keys = $this->redis->keys('*');
        $stats = array_fill_keys(array_values($types), 0);
        foreach ($keys as $key) {
            $type = $this->redis->type($key);
            $typename = $types[$type] ?? 'Other';
            $stats[$typename]++;
        }

        $this->assign('base', $baseInfo);
        $this->assign('stats_json', json_encode($stats, JSON_UNESCAPED_UNICODE));




        $this->assign('used_memory', $info['used_memory'] ?? 0);
        $this->assign('max_memory', $info['maxmemory'] ?? 0);
        $this->assign('used_memory_human', $info['used_memory_human'] ?? '-');
        $this->assign('max_memory_human', $info['maxmemory_human'] ?? '100MB');



        return $this->fetch();
    }

    public function view()
    {
        $key   = $this->request->param('key');
        $typeCode = $this->redis->type($key);
        $types = [
            Redis::REDIS_STRING => 'String',
            Redis::REDIS_LIST   => 'List',
            Redis::REDIS_SET    => 'Set',
            Redis::REDIS_ZSET   => 'ZSet',
            Redis::REDIS_HASH   => 'Hash',
        ];
        $type = $types[$typeCode] ?? 'Unknown';
        $value = null;

        switch ($typeCode) {
            case Redis::REDIS_STRING:
                $value = $this->redis->get($key);
                break;
            case Redis::REDIS_LIST:
                $value = $this->redis->lRange($key, 0, -1);
                break;
            case Redis::REDIS_SET:
                $value = $this->redis->sMembers($key);
                break;
            case Redis::REDIS_ZSET:
                $value = $this->redis->zRange($key, 0, -1, true); // 带分数
                break;
            case Redis::REDIS_HASH:
                $value = $this->redis->hGetAll($key);
                break;
        }

        $ttl = $this->redis->ttl($key);

        $this->assign('key', $key);
        $this->assign('type', $type);
        $this->assign('ttl', $ttl);
        $this->assign('value', $value);
        return $this->fetch();
    }
}
