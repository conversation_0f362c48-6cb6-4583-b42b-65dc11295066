<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_id" data-rule="required" data-source="film/index" class="form-control selectpage" name="row[film_id]" type="text" value="{$row.film_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_poll')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_poll" data-rule="required" class="form-control" name="row[film_poll]" type="number" value="{$row.film_poll|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_state')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_state" data-rule="required" class="form-control" name="row[film_state]" type="text" value="{$row.film_state|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_start_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_start_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[film_start_time]" type="text" value="{$row.film_start_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_end_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_end_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[film_end_time]" type="text" value="{$row.film_end_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_arranged_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_arranged_date" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[film_arranged_date]" type="text" value="{$row.film_arranged_date}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Year')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-year" data-rule="required" class="form-control" name="row[year]" type="number" value="{$row.year|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Week')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-week" data-rule="required" class="form-control" name="row[week]" type="number" value="{$row.week|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Inventory_votes')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-inventory_votes" data-rule="required" class="form-control" name="row[inventory_votes]" type="number" value="{$row.inventory_votes|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_by')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_by" data-rule="required" class="form-control" name="row[create_by]" type="text" value="{$row.create_by|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Create_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-create_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[create_time]" type="text" value="{$row.create_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Update_by')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-update_by" data-rule="required" class="form-control" name="row[update_by]" type="text" value="{$row.update_by|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Update_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-update_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[update_time]" type="text" value="{$row.update_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" data-rule="required" class="form-control" name="row[remark]" type="text" value="{$row.remark|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Del_flag')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-del_flag" data-rule="required" class="form-control" name="row[del_flag]" type="text" value="{$row.del_flag|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
