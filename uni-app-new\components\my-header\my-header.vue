<template>
  <view 
    class="my-header" 
    :style="{
      background: background,
      color: color,
      paddingTop: menuButtonInfo.top + 'px',
      height: menuButtonInfo.height + 'px',
      paddingBottom: '30px',
      position: isFixed ? 'fixed' : 'static',
      zIndex: isFixed ? 999 : 3
    }"
  >
    <view class="header_content">
      <view :class="['header_btns', menuClass]">
        <view 
          v-if="isBack" 
          class="back icon_back" 
          :style="{ lineHeight: menuButtonInfo.height + 'px' }"
          @tap="goBack"
        ></view>
        <view 
          v-if="isShowHome" 
          class="home icon_home" 
          :style="{ lineHeight: menuButtonInfo.height + 'px' }"
          @tap="goHome"
        ></view>
      </view>
      <view class="title">{{ title }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: '<PERSON><PERSON>eader',
  props: {
    title: {
      type: String,
      default: ''
    },
    background: {
      type: String,
      default: '#ffffff'
    },
    isBack: {
      type: [Boolean, String],
      default: false
    },
    isShowHome: {
      type: [Boolean, String],
      default: false
    },
    isFixed: {
      type: [Boolean, String],
      default: false
    },
    color: {
      type: String,
      default: '#ffffff'
    },
    menuClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      menuButtonInfo: {}
    }
  },
  created() {
    // 获取胶囊按钮信息，兼容不同平台
    // #ifdef MP-WEIXIN
    this.menuButtonInfo = uni.getMenuButtonBoundingClientRect()
    this.menuButtonInfo.top += 16
    // #endif
    
    // #ifndef MP-WEIXIN
    // 非微信小程序平台的默认值
    this.menuButtonInfo = {
      top: 44,
      height: 32
    }
    // #endif
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    goHome() {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style scoped>
.my-header {
  color: #333;
  font-family: PingFang SC;
  font-size: 35rpx;
  width: 100%;
}

.my-header .header_content {
  height: 100%;
  position: relative;
  width: 100%;
}

.my-header .header_content .header_btns {
  align-items: center;
  background-color: transparent;
  border-radius: 100rpx;
  display: flex;
  height: 100%;
  justify-content: flex-start;
  margin-left: 29rpx;
  position: relative;
  text-align: center;
  width: 160rpx;
}

.my-header .header_content .header_btns .back {
  font-size: 33rpx;
  height: 100%;
  margin-top: -30px;
  position: relative;
  width: 50%;
}

.my-header .header_content .header_btns .back::after {
  background-color: transparent;
  content: "";
  display: inline-block;
  height: 36rpx;
  position: absolute;
  right: 1rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
}

.my-header .header_content .header_btns .home {
  font-size: 33rpx;
  height: 100%;
  margin-top: -30px;
  width: 50%;
}

.my-header .header_content .title {
  display: inline-block;
  font-weight: 700;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 图标样式 */
.icon_back::before {
  content: "‹";
  font-size: 40rpx;
  font-weight: bold;
}

.icon_home::before {
  content: "⌂";
  font-size: 36rpx;
}
</style>