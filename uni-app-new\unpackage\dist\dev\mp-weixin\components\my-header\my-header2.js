"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "<PERSON>Header",
  props: {
    title: {
      type: String,
      default: ""
    },
    background: {
      type: String,
      default: "#ffffff"
    },
    isBack: {
      type: [Boolean, String],
      default: false
    },
    isShowHome: {
      type: [Boolean, String],
      default: false
    },
    isFixed: {
      type: [Boolean, String],
      default: false
    },
    color: {
      type: String,
      default: "#ffffff"
    },
    menuClass: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      menuButtonInfo: {}
    };
  },
  created() {
    this.menuButtonInfo = common_vendor.index.getMenuButtonBoundingClientRect();
    this.menuButtonInfo.top += 16;
  },
  methods: {
    goBack() {
      common_vendor.index.navigateBack();
    },
    goHome() {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.isBack
  }, $props.isBack ? {
    b: $data.menuButtonInfo.height + "px",
    c: common_vendor.o((...args) => $options.goBack && $options.goBack(...args))
  } : {}, {
    d: $props.isShowHome
  }, $props.isShowHome ? {
    e: $data.menuButtonInfo.height + "px",
    f: common_vendor.o((...args) => $options.goHome && $options.goHome(...args))
  } : {}, {
    g: common_vendor.n($props.menuClass),
    h: common_vendor.t($props.title),
    i: $props.background,
    j: $props.color,
    k: $data.menuButtonInfo.top + "px",
    l: $data.menuButtonInfo.height + "px",
    m: $props.isFixed ? "fixed" : "static",
    n: $props.isFixed ? 999 : 3
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3f622259"]]);
exports.default = Component;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/my-header/my-header2.js.map
