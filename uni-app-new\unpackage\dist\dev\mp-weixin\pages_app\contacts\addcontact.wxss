
.addLink.data-v-380f9cc6 {
  background-color: #f3f4f6;
  font-family: PingFang SC;
  height: auto;
  min-height: 100vh;
  width: 100%;
}
.addBox.data-v-380f9cc6 {
  box-sizing: border-box;
  height: auto;
  padding: 29rpx;
  width: 100%;
}
.label.data-v-380f9cc6 {
  color: #888;
  font-size: 27rpx;
  font-weight: 600;
  margin-right: 29rpx;
  min-width: 110rpx;
}
.checkCardType.data-v-380f9cc6 {
  align-items: center;
  background-color: #fff;
  border-radius: 15rpx;
  box-sizing: border-box;
  display: flex;
  margin-bottom: 29rpx;
  padding: 0 15rpx 0 29rpx;
  width: 100%;
}
.checkItem-itm.data-v-380f9cc6 {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}
.checkItem.data-v-380f9cc6 {
  align-items: center;
  display: flex;
  margin-left: 20rpx;
  margin-top: 20rpx;
}
.checkShow.data-v-380f9cc6 {
  border: 2rpx solid #888;
  border-radius: 50%;
  font-size: 25rpx;
  font-weight: 700;
  height: 29rpx;
  line-height: 29rpx;
  margin-right: 10rpx;
  text-align: center;
  width: 29rpx;
}
.checkShow.isCheck.data-v-380f9cc6 {
  background: #ffba38;
  border: 2rpx solid transparent;
}
.checkShow.isCheck.data-v-380f9cc6::before {
  content: "✓";
  display: inline-block;
  color: #fff;
}
.checkName.data-v-380f9cc6 {
  color: #000;
  font-size: 27rpx;
  font-weight: 600;
}
.addInfo.data-v-380f9cc6 {
  background-color: #fff;
  border-radius: 15rpx;
  box-sizing: border-box;
  padding: 0 29rpx;
  width: 100%;
}
.infoItem.data-v-380f9cc6 {
  align-items: center;
  border-bottom: 1rpx solid rgba(136, 136, 136, 0.5);
  display: flex;
  height: 92rpx;
  width: 100%;
}
.infoItem.data-v-380f9cc6:last-of-type {
  border-bottom: 0;
}
.uni-input.data-v-380f9cc6 {
  color: #000;
  font-family: PingFang SC !important;
  font-size: 27rpx;
  font-weight: 600;
}
.user_agree.data-v-380f9cc6 {
  box-sizing: border-box;
  height: 150rpx;
  padding: 20rpx 29rpx;
  width: 100%;
}
.user_agree .checkItem.data-v-380f9cc6 {
  align-items: center;
  display: flex;
}
.user_agree .checkShow.data-v-380f9cc6 {
  border: 2rpx solid #888;
  border-radius: 50%;
  font-size: 25rpx;
  font-weight: 700;
  height: 29rpx;
  line-height: 29rpx;
  margin-right: 25rpx;
  text-align: center;
  width: 29rpx;
}
.user_agree .checkShow.isCheck.data-v-380f9cc6 {
  background: #ffba38;
  border: 2rpx solid transparent;
}
.user_agree .checkShow.isCheck.data-v-380f9cc6::before {
  content: "✓";
  display: inline-block;
  color: #fff;
}
.user_agree .checkName.data-v-380f9cc6 {
  color: #000;
  font-size: 27rpx;
  font-weight: 600;
}
.user_agreement_btn.data-v-380f9cc6 {
  color: #5cb7ff;
  font-size: 27rpx;
  font-weight: 600;
}
.sureChoose.data-v-380f9cc6 {
  align-items: center;
  bottom: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 150rpx;
  justify-content: space-evenly;
  left: 0;
  padding: 0 29rpx;
  position: fixed;
  width: 100%;
}
.upBtn.data-v-380f9cc6 {
  background: #5cb7ff;
  border-radius: 10rpx;
  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);
  color: #fff;
  font-size: 35rpx;
  height: 77rpx;
  line-height: 77rpx;
  text-align: center;
  width: 654rpx;
}
.mask.data-v-380f9cc6 {
  background-color: rgba(0, 0, 0, 0.5);
  bottom: 0;
  height: auto;
  overflow: auto;
  padding: 237rpx 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
}
.maskContent.data-v-380f9cc6 {
  background: #fff;
  border: 2rpx solid #707070;
  border-radius: 19rpx;
  box-sizing: border-box;
  font-family: PingFang SC;
  height: auto;
  margin: 0 auto;
  padding: 58rpx 58rpx 48rpx;
  width: 654rpx;
}
.noticeTitle.data-v-380f9cc6 {
  color: #1b1b1b;
  font-size: 35rpx;
  font-weight: 700;
  margin-bottom: 30rpx;
  text-align: center;
}
.noticeView.data-v-380f9cc6 {
  height: auto;
  line-height: 50rpx;
  text-align: justify;
  width: 100%;
}
.title.data-v-380f9cc6 {
  font-size: 29rpx;
  font-weight: 700;
}
.text.data-v-380f9cc6 {
  font-size: 27rpx;
  margin: 30rpx 0;
}
.item.data-v-380f9cc6 {
  font-size: 27rpx;
  line-height: 40rpx;
  margin-bottom: 20rpx;
  padding-left: 40rpx;
  position: relative;
}
.item.data-v-380f9cc6::before {
  background-color: #000;
  border-radius: 50%;
  content: "";
  height: 10rpx;
  left: 0.5em;
  position: absolute;
  top: 0.5em;
  width: 10rpx;
}
.foot.data-v-380f9cc6 {
  font-size: 27rpx;
  margin: 40rpx 0;
}
.agreeBtn.data-v-380f9cc6 {
  background: #5cb7ff;
  border-radius: 10rpx;
  box-shadow: 0 6rpx 12rpx rgba(82, 162, 225, 0.34);
  color: #fff;
  font-size: 35rpx;
  height: 77rpx;
  line-height: 77rpx;
  margin: 0 auto;
  text-align: center;
  width: 381rpx;
}
