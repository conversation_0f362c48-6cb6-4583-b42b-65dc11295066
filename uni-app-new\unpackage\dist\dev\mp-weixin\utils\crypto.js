"use strict";
const common_vendor = require("../common/vendor.js");
function encryptBase64(text) {
  try {
    return common_vendor.index.base64Encode(text);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/crypto.js:15", "Base64编码失败:", error);
    return text;
  }
}
function encryptWithAes(text, key) {
  try {
    return common_vendor.index.base64Encode(text + key);
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/crypto.js:35", "AES加密失败:", error);
    return text;
  }
}
function generateAesKey() {
  try {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/crypto.js:55", "生成AES密钥失败:", error);
    return "defaultkey12345678901234567890";
  }
}
exports.encryptBase64 = encryptBase64;
exports.encryptWithAes = encryptWithAes;
exports.generateAesKey = generateAesKey;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/crypto.js.map
