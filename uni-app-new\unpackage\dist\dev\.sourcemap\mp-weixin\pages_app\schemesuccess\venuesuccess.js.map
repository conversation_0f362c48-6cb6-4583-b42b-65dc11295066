{"version": 3, "file": "venuesuccess.js", "sources": ["pages_app/schemesuccess/venuesuccess.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHNjaGVtZXN1Y2Nlc3NcdmVudWVzdWNjZXNzLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"venue-success\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      title=\"参观凭证\" \r\n      :isBack=\"true\" \r\n      :isShowHome=\"true\"\r\n      background=\"#ffffff\"\r\n      color=\"#333333\"\r\n      :isFixed=\"true\"\r\n    />\r\n    \r\n    <view class=\"content\">\r\n      <!-- 成功状态 -->\r\n      <view class=\"success-state\">\r\n        <view class=\"success-header\">\r\n          <image \r\n            src=\"/static/img/schemesuccess/schemestate_course_venue.png\" \r\n            mode=\"aspectFit\"\r\n            class=\"success-icon\"\r\n          />\r\n          <text class=\"success-text\">预约成功</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 参观信息 -->\r\n      <view class=\"venue-info\">\r\n        <view class=\"info-header\">\r\n          <text class=\"venue-name\">宝安科技馆 入馆预约</text>\r\n        </view>\r\n        \r\n        <view class=\"venue-details\">\r\n          <view class=\"detail-row\">\r\n            <text class=\"label\">预约日期：</text>\r\n            <text class=\"value important\">{{ venueInfo.date }}</text>\r\n          </view>\r\n          <view class=\"detail-row\">\r\n            <text class=\"label\">入馆时间：</text>\r\n            <text class=\"value\">{{ venueInfo.venueStartTime }} - {{ venueInfo.venueEndTime }}</text>\r\n          </view>\r\n          <view class=\"detail-row\">\r\n            <text class=\"label\">预约人数：</text>\r\n            <text class=\"value\">{{ venueInfo.subscribeType }}人</text>\r\n          </view>\r\n          <view class=\"detail-row\" v-if=\"venueInfo.linkmanName\">\r\n            <text class=\"label\">联系人：</text>\r\n            <text class=\"value\">{{ venueInfo.linkmanName }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 联系人列表 -->\r\n        <view v-if=\"contactsList.length > 0\" class=\"contacts-list\">\r\n          <view class=\"contacts-title\">预约人员</view>\r\n          <view \r\n            v-for=\"(contact, index) in contactsList\" \r\n            :key=\"contact.linkId\"\r\n            class=\"contact-item\"\r\n          >\r\n            <text class=\"contact-name\">{{ contact.linkmanName }}</text>\r\n            <text class=\"contact-id\">{{ hideIdCard(contact.linkmanCertificate) }}</text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 提示信息 -->\r\n        <view class=\"tips-section\">\r\n          <view class=\"tips-icon\"></view>\r\n          <text class=\"tips-text\">\r\n            在首页<text class=\"highlight\">个人中心—场馆预约—查看凭证</text>中查看此凭证\r\n          </text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 签到按钮 -->\r\n      <view class=\"signin-section\">\r\n        <button \r\n          :class=\"['signin-btn', { signed: isSigned }]\"\r\n          @tap=\"signUp\"\r\n          :disabled=\"isSigningUp || isSigned\"\r\n        >\r\n          {{ isSigned ? '已签到' : (isSigningUp ? '签到中...' : '入馆签到') }}\r\n        </button>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 签到结果弹窗 -->\r\n    <view v-if=\"showSignModal\" class=\"sign-modal\">\r\n      <view :class=\"['modal-dialog', { fail: signResult === 'fail' }]\">\r\n        <view class=\"modal-icon\"></view>\r\n        <view class=\"modal-text\">\r\n          <template v-if=\"signResult === 'success'\">\r\n            恭喜您，签到成功！\r\n          </template>\r\n          <template v-else>\r\n            您的定位较远<br />\r\n            请移步至宝安科技馆进行<text class=\"highlight\">现场签到</text>\r\n          </template>\r\n        </view>\r\n        <view class=\"modal-btn\" @tap=\"closeSignModal\">\r\n          {{ signResult === 'success' ? '确定' : '返回' }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'VenueSuccess',\r\n  data() {\r\n    return {\r\n      venueInfo: {},\r\n      contactsList: [],\r\n      \r\n      // 参数\r\n      venueSessionId: null,\r\n      batchNumber: null,\r\n      \r\n      // 状态\r\n      isSigned: false,\r\n      isSigningUp: false,\r\n      showSignModal: false,\r\n      signResult: 'success', // success | fail\r\n      \r\n      // 屏幕亮度\r\n      originalBrightness: 0\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    this.venueSessionId = options.id\r\n    this.batchNumber = options.batchNumber\r\n    \r\n    this.initPage()\r\n  },\r\n  \r\n  onUnload() {\r\n    this.restoreBrightness()\r\n  },\r\n  \r\n  methods: {\r\n    // 初始化页面\r\n    async initPage() {\r\n      try {\r\n        // 设置屏幕亮度\r\n        await this.setBrightness()\r\n        \r\n        // 获取参观信息\r\n        await this.getVenueInfo()\r\n      } catch (error) {\r\n        console.error('初始化失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 设置屏幕亮度\r\n    async setBrightness() {\r\n      try {\r\n        // 获取当前亮度\r\n        const brightness = await this.getScreenBrightness()\r\n        this.originalBrightness = brightness\r\n        \r\n        // 设置适中亮度\r\n        await this.setScreenBrightness(0.5)\r\n      } catch (error) {\r\n        console.warn('设置亮度失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 恢复屏幕亮度\r\n    async restoreBrightness() {\r\n      if (this.originalBrightness > 0) {\r\n        try {\r\n          await this.setScreenBrightness(this.originalBrightness)\r\n        } catch (error) {\r\n          console.warn('恢复亮度失败:', error)\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取屏幕亮度\r\n    getScreenBrightness() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.getScreenBrightness({\r\n          success: (res) => resolve(res.value),\r\n          fail: reject\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 设置屏幕亮度\r\n    setScreenBrightness(value) {\r\n      return new Promise((resolve, reject) => {\r\n        uni.setScreenBrightness({\r\n          value,\r\n          success: resolve,\r\n          fail: reject\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 获取参观信息\r\n    async getVenueInfo() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/venueSession/personalCenterVenueByVenueSessionId',\r\n          method: 'get',\r\n          data: {\r\n            venueSessionId: this.venueSessionId\r\n          }\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          const data = res.data.data\r\n          this.venueInfo = {\r\n            ...data,\r\n            date: this.formatDate(data.venueArrangedDate),\r\n            venueStartTime: this.formatTime(data.venueStartTime),\r\n            venueEndTime: this.formatTime(data.venueEndTime)\r\n          }\r\n          \r\n          // 检查签到状态\r\n          this.isSigned = data.signState === '1'\r\n          \r\n          // 获取联系人列表\r\n          await this.getContactsList()\r\n        }\r\n      } catch (error) {\r\n        console.error('获取参观信息失败:', error)\r\n        uni.showToast({\r\n          title: '获取信息失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 获取联系人列表\r\n    async getContactsList() {\r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/web/venueSession/getVenueSubscribePeoples',\r\n          method: 'get',\r\n          data: {\r\n            batchNumber: this.batchNumber\r\n          }\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          this.contactsList = res.data.data || []\r\n        }\r\n      } catch (error) {\r\n        console.warn('获取联系人失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 入馆签到\r\n    async signUp() {\r\n      if (this.isSigningUp || this.isSigned) return\r\n      \r\n      this.isSigningUp = true\r\n      \r\n      try {\r\n        uni.showLoading({\r\n          title: '获取位置信息中',\r\n          mask: true\r\n        })\r\n        \r\n        // 获取位置信息\r\n        const location = await this.getCurrentLocation()\r\n        \r\n        uni.hideLoading()\r\n        \r\n        // 验证位置\r\n        const locationRes = await this.$myRequest({\r\n          url: '/web/common/checkLocation',\r\n          method: 'post',\r\n          data: {\r\n            latitude: location.latitude,\r\n            longitude: location.longitude\r\n          }\r\n        })\r\n        \r\n        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {\r\n          this.signResult = 'fail'\r\n          this.showSignModal = true\r\n          return\r\n        }\r\n        \r\n        // 执行签到\r\n        const signRes = await this.$myRequest({\r\n          url: '/web/venueSession/venueSign',\r\n          method: 'post',\r\n          data: {\r\n            venueSessionId: this.venueSessionId\r\n          }\r\n        })\r\n        \r\n        if (signRes.code === 200) {\r\n          this.isSigned = true\r\n          this.signResult = 'success'\r\n          this.showSignModal = true\r\n        } else {\r\n          throw new Error(signRes.msg || '签到失败')\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading()\r\n        console.error('签到失败:', error)\r\n        \r\n        if (error.message && error.message.includes('定位')) {\r\n          this.signResult = 'fail'\r\n          this.showSignModal = true\r\n        } else {\r\n          uni.showToast({\r\n            title: error.message || '签到失败',\r\n            icon: 'error'\r\n          })\r\n        }\r\n      } finally {\r\n        this.isSigningUp = false\r\n      }\r\n    },\r\n\r\n    // 获取当前位置\r\n    getCurrentLocation() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.getLocation({\r\n          type: 'gcj02',\r\n          success: resolve,\r\n          fail: reject\r\n        })\r\n      })\r\n    },\r\n\r\n    // 关闭签到弹窗\r\n    closeSignModal() {\r\n      this.showSignModal = false\r\n    },\r\n\r\n    // 格式化日期\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return ''\r\n\r\n      try {\r\n        const date = new Date(dateStr.replace(/-/g, '/'))\r\n        const weekList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']\r\n        const weekDay = weekList[date.getDay()]\r\n        return `${dateStr} ${weekDay}`\r\n      } catch (error) {\r\n        return dateStr\r\n      }\r\n    },\r\n\r\n    // 格式化时间\r\n    formatTime(timeStr) {\r\n      if (!timeStr) return ''\r\n\r\n      try {\r\n        const date = new Date(timeStr.replace(/-/g, '/'))\r\n        const hours = date.getHours().toString().padStart(2, '0')\r\n        const minutes = date.getMinutes().toString().padStart(2, '0')\r\n        return `${hours}:${minutes}`\r\n      } catch (error) {\r\n        return timeStr\r\n      }\r\n    },\r\n\r\n    // 隐藏身份证号中间部分\r\n    hideIdCard(idCard) {\r\n      if (!idCard || idCard.length < 8) return idCard\r\n\r\n      return idCard.replace(idCard.substring(4, 15), '*******')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.venue-success {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\r\n  .content {\r\n    padding: 20rpx;\r\n    padding-top: 120rpx; // 为固定头部留出空间\r\n\r\n    // 成功状态\r\n    .success-state {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 40rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .success-header {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n\r\n        .success-icon {\r\n          width: 120rpx;\r\n          height: 120rpx;\r\n          margin-bottom: 20rpx;\r\n        }\r\n\r\n        .success-text {\r\n          font-size: 36rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n      }\r\n    }\r\n\r\n    // 参观信息\r\n    .venue-info {\r\n      background: rgba(255, 255, 255, 0.95);\r\n      border-radius: 20rpx;\r\n      padding: 30rpx;\r\n      margin-bottom: 30rpx;\r\n      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\r\n\r\n      .info-header {\r\n        margin-bottom: 20rpx;\r\n\r\n        .venue-name {\r\n          font-size: 32rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n        }\r\n      }\r\n\r\n      .venue-details {\r\n        margin-bottom: 30rpx;\r\n\r\n        .detail-row {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 12rpx;\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          .label {\r\n            font-size: 28rpx;\r\n            color: #666;\r\n            min-width: 140rpx;\r\n          }\r\n\r\n          .value {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n            flex: 1;\r\n\r\n            &.important {\r\n              color: #1976d2;\r\n              font-weight: 600;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .contacts-list {\r\n        margin-bottom: 30rpx;\r\n\r\n        .contacts-title {\r\n          font-size: 28rpx;\r\n          font-weight: 600;\r\n          color: #333;\r\n          margin-bottom: 16rpx;\r\n        }\r\n\r\n        .contact-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: center;\r\n          padding: 12rpx 0;\r\n          border-bottom: 1rpx solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .contact-name {\r\n            font-size: 26rpx;\r\n            color: #333;\r\n            font-weight: 500;\r\n          }\r\n\r\n          .contact-id {\r\n            font-size: 24rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n\r\n      .tips-section {\r\n        display: flex;\r\n        align-items: flex-start;\r\n        padding: 20rpx;\r\n        background: #f8f9fa;\r\n        border-radius: 12rpx;\r\n\r\n        .tips-icon {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n          margin-right: 12rpx;\r\n          margin-top: 4rpx;\r\n          background: url('/static/img/common/warning.png') center/contain no-repeat;\r\n        }\r\n\r\n        .tips-text {\r\n          flex: 1;\r\n          font-size: 26rpx;\r\n          color: #666;\r\n          line-height: 1.5;\r\n\r\n          .highlight {\r\n            color: #1976d2;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 签到区域\r\n    .signin-section {\r\n      .signin-btn {\r\n        width: 100%;\r\n        height: 88rpx;\r\n        background: linear-gradient(135deg, #4ecdc4, #44a08d);\r\n        color: white;\r\n        border-radius: 44rpx;\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n        border: none;\r\n\r\n        &::after {\r\n          border: none;\r\n        }\r\n\r\n        &.signed {\r\n          background: #e8f5e8;\r\n          color: #388e3c;\r\n        }\r\n\r\n        &:disabled {\r\n          opacity: 0.6;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 签到结果弹窗\r\n  .sign-modal {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: rgba(0, 0, 0, 0.5);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 1000;\r\n\r\n    .modal-dialog {\r\n      background: white;\r\n      border-radius: 20rpx;\r\n      padding: 60rpx 40rpx;\r\n      margin: 40rpx;\r\n      text-align: center;\r\n\r\n      &.fail {\r\n        .modal-icon {\r\n          background: url('/static/img/common/location-error.png') center/contain no-repeat;\r\n        }\r\n      }\r\n\r\n      .modal-icon {\r\n        width: 120rpx;\r\n        height: 120rpx;\r\n        margin: 0 auto 30rpx;\r\n        background: url('/static/img/common/success.png') center/contain no-repeat;\r\n      }\r\n\r\n      .modal-text {\r\n        font-size: 32rpx;\r\n        color: #333;\r\n        line-height: 1.6;\r\n        margin-bottom: 40rpx;\r\n\r\n        .highlight {\r\n          color: #1976d2;\r\n          font-weight: 500;\r\n        }\r\n      }\r\n\r\n      .modal-btn {\r\n        padding: 20rpx 60rpx;\r\n        background: linear-gradient(135deg, #667eea, #764ba2);\r\n        color: white;\r\n        border-radius: 40rpx;\r\n        font-size: 32rpx;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.venue-success {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.content {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>\r\n", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/schemesuccess/venuesuccess.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;;AA0GA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,WAAW,CAAE;AAAA,MACb,cAAc,CAAE;AAAA;AAAA,MAGhB,gBAAgB;AAAA,MAChB,aAAa;AAAA;AAAA,MAGb,UAAU;AAAA,MACV,aAAa;AAAA,MACb,eAAe;AAAA,MACf,YAAY;AAAA;AAAA;AAAA,MAGZ,oBAAoB;AAAA,IACtB;AAAA,EACD;AAAA,EAED,OAAO,SAAS;AACd,SAAK,iBAAiB,QAAQ;AAC9B,SAAK,cAAc,QAAQ;AAE3B,SAAK,SAAS;AAAA,EACf;AAAA,EAED,WAAW;AACT,SAAK,kBAAkB;AAAA,EACxB;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,WAAW;AACf,UAAI;AAEF,cAAM,KAAK,cAAc;AAGzB,cAAM,KAAK,aAAa;AAAA,MACxB,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,mDAAc,UAAU,KAAK;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AAEF,cAAM,aAAa,MAAM,KAAK,oBAAoB;AAClD,aAAK,qBAAqB;AAG1B,cAAM,KAAK,oBAAoB,GAAG;AAAA,MAClC,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,QAAA,mDAAa,WAAW,KAAK;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACxB,UAAI,KAAK,qBAAqB,GAAG;AAC/B,YAAI;AACF,gBAAM,KAAK,oBAAoB,KAAK,kBAAkB;AAAA,QACtD,SAAO,OAAO;AACdA,wBAAAA,MAAa,MAAA,QAAA,mDAAA,WAAW,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACpB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,oBAAoB;AAAA,UACtB,SAAS,CAAC,QAAQ,QAAQ,IAAI,KAAK;AAAA,UACnC,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB,OAAO;AACzB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,oBAAoB;AAAA,UACtB;AAAA,UACA,SAAS;AAAA,UACT,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,eAAe;AACnB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,gBAAgB,KAAK;AAAA,UACvB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,OAAO,IAAI,KAAK;AACtB,eAAK,YAAY;AAAA,YACf,GAAG;AAAA,YACH,MAAM,KAAK,WAAW,KAAK,iBAAiB;AAAA,YAC5C,gBAAgB,KAAK,WAAW,KAAK,cAAc;AAAA,YACnD,cAAc,KAAK,WAAW,KAAK,YAAY;AAAA,UACjD;AAGA,eAAK,WAAW,KAAK,cAAc;AAGnC,gBAAM,KAAK,gBAAgB;AAAA,QAC7B;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,mDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,kBAAkB;AACtB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,aAAa,KAAK;AAAA,UACpB;AAAA,SACD;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,eAAK,eAAe,IAAI,KAAK,QAAQ,CAAC;AAAA,QACxC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAa,MAAA,QAAA,mDAAA,YAAY,KAAK;AAAA,MAChC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,SAAS;AACb,UAAI,KAAK,eAAe,KAAK;AAAU;AAEvC,WAAK,cAAc;AAEnB,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAGD,cAAM,WAAW,MAAM,KAAK,mBAAmB;AAE/CA,sBAAAA,MAAI,YAAY;AAGhB,cAAM,cAAc,MAAM,KAAK,WAAW;AAAA,UACxC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,UAAU,SAAS;AAAA,YACnB,WAAW,SAAS;AAAA,UACtB;AAAA,SACD;AAED,YAAI,YAAY,SAAS,OAAO,CAAC,YAAY,KAAK,KAAK,UAAU;AAC/D,eAAK,aAAa;AAClB,eAAK,gBAAgB;AACrB;AAAA,QACF;AAGA,cAAM,UAAU,MAAM,KAAK,WAAW;AAAA,UACpC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,gBAAgB,KAAK;AAAA,UACvB;AAAA,SACD;AAED,YAAI,QAAQ,SAAS,KAAK;AACxB,eAAK,WAAW;AAChB,eAAK,aAAa;AAClB,eAAK,gBAAgB;AAAA,eAChB;AACL,gBAAM,IAAI,MAAM,QAAQ,OAAO,MAAM;AAAA,QACvC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAI,YAAY;AAChBA,sBAAAA,MAAc,MAAA,SAAA,mDAAA,SAAS,KAAK;AAE5B,YAAI,MAAM,WAAW,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjD,eAAK,aAAa;AAClB,eAAK,gBAAgB;AAAA,eAChB;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,MAAM,WAAW;AAAA,YACxB,MAAM;AAAA,WACP;AAAA,QACH;AAAA,MACF,UAAU;AACR,aAAK,cAAc;AAAA,MACrB;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB;AACnB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,YAAY;AAAA,UACd,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACf,WAAK,gBAAgB;AAAA,IACtB;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,cAAM,WAAW,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAC1D,cAAM,UAAU,SAAS,KAAK,OAAM,CAAE;AACtC,eAAO,GAAG,OAAO,IAAI,OAAO;AAAA,MAC5B,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,SAAS;AAClB,UAAI,CAAC;AAAS,eAAO;AAErB,UAAI;AACF,cAAM,OAAO,IAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG,CAAC;AAChD,cAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,cAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,eAAO,GAAG,KAAK,IAAI,OAAO;AAAA,MAC1B,SAAO,OAAO;AACd,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,QAAQ;AACjB,UAAI,CAAC,UAAU,OAAO,SAAS;AAAG,eAAO;AAEzC,aAAO,OAAO,QAAQ,OAAO,UAAU,GAAG,EAAE,GAAG,SAAS;AAAA,IAC1D;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClXA,GAAG,WAAW,eAAe;"}