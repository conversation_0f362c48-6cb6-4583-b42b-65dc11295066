import { request } from '../utils/request.js'

// 获取联系人列表
export function getContactList() {
  return request({
    url: '/apitp/contact/list',
    method: 'GET'
  })
}

// 添加联系人
export function addContact(contactData) {
  const { name, phone, idCard, isDefault } = contactData
  
  return request({
    url: '/apitp/contact/add',
    method: 'POST',
    data: {
      name,
      phone,
      idCard,
      isDefault: isDefault || false
    }
  })
}

// 更新联系人
export function updateContact(contactId, contactData) {
  const { name, phone, idCard, isDefault } = contactData
  
  return request({
    url: `/apitp/contact/update/${contactId}`,
    method: 'PUT',
    data: {
      name,
      phone,
      idCard,
      isDefault
    }
  })
}

// 删除联系人
export function deleteContact(contactId) {
  return request({
    url: `/apitp/contact/delete/${contactId}`,
    method: 'DELETE'
  })
}

// 获取联系人详情
export function getContactDetail(contactId) {
  return request({
    url: `/apitp/contact/detail/${contactId}`,
    method: 'GET'
  })
}

// 设置默认联系人
export function setDefaultContact(contactId) {
  return request({
    url: `/apitp/contact/setDefault/${contactId}`,
    method: 'PUT'
  })
}

// 批量删除联系人
export function batchDeleteContacts(contactIds) {
  return request({
    url: '/apitp/contact/batchDelete',
    method: 'DELETE',
    data: { contactIds }
  })
}

// 验证联系人信息
export function validateContact(contactData) {
  return request({
    url: '/apitp/contact/validate',
    method: 'POST',
    data: contactData
  })
}

// 导入联系人
export function importContacts(contactsData) {
  return request({
    url: '/apitp/contact/import',
    method: 'POST',
    data: { contacts: contactsData }
  })
}

// 导出联系人
export function exportContacts() {
  return request({
    url: '/apitp/contact/export',
    method: 'GET'
  })
}