/**
 * 加密工具类
 * 适配uni-app环境
 */

/**
 * Base64编码
 * @param {string} text 要编码的文本
 * @returns {string} Base64编码后的文本
 */
export function encryptBase64(text) {
  try {
    return uni.base64Encode(text)
  } catch (error) {
    console.error('Base64编码失败:', error)
    return text
  }
}

/**
 * AES加密
 * @param {string} text 要加密的文本
 * @param {string} key 加密密钥
 * @returns {string} 加密后的文本
 */
export function encryptWithAes(text, key) {
  try {
    // 在uni-app环境中，我们使用简化的AES加密实现
    // 实际项目中应该使用专门的AES加密库

    // 这里返回base64编码作为简化实现
    // 生产环境中应该使用真正的AES加密
    return uni.base64Encode(text + key)
  } catch (error) {
    console.error('AES加密失败:', error)
    return text
  }
}

/**
 * 生成AES密钥
 * @returns {string} 生成的AES密钥
 */
export function generateAesKey() {
  try {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''

    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }

    return result
  } catch (error) {
    console.error('生成AES密钥失败:', error)
    return 'defaultkey12345678901234567890'
  }
}

// 默认导出
export default {
  encryptBase64,
  encryptWithAes,
  generateAesKey
}