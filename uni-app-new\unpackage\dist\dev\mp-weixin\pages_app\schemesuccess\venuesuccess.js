"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "VenueSuccess",
  data() {
    return {
      venueInfo: {},
      contactsList: [],
      // 参数
      venueSessionId: null,
      batchNumber: null,
      // 状态
      isSigned: false,
      isSigningUp: false,
      showSignModal: false,
      signResult: "success",
      // success | fail
      // 屏幕亮度
      originalBrightness: 0
    };
  },
  onLoad(options) {
    this.venueSessionId = options.id;
    this.batchNumber = options.batchNumber;
    this.initPage();
  },
  onUnload() {
    this.restoreBrightness();
  },
  methods: {
    // 初始化页面
    async initPage() {
      try {
        await this.setBrightness();
        await this.getVenueInfo();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/venuesuccess.vue:150", "初始化失败:", error);
      }
    },
    // 设置屏幕亮度
    async setBrightness() {
      try {
        const brightness = await this.getScreenBrightness();
        this.originalBrightness = brightness;
        await this.setScreenBrightness(0.5);
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages_app/schemesuccess/venuesuccess.vue:164", "设置亮度失败:", error);
      }
    },
    // 恢复屏幕亮度
    async restoreBrightness() {
      if (this.originalBrightness > 0) {
        try {
          await this.setScreenBrightness(this.originalBrightness);
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages_app/schemesuccess/venuesuccess.vue:174", "恢复亮度失败:", error);
        }
      }
    },
    // 获取屏幕亮度
    getScreenBrightness() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getScreenBrightness({
          success: (res) => resolve(res.value),
          fail: reject
        });
      });
    },
    // 设置屏幕亮度
    setScreenBrightness(value) {
      return new Promise((resolve, reject) => {
        common_vendor.index.setScreenBrightness({
          value,
          success: resolve,
          fail: reject
        });
      });
    },
    // 获取参观信息
    async getVenueInfo() {
      try {
        const res = await this.$myRequest({
          url: "/web/venueSession/personalCenterVenueByVenueSessionId",
          method: "get",
          data: {
            venueSessionId: this.venueSessionId
          }
        });
        if (res.code === 200) {
          const data = res.data.data;
          this.venueInfo = {
            ...data,
            date: this.formatDate(data.venueArrangedDate),
            venueStartTime: this.formatTime(data.venueStartTime),
            venueEndTime: this.formatTime(data.venueEndTime)
          };
          this.isSigned = data.signState === "1";
          await this.getContactsList();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/venuesuccess.vue:227", "获取参观信息失败:", error);
        common_vendor.index.showToast({
          title: "获取信息失败",
          icon: "error"
        });
      }
    },
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: "/web/venueSession/getVenueSubscribePeoples",
          method: "get",
          data: {
            batchNumber: this.batchNumber
          }
        });
        if (res.code === 200) {
          this.contactsList = res.data.data || [];
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages_app/schemesuccess/venuesuccess.vue:250", "获取联系人失败:", error);
      }
    },
    // 入馆签到
    async signUp() {
      if (this.isSigningUp || this.isSigned)
        return;
      this.isSigningUp = true;
      try {
        common_vendor.index.showLoading({
          title: "获取位置信息中",
          mask: true
        });
        const location = await this.getCurrentLocation();
        common_vendor.index.hideLoading();
        const locationRes = await this.$myRequest({
          url: "/web/common/checkLocation",
          method: "post",
          data: {
            latitude: location.latitude,
            longitude: location.longitude
          }
        });
        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {
          this.signResult = "fail";
          this.showSignModal = true;
          return;
        }
        const signRes = await this.$myRequest({
          url: "/web/venueSession/venueSign",
          method: "post",
          data: {
            venueSessionId: this.venueSessionId
          }
        });
        if (signRes.code === 200) {
          this.isSigned = true;
          this.signResult = "success";
          this.showSignModal = true;
        } else {
          throw new Error(signRes.msg || "签到失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/venuesuccess.vue:305", "签到失败:", error);
        if (error.message && error.message.includes("定位")) {
          this.signResult = "fail";
          this.showSignModal = true;
        } else {
          common_vendor.index.showToast({
            title: error.message || "签到失败",
            icon: "error"
          });
        }
      } finally {
        this.isSigningUp = false;
      }
    },
    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getLocation({
          type: "gcj02",
          success: resolve,
          fail: reject
        });
      });
    },
    // 关闭签到弹窗
    closeSignModal() {
      this.showSignModal = false;
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr.replace(/-/g, "/"));
        const weekList = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        const weekDay = weekList[date.getDay()];
        return `${dateStr} ${weekDay}`;
      } catch (error) {
        return dateStr;
      }
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8)
        return idCard;
      return idCard.replace(idCard.substring(4, 15), "*******");
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "参观凭证",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333",
      isFixed: true
    }),
    b: common_assets._imports_0$4,
    c: common_vendor.t($data.venueInfo.date),
    d: common_vendor.t($data.venueInfo.venueStartTime),
    e: common_vendor.t($data.venueInfo.venueEndTime),
    f: common_vendor.t($data.venueInfo.subscribeType),
    g: $data.venueInfo.linkmanName
  }, $data.venueInfo.linkmanName ? {
    h: common_vendor.t($data.venueInfo.linkmanName)
  } : {}, {
    i: $data.contactsList.length > 0
  }, $data.contactsList.length > 0 ? {
    j: common_vendor.f($data.contactsList, (contact, index, i0) => {
      return {
        a: common_vendor.t(contact.linkmanName),
        b: common_vendor.t($options.hideIdCard(contact.linkmanCertificate)),
        c: contact.linkId
      };
    })
  } : {}, {
    k: common_vendor.t($data.isSigned ? "已签到" : $data.isSigningUp ? "签到中..." : "入馆签到"),
    l: common_vendor.n({
      signed: $data.isSigned
    }),
    m: common_vendor.o((...args) => $options.signUp && $options.signUp(...args)),
    n: $data.isSigningUp || $data.isSigned,
    o: $data.showSignModal
  }, $data.showSignModal ? common_vendor.e({
    p: $data.signResult === "success"
  }, $data.signResult === "success" ? {} : {}, {
    q: common_vendor.t($data.signResult === "success" ? "确定" : "返回"),
    r: common_vendor.o((...args) => $options.closeSignModal && $options.closeSignModal(...args)),
    s: common_vendor.n({
      fail: $data.signResult === "fail"
    })
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8101f867"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/schemesuccess/venuesuccess.js.map
