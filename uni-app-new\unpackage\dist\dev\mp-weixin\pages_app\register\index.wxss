
.register-container.data-v-594d5248 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}
.register-content.data-v-594d5248 {
  padding: 40rpx;
  min-height: calc(100vh - 88px);
  display: flex;
  flex-direction: column;
}

/* Logo区域 */
.logo-section.data-v-594d5248 {
  text-align: center;
  margin-bottom: 60rpx;
  margin-top: 40rpx;
}
.logo.data-v-594d5248 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.app-name.data-v-594d5248 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}
.subtitle.data-v-594d5248 {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单区域 */
.form-section.data-v-594d5248 {
  flex: 1;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

/* 输入组 */
.input-group.data-v-594d5248 {
  margin-bottom: 40rpx;
}
.input-wrapper.data-v-594d5248 {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}
.input-wrapper.data-v-594d5248:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.input-icon.data-v-594d5248 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 32rpx;
}
.form-input.data-v-594d5248 {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
}
.form-input.data-v-594d5248::-webkit-input-placeholder {
  color: #adb5bd;
}
.form-input.data-v-594d5248::placeholder {
  color: #adb5bd;
}

/* 验证码相关 */
.verification-wrapper.data-v-594d5248 {
  padding-right: 20rpx;
}
.verification-input.data-v-594d5248 {
  flex: 1;
}
.verification-btn.data-v-594d5248 {
  padding: 0 24rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #667eea;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}
.verification-btn.disabled.data-v-594d5248 {
  background: #adb5bd;
  color: #ffffff;
}
.verification-btn.data-v-594d5248:not(.disabled):active {
  background: #5a67d8;
}

/* 图形验证码 */
.captcha-wrapper.data-v-594d5248 {
  padding-right: 20rpx;
}
.captcha-input.data-v-594d5248 {
  flex: 1;
}
.captcha-image.data-v-594d5248 {
  width: 120rpx;
  height: 60rpx;
  margin-left: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e9ecef;
}
.captcha-img.data-v-594d5248 {
  width: 100%;
  height: 100%;
}
.refresh-text.data-v-594d5248 {
  font-size: 20rpx;
  color: #6c757d;
}

/* 错误提示 */
.error-text.data-v-594d5248 {
  display: block;
  color: #dc3545;
  font-size: 24rpx;
  margin-top: 10rpx;
  margin-left: 80rpx;
}

/* 密码提示 */
.password-hint.data-v-594d5248 {
  display: block;
  color: #6c757d;
  font-size: 22rpx;
  margin-top: 10rpx;
  margin-left: 80rpx;
}

/* 用户协议 */
.agreement-section.data-v-594d5248 {
  margin-bottom: 40rpx;
}
.agreement-checkbox.data-v-594d5248 {
  display: flex;
  align-items: flex-start;
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
}
.agreement-text.data-v-594d5248 {
  margin-left: 20rpx;
  flex: 1;
}
.link-text.data-v-594d5248 {
  color: #667eea;
  text-decoration: underline;
}

/* 按钮区域 */
.button-section.data-v-594d5248 {
  margin-bottom: 40rpx;
}
.register-button.data-v-594d5248 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s ease;
}
.register-button.disabled.data-v-594d5248 {
  background: #adb5bd;
  color: #ffffff;
}
.register-button.data-v-594d5248:not(.disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 登录链接 */
.login-link-section.data-v-594d5248 {
  text-align: center;
}
.login-link.data-v-594d5248 {
  color: #667eea;
  font-size: 28rpx;
  text-decoration: underline;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.register-content.data-v-594d5248 {
    padding: 30rpx;
}
.form-section.data-v-594d5248 {
    padding: 40rpx 30rpx;
}
.logo.data-v-594d5248 {
    width: 100rpx;
    height: 100rpx;
}
.app-name.data-v-594d5248 {
    font-size: 42rpx;
}
}

/* 平台兼容性 */
.input-wrapper.data-v-594d5248:focus-within {
  border-color: #667eea;
}








/* 通用工具类 */
.flex.data-v-594d5248 {
  display: flex;
}
.align-center.data-v-594d5248 {
  align-items: center;
}
.justify-center.data-v-594d5248 {
  justify-content: center;
}
.text-center.data-v-594d5248 {
  text-align: center;
}

/* 图标字体 - 使用简单文本图标 */
.icon-user.data-v-594d5248::before {
  content: '👤';
}
.icon-phone.data-v-594d5248::before {
  content: '📱';
}
.icon-code.data-v-594d5248::before {
  content: '🔢';
}
.icon-password.data-v-594d5248::before {
  content: '🔒';
}
.icon-shield.data-v-594d5248::before {
  content: '🛡️';
}
