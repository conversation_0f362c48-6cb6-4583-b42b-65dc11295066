{"version": 3, "file": "privacy-popup.js", "sources": ["components/privacy-popup/privacy-popup.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovbXlfcHJvamVjdC9rZWppZ3Vhbi91bmktYXBwLW5ldy9jb21wb25lbnRzL3ByaXZhY3ktcG9wdXAvcHJpdmFjeS1wb3B1cC52dWU"], "sourcesContent": ["<template>\r\n  <uni-popup ref=\"popup\" type=\"center\" :mask-click=\"false\">\r\n    <view class=\"privacy-popup\">\r\n      <view class=\"privacy-header\">\r\n        <text class=\"privacy-title\">隐私保护指引</text>\r\n      </view>\r\n      <view class=\"privacy-content\">\r\n        <text class=\"privacy-text\">\r\n          感谢您使用宝安科技馆预约服务平台！我们非常重视您的隐私保护和个人信息安全。\r\n          在您使用我们的服务前，请您仔细阅读并充分理解《隐私政策》的各项条款。\r\n        </text>\r\n        <text class=\"privacy-text\">\r\n          我们会严格按照法律法规要求和隐私政策使用您的个人信息，为您提供更好的服务体验。\r\n        </text>\r\n      </view>\r\n      <view class=\"privacy-buttons\">\r\n        <button class=\"privacy-btn reject-btn\" @tap=\"handleReject\">拒绝</button>\r\n        <button class=\"privacy-btn agree-btn\" @tap=\"handleAgree\">同意</button>\r\n      </view>\r\n    </view>\r\n  </uni-popup>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'PrivacyPopup',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      default: 'privacy-popup'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      isShow: false\r\n    }\r\n  },\r\n  mounted() {\r\n    // 检查是否需要显示隐私政策弹窗\r\n    this.checkPrivacyStatus()\r\n  },\r\n  watch: {\r\n    // 监听父组件传入的显示状态\r\n    '$parent.showPrivacy'(newVal) {\r\n      if (newVal) {\r\n        this.show()\r\n      } else {\r\n        this.hide()\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    checkPrivacyStatus() {\r\n      // 检查用户是否已经同意隐私政策\r\n      try {\r\n        const privacyAgreed = uni.getStorageSync('privacy_agreed')\r\n        if (!privacyAgreed) {\r\n          // 延迟显示，确保页面渲染完成\r\n          this.$nextTick(() => {\r\n            this.show()\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('检查隐私状态失败:', error)\r\n        // 出错时默认显示隐私政策\r\n        this.show()\r\n      }\r\n    },\r\n    show() {\r\n      this.isShow = true\r\n      this.$refs.popup.open()\r\n    },\r\n    hide() {\r\n      this.isShow = false\r\n      this.$refs.popup.close()\r\n    },\r\n    handleAgree() {\r\n      try {\r\n        // 用户同意隐私政策\r\n        uni.setStorageSync('privacy_agreed', true)\r\n        uni.setStorageSync('privacy_agreed_time', Date.now())\r\n        \r\n        this.hide()\r\n        \r\n        // 触发同意事件\r\n        this.$emit('agree')\r\n        \r\n        // 全局事件通知\r\n        uni.$emit && uni.$emit('privacyAgree', true)\r\n        \r\n        uni.showToast({\r\n          title: '感谢您的信任',\r\n          icon: 'success',\r\n          duration: 1500\r\n        })\r\n      } catch (error) {\r\n        console.error('保存隐私政策同意状态失败:', error)\r\n        uni.showToast({\r\n          title: '操作失败，请重试',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    handleReject() {\r\n      // 用户拒绝隐私政策\r\n      this.hide()\r\n      \r\n      // 触发拒绝事件\r\n      this.$emit('reject')\r\n      \r\n      // 全局事件通知\r\n      uni.$emit('privacyReject', false)\r\n      \r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '拒绝隐私政策将无法使用相关功能，您可以稍后在设置中重新选择。',\r\n        showCancel: false,\r\n        confirmText: '知道了'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.privacy-popup {\r\n  width: 600rpx;\r\n  background: #ffffff;\r\n  border-radius: 20rpx;\r\n  padding: 40rpx;\r\n  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.privacy-header {\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.privacy-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #333333;\r\n}\r\n\r\n.privacy-content {\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.privacy-text {\r\n  display: block;\r\n  font-size: 28rpx;\r\n  color: #666666;\r\n  line-height: 1.6;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.privacy-buttons {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 20rpx;\r\n}\r\n\r\n.privacy-btn {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  border-radius: 40rpx;\r\n  font-size: 30rpx;\r\n  border: none;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.reject-btn {\r\n  background: #f5f5f5;\r\n  color: #666666;\r\n}\r\n\r\n.agree-btn {\r\n  background: #007aff;\r\n  color: #ffffff;\r\n}\r\n\r\n.privacy-btn:active {\r\n  opacity: 0.8;\r\n}\r\n</style>", "import Component from 'D:/my_project/kejiguan/uni-app-new/components/privacy-popup/privacy-popup.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAwBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,IAAI;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,QAAQ;AAAA,IACV;AAAA,EACD;AAAA,EACD,UAAU;AAER,SAAK,mBAAmB;AAAA,EACzB;AAAA,EACD,OAAO;AAAA;AAAA,IAEL,sBAAsB,QAAQ;AAC5B,UAAI,QAAQ;AACV,aAAK,KAAK;AAAA,aACL;AACL,aAAK,KAAK;AAAA,MACZ;AAAA,IACF;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,qBAAqB;AAEnB,UAAI;AACF,cAAM,gBAAgBA,cAAAA,MAAI,eAAe,gBAAgB;AACzD,YAAI,CAAC,eAAe;AAElB,eAAK,UAAU,MAAM;AACnB,iBAAK,KAAK;AAAA,WACX;AAAA,QACH;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,oDAAA,aAAa,KAAK;AAEhC,aAAK,KAAK;AAAA,MACZ;AAAA,IACD;AAAA,IACD,OAAO;AACL,WAAK,SAAS;AACd,WAAK,MAAM,MAAM,KAAK;AAAA,IACvB;AAAA,IACD,OAAO;AACL,WAAK,SAAS;AACd,WAAK,MAAM,MAAM,MAAM;AAAA,IACxB;AAAA,IACD,cAAc;AACZ,UAAI;AAEFA,4BAAI,eAAe,kBAAkB,IAAI;AACzCA,sBAAAA,MAAI,eAAe,uBAAuB,KAAK,IAAG,CAAE;AAEpD,aAAK,KAAK;AAGV,aAAK,MAAM,OAAO;AAGlBA,sBAAAA,MAAI,SAASA,cAAAA,MAAI,MAAM,gBAAgB,IAAI;AAE3CA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,SACX;AAAA,MACD,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,oDAAc,iBAAiB,KAAK;AACpCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA,IACD,eAAe;AAEb,WAAK,KAAK;AAGV,WAAK,MAAM,QAAQ;AAGnBA,0BAAI,MAAM,iBAAiB,KAAK;AAEhCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,OACd;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;ACxHA,GAAG,gBAAgB,SAAS;"}