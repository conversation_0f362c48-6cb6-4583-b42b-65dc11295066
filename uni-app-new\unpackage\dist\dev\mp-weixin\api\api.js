"use strict";
const common_vendor = require("../common/vendor.js");
const utils_signatureUtil = require("../utils/signatureUtil.js");
const config = require("../config.js");
const utils_request = require("../utils/request.js");
const utils_auth = require("../utils/auth.js");
const appId = config.config.appId;
function getImages(imagePath) {
  if (!imagePath || imagePath === "") {
    return "";
  }
  if (imagePath.startsWith("http://") || imagePath.startsWith("https://") || imagePath.startsWith("data:image/jpeg;base64,")) {
    return imagePath;
  }
  return utils_request.baseUrl + imagePath;
}
function myRequest(options) {
  const token = utils_auth.getToken();
  if (!options.noLoadingFlag) {
    common_vendor.index.showLoading({
      title: "正在加载中..."
    });
  }
  return new Promise((resolve, reject) => {
    const requestOptions = {
      url: options.url,
      method: options.method || "GET",
      data: options.data || {},
      timeout: options.timeout || 1e4
    };
    if (!options.url.includes(`/wx/user/${appId}/login`)) {
      const { signature, timestamp } = utils_signatureUtil.signatureGenerate(requestOptions);
      requestOptions.headers = {
        Authorization: "wx " + token,
        sign: signature,
        timestamp
      };
    }
    utils_request.request(requestOptions).then((response) => {
      resolve(response);
    }).catch((error) => {
      common_vendor.index.__f__("error", "at api/api.js:58", "请求失败:", error);
      common_vendor.index.showToast({
        title: "请求接口失败！",
        icon: "error"
      });
      reject(error);
    }).finally(() => {
      common_vendor.index.hideLoading();
    });
  });
}
exports.getImages = getImages;
exports.myRequest = myRequest;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/api.js.map
