define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'report/reportvenue/getVenueReport' + location.search,
                    show_url: 'report/reportvenue/show', //  添加
                    table: 'venue',
                }
            });
    var Controller = {
        index: function () {


            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                search:false,
                showExport: false,
                searchFormVisible: true,
                columns: [
                    [
                        {field: 'venueState', title: __('venueState'),operate:false,formatter: function(value, row, index) {
                            return value == 1 ? '正常' : '关闭';
                        }},
                        {field: 'venueStartTime', title: __('venueTime'),operate:false,    formatter: function(value, row, index) {
                            if (!value) return '-';
                            var hour = new Date(value.replace(/-/g, '/')).getHours(); // 修复 Safari 不支持 yyyy-mm-dd 格式问题
                            return (hour < 12 ? '上午场' : '下午场');
                        }},
                        {field: 'venueStartTime', title: __('venueStartTime'),operate:false,},
                        {field: 'venueEndTime', title: __('venueEndTime'),operate:false},
                        {field: 'week', title: __('week'),operate:false,    formatter: function (value, row, index) {
                            var weekList = ["星期", "周一", "周二", "周三", "周四", "周五", "周六","周日"];
                            return weekList[value] !== undefined ? weekList[value] : '-';
                        }},
                        {field: 'venuePoll', title: __('usevenuePoll'),operate:false,    formatter: function (value, row, index) {
                            // 计算实际使用场地数，防止空值导致 NaN
                            var poll = Number(row.venuePoll) || 0;
                            var votes = Number(row.inventoryVotes) || 0;
                            return poll - votes;
                        }},
                        {field: 'start_end_Date', title: __('Venue_time'), operate:'RANGE', addclass:'datetimerange',visible:false,

                        },
                        // {field: 'operate', title: '查看预约情况', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate},
                        {field: 'operate', title: '查看预约情况', table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'show',
                                    text: '查看',
                                    title: '查看详情',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-eye',
                                    url: $.fn.bootstrapTable.defaults.extend.show_url
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            //保留原始 daterangepicker 初始化逻辑
            Form.events.daterangepicker = (function (origFunc) {
                return function (form) {
                    // 先执行原逻辑
                    origFunc && origFunc.apply(this, arguments);

                    // 然后追加参数
                    $(".datetimerange", form).each(function () {
                        // 原有配置
                        var old = $(this).attr('data-daterangepicker-options') || '{}';
                        var config = $.extend(true, {}, JSON.parse(old), {
                            showDropdowns: true,
                        });
                        $(this).attr("data-daterangepicker-options", JSON.stringify(config));
                    });
                };
            })(Form.events.daterangepicker);

            // 通用导出函数
            function handleExport(buttonClass, action) {
                $(document).on('click', buttonClass, function () {
                    var options = table.bootstrapTable('getOptions');
                    var params = options.queryParams({}); // 获取搜索参数
                    var query = $.param(params); // 序列化参数
                    var url = Fast.api.fixurl('report/reportvenue/' + action) + "?" + query;
                    window.open(url);
                });
            }

            // 初始化所有导出按钮事件绑定
            handleExport('.btn-export', 'export'); // 导出全部
            handleExport('.btn-exportdailySummary', 'exportdailySummary'); // 日汇总
            handleExport('.btn-monthlySummaryExport', 'monthlySummaryExport'); // 月汇总
            handleExport('.btn-yearlySummaryExport', 'yearlySummaryExport'); // 年汇总

           

        },
        show: function () {
            var ids = Fast.api.query('ids'); // 获取主表id参数
            
            var table = $("#table");
            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.show_url + '?ids=' + ids, // 子表接口,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                search:false,
                showExport: false,
                searchFormVisible: true,
                columns: [
                    [



                        {field: 'l.linkman_name', title: __('linkman_name'), operate:  'LIKE',
                            formatter: function (value, row, index) {
                                return row.linkman_name;
                              }
                        },
                        {field: 'linkman_phone', title: __('linkman_phone'), operate: false},
                        {field: 'linkmanAge', title: __('linkmanAge'), operate: false},
                        {field: 'linkmanCertificate', title: __('linkmanCertificate'), operate: false},
                        {field: 'ugentPhone', title: __('ugentPhone'), operate: false},
                        {field: 'signState', title: __('signState'), operate: false},
                        {
                            field: 'linkmanCertificate',
                            title: __('ugReservationnumberentPhone'),
                            operate: false,
                            formatter: function(value, row, index) {
                                if (!value) return '';
                                return value.slice(-6);
                            }
                        },

                    ]
                ]
            });
            // 为表格绑定事件
            Table.api.bindevent(table);

        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
