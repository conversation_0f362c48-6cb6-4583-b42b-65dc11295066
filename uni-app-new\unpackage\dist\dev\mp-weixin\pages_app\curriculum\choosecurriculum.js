"use strict";
const common_vendor = require("../../common/vendor.js");
const api_api = require("../../api/api.js");
require("../../config.js");
const utils_index = require("../../utils/index.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "ChooseCurriculum",
  components: {
    MyHeader: () => "../../components/my-header/my-header2.js"
  },
  data() {
    return {
      courseId: null,
      courseInfo: {},
      contactsList: [],
      selectedContacts: [],
      isSubmitting: false
    };
  },
  computed: {
    canSubmit() {
      return this.courseInfo.inventoryVotes > 0 && this.selectedContacts.length > 0 && !this.isSubmitting;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.courseId = options.id;
      this.getCourseInfo();
      this.getContactsList();
    } else {
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "error"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    getImages: api_api.getImages,
    // 获取课程信息
    async getCourseInfo() {
      try {
        const response = await api_api.myRequest({
          url: "/web/session/getSessionById",
          method: "get",
          data: {
            id: this.courseId
          }
        });
        if (response.data.code === 200) {
          const data = response.data.data;
          data.courseEndTime = utils_index.Utils.changeTime(data.courseEndTime);
          data.courseStartTime = utils_index.Utils.changeTime(data.courseStartTime);
          this.courseInfo = data;
        } else {
          throw new Error(response.data.msg || "获取课程信息失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/curriculum/choosecurriculum.vue:185", "获取课程信息失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取课程信息失败",
          icon: "error"
        });
      }
    },
    // 获取联系人列表
    async getContactsList() {
      try {
        const response = await api_api.myRequest({
          url: "/web/linkman/list",
          method: "get"
        });
        if (response.data.code === 200) {
          this.contactsList = response.data.rows || [];
        } else {
          throw new Error(response.data.msg || "获取联系人列表失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/curriculum/choosecurriculum.vue:207", "获取联系人列表失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取联系人列表失败",
          icon: "error"
        });
      }
    },
    // 选择联系人
    selectContact(contact, index) {
      const position = this.selectedContacts.indexOf(index);
      if (position > -1) {
        this.selectedContacts.splice(position, 1);
      } else {
        this.selectedContacts.push(index);
      }
    },
    // 前往添加联系人
    goToAddContact() {
      common_vendor.index.navigateTo({
        url: "/pages_app/contacts/addcontact"
      });
    },
    // 提交预约
    async submitReservation() {
      if (!this.canSubmit)
        return;
      this.isSubmitting = true;
      try {
        common_vendor.index.showLoading({
          title: "提交中...",
          mask: true
        });
        const selectedPeople = this.selectedContacts.map((index) => {
          return this.contactsList[index].linkId;
        });
        const response = await api_api.myRequest({
          url: "/web/session/subscribe",
          method: "post",
          data: {
            sessionId: this.courseId,
            linkmanIds: selectedPeople.join(",")
          }
        });
        common_vendor.index.hideLoading();
        if (response.data.code === 200) {
          const batchNumber = response.data.data;
          common_vendor.index.redirectTo({
            url: `/pages_app/schemesuccess/curriculumsuccess?batchNumber=${batchNumber}`
          });
        } else {
          throw new Error(response.data.msg || "预约失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages_app/curriculum/choosecurriculum.vue:269", "预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "预约失败",
          icon: "error"
        });
      } finally {
        this.isSubmitting = false;
      }
    },
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8)
        return idCard;
      return idCard.replace(idCard.substring(4, 15), "*******");
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "课程详情",
      isBack: true,
      isShowHome: true,
      background: "transparent",
      color: "#ffffff"
    }),
    b: common_assets._imports_0$6,
    c: $data.courseInfo.courseCover
  }, $data.courseInfo.courseCover ? {
    d: $options.getImages($data.courseInfo.courseCover)
  } : {}, {
    e: common_vendor.t($data.courseInfo.courseName || "课程加载中..."),
    f: common_vendor.t($data.courseInfo.courseAgeProp || "--"),
    g: common_vendor.t($data.courseInfo.courseStartTime || "--"),
    h: common_vendor.t($data.courseInfo.courseEndTime || "--"),
    i: common_vendor.t($data.courseInfo.courseAddress || "--"),
    j: common_vendor.t($data.courseInfo.inventoryVotes || 0),
    k: common_assets._imports_1$2,
    l: $data.courseInfo.courseIntroduce || "暂无课程介绍",
    m: common_assets._imports_1$2,
    n: common_assets._imports_1$2,
    o: $data.contactsList.length > 0
  }, $data.contactsList.length > 0 ? {
    p: common_vendor.f($data.contactsList, (contact, index, i0) => {
      return {
        a: common_vendor.t(contact.linkmanName),
        b: common_vendor.t($options.hideIdCard(contact.linkmanCertificate)),
        c: common_vendor.n({
          "selected": $data.selectedContacts.includes(index)
        }),
        d: contact.linkId,
        e: common_vendor.o(($event) => $options.selectContact(contact, index), contact.linkId)
      };
    })
  } : {
    q: common_vendor.o((...args) => $options.goToAddContact && $options.goToAddContact(...args))
  }, {
    r: common_vendor.t($data.courseInfo.inventoryVotes > 0 ? "立即预约" : "名额已满"),
    s: !$options.canSubmit,
    t: common_vendor.o((...args) => $options.submitReservation && $options.submitReservation(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-110d03ff"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/curriculum/choosecurriculum.js.map
