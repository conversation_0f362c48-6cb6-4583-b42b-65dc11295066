# 计算下下个月开闭馆情况(月底23:00执行)
holidayTask.taskForCalendar()
0 0 23 L * ?  【每月最后一天的 23:00（当地时间）触发任务。】

默认策略：周二闭馆，其它开放；
这个任务：加入数据库已经存在某条记录，他不会覆盖。



# 将排好下一个月的场馆的上午场、下午场(月初1:00执行)
vholidayTask.timeOperateVenue(true)

 → 遍历下月日期 → 判断是否开馆 → 生成场次（上午、下午） → 去重插入

① 计算“下个月”的起止日期： 比如当前是 7月1日，isAuto=true，则生成的是8月1日至8月31日的数据。
② 查询“预约时间段模板”  select constant_value from constant where id = 2 or id = 3





admin/venue/getDetail?date=2025-7-7
步骤  逻辑  说明
①   判断 now > date   如果传入时间是“今天之前”，直接返回空
②   holidaysMapper.selectIsSpecial(date)    获取数据（根据传参）
③   holidays.getIsClose() 是否为 0（闭馆） 是则返回空，不再查数据库
④   执行 SQL 查询 venue 表，筛选 venue_start_time 日期为该日的记录 （date_format(venue_start_time,'%y%m%d') = date_format(#{date},'%y%m%d')）
⑤   条件：del_flag = 0，逻辑删除排除
⑥   返回字段：id, venue_poll, start/end_time, is_open, week, inventory_votes
