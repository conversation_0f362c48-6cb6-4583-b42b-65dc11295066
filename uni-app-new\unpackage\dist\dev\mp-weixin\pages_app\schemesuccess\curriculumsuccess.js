"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "CurriculumSuccess",
  data() {
    return {
      courseInfo: {},
      contactsList: [],
      weekList: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
      // 参数
      courseId: null,
      // 状态
      isSigned: false,
      isSigningUp: false,
      showSignModal: false,
      signResult: "success"
      // success | fail
    };
  },
  onLoad(options) {
    this.courseId = options.id;
    this.getCourseInfo();
  },
  methods: {
    // 获取课程信息
    async getCourseInfo() {
      try {
        const res = await this.$myRequest({
          url: "/web/session/showVoucher",
          method: "get",
          data: {
            courseId: this.courseId
          }
        });
        if (res.code === 200) {
          const data = res.data.data;
          const startTime = data.courseStartTime;
          const date = startTime.slice(0, 10).replace(/-/g, ".") + "  " + this.weekList[new Date(startTime).getDay()];
          this.courseInfo = {
            ...data,
            date,
            courseStartTime: this.formatTime(data.courseStartTime),
            courseEndTime: this.formatTime(data.courseEndTime)
          };
          this.isSigned = data.signState === "1";
          await this.getContactsList();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/curriculumsuccess.vue:167", "获取课程信息失败:", error);
        common_vendor.index.showToast({
          title: "获取信息失败",
          icon: "error"
        });
      }
    },
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: "/web/session/getCourseSubscribePeoples",
          method: "get",
          data: {
            courseId: this.courseId
          }
        });
        if (res.code === 200) {
          this.contactsList = res.data.data || [];
        }
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages_app/schemesuccess/curriculumsuccess.vue:190", "获取联系人失败:", error);
      }
    },
    // 课程签到
    async signUp() {
      if (this.isSigningUp || this.isSigned)
        return;
      this.isSigningUp = true;
      try {
        common_vendor.index.showLoading({
          title: "获取位置信息中",
          mask: true
        });
        const location = await this.getCurrentLocation();
        common_vendor.index.hideLoading();
        const locationRes = await this.$myRequest({
          url: "/web/common/checkLocation",
          method: "post",
          data: {
            latitude: location.latitude,
            longitude: location.longitude
          }
        });
        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {
          this.signResult = "fail";
          this.showSignModal = true;
          return;
        }
        const signRes = await this.$myRequest({
          url: "/web/session/courseSign",
          method: "post",
          data: {
            courseId: this.courseId
          }
        });
        if (signRes.code === 200) {
          this.isSigned = true;
          this.signResult = "success";
          this.showSignModal = true;
        } else {
          throw new Error(signRes.msg || "签到失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages_app/schemesuccess/curriculumsuccess.vue:245", "签到失败:", error);
        if (error.message && error.message.includes("定位")) {
          this.signResult = "fail";
          this.showSignModal = true;
        } else {
          common_vendor.index.showToast({
            title: error.message || "签到失败",
            icon: "error"
          });
        }
      } finally {
        this.isSigningUp = false;
      }
    },
    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getLocation({
          type: "gcj02",
          success: resolve,
          fail: reject
        });
      });
    },
    // 关闭签到弹窗
    closeSignModal() {
      this.showSignModal = false;
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8)
        return idCard;
      return idCard.replace(idCard.substring(4, 15), "*******");
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "课程凭证",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333",
      isFixed: true
    }),
    b: common_assets._imports_0$7,
    c: $data.courseInfo.courseCover,
    d: common_vendor.t($data.courseInfo.courseName),
    e: $data.courseInfo.courseTeacher
  }, $data.courseInfo.courseTeacher ? {
    f: common_vendor.t($data.courseInfo.courseTeacher)
  } : {}, {
    g: $data.courseInfo.courseLocation
  }, $data.courseInfo.courseLocation ? {
    h: common_vendor.t($data.courseInfo.courseLocation)
  } : {}, {
    i: common_vendor.t($data.courseInfo.courseStartTime),
    j: common_vendor.t($data.courseInfo.courseEndTime),
    k: common_vendor.t($data.courseInfo.date),
    l: $data.contactsList.length > 0
  }, $data.contactsList.length > 0 ? {
    m: common_vendor.f($data.contactsList, (contact, index, i0) => {
      return {
        a: common_vendor.t(contact.linkmanName),
        b: common_vendor.t($options.hideIdCard(contact.linkmanCertificate)),
        c: contact.linkId
      };
    })
  } : {}, {
    n: common_vendor.t($data.isSigned ? "已签到" : $data.isSigningUp ? "签到中..." : "课程签到"),
    o: common_vendor.n({
      signed: $data.isSigned
    }),
    p: common_vendor.o((...args) => $options.signUp && $options.signUp(...args)),
    q: $data.isSigningUp || $data.isSigned,
    r: $data.showSignModal
  }, $data.showSignModal ? common_vendor.e({
    s: $data.signResult === "success"
  }, $data.signResult === "success" ? {} : {}, {
    t: common_vendor.t($data.signResult === "success" ? "确定" : "返回"),
    v: common_vendor.o((...args) => $options.closeSignModal && $options.closeSignModal(...args)),
    w: common_vendor.n({
      fail: $data.signResult === "fail"
    })
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-5226135c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/schemesuccess/curriculumsuccess.js.map
