"use strict";
const config = {
  // 开发环境
  development: {
    //baseUrl: 'https://bakjgyyxt.baoan.gov.cn',
    baseUrl: "https://www.j8j0.com/ ",
    debug: true,
    timeout: 1e4
  },
  // 生产环境
  production: {
    baseUrl: "https://www.j8j0.com/ ",
    debug: false,
    timeout: 3e4
  }
};
const getConfig = () => {
  let currentEnv = "production";
  currentEnv = "development";
  return config[currentEnv];
};
const currentConfig = getConfig();
const config$1 = {
  // 基础配置
  baseUrl: currentConfig.baseUrl,
  debug: currentConfig.debug,
  timeout: currentConfig.timeout,
  // 业务配置
  iSzgm: false,
  clientId: "be7052a7e4f802c20df10a8d131adb12",
  // 加密配置
  publicKey: "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==",
  privateKey: "**********",
  // 应用信息
  appInfo: {
    name: "baoanquestacon-app",
    version: "2.0.0",
    logo: "/static/favicon.ico",
    description: "宝安科技馆预约服务平台"
  },
  // 微信小程序配置
  appId: "wx7fdbf0566b7e1707",
  productionTip: false,
  // 地理位置配置
  baoanLocation: {
    latitude: 22.55866135902317,
    longitude: 113.91141057014467,
    name: "宝安科技馆",
    address: "深圳市宝安区"
  },
  // API接口配置
  api: {
    // 用户相关接口
    user: {
      login: "/wx/user/{appId}/login",
      register: "/wx/user/register",
      info: "/wx/user/info",
      logout: "/wx/user/logout"
    },
    // 预约相关接口
    reservation: {
      venue: "/apitp/venue",
      movie: "/apitp/movie",
      course: "/apitp/course",
      create: "/apitp/reservation/create",
      list: "/apitp/reservation/list",
      cancel: "/apitp/reservation/cancel"
    },
    // 内容相关接口
    content: {
      announcement: "/apitp/announcement/getInfo",
      news: "/apitp/news/list"
    }
  },
  // 页面配置
  pages: {
    // 主包页面
    main: [
      "pages/index/index"
    ],
    // 子包页面
    subPackages: {
      "pages_app": [
        "login/index",
        "register/index",
        "user/index",
        "contacts/index",
        "entervenue/index",
        "vieworder/index",
        "curriculum/index"
      ]
    }
  },
  // 存储配置
  storage: {
    prefix: "baoan_",
    keys: {
      token: "token",
      userInfo: "userInfo",
      contacts: "contacts",
      settings: "settings"
    }
  },
  // 错误码配置
  errorCodes: {
    SUCCESS: 200,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500,
    NETWORK_ERROR: -1,
    TIMEOUT: -2
  },
  // 平台特定配置
  platform: {
    weixin: {
      shareConfig: {
        title: "宝安科技馆预约服务",
        desc: "便捷的场馆预约服务平台",
        imageUrl: "/static/favicon.ico"
      }
    }
  }
};
exports.config = config$1;
//# sourceMappingURL=../.sourcemap/mp-weixin/config.js.map
