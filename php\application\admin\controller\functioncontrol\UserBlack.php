<?php

namespace app\admin\controller\functioncontrol;

use app\common\controller\Backend;

/**
 * 黑名单管理
 *
 * @icon fa fa-circle-o
 */
class UserBlack extends Backend
{

    /**
     * UserBlack模型对象
     * @var \app\admin\model\UserBlack
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\UserBlack;

    }

    public function index()
    {
        // 是否为 ajax 请求
        if ($this->request->isAjax()) {
            // 如果发送的 source 是 Selectpage，则转发
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            // 构造查询参数
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();




            $list =     $this->model->with(['filmBlackRecordList'])
                ->where($where)
                ->where('del_flag', '0')
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            // print_r($list );

            $total =    $this->model->where($where)->where('del_flag', '0')->count();

            return json([
                'total' => $total,
                'rows' => collection($list)->toArray(),
            ]);
        }

        // 非 Ajax 请求渲染视图
        return $this->view->fetch();
    }

    public function del($ids = "")
    {
        if ($ids) {
            $pk = $this->model->getPk(); // 获取主键字段
            $where[$pk] = ['in', $ids];

            // 将 del_flag 设置为 2，实现逻辑删除
            $count = $this->model->where($where)->update(['del_flag' => 2]);

            if ($count) {
                $this->success("操作成功，记录已删除");
            } else {
                $this->error("未更新任何记录");
            }
        } else {
            $this->error("参数错误");
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
