<?php

namespace app\admin\controller\report;

use app\common\controller\Backend;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use think\Db;

/**
 * 场馆报表
 *
 * @icon fa fa-circle-o
 */
class Reportvenue extends Backend
{

    /**
     * venue模型对象
     * @var \app\admin\model\report\Venue
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Venue;
    }

    /**
     * 获取场馆预约报表
     * 
     * @return \think\response\Json
     */
    public function getVenueReport()
    {
        // 如果是 Selectpage 请求
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }

        // 获取分页参数

        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }

        // 构建基本 SQL
        $query = $this->model->alias('v')
            ->join(['holidays' => 'h'], "DATE_FORMAT(h.holidays_date, '%y%m%d') = DATE_FORMAT(v.venue_start_time, '%y%m%d')", 'LEFT')
            ->where('v.del_flag', 0);

        if ($startDate && $endDate) {
            $query->whereRaw("DATE_FORMAT(v.venue_start_time, '%y%m%d') >= DATE_FORMAT(:startDate, '%y%m%d')", ['startDate' => $startDate])
                ->whereRaw("DATE_FORMAT(:endDate, '%y%m%d') >= DATE_FORMAT(v.venue_start_time, '%y%m%d')", ['endDate' => $endDate]);
        } elseif ($startDate && !$endDate) {
            $query->whereRaw("DATE_FORMAT(v.venue_start_time, '%y%m%d') >= DATE_FORMAT(:now, '%y%m%d')", ['now' => date('Y-m-d')]);
        } elseif ($endDate && !$startDate) {
            $query->whereRaw("DATE_FORMAT(v.venue_end_time, '%y%m%d') <= DATE_FORMAT(:now, '%y%m%d')", ['now' => date('Y-m-d')]);
        }

        // 查询列表
        $list = $query->field([
            'v.id as id',
            'h.is_close as venueState',
            'v.venue_start_time as venueStartTime',
            'v.venue_end_time as venueEndTime',
            'v.week as week',
            'v.venue_poll as venuePoll',
            'v.inventory_votes as inventoryVotes'
        ])
            ->orderRaw("DATE_FORMAT(v.venue_start_time, '%y%m%d') DESC, v.venue_start_time ASC")
            ->paginate($limit);
        // ->select(false);  print_r($list);die;
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    public function show($ids = null)
    {
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }

        if (!$ids) {
            $this->error("缺少ID");
        }

        // 使用 FastAdmin 的 buildparams 自动构造分页、排序、筛选条件
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();



// 查询是否闭馆（等价 Java 的 judgeIsClose）
$isClose = $this->model
->alias('v')
->join(['holidays'=>'h'], 'DATE_FORMAT(v.venue_start_time,"%y%m%d") = DATE_FORMAT(h.holidays_date,"%y%m%d")')
->where('v.id', $ids)
->value('h.is_close');




            // 基础查询（join 三张表）
            $query = Db::table('venue_subscribe')
       
                ->alias('s')
                ->join(['user_linkman'=>'l'], 'l.id = s.user_linkman_id', 'LEFT')
                ->join(['emergency_contact'=>'ec'], 'ec.user_id = s.user_id', 'LEFT')
                ->field([
                    's.id',
                    'ec.linkman_phone' => 'ugentPhone',
                    "IF(s.sign_state = 0,'未签到','已签到')" => 'signState',
                    'l.linkman_name' => 'linkman_name',
                    'l.linkman_age' => 'linkmanAge',
                    'l.linkman_certificate' => 'linkmanCertificate',
                    'l.linkman_phone' => 'linkman_phone',
                ])
                ->where('s.venue_id', $ids)
                ->where(function ($q) use ($isClose) {
                    if ($isClose === '1') {
                        $q->where('s.del_flag', 0);
                    } elseif ($isClose === '0') {
                        $q->where(['s.del_flag' => 2, 's.is_close' => 1]);
                    }
                });

            // 附加搜索条件
            if (!empty($where)) {
                $query->where($where);
            }

  

            // 查询分页数据
            $list = $query
                ->order($sort, $order)
                ->paginate($limit);



  // 使用 each  脱敏处理
$list->each(function ($row) {
    $row['linkman_name'] = desensitize_name($row['linkman_name']);
    $row['linkman_phone'] = desensitize_phone($row['linkman_phone']);
    $row['linkmanCertificate'] = desensitize_certificate($row['linkmanCertificate']);

    return $row;
});

        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }


    public function export()
    {
        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }
        if (empty($startDate) || empty($endDate)) {
            $this->error('请先选择 场馆时间', null, null, 10);
        }
        // 查询全部数据
        $query = $this->model
            ->alias('v')
            ->join(['holidays' => 'h'], "DATE_FORMAT(h.holidays_date, '%y%m%d') = DATE_FORMAT(v.venue_start_time, '%y%m%d')", 'LEFT')

            ->join(['venue_subscribe' => 's'], 's.venue_id = v.id', 'LEFT')
            ->join(['user_linkman' => 'l'], 'l.id = s.user_linkman_id')
            ->field([
                'v.id as venue_id',
                'h.is_close as is_open',
                "DATE_FORMAT(v.venue_start_time, '%Y-%m-%d') as venue_start_time",
                "CONCAT(DATE_FORMAT(v.venue_start_time,'%H:%i:%s'), '-', DATE_FORMAT(v.venue_end_time,'%H:%i:%s')) as venue_end_time",
                'v.venue_poll',
                's.user_id',
                's.sign_state',
                's.is_close',
                's.del_flag',
                'l.linkman_name',
                'l.linkman_phone',
                'l.linkman_age',
                'l.linkman_certificate'
            ]);

        if ($startDate && $endDate) {
            $query->whereRaw("DATE_FORMAT(v.venue_start_time,'%y%m%d') >= DATE_FORMAT(:startDate,'%y%m%d')", ['startDate' => $startDate])
                ->whereRaw("DATE_FORMAT(v.venue_end_time,'%y%m%d') <= DATE_FORMAT(:endDate,'%y%m%d')", ['endDate' => $endDate]);
        }

        $list = $query->select();


        // 分组组装数据
        $resultList = [];
        // 手动按 venue_id 分组
        $groupByVenue = [];
        foreach ($list as $row) {
            $groupByVenue[$row['venue_id']][] = $row;
        }


        foreach ($groupByVenue as $venueId => $rows) {
            $isOpen = $rows[0]['is_open']; // 0=闭馆, 1=开放
            $filtered = array_filter($rows, function ($item) use ($isOpen) {
                return ($isOpen == 0 && $item['del_flag'] == 2 && $item['is_close'] == 1)
                    || ($isOpen == 1 && $item['del_flag'] == 0);
            });


            // 再按 user_id 分组
            $groupByUser = [];
            foreach ($filtered as $item) {
                $groupByUser[$item['user_id']][] = $item;
            }
            foreach ($groupByUser as $userRows) {
                $row0 = $userRows[0];
                $vo = [
                    'venue_start_time' => $row0['venue_start_time'],
                    'venue_end_time'  => $this->formatSessionTime($row0['venue_end_time']),
                    'is_close'       =>  $isOpen == 0 ? '闭馆' : '正常',
                    'venue_poll'       => $row0['venue_poll'],
                    'num'              => count($userRows),
                    'sign_state'       => $row0['sign_state'] == '0' ? '未签到' : '已签到',
                ];
                // 联系人字段缺省处理：PHP 中未处理空位补齐
                for ($i = 1; $i <= 5; $i++) {
                    $vo["linkmanName{$i}"] = '';
                    $vo["linkmanPhone{$i}"] = '';
                    $vo["linkmanCertificate{$i}"] = '';
                    $vo["linkmanAge{$i}"] = '';
                }
                foreach ($userRows as $i => $r) {
                    if ($i >= 5) break;
                    $idx = $i + 1;
                    $vo["linkman_name{$idx}"] = desensitize_name($r['linkman_name']);
                    $vo["linkman_phone{$idx}"] = desensitize_phone($r['linkman_phone']);
                    $vo["linkman_certificate{$idx}"] = desensitize_certificate($r['linkman_certificate']);
                    $vo["linkman_age{$idx}"] = $r['linkman_age'];
                }

                $resultList[] = $vo;
            }
        }

        // 生成 Excel
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $headers = [
            'venue_start_time'       => '预约日期',
            'venue_end_time'         => '预约场次',
            'is_close'              => '场次状态',
            'venue_poll'            => '可预约人数',
            'sign_state'            => '签到状态',
            'num'                  => '联系人数量',
        ];
        for ($i = 1; $i <= 5; $i++) {
            $headers["linkman_name{$i}"] = "联系人{$i}姓名";
            $headers["linkman_phone{$i}"] = "联系人{$i}手机号";
            $headers["linkman_certificate{$i}"] = "联系人{$i}证件号";
            $headers["linkman_age{$i}"] = "联系人{$i}年龄";
        }

        // 写表头
        $colIndex = 0;
        foreach ($headers as $key => $label) {
            $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
            $sheet->setCellValue($colLetter . '1', $label);
            $colIndex++;
        }

        // 写数据
        $rowIndex = 2;
        foreach ($resultList as $data) {
            $colIndex = 0;
            foreach ($headers as $key => $label) {
                $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
                $sheet->setCellValue($colLetter . $rowIndex, $data[$key] ?? '');
                $colIndex++;
            }
            $rowIndex++;
        }

        // 输出
        ob_clean(); // 清空缓冲区

        $filename = '场馆导出_' . date('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }
    public function exportdailySummary()
    {
        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }
        if (empty($startDate) || empty($endDate)) {
            $this->error('请先选择 场馆时间', null, null, 10);
        }
        $list = $this->model
            ->alias('v')
            ->join(['venue_subscribe' => 's'], 's.venue_id = v.id AND s.del_flag = 0', 'LEFT')
            ->whereRaw("DATE(v.venue_start_time) BETWEEN :start AND :end", [
                'start' => $startDate,
                'end' => $endDate
            ])
            ->field([
                "DATE(v.venue_start_time) as report_date",
                "COUNT(DISTINCT v.id) as total_sessions",
                "COUNT(s.id) as total_reservations",
                "SUM(CASE WHEN s.sign_state = '1' THEN 1 ELSE 0 END) as total_signed",
                "ROUND(
                IFNULL(SUM(CASE WHEN s.sign_state = '1' THEN 1 ELSE 0 END) / COUNT(s.id) * 100, 0), 1
            ) as sign_rate"
            ])
            ->group("DATE(v.venue_start_time)")
            ->order("report_date ASC")
            ->select();


        // 准备表头
        $headers = [
            'report_date' => '日期',
            'total_sessions' => '总场次数',
            'total_reservations' => '总预约人数',
            'total_signed' => '签到人数',
            'sign_rate' => '签到率',
        ];

        // 创建 Excel 表格
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();


        // 写表头
        $colIndex = 0;
        foreach ($headers as $key => $label) {
            $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
            $sheet->setCellValue($colLetter . '1', $label);
            $colIndex++;
        }
        $sheet->getColumnDimension('A')->setWidth(13); // 设置A列宽度为20
        // 冻结第一行
        $sheet->freezePane('A2');
        // 加粗第一行字体
        $sheet->getStyle('A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '1')
            ->getFont()->setBold(true);


        // 写数据
        $rowIndex = 2;
        foreach ($list as $data) {
            $colIndex = 0;
            foreach ($headers as $key => $label) {
                $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
                // 附加 % 到签到率
                $value = $data[$key];
                if ($key === 'sign_rate') {
                    $value .= '%';
                }

                $sheet->setCellValue($colLetter . $rowIndex, $value ?? '');
                $colIndex++;
            }
            $rowIndex++;
        }

        // 输出为 Excel 文件
        ob_clean();
        $filename = '按日汇总场馆预约_' . date('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    public function monthlySummaryExport()
    {
        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }
        if (empty($startDate) || empty($endDate)) {
            $this->error('请先选择 场馆时间', null, null, 10);
        }

        $list =  $this->model
            ->alias('v')
            ->join(['venue_subscribe' => 's'], 's.venue_id = v.id AND s.del_flag = 0', 'LEFT')
            ->whereRaw("DATE(v.venue_start_time) BETWEEN :start AND :end", [
                'start' => $startDate,
                'end' => $endDate
            ])
            ->field([
                "DATE_FORMAT(v.venue_start_time, '%Y-%m') as report_month",
                "COUNT(DISTINCT v.id) as total_sessions",
                "COUNT(s.id) as total_reservations",
                "ROUND(COUNT(s.id)/COUNT(DISTINCT v.id), 1) as avg_reservations_per_session",
                "SUM(CASE WHEN s.sign_state = '1' THEN 1 ELSE 0 END) as total_signed",
                "ROUND(IFNULL(SUM(CASE WHEN s.sign_state = '1' THEN 1 ELSE 0 END) / COUNT(s.id) * 100, 0), 1) as sign_rate"
            ])
            ->group("report_month")
            ->order("report_month ASC")
            ->select();

        // 构造表头
        $headers = [
            'report_month' => '年月',
            'total_sessions' => '总场次数',
            'total_reservations' => '总预约人数',
            'avg_reservations_per_session' => '平均预约人数/场',
            'total_signed' => '总签到人数',
            'sign_rate' => '签到率',
        ];

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 写表头
        $colIndex = 0;
        foreach ($headers as $key => $label) {
            $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
            $sheet->setCellValue($colLetter . '1', $label);
            $colIndex++;
        }
        $sheet->getColumnDimension('A')->setWidth(13); // 设置A列宽度为20
        // 冻结第一行
        $sheet->freezePane('A2');
        // 加粗第一行字体
        $sheet->getStyle('A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '1')
            ->getFont()->setBold(true);

        $row = 2;
        foreach ($list as $item) {
            $col = 0;
            foreach ($headers as $key => $_) {
                $val = $item[$key];
                if ($key === 'sign_rate') {
                    $val .= '%';
                }
                $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col + 1);
                $sheet->setCellValue($colLetter . $row, $val);
                $col++;
            }
            $row++;
        }

        ob_clean();
        $filename = '按月汇总场馆预约_' . date('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        header('Cache-Control: max-age=0');
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }
    public function yearlySummaryExport()
    {
        // 获取日期范围参数（通过 filter）
        $filter = json_decode($this->request->get("filter", '{}'), true);
        $dateRange = isset($filter['start_end_Date']) ? $filter['start_end_Date'] : null;
        if ($dateRange && strpos($dateRange, ' - ') !== false) {
            [$startDate, $endDate] = explode(' - ', $dateRange);
            $startDate = trim($startDate);
            $endDate = trim($endDate);
        } else {
            $startDate = $endDate = null;
        }
        if (empty($startDate) || empty($endDate)) {
            $this->error('请先选择 场馆时间', null, null, 10);
        }

        $list = $this->model
            ->alias('v')
            ->join(['venue_subscribe' => 's'], 's.venue_id = v.id AND s.del_flag = 0', 'LEFT')
            ->whereRaw("DATE(v.venue_start_time) BETWEEN :start AND :end", [
                'start' => $startDate,
                'end' => $endDate
            ])
            ->field([
                "DATE_FORMAT(v.venue_start_time, '%Y') as report_year",
                "COUNT(s.id) as total_reservations",
                "SUM(CASE WHEN s.sign_state = '1' THEN 1 ELSE 0 END) as total_signed",
                "ROUND(IFNULL(SUM(CASE WHEN s.sign_state = '1' THEN 1 ELSE 0 END) / COUNT(s.id) * 100, 0), 1) as sign_rate"
            ])
            ->group("report_year")
            ->order("report_year ASC")
            ->select();

        $headers = [
            'report_year' => '年份',
            'total_reservations' => '总预约人数',
            'total_signed' => '总签到人数',
            'sign_rate' => '总签到率',
        ];

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 写表头
        $colIndex = 0;
        foreach ($headers as $key => $label) {
            $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($colIndex + 1);
            $sheet->setCellValue($colLetter . '1', $label);
            $colIndex++;
        }
        $sheet->getColumnDimension('A')->setWidth(13); // 设置A列宽度为20
        // 冻结第一行
        $sheet->freezePane('A2');
        // 加粗第一行字体
        $sheet->getStyle('A1:' . \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex(count($headers)) . '1')
            ->getFont()->setBold(true);

        $row = 2;
        foreach ($list as $item) {
            $col = 0;
            foreach ($headers as $key => $_) {
                $val = $item[$key];
                if ($key === 'sign_rate') {
                    $val .= '%';
                }
                $colLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col + 1);
                $sheet->setCellValue($colLetter . $row, $val);
                $col++;
            }
            $row++;
        }

        ob_clean();
        $filename = '按年汇总场馆预约_' . date('YmdHis') . '.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header("Content-Disposition: attachment;filename=\"$filename\"");
        header('Cache-Control: max-age=0');
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    protected function formatSessionTime($timeStr)
    {
        // $timeStr 示例：10:00:00-13:30:00
        if (strpos($timeStr, '-') === false) return $timeStr;

        [$start, $end] = explode('-', $timeStr);
        $startHour = intval(substr($start, 0, 2));

        $prefix = $startHour < 12 ? '上午场' : '下午场';

        return $prefix . $timeStr;
    }

    // ------------------
    // 脱敏函数
    // ------------------

    private function desensitizePhone($phone)
    {
        return substr($phone, 0, 3) . '****' . substr($phone, -4);
    }

    private function desensitizeCertificate($cert)
    {
        return substr($cert, 0, 6) . '********' . substr($cert, -4);
    }

    private function desensitizeName($name)
    {
        $len = mb_strlen($name, 'UTF-8');

        if ($len === 1) {
            return '*';  // 单字名：全部隐藏
        } elseif ($len === 2) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*';  // 露姓，隐名
        } elseif ($len === 3) {
            return mb_substr($name, 0, 1, 'UTF-8') . '*' . mb_substr($name, 2, 1, 'UTF-8'); // 姓+*+末字
        } elseif ($len >= 4) {
            return mb_substr($name, 0, 1, 'UTF-8') . str_repeat('*', $len - 2) . mb_substr($name, -1, 1, 'UTF-8'); // 姓+若干星+末字
        }

        return $name;
    }
}
