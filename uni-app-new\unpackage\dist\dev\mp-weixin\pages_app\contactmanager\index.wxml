<view class="contactContent data-v-82585584"><view class="content data-v-82585584"><my-header wx:if="{{a}}" class="data-v-82585584" u-i="82585584-0" bind:__l="__l" u-p="{{b}}"/><view class="contactBox data-v-82585584"><view class="addContact data-v-82585584" bindtap="{{c}}"><text class="add data-v-82585584">+</text> 添加联系人 </view><view class="contactList data-v-82585584"><view wx:for="{{d}}" wx:for-item="item" wx:key="g" class="{{['contactItem', 'data-v-82585584', item.h && 'isMove']}}" bindtouchstart="{{item.i}}" bindtouchmove="{{item.j}}" data-index="{{item.k}}"><view class="left data-v-82585584"><view class="peopleName data-v-82585584">{{item.a}}</view><view class="peopleCard data-v-82585584">证件号 {{item.b}}</view><view class="peopleMablie data-v-82585584"><text class="data-v-82585584">手机号码 {{item.c}}</text><text class="data-v-82585584">年龄 {{item.d}}</text></view></view><view class="handlePlace data-v-82585584"><view class="edit data-v-82585584" bindtap="{{item.e}}">编辑</view><view class="del data-v-82585584" bindtap="{{item.f}}">删除</view></view></view></view></view></view></view>