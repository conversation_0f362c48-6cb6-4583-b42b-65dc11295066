/*
 * Skin: Black pink
 * -----------
 */
@import "../bootstrap-less/mixins.less";
@import "../bootstrap-less/variables.less";
@import "../fastadmin/variables.less";
@import "../fastadmin/mixins.less";

@primary: #f5549f;
@sidebar-dark-submenu-bg: darken(@sidebar-dark-bg, 5%);
.skin-black-pink {

    .main-header {
        background: @sidebar-dark-bg;
        .box-shadow(0px 1px 1px rgba(0, 0, 0, 0.05));

        .navbar {
            .navbar-variant(#fff; #666; #333; rgba(0, 0, 0, .02));

            .navbar-nav {
                > li > a {
                    border-right: none;
                }
            }

            .navbar-custom-menu .navbar-nav, .navbar-right {
                > li {
                    > a {
                        border-left: none;
                        border-right-width: 0;
                    }
                }
            }

            @media (max-width: @screen-header-collapse) {
                .navbar-variant(@sidebar-dark-submenu-bg; #fff);
            }
        }

        .logo {
            .logo-variant(@sidebar-dark-bg; #fff);
            border-right: 1px solid @sidebar-dark-bg;
            @media (max-width: @screen-header-collapse) {
                .logo-variant(@sidebar-dark-submenu-bg, #fff);
                border-right: none;
            }
        }

        li.user-header {
            background-color: @sidebar-dark-bg;
        }

        .nav-addtabs > li > a, .nav-addtabs > li.active > a {
            border-right-color: transparent;
        }

    }

    .content-header {
        background: transparent;
        box-shadow: none;
    }

    .skin-dark-sidebar(#fff);

    .treeview-menu {
        > li {
            > a {
                padding-left: 18px;
            }

            &.active > a {
                background-color: @primary;
            }
        }
    }

    .sidebar-menu {
        > li {

            > a {
                //border-left: 3px solid transparent;
            }

            &.active > a {
                color: @sidebar-dark-hover-color;
                background: @primary;
                border-left-color: @primary;
            }

            &:hover > a {
                border-left-color: transparent;
            }
        }

        li.treeview > a {
            background: transparent;
            border-left-color: transparent;
        }

        li.treeview {
            &.active > a,&.treeview-open > a {
                background-color: @sidebar-dark-submenu-bg;
                border-left-color: @sidebar-dark-submenu-bg;
            }
        }

        .treeview-menu {
            padding-left: 0;

            .treeview-menu {
                padding-left: 0;

                > li > a {
                    padding-left: 30px;
                }
            }

            li.treeview > a {
                background: transparent;
                border-left-color: transparent;
            }
        }
    }

    &.sidebar-collapse {

        .sidebar-menu {
            li:hover > a, li.active > a {
                color: @sidebar-dark-hover-color;
                background: @primary;
            }

            .treeview-menu {
                li.active > a {
                    color: @sidebar-dark-hover-color;
                    background: @primary;
                }

                li.treeview > a {
                    background: transparent;
                    border-left-color: transparent;
                }
            }
        }
    }

    &.multiplenav {
        @media (max-width: @screen-header-collapse) {
            .sidebar .mobilenav a.btn-app {
                background: lighten(@sidebar-dark-bg, 10%);
                color: #fff;

                &.active {
                    background: @primary;
                    color: #fff;
                }
            }
        }
    }
}
