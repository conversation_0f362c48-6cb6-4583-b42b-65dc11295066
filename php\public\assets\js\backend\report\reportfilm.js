define(['jquery', 'bootstrap', 'backend', 'table', 'form','moment'], function ($, undefined, Backend, Table, Form,moment) { 
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'report/reportfilm/getFilmReport' + location.search,
                    show_url: 'report/reportfilm/show', 
                    table: 'film_session',
                }
            });
            var film_base_lang = Config.film_base_lang;

    var Controller = {
        index: function () {

    

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'film_start_time',
                fixedColumns: true,
                fixedRightNumber: 1,
                search:false,
                showExport: false,
                searchFormVisible: true,
                columns: [
                    [       
                        {field: 'film_name', title: __('film_name'),operate:false,},
                        {
                            field: 'is_close',
                            title: __('is_close'),
                            operate: false,
                            formatter: function (value, row, index) {
                                if (value == 0) return '闭馆';
                                if (value == 1) return '开馆';
                                return '开馆';
                            }
                        },
                        
                                     
                        {
                            field: 'del_flag',
                            operate:false,
                            title:  __('del_flag'),
                            searchList: {0: __('del_flag_0'), 2: __('del_flag_2')},
                            formatter: Table.api.formatter.normal
                        },
                         {
                            field: 'film_type',
                            operate:false,
                            title: Config.film_base_lang['Film_type'],
                            searchList: {1: Config.film_base_lang['Film_type_1'], 2: Config.film_base_lang['Film_type_2']},
                            formatter: Table.api.formatter.normal
                        },

                        {field: 'film_start_time', title: __('film_start_time'),operate:false,},
                        {field: 'film_end_time', title: __('film_end_time'),operate:false,},
                        {field: 'film_start_time', title: __('week'),operate:false,    formatter: function (value, row, index) {
                            var weekList = ["星期", "周一", "周二", "周三", "周四", "周五", "周六","周日"];
                            let weekday = moment(value).isoWeekday(); // 1=周一，7=周日
                            return weekList[weekday] !== undefined ? weekList[weekday] : '-';
                        }},
                        {field: 'film_poll', title: __('usePoll'),operate:false,    formatter: function (value, row, index) {
                            // 计算实际使用场地数，防止空值导致 NaN
                            var poll = Number(row.film_poll) || 0;
                            var votes = Number(row.inventory_votes) || 0;
                            return poll - votes;
                        }},
                        {field: 'start_end_Date', title: __('start_end_Date'), operate:'RANGE', addclass:'datetimerange',visible:false,},
                        {field: 'operate', title: '查看预约情况', table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'show',
                                    text: '查看',
                                    title: '查看详情',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-eye',
                                    url: $.fn.bootstrapTable.defaults.extend.show_url
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            //保留原始 daterangepicker 初始化逻辑
            Form.events.daterangepicker = (function (origFunc) {
                return function (form) {
                    // 先执行原逻辑
                    origFunc && origFunc.apply(this, arguments);

                    // 然后追加参数
                    $(".datetimerange", form).each(function () {
                        // 原有配置
                        var old = $(this).attr('data-daterangepicker-options') || '{}';
                        var config = $.extend(true, {}, JSON.parse(old), {
                            showDropdowns: true,
                        });
                        $(this).attr("data-daterangepicker-options", JSON.stringify(config));
                    });
                };
            })(Form.events.daterangepicker);

            // 通用导出函数
            function handleExport(buttonClass, action) {
                $(document).on('click', buttonClass, function () {
                    var options = table.bootstrapTable('getOptions');
                    var params = options.queryParams({}); // 获取搜索参数
                    var query = $.param(params); // 序列化参数
                    var url = Fast.api.fixurl('report/reportfilm/' + action) + "?" + query;
                    window.open(url);
                });
            }

            // 初始化所有导出按钮事件绑定
            handleExport('.btn-export', 'export'); // 导出全部
            handleExport('.btn-exportdailySummary', 'exportdailySummary'); // 日汇总
            handleExport('.btn-monthlySummaryExport', 'monthlySummaryExport'); // 月汇总
            handleExport('.btn-yearlySummaryExport', 'yearlySummaryExport'); // 年汇总
        },
        show: function () {

            var ids = Fast.api.query('ids'); // 获取主表id参数
            
            var table = $("#table");
            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.show_url + '?ids=' + ids, // 子表接口,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                search:false,
                showExport: false,
                searchFormVisible: true,
                columns: [
                    [
                        {field: 'l.linkman_name', title: __('linkman_name'), operate:  'LIKE',
                            formatter: function (value, row, index) {
                                return row.linkman_name;
                              }
                        },
                        {field: 'linkman_phone', title: __('linkman_phone'), operate: false},
                        {field: 'linkmanAge', title: __('linkmanAge'), operate: false},
                        {field: 'linkmanCertificate', title: __('linkmanCertificate'), operate: false},
                        {
                            field: 'subscribeType',
                            title: __('subscribeType'),
                            operate: false,
                            formatter: function (value, row, index) {
                                const subscribeTypeList = ["一人", "二人", "三人"];
                                return subscribeTypeList[value - 1] || '';
                            }
                        },                   
                        {
                            field: 'isTicket',
                            title: __('isTicket'),
                            operate: false,
                            formatter: function (value, row, index) {
                                if (row.subscribeState == '1') {
                                    return '未签到';
                                } else if (row.subscribeState == '6') {
                                    return '已过期';
                                } else {
                                    const  isTicketList =  ["未检票", "已检票"];
                                    return isTicketList[row.isTicket] || '未知状态';
                                }
                            }
                        }
                   

                    ]
                ]
            });

        



                  // 为表格绑定事件
                  Table.api.bindevent(table);
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };

    return Controller;
});
