<?php

namespace app\admin\controller\sys;

use app\common\controller\Backend;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class User extends Backend
{

    /**
     * User模型对象
     * @var \app\admin\model\sys\User
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\sys\User;
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->field(
                [
                    'user_id as id',
                    'user_name',
                    'nick_name',
                    'phonenumber',
                    'avatar',
                    'status',
                    'create_time',
                ]
            ) // 仅保留这些字段
            ->where($where)
            ->where('del_flag', '0')
            ->order($sort, $order)
            ->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    public function del($ids = "")
    {
        if ($ids) {
            $pk = $this->model->getPk(); // 获取主键字段
            $where[$pk] = ['in', $ids];

            // 将 del_flag 设置为 2，实现逻辑删除
            $count = $this->model->where($where)->update(['del_flag' => 2]);

            if ($count) {
                $this->success("操作成功，记录已删除");
            } else {
                $this->error("未更新任何记录");
            }
        } else {
            $this->error("参数错误");
        }
    }
}
