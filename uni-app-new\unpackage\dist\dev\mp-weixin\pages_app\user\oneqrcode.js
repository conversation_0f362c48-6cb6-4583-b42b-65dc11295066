"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "OneQRCode",
  data() {
    return {
      qrcodeImage: "",
      refreshing: false,
      refreshTimer: null,
      originalBrightness: 0
    };
  },
  onLoad() {
    this.initPage();
  },
  onUnload() {
    this.cleanup();
  },
  methods: {
    // 初始化页面
    async initPage() {
      try {
        await this.setBrightness();
        await this.generateQRCode();
        this.startAutoRefresh();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/oneqrcode.vue:83", "初始化失败:", error);
        common_vendor.index.showToast({
          title: "初始化失败",
          icon: "error"
        });
      }
    },
    // 设置屏幕亮度
    async setBrightness() {
      try {
        const brightness = await this.getScreenBrightness();
        this.originalBrightness = brightness;
        await this.setScreenBrightness(0.5);
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages_app/user/oneqrcode.vue:101", "设置亮度失败:", error);
      }
    },
    // 获取屏幕亮度
    getScreenBrightness() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getScreenBrightness({
          success: (res) => resolve(res.value),
          fail: reject
        });
      });
    },
    // 设置屏幕亮度
    setScreenBrightness(value) {
      return new Promise((resolve, reject) => {
        common_vendor.index.setScreenBrightness({
          value,
          success: resolve,
          fail: reject
        });
      });
    },
    // 生成二维码
    async generateQRCode() {
      if (this.refreshing)
        return;
      this.refreshing = true;
      try {
        const res = await this.$myRequest({
          url: "/auth/venue/getAdminCode",
          method: "get"
        });
        if (res.code === 200) {
          const qrData = res.data.data || "";
          this.qrcodeImage = this.drawQRCode(qrData);
        } else {
          throw new Error(res.msg || "获取二维码失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/oneqrcode.vue:145", "生成二维码失败:", error);
        common_vendor.index.showToast({
          title: error.message || "生成二维码失败",
          icon: "error"
        });
      } finally {
        this.refreshing = false;
      }
    },
    // 绘制二维码
    drawQRCode(data) {
      try {
        const canvas = common_vendor.index.createCanvasContext("qrcode-canvas");
        return this.generateQRCodeImage(data);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/oneqrcode.vue:169", "绘制二维码失败:", error);
        return "";
      }
    },
    // 生成二维码图片（简化实现）
    generateQRCodeImage(data) {
      try {
        const size = 400;
        const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}`;
        return qrcodeUrl;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/oneqrcode.vue:186", "生成二维码图片失败:", error);
        return "";
      }
    },
    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.generateQRCode();
      }, 6e4);
    },
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    // 手动刷新二维码
    refreshQRCode() {
      this.generateQRCode();
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 清理资源
    async cleanup() {
      this.stopAutoRefresh();
      if (this.originalBrightness > 0) {
        try {
          await this.setScreenBrightness(this.originalBrightness);
        } catch (error) {
          common_vendor.index.__f__("warn", "at pages_app/user/oneqrcode.vue:226", "恢复亮度失败:", error);
        }
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.qrcodeImage
  }, $data.qrcodeImage ? {
    c: $data.qrcodeImage
  } : {}, {
    d: common_vendor.t($data.refreshing ? "刷新中..." : "刷新二维码"),
    e: $data.refreshing,
    f: common_vendor.o((...args) => $options.refreshQRCode && $options.refreshQRCode(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-14454ffc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/user/oneqrcode.js.map
