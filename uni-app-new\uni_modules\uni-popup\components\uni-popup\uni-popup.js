var t = require("../../../../@babel/runtime/helpers/defineProperty"),
  i = require("../../../../common/vendor.js"),
  o = {
    name: "uniPopup",
    components: {},
    emits: ["change", "maskClick"],
    props: {
      animation: {
        type: <PERSON>olean,
        default: !0
      },
      type: {
        type: String,
        default: "center"
      },
      isMaskClick: {
        type: Boolean,
        default: null
      },
      maskClick: {
        type: Boolean,
        default: null
      },
      backgroundColor: {
        type: String,
        default: "none"
      },
      safeArea: {
        type: Boolean,
        default: !0
      },
      maskBackgroundColor: {
        type: String,
        default: "rgba(0, 0, 0, 0.4)"
      }
    },
    watch: {
      type: {
        handler: function(t) {
          this.config[t] && this[this.config[t]](!0)
        },
        immediate: !0
      },
      isDesktop: {
        handler: function(t) {
          this.config[t] && this[this.config[this.type]](!0)
        },
        immediate: !0
      },
      maskClick: {
        handler: function(t) {
          this.mkclick = t
        },
        immediate: !0
      },
      isMaskClick: {
        handler: function(t) {
          this.mkclick = t
        },
        immediate: !0
      },
      showPopup: function(t) {}
    },
    data: function() {
      return {
        duration: 300,
        ani: [],
        showPopup: !1,
        showTrans: !1,
        popupWidth: 0,
        popupHeight: 0,
        config: {
          top: "top",
          bottom: "bottom",
          center: "center",
          left: "left",
          right: "right",
          message: "top",
          dialog: "center",
          share: "bottom"
        },
        maskClass: {
          position: "fixed",
          bottom: 0,
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: "rgba(0, 0, 0, 0.4)"
        },
        transClass: {
          position: "fixed",
          left: 0,
          right: 0
        },
        maskShow: !0,
        mkclick: !0,
        popupstyle: this.isDesktop ? "fixforpc-top" : "top"
      }
    },
    computed: {
      isDesktop: function() {
        return this.popupWidth >= 500 && this.popupHeight >= 500
      },
      bg: function() {
        return "" === this.backgroundColor || "none" === this.backgroundColor ? "transparent" : this.backgroundColor
      }
    },
    mounted: function() {
      var t = this;
      ! function() {
        var o = i.index.getSystemInfoSync(),
          s = o.windowWidth,
          e = o.windowHeight,
          n = o.windowTop,
          a = o.safeArea,
          r = o.screenHeight;
        o.safeAreaInsets;
        t.popupWidth = s, t.popupHeight = e + (n || 0), a && t.safeArea ? t.safeAreaInsets = r - a.bottom : t.safeAreaInsets = 0
      }()
    },
    unmounted: function() {
      this.setH5Visible()
    },
    created: function() {
      null === this.isMaskClick && null === this.maskClick ? this.mkclick = !0 : this.mkclick = null !== this.isMaskClick ? this.isMaskClick : this.maskClick, this.animation ? this.duration = 300 : this.duration = 0, this.messageChild = null, this.clearPropagation = !1, this.maskClass.backgroundColor = this.maskBackgroundColor
    },
    methods: {
      setH5Visible: function() {},
      closeMask: function() {
        this.maskShow = !1
      },
      disableMask: function() {
        this.mkclick = !1
      },
      clear: function(t) {
        t.stopPropagation(), this.clearPropagation = !0
      },
      open: function(t) {
        this.showPopup || (t && -1 !== ["top", "center", "bottom", "left", "right", "message", "dialog", "share"].indexOf(t) || (t = this.type), this.config[t] ? (this[this.config[t]](), this.$emit("change", {
          show: !0,
          type: t
        })) : console.error("缺少类型：", t))
      },
      close: function(t) {
        var i = this;
        this.showTrans = !1, this.$emit("change", {
          show: !1,
          type: this.type
        }), clearTimeout(this.timer), this.timer = setTimeout((function() {
          i.showPopup = !1
        }), 300)
      },
      touchstart: function() {
        this.clearPropagation = !1
      },
      onTap: function() {
        this.clearPropagation ? this.clearPropagation = !1 : (this.$emit("maskClick"), this.mkclick && this.close())
      },
      top: function(t) {
        var i = this;
        this.popupstyle = this.isDesktop ? "fixforpc-top" : "top", this.ani = ["slide-top"], this.transClass = {
          position: "fixed",
          left: 0,
          right: 0,
          backgroundColor: this.bg
        }, !t && (this.showPopup = !0, this.showTrans = !0, this.$nextTick((function() {
          i.messageChild && "message" === i.type && i.messageChild.timerClose()
        })))
      },
      bottom: function(t) {
        this.popupstyle = "bottom", this.ani = ["slide-bottom"], this.transClass = {
          position: "fixed",
          left: 0,
          right: 0,
          bottom: 0,
          paddingBottom: this.safeAreaInsets + "px",
          backgroundColor: this.bg
        }, !t && (this.showPopup = !0, this.showTrans = !0)
      },
      center: function(t) {
        this.popupstyle = "center", this.ani = ["zoom-out", "fade"], this.transClass = {
          position: "fixed",
          display: "flex",
          flexDirection: "column",
          bottom: 0,
          left: 0,
          right: 0,
          top: 0,
          justifyContent: "center",
          alignItems: "center"
        }, !t && (this.showPopup = !0, this.showTrans = !0)
      },
      left: function(t) {
        this.popupstyle = "left", this.ani = ["slide-left"], this.transClass = {
          position: "fixed",
          left: 0,
          bottom: 0,
          top: 0,
          backgroundColor: this.bg,
          display: "flex",
          flexDirection: "column"
        }, !t && (this.showPopup = !0, this.showTrans = !0)
      },
      right: function(t) {
        this.popupstyle = "right", this.ani = ["slide-right"], this.transClass = {
          position: "fixed",
          bottom: 0,
          right: 0,
          top: 0,
          backgroundColor: this.bg,
          display: "flex",
          flexDirection: "column"
        }, !t && (this.showPopup = !0, this.showTrans = !0)
      }
    }
  };
Array || i.resolveComponent("uni-transition")();
Math;
var s = i._export_sfc(o, [
  ["render", function(o, s, e, n, a, r) {
    return i.e({
      a: a.showPopup
    }, a.showPopup ? i.e({
      b: a.maskShow
    }, a.maskShow ? {
      c: i.o(r.onTap),
      d: i.p(t(t(t(t({
        name: "mask"
      }, "mode-class", "fade"), "styles", a.maskClass), "duration", a.duration), "show", a.showTrans))
    } : {}, {
      e: r.bg,
      f: i.n(a.popupstyle),
      g: i.o((function() {
        return r.clear && r.clear.apply(r, arguments)
      })),
      h: i.o(r.onTap),
      i: i.p(t(t(t(t(t({}, "mode-class", a.ani), "name", "content"), "styles", a.transClass), "duration", a.duration), "show", a.showTrans)),
      j: i.o((function() {
        return r.touchstart && r.touchstart.apply(r, arguments)
      })),
      k: i.n(a.popupstyle),
      l: i.n(r.isDesktop ? "fixforpc-z-index" : "")
    }) : {})
  }]
]);
wx.createComponent(s);