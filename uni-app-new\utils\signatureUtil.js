/**
 * 签名工具
 * 适配uni-app环境
 */

/**
 * 简单的MD5实现（用于签名）
 * 在实际项目中应该使用专门的MD5库
 */
function simpleMD5(str) {
  // 这是一个简化的MD5实现，仅用于演示
  // 实际项目中应该使用真正的MD5库
  let hash = 0
  if (str.length === 0) return hash.toString()

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }

  return Math.abs(hash).toString(16).padStart(8, '0')
}

/**
 * 序列化数据为查询字符串
 * @param {Object|string} data 数据
 * @returns {string} 序列化后的字符串
 */
function serializeData(data) {
  if (typeof data === 'string') {
    try {
      data = JSON.parse(data)
    } catch (error) {
      return ''
    }
  }

  if (!data || typeof data !== 'object') {
    return ''
  }

  let result = ''
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      result += key + '=' + data[key] + '&'
    }
  }

  return result
}

/**
 * 生成签名
 * @param {Object} options 选项
 * @param {Object} options.data 数据
 * @param {string} options.url URL
 * @param {Object} options.headers 请求头
 * @returns {Object} 签名结果
 */
export function signatureGenerate({ data, url, headers }) {
  const timestamp = new Date().getTime()
  const dataStr = serializeData(data)
  const signStr = '&timestamp=' + timestamp + '&url=' + url + dataStr

  return {
    signature: simpleMD5(signStr).toUpperCase(),
    timestamp: timestamp
  }
}

// 默认导出
export default {
  signatureGenerate
}