<style>
    .table .img-sm {
        width: 120px !important;
  height: auto !important;

    }
</style>
    

<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('film/base/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('film/base/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('film/base/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('film/base/edit')}"
                           data-operate-del="{:$auth->check('film/base/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
<!-- 影片排期输入区域（默认隐藏） -->
<div id="schedule-form" class="well" style="display:none; margin-top:20px;">
    <h4>添加影片排期</h4>
    <div class="row">
        <div class="col-sm-4">
            <label>影片：
            <p id="schedule-film-name" style="font-weight:bold;margin-bottom:5px;"></p></label>
            <img id="schedule-film-cover" src="" style="border-radius:4px;width: 120px !important;height: auto !important;">
        </div>
        <div class="col-sm-8">
            <!-- 简洁横向排布 -->
            <div class="form-group form-inline">
                <label for="film_poll" style="margin-right:10px;">影片票数：</label>
                <input type="number" id="film_poll" class="form-control" placeholder="总票数" style="width:130px;">
            </div>

            <div class="form-group form-inline">
                <label style="margin-right:10px;">开始时间：</label>
                <!-- class = timepicker 先取消 -->
                <input type="time" class="form-control " id="film_start_time" placeholder="选择开始时间" style="width:130px;">
            </div>
            
            <div class="form-group form-inline">
                <label style="margin-right:10px;">结束时间：</label>
                <input type="time" class="form-control " id="film_end_time" placeholder="选择结束时间" style="width:130px;">
            </div>

            <button class="btn btn-primary" id="btn-submit-schedule" style="margin-top:10px;">提交排期</button>
        </div>
    </div>
</div>
