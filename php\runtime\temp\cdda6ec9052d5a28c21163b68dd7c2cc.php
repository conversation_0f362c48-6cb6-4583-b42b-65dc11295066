<?php if (!defined('THINK_PATH')) exit(); /*a:4:{s:83:"D:\my_project\kejiguan\php\public/../application/admin\view\announcement\index.html";i:1753348167;s:69:"D:\my_project\kejiguan\php\application\admin\view\layout\default.html";i:1753348167;s:66:"D:\my_project\kejiguan\php\application\admin\view\common\meta.html";i:1753348167;s:68:"D:\my_project\kejiguan\php\application\admin\view\common\script.html";i:1753348167;}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo (isset($title) && ($title !== '')?$title:''); ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="referrer" content="never">
<meta name="robots" content="noindex, nofollow">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<!-- Loading Bootstrap -->
<link href="/assets/css/backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">

<?php if(\think\Config::get('fastadmin.adminskin')): ?>
<link href="/assets/css/skins/<?php echo htmlentities(\think\Config::get('fastadmin.adminskin') ?? ''); ?>.css?v=<?php echo htmlentities(\think\Config::get('site.version') ?? ''); ?>" rel="stylesheet">
<?php endif; ?>

<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config:  <?php echo json_encode($config ?? ''); ?>
    };
</script>

    </head>

    <body class="inside-header inside-aside <?php echo defined('IS_DIALOG') && IS_DIALOG ? 'is-dialog' : ''; ?>">
        <div id="main" role="main">
            <div class="tab-content tab-addtabs">
                <div id="content">
                    <div class="row">
                        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                            <section class="content-header hide">
                                <h1>
                                    <?php echo __('Dashboard'); ?>
                                    <small><?php echo __('Control panel'); ?></small>
                                </h1>
                            </section>
                            <?php if(!IS_DIALOG && !\think\Config::get('fastadmin.multiplenav') && \think\Config::get('fastadmin.breadcrumb')): ?>
                            <!-- RIBBON -->
                            <div id="ribbon">
                                <ol class="breadcrumb pull-left">
                                    <?php if($auth->check('dashboard')): ?>
                                    <li><a href="dashboard" class="addtabsit"><i class="fa fa-dashboard"></i> <?php echo __('Dashboard'); ?></a></li>
                                    <?php endif; ?>
                                </ol>
                                <ol class="breadcrumb pull-right">
                                    <?php foreach($breadcrumb as $vo): ?>
                                    <li><a href="javascript:;" data-url="<?php echo htmlentities($vo['url'] ?? ''); ?>"><?php echo htmlentities($vo['title'] ?? ''); ?></a></li>
                                    <?php endforeach; ?>
                                </ol>
                            </div>
                            <!-- END RIBBON -->
                            <?php endif; ?>
                            <div class="content">
                                <div class="container-fluid">
<!-- 公告发布 -->
<div class="row">
  <div class="col-md-6">
    <div class="box box-primary">
      <div class="box-header with-border">
        <div class="clearfix">
          <strong class="pull-left" style="font-size:16px;">公告发布</strong>
          <span class="pull-right text-muted" style="font-size:14px; font-weight:normal;">再次发布公告栏, 将会在小程序首页展示</span>
        </div>
      </div>
      <div class="box-body">
        <textarea
          class="form-control"
          id="notice_announcement"
          placeholder="请输入公告内容..."
          rows="5"
          style="resize: vertical; overflow-x: hidden;"></textarea>
        <div class="text-center" style="margin-top: 10px;">
          <button id="btn-announcement" class="btn btn-primary">发布</button>
        </div>
      </div>
    </div>
  </div>
</div>




<!-- 入馆预约须知 -->
<div class="row">
  <div class="col-md-12">
    <div class="box box-info">
      <div class="box-header with-border">
        <div class="clearfix">
          <strong class="pull-left" style="font-size:16px;">入馆预约须知</strong>
          <span class="pull-right text-muted" style="font-size:14px; font-weight:normal;">
            再次发布入馆预约, 将会在小程序入馆预约页展示
          </span>
        </div>
      </div>
      <div class="box-body">
        <div class="row">
          <div class="col-md-4">
            <label>预约对象</label>
            <textarea class="form-control"
          id="entrance_1_object"
          placeholder="请输入公告内容..."
          rows="5"
          style="resize: vertical; overflow-x: hidden;" id="entry_target"></textarea>
          </div>
          <div class="col-md-4">
            <label>预约登记</label>
            <textarea class="form-control"
          id="entrance_1_register"
          placeholder="请输入公告内容..."
          rows="5"
          style="resize: vertical; overflow-x: hidden;" id="entry_register"></textarea>
          </div>
          <div class="col-md-4">
            <label>入场审核</label>
            <textarea class="form-control"
          id="entrance_1_audit"
          placeholder="请输入公告内容..."
          rows="5"
          style="resize: vertical; overflow-x: hidden;" id="entry_review"></textarea>
          </div>
        </div>
        <div class="text-center">
          <button id="btn-entry" class="btn btn-info" style="margin-top:15px; width:160px;">发布</button>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- 观影预约须知 -->
<div class="row">
  <div class="col-md-12">
    <div class="box box-success">
      <div class="box-header with-border">
        <div class="clearfix">
          <strong class="pull-left" style="font-size:16px;">观影预约须知</strong>
          <span class="pull-right text-muted" style="font-size:14px; font-weight:normal;">
            再次发布观影预约, 将会在小程序观影预约页展示
          </span>
        </div>
      </div>
      <div class="box-body">
        <div class="row">
          <div class="col-md-4">
            <label>观影对象</label>
            <textarea class="form-control" rows="5" id="entrance_2_object" style="resize: vertical;"></textarea>
          </div>
          <div class="col-md-4">
            <label>观影登记</label>
            <textarea class="form-control" rows="5" id="entrance_2_register" style="resize: vertical;"></textarea>
          </div>
          <div class="col-md-4">
            <label>入场审核</label>
            <textarea class="form-control" rows="5" id="entrance_2_audit" style="resize: vertical;"></textarea>
          </div>
        </div>
        <div class="text-center">
          <button id="btn-movie" class="btn btn-success" style="margin-top:15px; width:160px;">发布</button>
        </div>
      </div>
    </div>
  </div>
</div>




<!-- 观影预约成功 -->
<div class="row">
  <div class="col-md-12">
    <div class="box box-warning">
      <div class="box-header with-border">
        <div class="clearfix">
          <strong class="pull-left" style="font-size:16px;">观影预约须知</strong>
          <span class="pull-right text-muted" style="font-size:14px; font-weight:normal;">
            再次发布观影预约, 将会在小程序观影预约页展示
          </span>
        </div>
      </div>
      <div class="box-body">
        <div class="row">
          <div class="col-md-6">
            <label>注意事项</label>
            <textarea class="form-control" rows="5" id="beiyong_three" style="resize: vertical;"></textarea>
          </div>
          <div class="col-md-6">
            <label>电影开播条件</label>
            <textarea class="form-control" rows="5" id="beiyong_four" style="resize: vertical;"></textarea>
          </div>
        </div>
        <div class="text-center">
          <button id="btn-movie-success" class="btn btn-warning" style="margin-top:15px; width:160px;">发布</button>
        </div>
      </div>
    </div>
  </div>
</div>





</div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/assets/js/require.min.js" data-main="/assets/js/require-backend<?php echo \think\Config::get('app_debug')?'':'.min'; ?>.js?v=<?php echo htmlentities($site['version'] ?? ''); ?>"></script>

    </body>
</html>
