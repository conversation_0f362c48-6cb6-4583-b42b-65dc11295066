define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'functioncontrol/user_black/index' + location.search,
                    //    add_url: 'functioncontrol/user_black/add',
                    // edit_url: 'functioncontrol/user_black/edit',
                    del_url: 'functioncontrol/user_black/del',
                    multi_url: 'functioncontrol/user_black/multi',
                    import_url: 'functioncontrol/user_black/import',
                    table: 'film_black_list',

                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        { checkbox: true },
                        // {field: 'id', title: __('Id')},
                        // {field: 'user_id', title: __('User_id')},
                        { field: 'user_phone', title: __('User_phone'), operate: 'LIKE' },

                        {
                            field: 'is_start',
                            title: __('Is_start'),
                            formatter: Table.api.formatter.toggle,
                            events: Table.api.events.toggle,
                            searchList: { "0": "启用", "1": "禁用" },
                            custom: { "0": "success", "1": "grey" }, // 控制颜色
                            yes: 0, // ✅ 反转 yes 和 no 的含义
                            no: 1
                        },
                        {
                            field: 'film_black_record_list',
                            title: '记录',
                            formatter: function (value, row, index) {
                                let list = value || [];
                                let html = '';
                                let showCount = 3;
                                let id = 'record-' + index;

                                html += `<div class="record-wrapper" id="${id}">`;

                                list.slice(0, showCount).forEach(item => {
                                    html += `<div class="record-line">${item.cause}</div>`;
                                });

                                if (list.length > showCount) {
                                    html += `<div class="record-extra" style="display:none;">`;
                                    list.slice(showCount).forEach(item => {
                                        html += `<div class="record-line">${item.cause}</div>`;
                                    });
                                    html += `</div>`;
                                    html += `<div class="record-toggle" data-index="${index}" style="color:#1890ff;cursor:pointer;margin-top:4px;">展开</div>`;
                                }

                                html += `</div>`;
                                return html;
                            }
                        }
                        ,



                        // {field: 'black_list_type', title: __('Black_list_type')},
                        // {field: 'unlock_time', title: __('Unlock_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'create_by', title: __('Create_by'), operate: 'LIKE'},
                        // {field: 'create_time', title: __('Create_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'update_by', title: __('Update_by'), operate: 'LIKE'},
                        // {field: 'update_time', title: __('Update_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        // {field: 'remark', title: __('Remark'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content},
                        // {field: 'del_flag', title: __('Del_flag'), formatter: Table.api.formatter.flag},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'del',
                                    title: __('Delete'),
                                    icon: 'fa fa-trash',
                                    classname: 'btn btn-xs btn-danger btn-delone'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            $(document).on('click', '.record-toggle', function (e) {


                const $toggle = $(this);
                const $wrapper = $toggle.closest('.record-wrapper');
                const $extra = $wrapper.find('.record-extra');

                if ($extra.is(':visible')) {
                    $extra.slideUp(200);
                    $toggle.text('展开');
                } else {
                    $extra.slideDown(200);
                    $toggle.text('收起');
                }
            });


        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
