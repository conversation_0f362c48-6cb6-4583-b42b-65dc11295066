<template>
  <view class="venue-success">
    <!-- 自定义头部 -->
    <my-header 
      title="参观凭证" 
      :isBack="true" 
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
      :isFixed="true"
    />
    
    <view class="content">
      <!-- 成功状态 -->
      <view class="success-state">
        <view class="success-header">
          <image 
            src="/static/img/schemesuccess/schemestate_course_venue.png" 
            mode="aspectFit"
            class="success-icon"
          />
          <text class="success-text">预约成功</text>
        </view>
      </view>
      
      <!-- 参观信息 -->
      <view class="venue-info">
        <view class="info-header">
          <text class="venue-name">宝安科技馆 入馆预约</text>
        </view>
        
        <view class="venue-details">
          <view class="detail-row">
            <text class="label">预约日期：</text>
            <text class="value important">{{ venueInfo.date }}</text>
          </view>
          <view class="detail-row">
            <text class="label">入馆时间：</text>
            <text class="value">{{ venueInfo.venueStartTime }} - {{ venueInfo.venueEndTime }}</text>
          </view>
          <view class="detail-row">
            <text class="label">预约人数：</text>
            <text class="value">{{ venueInfo.subscribeType }}人</text>
          </view>
          <view class="detail-row" v-if="venueInfo.linkmanName">
            <text class="label">联系人：</text>
            <text class="value">{{ venueInfo.linkmanName }}</text>
          </view>
        </view>
        
        <!-- 联系人列表 -->
        <view v-if="contactsList.length > 0" class="contacts-list">
          <view class="contacts-title">预约人员</view>
          <view 
            v-for="(contact, index) in contactsList" 
            :key="contact.linkId"
            class="contact-item"
          >
            <text class="contact-name">{{ contact.linkmanName }}</text>
            <text class="contact-id">{{ hideIdCard(contact.linkmanCertificate) }}</text>
          </view>
        </view>
        
        <!-- 提示信息 -->
        <view class="tips-section">
          <view class="tips-icon"></view>
          <text class="tips-text">
            在首页<text class="highlight">个人中心—场馆预约—查看凭证</text>中查看此凭证
          </text>
        </view>
      </view>
      
      <!-- 签到按钮 -->
      <view class="signin-section">
        <button 
          :class="['signin-btn', { signed: isSigned }]"
          @tap="signUp"
          :disabled="isSigningUp || isSigned"
        >
          {{ isSigned ? '已签到' : (isSigningUp ? '签到中...' : '入馆签到') }}
        </button>
      </view>
    </view>
    
    <!-- 签到结果弹窗 -->
    <view v-if="showSignModal" class="sign-modal">
      <view :class="['modal-dialog', { fail: signResult === 'fail' }]">
        <view class="modal-icon"></view>
        <view class="modal-text">
          <template v-if="signResult === 'success'">
            恭喜您，签到成功！
          </template>
          <template v-else>
            您的定位较远<br />
            请移步至宝安科技馆进行<text class="highlight">现场签到</text>
          </template>
        </view>
        <view class="modal-btn" @tap="closeSignModal">
          {{ signResult === 'success' ? '确定' : '返回' }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'VenueSuccess',
  data() {
    return {
      venueInfo: {},
      contactsList: [],
      
      // 参数
      venueSessionId: null,
      batchNumber: null,
      
      // 状态
      isSigned: false,
      isSigningUp: false,
      showSignModal: false,
      signResult: 'success', // success | fail
      
      // 屏幕亮度
      originalBrightness: 0
    }
  },
  
  onLoad(options) {
    this.venueSessionId = options.id
    this.batchNumber = options.batchNumber
    
    this.initPage()
  },
  
  onUnload() {
    this.restoreBrightness()
  },
  
  methods: {
    // 初始化页面
    async initPage() {
      try {
        // 设置屏幕亮度
        await this.setBrightness()
        
        // 获取参观信息
        await this.getVenueInfo()
      } catch (error) {
        console.error('初始化失败:', error)
      }
    },
    
    // 设置屏幕亮度
    async setBrightness() {
      try {
        // 获取当前亮度
        const brightness = await this.getScreenBrightness()
        this.originalBrightness = brightness
        
        // 设置适中亮度
        await this.setScreenBrightness(0.5)
      } catch (error) {
        console.warn('设置亮度失败:', error)
      }
    },
    
    // 恢复屏幕亮度
    async restoreBrightness() {
      if (this.originalBrightness > 0) {
        try {
          await this.setScreenBrightness(this.originalBrightness)
        } catch (error) {
          console.warn('恢复亮度失败:', error)
        }
      }
    },
    
    // 获取屏幕亮度
    getScreenBrightness() {
      return new Promise((resolve, reject) => {
        uni.getScreenBrightness({
          success: (res) => resolve(res.value),
          fail: reject
        })
      })
    },
    
    // 设置屏幕亮度
    setScreenBrightness(value) {
      return new Promise((resolve, reject) => {
        uni.setScreenBrightness({
          value,
          success: resolve,
          fail: reject
        })
      })
    },
    
    // 获取参观信息
    async getVenueInfo() {
      try {
        const res = await this.$myRequest({
          url: '/web/venueSession/personalCenterVenueByVenueSessionId',
          method: 'get',
          data: {
            venueSessionId: this.venueSessionId
          }
        })
        
        if (res.code === 200) {
          const data = res.data.data
          this.venueInfo = {
            ...data,
            date: this.formatDate(data.venueArrangedDate),
            venueStartTime: this.formatTime(data.venueStartTime),
            venueEndTime: this.formatTime(data.venueEndTime)
          }
          
          // 检查签到状态
          this.isSigned = data.signState === '1'
          
          // 获取联系人列表
          await this.getContactsList()
        }
      } catch (error) {
        console.error('获取参观信息失败:', error)
        uni.showToast({
          title: '获取信息失败',
          icon: 'error'
        })
      }
    },
    
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: '/web/venueSession/getVenueSubscribePeoples',
          method: 'get',
          data: {
            batchNumber: this.batchNumber
          }
        })
        
        if (res.code === 200) {
          this.contactsList = res.data.data || []
        }
      } catch (error) {
        console.warn('获取联系人失败:', error)
      }
    },
    
    // 入馆签到
    async signUp() {
      if (this.isSigningUp || this.isSigned) return
      
      this.isSigningUp = true
      
      try {
        uni.showLoading({
          title: '获取位置信息中',
          mask: true
        })
        
        // 获取位置信息
        const location = await this.getCurrentLocation()
        
        uni.hideLoading()
        
        // 验证位置
        const locationRes = await this.$myRequest({
          url: '/web/common/checkLocation',
          method: 'post',
          data: {
            latitude: location.latitude,
            longitude: location.longitude
          }
        })
        
        if (locationRes.code !== 200 || !locationRes.data.data.isNearby) {
          this.signResult = 'fail'
          this.showSignModal = true
          return
        }
        
        // 执行签到
        const signRes = await this.$myRequest({
          url: '/web/venueSession/venueSign',
          method: 'post',
          data: {
            venueSessionId: this.venueSessionId
          }
        })
        
        if (signRes.code === 200) {
          this.isSigned = true
          this.signResult = 'success'
          this.showSignModal = true
        } else {
          throw new Error(signRes.msg || '签到失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('签到失败:', error)
        
        if (error.message && error.message.includes('定位')) {
          this.signResult = 'fail'
          this.showSignModal = true
        } else {
          uni.showToast({
            title: error.message || '签到失败',
            icon: 'error'
          })
        }
      } finally {
        this.isSigningUp = false
      }
    },

    // 获取当前位置
    getCurrentLocation() {
      return new Promise((resolve, reject) => {
        uni.getLocation({
          type: 'gcj02',
          success: resolve,
          fail: reject
        })
      })
    },

    // 关闭签到弹窗
    closeSignModal() {
      this.showSignModal = false
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''

      try {
        const date = new Date(dateStr.replace(/-/g, '/'))
        const weekList = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        const weekDay = weekList[date.getDay()]
        return `${dateStr} ${weekDay}`
      } catch (error) {
        return dateStr
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''

      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },

    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard

      return idCard.replace(idCard.substring(4, 15), '*******')
    }
  }
}
</script>

<style lang="scss" scoped>
.venue-success {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding: 20rpx;
    padding-top: 120rpx; // 为固定头部留出空间

    // 成功状态
    .success-state {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 40rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .success-header {
        display: flex;
        flex-direction: column;
        align-items: center;

        .success-icon {
          width: 120rpx;
          height: 120rpx;
          margin-bottom: 20rpx;
        }

        .success-text {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
        }
      }
    }

    // 参观信息
    .venue-info {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .info-header {
        margin-bottom: 20rpx;

        .venue-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .venue-details {
        margin-bottom: 30rpx;

        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            font-size: 28rpx;
            color: #666;
            min-width: 140rpx;
          }

          .value {
            font-size: 28rpx;
            color: #333;
            flex: 1;

            &.important {
              color: #1976d2;
              font-weight: 600;
            }
          }
        }
      }

      .contacts-list {
        margin-bottom: 30rpx;

        .contacts-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
        }

        .contact-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .contact-name {
            font-size: 26rpx;
            color: #333;
            font-weight: 500;
          }

          .contact-id {
            font-size: 24rpx;
            color: #999;
          }
        }
      }

      .tips-section {
        display: flex;
        align-items: flex-start;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;

        .tips-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
          margin-top: 4rpx;
          background: url('/static/img/common/warning.png') center/contain no-repeat;
        }

        .tips-text {
          flex: 1;
          font-size: 26rpx;
          color: #666;
          line-height: 1.5;

          .highlight {
            color: #1976d2;
            font-weight: 500;
          }
        }
      }
    }

    // 签到区域
    .signin-section {
      .signin-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #4ecdc4, #44a08d);
        color: white;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;

        &::after {
          border: none;
        }

        &.signed {
          background: #e8f5e8;
          color: #388e3c;
        }

        &:disabled {
          opacity: 0.6;
        }
      }
    }
  }

  // 签到结果弹窗
  .sign-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-dialog {
      background: white;
      border-radius: 20rpx;
      padding: 60rpx 40rpx;
      margin: 40rpx;
      text-align: center;

      &.fail {
        .modal-icon {
          background: url('/static/img/common/location-error.png') center/contain no-repeat;
        }
      }

      .modal-icon {
        width: 120rpx;
        height: 120rpx;
        margin: 0 auto 30rpx;
        background: url('/static/img/common/success.png') center/contain no-repeat;
      }

      .modal-text {
        font-size: 32rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 40rpx;

        .highlight {
          color: #1976d2;
          font-weight: 500;
        }
      }

      .modal-btn {
        padding: 20rpx 60rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 40rpx;
        font-size: 32rpx;
        font-weight: 500;
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.venue-success {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
