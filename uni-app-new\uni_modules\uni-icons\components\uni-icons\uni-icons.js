var t = require("./uniicons_file_vue.js"),
  n = require("../../../../common/vendor.js"),
  i = {
    name: "UniIcons",
    emits: ["click"],
    props: {
      type: {
        type: String,
        default: ""
      },
      color: {
        type: String,
        default: "#333333"
      },
      size: {
        type: [Number, String],
        default: 24
      },
      customPrefix: {
        type: String,
        default: ""
      },
      fontFamily: {
        type: String,
        default: ""
      }
    },
    data: function() {
      return {
        icons: t.fontData
      }
    },
    computed: {
      unicode: function() {
        var t = this,
          n = this.icons.find((function(n) {
            return n.font_class === t.type
          }));
        return n ? n.unicode : ""
      },
      iconSize: function() {
        return "number" == typeof(t = this.size) || /^[0-9]*$/g.test(t) ? t + "px" : t;
        var t
      },
      styleObj: function() {
        return "" !== this.fontFamily ? "color: ".concat(this.color, "; font-size: ").concat(this.iconSize, "; font-family: ").concat(this.fontFamily, ";") : "color: ".concat(this.color, "; font-size: ").concat(this.iconSize, ";")
      }
    },
    methods: {
      _onClick: function() {
        this.$emit("click")
      }
    }
  };
var e = n._export_sfc(i, [
  ["render", function(t, i, e, o, c, r) {
    return {
      a: n.s(r.styleObj),
      b: n.n("uniui-" + e.type),
      c: n.n(e.customPrefix),
      d: n.n(e.customPrefix ? e.type : ""),
      e: n.o((function() {
        return r._onClick && r._onClick.apply(r, arguments)
      }))
    }
  }]
]);
wx.createComponent(e);