{"version": 3, "file": "config.js", "sources": ["config.js"], "sourcesContent": ["/**\r\n * 应用配置文件\r\n * 适配uni-app多平台环境\r\n */\r\n\r\n// 环境配置\r\nconst config = {\r\n  // 开发环境\r\n  development: {\r\n    //baseUrl: 'https://bakjgyyxt.baoan.gov.cn',\r\n    baseUrl:\"https://www.j8j0.com/ \",\r\n    debug: true,\r\n    timeout: 10000\r\n  },\r\n  // 生产环境\r\n  production: {\r\n    baseUrl: 'https://www.j8j0.com/ ',\r\n    debug: false,\r\n    timeout: 30000\r\n  }\r\n}\r\n\r\n// 获取当前环境配置\r\nconst getConfig = () => {\r\n  let currentEnv = 'production'\r\n  \r\n  // #ifdef MP-WEIXIN\r\n  // 微信小程序环境判断\r\n  currentEnv = process.env.NODE_ENV === 'development' ? 'development' : 'production'\r\n  // #endif\r\n\r\n  // #ifdef H5\r\n  // H5环境判断\r\n  currentEnv = process.env.NODE_ENV === 'development' ? 'development' : 'production'\r\n  // #endif\r\n\r\n  // #ifdef APP-PLUS\r\n  // App环境默认使用生产环境\r\n  currentEnv = 'production'\r\n  // #endif\r\n  \r\n  return config[currentEnv]\r\n}\r\n\r\nconst currentConfig = getConfig()\r\n\r\n// 导出配置对象\r\nexport default {\r\n  // 基础配置\r\n  baseUrl: currentConfig.baseUrl,\r\n  debug: currentConfig.debug,\r\n  timeout: currentConfig.timeout,\r\n  \r\n  // 业务配置\r\n  iSzgm: false,\r\n  clientId: 'be7052a7e4f802c20df10a8d131adb12',\r\n  \r\n  // 加密配置\r\n  publicKey: 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==',\r\n  privateKey: '**********',\r\n  \r\n  // 应用信息\r\n  appInfo: {\r\n    name: 'baoanquestacon-app',\r\n    version: '2.0.0',\r\n    logo: '/static/favicon.ico',\r\n    description: '宝安科技馆预约服务平台'\r\n  },\r\n  \r\n  // 微信小程序配置\r\n  appId: 'wx7fdbf0566b7e1707',\r\n  productionTip: false,\r\n  \r\n  // 地理位置配置\r\n  baoanLocation: {\r\n    latitude: 22.55866135902317,\r\n    longitude: 113.91141057014467,\r\n    name: '宝安科技馆',\r\n    address: '深圳市宝安区'\r\n  },\r\n  \r\n  // API接口配置\r\n  api: {\r\n    // 用户相关接口\r\n    user: {\r\n      login: '/wx/user/{appId}/login',\r\n      register: '/wx/user/register',\r\n      info: '/wx/user/info',\r\n      logout: '/wx/user/logout'\r\n    },\r\n    \r\n    // 预约相关接口\r\n    reservation: {\r\n      venue: '/apitp/venue',\r\n      movie: '/apitp/movie',\r\n      course: '/apitp/course',\r\n      create: '/apitp/reservation/create',\r\n      list: '/apitp/reservation/list',\r\n      cancel: '/apitp/reservation/cancel'\r\n    },\r\n    \r\n    // 内容相关接口\r\n    content: {\r\n      announcement: '/apitp/announcement/getInfo',\r\n      news: '/apitp/news/list'\r\n    }\r\n  },\r\n  \r\n  // 页面配置\r\n  pages: {\r\n    // 主包页面\r\n    main: [\r\n      'pages/index/index'\r\n    ],\r\n    \r\n    // 子包页面\r\n    subPackages: {\r\n      'pages_app': [\r\n        'login/index',\r\n        'register/index',\r\n        'user/index',\r\n        'contacts/index',\r\n        'entervenue/index',\r\n        'vieworder/index',\r\n        'curriculum/index'\r\n      ]\r\n    }\r\n  },\r\n  \r\n  // 存储配置\r\n  storage: {\r\n    prefix: 'baoan_',\r\n    keys: {\r\n      token: 'token',\r\n      userInfo: 'userInfo',\r\n      contacts: 'contacts',\r\n      settings: 'settings'\r\n    }\r\n  },\r\n  \r\n  // 错误码配置\r\n  errorCodes: {\r\n    SUCCESS: 200,\r\n    UNAUTHORIZED: 401,\r\n    FORBIDDEN: 403,\r\n    NOT_FOUND: 404,\r\n    SERVER_ERROR: 500,\r\n    NETWORK_ERROR: -1,\r\n    TIMEOUT: -2\r\n  },\r\n  \r\n  // 平台特定配置\r\n  platform: {\r\n    // #ifdef MP-WEIXIN\r\n    weixin: {\r\n      shareConfig: {\r\n        title: '宝安科技馆预约服务',\r\n        desc: '便捷的场馆预约服务平台',\r\n        imageUrl: '/static/favicon.ico'\r\n      }\r\n    }\r\n    // #endif\r\n  }\r\n}\r\n\r\n// 导出环境判断函数\r\nexport const isDevelopment = () => currentConfig.debug\r\nexport const isProduction = () => !currentConfig.debug\r\n\r\n// 导出平台判断函数\r\nexport const getPlatform = () => {\r\n  // #ifdef MP-WEIXIN\r\n  return 'weixin'\r\n  // #endif\r\n  \r\n  // #ifdef H5\r\n  return 'h5'\r\n  // #endif\r\n  \r\n  // #ifdef APP-PLUS\r\n  return 'app'\r\n  // #endif\r\n  \r\n  return 'unknown'\r\n}"], "names": [], "mappings": ";AAMA,MAAM,SAAS;AAAA;AAAA,EAEb,aAAa;AAAA;AAAA,IAEX,SAAQ;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACF;AAGA,MAAM,YAAY,MAAM;AACtB,MAAI,aAAa;AAIjB,eAAsD;AAatD,SAAO,OAAO,UAAU;AAC1B;AAEA,MAAM,gBAAgB,UAAU;AAGhC,MAAe,WAAA;AAAA;AAAA,EAEb,SAAS,cAAc;AAAA,EACvB,OAAO,cAAc;AAAA,EACrB,SAAS,cAAc;AAAA;AAAA,EAGvB,OAAO;AAAA,EACP,UAAU;AAAA;AAAA,EAGV,WAAW;AAAA,EACX,YAAY;AAAA;AAAA,EAGZ,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,aAAa;AAAA,EACf;AAAA;AAAA,EAGA,OAAO;AAAA,EACP,eAAe;AAAA;AAAA,EAGf,eAAe;AAAA,IACb,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAGA,KAAK;AAAA;AAAA,IAEH,MAAM;AAAA,MACJ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA;AAAA,IAGA,aAAa;AAAA,MACX,OAAO;AAAA,MACP,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA;AAAA,IAGA,SAAS;AAAA,MACP,cAAc;AAAA,MACd,MAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA,EAGA,OAAO;AAAA;AAAA,IAEL,MAAM;AAAA,MACJ;AAAA,IACF;AAAA;AAAA,IAGA,aAAa;AAAA,MACX,aAAa;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,SAAS;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,MACJ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA,IACZ;AAAA,EACF;AAAA;AAAA,EAGA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,IACd,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA;AAAA,EAGA,UAAU;AAAA,IAER,QAAQ;AAAA,MACN,aAAa;AAAA,QACX,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EAEF;AACF;;"}