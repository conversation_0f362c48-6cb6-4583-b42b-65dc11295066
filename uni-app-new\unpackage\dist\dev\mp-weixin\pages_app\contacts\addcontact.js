"use strict";
const common_vendor = require("../../common/vendor.js");
const api_api = require("../../api/api.js");
const config = require("../../config.js");
const _sfc_main = {
  name: "AddContact",
  data() {
    return {
      form: {
        linkmanAge: "",
        linkmanCertificate: "",
        linkmanCertificateType: 0,
        linkmanName: "",
        linkmanPhone: ""
      },
      cardTypeList: [
        { label: "身份证", value: 0 },
        { label: "港澳台居民通行证/居住证", value: 1 },
        { label: "临时身份证", value: 2 },
        { label: "护照", value: 3 }
      ],
      linkmanID: null,
      agree: false,
      isShowMask: false,
      noticeList: [
        {
          textList: ["我们非常重视对您的个人隐私保护，有时候我们需要某些信息才能为您提供您请求的服务，本隐私声明解释了这些情况下的数据收集和使用情况。"]
        },
        {
          title: "关于您的个人信息",
          textList: [
            "我们严格保护您个人信息的安全。我们使用各种安全技术和程序来保护您的个人信息不被未经授权的访问、使用或泄漏。",
            "我们会在法律要求或符合我们的相关服务条款、软件许可使用协议约定的情况下透露您的个人信息，或者有充分理由相信必须这样做才能："
          ],
          ulList: [
            "满足法律或行政法规的明文规定，或者符合我们APP/小程序适用的法律程序；",
            "符合我们相关服务条款、软件许可使用协议的约定；",
            "在紧急情况下保护服务的用户或大众的个人安全。"
          ],
          footText: "我们不会未经您的允许将这些信息与第三方共享，本声明已经列出的上述情况除外。"
        },
        {
          title: "关于免责说明",
          textList: ["就下列相关事宜的发生，我们不承担任何法律责任："],
          ulList: [
            "由于您将用户密码告知他人或与他人共享注册帐户，由此导致的任何个人信息的泄漏，或其他非因我们原因导致的个人信息的泄漏；",
            "我们根据法律规定或政府相关政策要求提供您的个人信息；",
            "任何由于黑客攻击、电脑病毒侵入或政府管制而造成的暂时性网站关闭；",
            "因不可抗力导致的任何后果；",
            "我们在各服务条款及声明中列明的使用方式或免责情形。"
          ]
        }
      ]
    };
  },
  computed: {
    config() {
      return config.config;
    }
  },
  onLoad(options) {
    this.linkmanID = options.id || null;
    if (this.linkmanID) {
      this.getContactDetail();
    }
  },
  methods: {
    // 获取联系人详情
    getContactDetail() {
      api_api.myRequest({
        url: `/auth/linkman/${this.linkmanID}`,
        method: "GET"
      }).then((res) => {
        if (res.data && res.data.data) {
          this.form = res.data.data;
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages_app/contacts/addcontact.vue:179", "获取联系人详情失败:", err);
        common_vendor.index.showToast({
          title: "获取联系人详情失败",
          icon: "error"
        });
      });
    },
    // 选择证件类型
    checkCardType(value) {
      this.form.linkmanCertificateType = value;
    },
    // 提交表单
    submit() {
      if (!this.form.linkmanName || this.form.linkmanName.trim() === "") {
        common_vendor.index.showModal({
          title: "提示",
          content: "请输入姓名",
          showCancel: false
        });
        return;
      }
      if (!this.form.linkmanCertificate || this.form.linkmanCertificate.trim() === "") {
        common_vendor.index.showModal({
          title: "提示",
          content: "请输入证件号码",
          showCancel: false
        });
        return;
      }
      if (!this.form.linkmanPhone || this.form.linkmanPhone.trim() === "") {
        common_vendor.index.showModal({
          title: "提示",
          content: "请输入手机号码",
          showCancel: false
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(this.form.linkmanPhone)) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请输入正确的手机号码",
          showCancel: false
        });
        return;
      }
      if (this.form.linkmanCertificateType !== 3 && !/^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·\s]{2,20}$/.test(this.form.linkmanName)) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请输入正确的姓名",
          showCancel: false
        });
        return;
      }
      if (this.form.linkmanCertificateType === 1 || this.form.linkmanCertificateType === 3) {
        if (!this.form.linkmanAge || !/^\d{1,2}$/.test(this.form.linkmanAge)) {
          common_vendor.index.showModal({
            title: "提示",
            content: "请输入年龄",
            showCancel: false
          });
          return;
        }
        const age = parseInt(this.form.linkmanAge);
        if (age < 1 || age > 120) {
          common_vendor.index.showModal({
            title: "提示",
            content: "请输入正确的年龄（1-120岁）",
            showCancel: false
          });
          return;
        }
      }
      if (!this.agree) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请详细阅读并勾选下方《用户服务协议和隐私政策》",
          showCancel: false
        });
        return;
      }
      const url = this.linkmanID ? "/auth/linkman/edit" : "/auth/linkman/add";
      const method = this.linkmanID ? "PUT" : "POST";
      api_api.myRequest({
        url,
        method,
        data: this.form
      }).then((res) => {
        common_vendor.index.showToast({
          title: this.linkmanID ? "修改成功" : "添加成功",
          icon: "success",
          duration: 2e3,
          success: () => {
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 2e3);
          }
        });
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages_app/contacts/addcontact.vue:297", "提交失败:", err);
        common_vendor.index.showToast({
          title: "提交失败",
          icon: "error"
        });
      });
    },
    // 切换协议同意状态
    changeAgree() {
      this.agree = !this.agree;
    },
    // 显示/隐藏用户协议
    showUserAgreement(show) {
      this.isShowMask = show;
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$options.config.iSzgm
  }, !$options.config.iSzgm ? {
    b: common_vendor.p({
      isBack: true,
      isShowHome: false,
      title: $data.linkmanID ? "编辑人员" : "新增人员",
      color: "#000"
    })
  } : {}, {
    c: common_vendor.f($data.cardTypeList, (item, index, i0) => {
      return {
        a: index === $data.form.linkmanCertificateType ? 1 : "",
        b: common_vendor.t(item.label),
        c: index,
        d: common_vendor.o(($event) => $options.checkCardType(item.value), index)
      };
    }),
    d: $data.form.linkmanName,
    e: common_vendor.o(($event) => $data.form.linkmanName = $event.detail.value),
    f: $data.form.linkmanCertificate,
    g: common_vendor.o(($event) => $data.form.linkmanCertificate = $event.detail.value),
    h: $data.form.linkmanPhone,
    i: common_vendor.o(($event) => $data.form.linkmanPhone = $event.detail.value),
    j: $data.form.linkmanCertificateType === 1 || $data.form.linkmanCertificateType === 3
  }, $data.form.linkmanCertificateType === 1 || $data.form.linkmanCertificateType === 3 ? {
    k: $data.form.linkmanAge,
    l: common_vendor.o(($event) => $data.form.linkmanAge = $event.detail.value)
  } : {}, {
    m: $data.agree ? 1 : "",
    n: common_vendor.o(($event) => $options.showUserAgreement(true)),
    o: common_vendor.o((...args) => $options.changeAgree && $options.changeAgree(...args)),
    p: common_vendor.o((...args) => $options.submit && $options.submit(...args)),
    q: $data.isShowMask
  }, $data.isShowMask ? {
    r: common_vendor.f($data.noticeList, (notice, index, i0) => {
      return common_vendor.e({
        a: notice.title
      }, notice.title ? {
        b: common_vendor.t(notice.title)
      } : {}, {
        c: common_vendor.f(notice.textList, (text, textIndex, i1) => {
          return {
            a: common_vendor.t(text),
            b: textIndex
          };
        }),
        d: common_vendor.f(notice.ulList, (item, itemIndex, i1) => {
          return {
            a: common_vendor.t(item),
            b: itemIndex
          };
        }),
        e: notice.footText
      }, notice.footText ? {
        f: common_vendor.t(notice.footText)
      } : {}, {
        g: index
      });
    }),
    s: common_vendor.o(($event) => $options.showUserAgreement(false))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-380f9cc6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/contacts/addcontact.js.map
