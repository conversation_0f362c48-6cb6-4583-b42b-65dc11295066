"use strict";
const common_vendor = require("../../common/vendor.js");
const api_api = require("../../api/api.js");
const config = require("../../config.js");
const utils_index = require("../../utils/index.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "CurriculumIndex",
  components: {
    MyHeader: () => "../../components/my-header/my-header2.js"
  },
  data() {
    return {
      config: config.config,
      userInfo: {
        avatar: null,
        nickName: null,
        sex: null,
        phonenumber: null
      },
      isShowMask: true,
      noticeList: [
        {
          title: "一、报名方式",
          view: [
            "1、本次公益培训课程仅接受微信报名，名额有限，先到先得，额满为止",
            "2、课程咨询热线：0755-27880235。",
            "3、公益课开始上课后自动关闭报名入口。",
            "4、学员可通过个人中心-课程预约-查看凭证，管理查询个人预约信息。"
          ]
        },
        {
          title: "二、温馨提示",
          view: [
            "1、学员必须以本人真实信息报名，如现场确认时发现报名信息不符合则取消资格",
            "2、课程期间，请学员严格遵守上课时间，为保证教学质量迟到超过10分钟则不能进入教室学习。"
          ]
        }
      ],
      selected: [],
      times: null,
      readText: "确定已阅读（5s）",
      readTime: 5,
      timeSection: {
        start: "",
        end: ""
      },
      curriculumList: [],
      isShowGray: false,
      fulldate: null
    };
  },
  computed: {
    // 兼容原有的config计算属性
    configComputed() {
      return config.config;
    }
  },
  created() {
    this.getTimeSection();
    this.showMask();
  },
  onShow() {
    const token = common_vendor.index.getStorageSync("token");
    if (token) {
      this.getDayInfo();
    } else {
      this.showLoginModal();
    }
  },
  methods: {
    getImages: api_api.getImages,
    // 获取时间区间
    getTimeSection() {
      const now = /* @__PURE__ */ new Date();
      const start = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
      const endDate = new Date(now.getTime() + 6 * 24 * 60 * 60 * 1e3);
      const end = endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate();
      this.timeSection.start = start;
      this.timeSection.end = end;
    },
    // 同意须知
    agree() {
      if (this.readTime === 1) {
        setTimeout(() => {
          this.isShowMask = false;
        }, 300);
      }
    },
    // 显示须知弹窗
    showMask() {
      const accountInfo = common_vendor.index.getAccountInfoSync();
      this.isShowMask = accountInfo.miniProgram.envVersion !== "develop";
      this.isShowGray = true;
      this.readTime = 5;
      this.times = setInterval(() => {
        if (this.readTime === 1) {
          this.readText = "确定已阅读";
          this.isShowGray = false;
          clearInterval(this.times);
        } else {
          this.readTime--;
          this.readText = `确定已阅读（${this.readTime}s）`;
        }
      }, 1e3);
    },
    // 选择课程
    chooseCourse(courseId) {
      common_vendor.index.navigateTo({
        url: `/pages_app/curriculum/choosecurriculum?id=${courseId}`
      });
    },
    // 显示登录弹窗
    showLoginModal() {
      common_vendor.index.showModal({
        title: "温馨提示",
        content: "授权微信登录后才能正常使用小程序功能",
        success: (res) => {
          if (res.cancel) {
            common_vendor.index.showToast({
              title: "您拒绝了请求，不能正常使用小程序",
              icon: "error",
              duration: 2e3
            });
          } else {
            this.performLogin();
          }
        }
      });
    },
    // 执行登录
    performLogin() {
      common_vendor.index.login({
        provider: "weixin",
        success: (res) => {
          const requestData = {
            data: { code: res.code },
            url: `/wx/user/${config.config.appId}/login`,
            method: "get"
          };
          api_api.myRequest(requestData).then((response) => {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({ title: "登录成功" });
            const user = response.data.data.user;
            common_vendor.index.setStorageSync("token", response.data.data.token);
            common_vendor.index.setStorageSync("userInfo", user);
            this.userInfo = user || {
              avatar: null,
              nickName: null,
              sex: null,
              phonenumber: null
            };
            this.getDayInfo();
          }).catch((error) => {
            common_vendor.index.__f__("log", "at pages_app/curriculum/index.vue:284", "登录异常:", error);
            common_vendor.index.showToast({
              title: "登录异常:" + error.errMsg,
              icon: "error",
              duration: 5e3
            });
          });
        }
      });
    },
    // 获取场馆开放信息
    async getDayInfo() {
      try {
        const response = await api_api.myRequest({
          url: "/auth/venue/getVenueInfo"
        });
        if (response.data.code === 200) {
          const data = response.data.data;
          data.forEach((item) => {
            const date = utils_index.Utils.changeTime(item.day, true);
            if (item.isClose === "0") {
              this.selected.push({
                date,
                info: "闭馆"
              });
            }
          });
          for (let i = 0; i < data.length; i++) {
            if (data[i].isClose === "1") {
              this.timeSection.start = utils_index.Utils.changeTime(data[i].day, true);
              break;
            }
          }
          if (this.timeSection.start) {
            this.getSessionDetail({
              fulldate: this.fulldate || this.timeSection.start
            });
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/curriculum/index.vue:337", "获取场馆信息失败:", error);
      }
    },
    // 获取课程详情
    getSessionDetail(event) {
      api_api.myRequest({
        url: "/web/session/getSessionDetail",
        method: "post",
        data: {
          date: event.fulldate
        }
      }).then((response) => {
        if (response.data.code === 200) {
          this.fulldate = event.fulldate;
          const data = response.data.data;
          const now = /* @__PURE__ */ new Date();
          data.forEach((item) => {
            item.isShow = new Date(item.courseStartTime) > now;
            item.courseEndTime = utils_index.Utils.changeTime(item.courseEndTime);
            item.courseStartTime = utils_index.Utils.changeTime(item.courseStartTime);
          });
          this.curriculumList = data.filter((item) => item.isShow);
        }
      }).catch((error) => {
        common_vendor.index.__f__("error", "at pages_app/curriculum/index.vue:364", "获取课程详情失败:", error);
        common_vendor.index.showToast({
          title: "获取课程信息失败",
          icon: "error"
        });
      });
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  const _component_uni_calendar = common_vendor.resolveComponent("uni-calendar");
  (_easycom_my_header2 + _component_uni_calendar)();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.config.iSzgm
  }, !$data.config.iSzgm ? {
    b: common_vendor.p({
      ["is-back"]: true,
      ["is-show-home"]: true,
      title: "宝安科技馆",
      background: "transparent",
      ["menu-class"]: "df"
    })
  } : {}, {
    c: common_vendor.o($options.getSessionDetail),
    d: common_vendor.p({
      date: $data.timeSection.start,
      insert: true,
      lunar: false,
      ["show-month"]: false,
      ["start-date"]: $data.timeSection.start,
      ["end-date"]: $data.timeSection.end,
      selected: $data.selected
    }),
    e: common_assets._imports_0$5,
    f: $data.curriculumList.length > 0
  }, $data.curriculumList.length > 0 ? {
    g: common_vendor.f($data.curriculumList, (item, k0, i0) => {
      return {
        a: $options.getImages(item.courseCover),
        b: common_vendor.t(item.courseName),
        c: common_vendor.t(item.courseAgeProp),
        d: common_vendor.t(item.courseStartTime),
        e: common_vendor.t(item.courseEndTime),
        f: common_vendor.t(item.courseAddress),
        g: common_vendor.t(item.inventoryVotes),
        h: common_vendor.n({
          "gray": item.inventoryVotes == 0
        }),
        i: item.id,
        j: common_vendor.n({
          "disNone": !item.isShow
        }),
        k: common_vendor.o(($event) => $options.chooseCourse(item.id), item.id)
      };
    })
  } : {}, {
    h: $data.isShowMask
  }, $data.isShowMask ? {
    i: common_vendor.f($data.noticeList, (notice, index, i0) => {
      return {
        a: common_vendor.t(notice.title),
        b: common_vendor.f(notice.view, (item, itemIndex, i1) => {
          return {
            a: common_vendor.t(item),
            b: itemIndex
          };
        }),
        c: index
      };
    }),
    j: common_vendor.t($data.readText),
    k: common_vendor.n({
      "gray": $data.isShowGray
    }),
    l: common_vendor.o((...args) => $options.agree && $options.agree(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/curriculum/index.js.map
