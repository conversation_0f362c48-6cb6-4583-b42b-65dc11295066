var e = require("../../../../@babel/runtime/helpers/toConsumableArray"),
  t = require("./uni-data-picker.js"),
  a = require("../../../../common/vendor.js"),
  s = {
    name: "UniDataPickerView",
    emits: ["nodeclick", "change", "datachange", "update:modelValue"],
    mixins: [t.dataPicker],
    props: {
      managedMode: {
        type: Boolean,
        default: !1
      },
      ellipsis: {
        type: Boolean,
        default: !0
      }
    },
    created: function() {
      var e = this;
      this.managedMode || this.$nextTick((function() {
        e.loadData()
      }))
    },
    methods: {
      onPropsChange: function() {
        var e = this;
        this._treeData = [], this.selectedIndex = 0, this.$nextTick((function() {
          e.loadData()
        }))
      },
      handleSelect: function(e) {
        this.selectedIndex = e
      },
      handleNodeClick: function(t, a, s) {
        var i = this;
        if (!t.disable) {
          var n = this.dataList[a][s],
            d = n[this.map.text],
            l = n[this.map.value];
          if (a < this.selected.length - 1 ? (this.selected.splice(a, this.selected.length - a), this.selected.push({
              text: d,
              value: l
            })) : a === this.selected.length - 1 && this.selected.splice(a, 1, {
              text: d,
              value: l
            }), n.isleaf) this.onSelectedChange(n, n.isleaf);
          else {
            var c = this._updateBindData(),
              o = c.isleaf,
              h = c.hasNodes;
            this.isLocalData ? this.onSelectedChange(n, !h || o) : this.isCloudDataList ? this.onSelectedChange(n, !0) : this.isCloudDataTree && (o ? this.onSelectedChange(n, n.isleaf) : h || this.loadCloudDataNode((function(t) {
              var a;
              t.length ? ((a = i._treeData).push.apply(a, e(t)), i._updateBindData(n)) : n.isleaf = !0, i.onSelectedChange(n, n.isleaf)
            })))
          }
        }
      },
      updateData: function(e) {
        this._treeData = e.treeData, this.selected = e.selected, this._treeData.length ? this._updateBindData() : this.loadData()
      },
      onDataChange: function() {
        this.$emit("datachange")
      },
      onSelectedChange: function(e, t) {
        t && this._dispatchEvent(), e && this.$emit("nodeclick", e)
      },
      _dispatchEvent: function() {
        this.$emit("change", this.selected.slice(0))
      }
    }
  };
Array || a.resolveComponent("uni-load-more")();
Math;
var i = a._export_sfc(s, [
  ["render", function(e, t, s, i, n, d) {
    return a.e({
      a: !e.isCloudDataList
    }, e.isCloudDataList ? {} : {
      b: a.f(e.selected, (function(t, s, i) {
        return {
          a: a.t(t.text || ""),
          b: s,
          c: s == e.selectedIndex ? 1 : "",
          d: a.o((function(e) {
            return d.handleSelect(s)
          }), s)
        }
      }))
    }, {
      c: a.f(e.dataList[e.selectedIndex], (function(t, s, i) {
        return a.e({
          a: a.t(t[e.map.text]),
          b: e.selected.length > e.selectedIndex && t[e.map.value] == e.selected[e.selectedIndex].value
        }, (e.selected.length > e.selectedIndex && (t[e.map.value], e.selected[e.selectedIndex].value), {}), {
          c: t.disable ? 1 : "",
          d: s,
          e: a.o((function(a) {
            return d.handleNodeClick(t, e.selectedIndex, s)
          }), s)
        })
      })),
      d: e.loading
    }, e.loading ? {
      e: a.p({
        contentText: e.loadMore,
        status: "loading"
      })
    } : {}, {
      f: e.errorMessage
    }, e.errorMessage ? {
      g: a.t(e.errorMessage)
    } : {})
  }]
]);
wx.createComponent(i);