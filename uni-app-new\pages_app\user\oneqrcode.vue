<template>
  <view class="qrcode-container">
    <!-- 装饰元素 -->
    <view class="left-bottom-sign"></view>
    <view class="right-top-sign"></view>
    
    <!-- 返回按钮 -->
    <view class="back-btn" @tap="goBack">
      <text class="back-icon">‹</text>
    </view>
    
    <!-- 主体内容 -->
    <view class="wrapper">
      <view class="left-top-sign">QRCODE</view>
      <view class="welcome">管理员二维码</view>
      
      <!-- 二维码显示区域 -->
      <view class="qrcode-content">
        <view class="qrcode-wrapper">
          <image 
            v-if="qrcodeImage" 
            :src="qrcodeImage" 
            class="qrcode-image"
            mode="aspectFit"
          />
          <view v-else class="qrcode-loading">
            <text class="loading-text">生成中...</text>
          </view>
        </view>
      </view>
      
      <!-- 刷新按钮 -->
      <button 
        class="refresh-btn" 
        :disabled="refreshing"
        @tap="refreshQRCode"
      >
        {{ refreshing ? '刷新中...' : '刷新二维码' }}
      </button>
      
      <!-- 提示信息 -->
      <view class="tips">
        <text class="tips-text">二维码每60秒自动刷新</text>
        <text class="tips-text">请保持屏幕亮度适中</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'OneQRCode',
  data() {
    return {
      qrcodeImage: '',
      refreshing: false,
      refreshTimer: null,
      originalBrightness: 0
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onUnload() {
    this.cleanup()
  },
  
  methods: {
    // 初始化页面
    async initPage() {
      try {
        // 保存原始亮度并设置适中亮度
        await this.setBrightness()
        
        // 生成二维码
        await this.generateQRCode()
        
        // 设置自动刷新
        this.startAutoRefresh()
      } catch (error) {
        console.error('初始化失败:', error)
        uni.showToast({
          title: '初始化失败',
          icon: 'error'
        })
      }
    },
    
    // 设置屏幕亮度
    async setBrightness() {
      try {
        // 获取当前亮度
        const brightness = await this.getScreenBrightness()
        this.originalBrightness = brightness
        
        // 设置适中亮度
        await this.setScreenBrightness(0.5)
      } catch (error) {
        console.warn('设置亮度失败:', error)
      }
    },
    
    // 获取屏幕亮度
    getScreenBrightness() {
      return new Promise((resolve, reject) => {
        uni.getScreenBrightness({
          success: (res) => resolve(res.value),
          fail: reject
        })
      })
    },
    
    // 设置屏幕亮度
    setScreenBrightness(value) {
      return new Promise((resolve, reject) => {
        uni.setScreenBrightness({
          value,
          success: resolve,
          fail: reject
        })
      })
    },
    
    // 生成二维码
    async generateQRCode() {
      if (this.refreshing) return
      
      this.refreshing = true
      
      try {
        const res = await this.$myRequest({
          url: '/auth/venue/getAdminCode',
          method: 'get'
        })
        
        if (res.code === 200) {
          const qrData = res.data.data || ''
          this.qrcodeImage = this.drawQRCode(qrData)
        } else {
          throw new Error(res.msg || '获取二维码失败')
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        uni.showToast({
          title: error.message || '生成二维码失败',
          icon: 'error'
        })
      } finally {
        this.refreshing = false
      }
    },
    
    // 绘制二维码
    drawQRCode(data) {
      try {
        // 这里需要引入二维码生成库
        // 由于uni-app环境限制，这里使用简化的实现
        // 实际项目中应该使用专门的二维码生成库
        
        // 创建canvas绘制二维码
        const canvas = uni.createCanvasContext('qrcode-canvas')
        
        // 简化实现：返回一个占位图片
        // 实际应该使用qrcode.js等库生成
        return this.generateQRCodeImage(data)
      } catch (error) {
        console.error('绘制二维码失败:', error)
        return ''
      }
    },
    
    // 生成二维码图片（简化实现）
    generateQRCodeImage(data) {
      // 这里应该集成真正的二维码生成库
      // 例如：qrcode.js, qrious等
      // 当前返回占位实现
      
      try {
        // 使用第三方二维码API或本地库
        const size = 400
        const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}`
        return qrcodeUrl
      } catch (error) {
        console.error('生成二维码图片失败:', error)
        return ''
      }
    },
    
    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.generateQRCode()
      }, 60000) // 60秒刷新一次
    },
    
    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    // 手动刷新二维码
    refreshQRCode() {
      this.generateQRCode()
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 清理资源
    async cleanup() {
      // 停止自动刷新
      this.stopAutoRefresh()
      
      // 恢复原始亮度
      if (this.originalBrightness > 0) {
        try {
          await this.setScreenBrightness(this.originalBrightness)
        } catch (error) {
          console.warn('恢复亮度失败:', error)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.qrcode-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;

  // 装饰元素
  .left-bottom-sign {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 200rpx;
    height: 200rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0 200rpx 0 0;
  }

  .right-top-sign {
    position: absolute;
    right: 0;
    top: 0;
    width: 150rpx;
    height: 150rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0 0 0 150rpx;
  }

  // 返回按钮
  .back-btn {
    position: absolute;
    left: 30rpx;
    top: 100rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .back-icon {
      font-size: 40rpx;
      color: white;
      font-weight: bold;
    }
  }

  // 主体内容
  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 60rpx 40rpx;

    .left-top-sign {
      position: absolute;
      left: 40rpx;
      top: 200rpx;
      font-size: 48rpx;
      font-weight: bold;
      color: rgba(255, 255, 255, 0.3);
      letter-spacing: 4rpx;
    }

    .welcome {
      font-size: 48rpx;
      font-weight: 600;
      color: white;
      margin-bottom: 80rpx;
      text-align: center;
    }

    // 二维码内容区域
    .qrcode-content {
      margin-bottom: 60rpx;

      .qrcode-wrapper {
        width: 480rpx;
        height: 480rpx;
        background: white;
        border-radius: 20rpx;
        padding: 40rpx;
        box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;

        .qrcode-image {
          width: 400rpx;
          height: 400rpx;
        }

        .qrcode-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 400rpx;
          height: 400rpx;

          .loading-text {
            font-size: 32rpx;
            color: #999;
          }
        }
      }
    }

    // 刷新按钮
    .refresh-btn {
      width: 400rpx;
      height: 88rpx;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 44rpx;
      border: none;
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 40rpx;

      &::after {
        border: none;
      }

      &:disabled {
        background: rgba(255, 255, 255, 0.5);
        color: #999;
      }
    }

    // 提示信息
    .tips {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10rpx;

      .tips-text {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.7);
        text-align: center;
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.qrcode-container {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.wrapper {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
