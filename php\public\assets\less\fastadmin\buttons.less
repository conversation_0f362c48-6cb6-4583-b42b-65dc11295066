/*
 * Component: Button
 * -----------------
 */

.btn {
    /*.border-radius(@btn-border-radius);*/
    .box-shadow(@btn-boxshadow);
    border: 1px solid transparent;

    &.uppercase {
        text-transform: uppercase
    }

    // Flat buttons
    &.btn-flat {
        .border-radius(0);
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        border-width: 1px;
    }

    // Active state
    &:active {
        -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
        -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
        box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    }

    &:focus {
        outline: none;
    }

    // input file btn
    &.btn-file {
        position: relative;
        overflow: hidden;
        > input[type='file'] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            .opacity(0);
            outline: none;
            background: white;
            cursor: inherit;
            display: block;
        }
    }
}

//Button color variations
.btn-default {
    background-color: #f4f4f4;
    color: #444;
    border-color: #ddd;
    &:hover,
        &:active,
        &.hover {
        background-color: darken(#f4f4f4, 5%);
    }
}


.btn-primary-light {
    background-color: @primary-light-bg;
    border-color: @primary-light-border;
    color: @primary-light-text;
    &:hover,
    &:active,
    &.hover {
        background-color: darken(@primary-light-bg, 5%);
        border-color: darken(@primary-light-bg, 10%);
        color: @primary-light-text;
    }
}

.btn-success-light {
    background-color: @success-light-bg;
    border-color: @success-light-border;
    color: @success-light-text;
    &:hover,
    &:active,
    &.hover {
        background-color: darken(@success-light-bg, 5%);
        border-color: darken(@success-light-bg, 10%);
        color: @success-light-text;
    }
}

.btn-danger-light,
.btn-error-light {
    background-color: @danger-light-bg;
    border-color: @danger-light-border;
    color: @danger-light-text;
    &:hover,
    &:active,
    &.hover {
        background-color: darken(@danger-light-bg, 5%);
        border-color: darken(@danger-light-bg, 10%);
        color: @danger-light-text;
    }
}

.btn-warning-light {
    background-color: @warning-light-bg;
    border-color: @warning-light-border;
    color: @warning-light-text;
    &:hover,
    &:active,
    &.hover {
        background-color: darken(@warning-light-bg, 5%);
        border-color: darken(@warning-light-bg, 10%);
        color: @warning-light-text;
    }
}

.btn-info-light {
    background-color: @info-light-bg;
    border-color: @info-light-border;
    color: @info-light-text;
    &:hover,
    &:active,
    &.hover {
        background-color: darken(@info-light-bg, 5%);
        border-color: darken(@info-light-bg, 10%);
        color: @info-light-text;
    }
}

.btn-outline {
    border: 1px solid #fff;
    background: transparent;
    color: #fff;
    &:hover,
        &:focus,
        &:active {
        color: rgba(255, 255, 255, .7);
        border-color: rgba(255, 255, 255, .7);
    }
}

.btn-link {
    .box-shadow(none);
}

//General .btn with bg class
.btn[class*='bg-']:hover {
    .box-shadow(inset 0 0 100px rgba(0, 0, 0, 0.2));
}

// Application buttons
.btn-app {
    .border-radius(3px);
    position: relative;
    padding: 15px 5px;
    margin: 0 0 10px 10px;
    min-width: 80px;
    height: 60px;
    text-align: center;
    color: #666;
    border: 1px solid #ddd;
    background-color: #f4f4f4;
    font-size: 12px;
    //Icons within the btn
    > .fa, > .glyphicon, > .ion {
        font-size: 20px;
        display: block;
    }

    &:hover {
        background: #f4f4f4;
        color: #444;
        border-color: #aaa;
    }

    &:active, &:focus {
        -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
        -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
        box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    }

    //The badge
    > .badge {
        position: absolute;
        top: -3px;
        right: -10px;
        font-size: 10px;
        font-weight: 400;
    }
}
