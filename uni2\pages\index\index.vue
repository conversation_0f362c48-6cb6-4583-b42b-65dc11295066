<template>
	<view class="content">
		<Header :isBack="false" :isShowHome="false" title="宝安科技馆" color="#fff" :background="`transparent`">
		</Header>
		<view class="title">线上智能服务平台</view>

		<!-- 仅保留以下 btn_list -->
		<view class="btn_list">
			<button type="default" v-for="(item,index) in jumpList" :key="index" :style="{
				background:`url(${item.bg}) no-repeat center center`,
				backgroundSize:`100% 100%`,
				boxShadow:item.shadow
			}" @click="jumpTo(item.path,item.id)">
				<view class="nav_name">
					{{item.name}}
				</view>
				<view class="icon">
					<image :src="item.icon" mode="aspectFill" class="set_icon"></image>
				</view>
			</button>
		</view>
		<uni-notice-bar class="scrollText" scrollable="true" :speed="41.8" single="true" background-color="#ffffff"
			:text="notice">
		</uni-notice-bar>
	</view>
</template>

<script>
	import {
		header
	} from '@/component/header.vue';
	import {
		uniNoticeBar
	} from '@/uni_modules/uni-notice-bar/components/uni-notice-bar/uni-notice-bar.vue';
	// import Utils from '../../utils/index.js'
	// import wsWxPrivacy from '@/uni_modules/ws-wx-privacy/components/ws-wx-privacy/ws-wx-privacy.vue'
	export default {
		data() {
			return {
				jumpList: [{
					bg: require("../../static/img/home/<USER>"),
					icon: require("../../static/img/home/<USER>"),
					// shadow: `0px 5px 15px 0px rgba(80, 184 , 254 , 0.5)`,
					path: '/pages/entervenue/index',
					name: '入馆预约',
					id: 0
				}, {
					bg: require("../../static/img/home/<USER>"),
					icon: require("../../static/img/home/<USER>"),
					path: '/pages/vieworder/index',
					// shadow: `0px 5px 15px 0px rgba(247, 178, 46 , 0.5)`,
					name: '观影登记',
					id: 1
				}, {
					bg: require("../../static/img/home/<USER>"),
					icon: require("../../static/img/home/<USER>"),
					path: '/pages/curriculum/index',
					// shadow: `0px 5px 15px 0px rgba(131, 93, 255 , 0.5)`,
					name: '课程预约',
					id: 2
				}, {
					bg: require("../../static/img/home/<USER>"),
					icon: require("../../static/img/home/<USER>"),
					path: '/pages/user/index',
					// shadow: `0px 5px 15px 0px rgba(49, 223, 224 , 0.5)`,
					name: '个人中心',
					id: 3
				}],
				notice: '',	
				path: null,
				privacyAllow: false
			}
		},
		components: {
			header,
			uniNoticeBar,
			// wsWxPrivacy
		},
		onLoad() {
			    // #ifdef MP-WEIXIN
    const plugin = uni.requirePrivacyAuthorize || requirePlugin('ws-wx-privacy')?.requirePrivacyAuthorize
    if (plugin) {
      plugin({
        success: () => {
          console.log('✅ 用户已同意隐私协议')
          this.privacyAllow = true
        },
        fail: () => {
          console.log('❌ 用户拒绝隐私协议')
          this.privacyAllow = false
          uni.showModal({
            title: '提示',
            content: '您需要同意隐私协议才能继续使用本应用',
            showCancel: false,
            success: () => {
              uni.exitMiniProgram()
            }
          })
        }
      })
    } else {
      // 未启用隐私插件时直接放行（测试阶段）
      this.privacyAllow = true
    }
    // #endif			
			// #ifndef MP-WEIXIN
			// 非微信小程序环境直接初始化
			this.privacyAllow = true
			this.initApp()
			// #endif
		},  		
		onShow() {
			this.getNotice();	
		},
		methods: {
		
			// 初始化应用
			initApp() {
				// 在这里放置需要在隐私授权后执行的初始化逻辑
				console.log('应用初始化完成')
			},
			async jumpTo(path, id) {
				// 检查隐私授权状态
				if (!this.privacyAllow) {
					uni.showToast({
						title: '隐私保护，请退出小程序',
						icon: 'none'
					})
					return
				}				
				console.log('navigateTo:',path)
      			uni.navigateTo({ url: path })
			},
			getNotice() {
				this.$myRequest({
					url: '/apitp/announcement/getInfo'
				}).then(res => {
					if (res.data.code === 200) {
						this.notice = res.data.msg;
					}
				})
			}
		},
	}
</script>

<style lang="less" scoped>
	.content {
		width: 100%;
		height: 100vh;
		background-color: #f8f8f8;
		background: url(../../static/img/home/<USER>
		background-size: contain;
		color: #fff;
		overflow: scroll;

		.title {
			margin-top: 27upx;
			margin-bottom: 31upx;
			font-size: 58upx;
			font-family: 'YouSheBiaoTiHei';
			text-align: center;
		}

		.search {
			width: 692upx;
			height: 58upx;
			margin: 0 auto 31upx auto;
			background-color: #fff;
			border-radius: 19upx;
			display: flex;
			justify-content: center;
			align-items: center;

			.icon {
				width: 35upx;
				height: 35upx;
				background: url(../../static/img/home/<USER>
				background-size: 100% 100%;
				margin-right: 19upx;
			}


			.des {
				font-size: 27upx;
				font-family: 'PingFang SC';
				color: #888888;
			}



		}

		.btn_list {
			width: 100%;
			height: auto;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			margin-top: 79upx;

			button {
				width: 692upx;
				height: 192upx;
				margin: 0 auto 28upx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				box-sizing: border-box;
				padding: 0upx 87upx 0upx 79upx;
				margin-bottom: 38.46upx;
				font-weight: 600;

				&::after {
					border: none;
				}

				.nav_name {
					font-size: 42upx;
					font-family: 'PingFang SC';
					color: #FFFFFF;
				}

				.icon {
					width: 160upx;
					height: 160upx;
					background-color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;

					.set_icon {
						width: 115upx;
						height: 105upx;
					}
				}
			}
		}

		.scrollText {
			width: 727upx;
			display: block;
			margin: 131upx auto 69upx auto;
		}
	}
</style>
