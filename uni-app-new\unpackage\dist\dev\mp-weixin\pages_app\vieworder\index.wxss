/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.vieworder.data-v-87a111fe {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.vieworder .content.data-v-87a111fe {
  padding-top: 120rpx;
}
.vieworder .content .movie_content.data-v-87a111fe {
  padding: 20rpx;
}
.vieworder .content .movie_content .movie_list .movie_item.data-v-87a111fe {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  display: flex;
  transition: all 0.3s ease;
}
.vieworder .content .movie_content .movie_list .movie_item.data-v-87a111fe:active {
  transform: scale(0.98);
}
.vieworder .content .movie_content .movie_list .movie_item .movie_poster.data-v-87a111fe {
  width: 120rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_poster .poster_image.data-v-87a111fe {
  width: 100%;
  height: 100%;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_info.data-v-87a111fe {
  flex: 1;
  margin-right: 20rpx;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_info .movie_title.data-v-87a111fe {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_info .movie_type.data-v-87a111fe {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_info .movie_duration.data-v-87a111fe,
.vieworder .content .movie_content .movie_list .movie_item .movie_info .movie_rating.data-v-87a111fe {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_info .movie_desc.data-v-87a111fe {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-top: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_action.data-v-87a111fe {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  min-width: 120rpx;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_action .movie_status.data-v-87a111fe {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_action .reserve_btn.data-v-87a111fe {
  width: 120rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 30rpx;
  font-size: 24rpx;
  border: none;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_action .reserve_btn.data-v-87a111fe::after {
  border: none;
}
.vieworder .content .movie_content .movie_list .movie_item .movie_action .reserve_btn.data-v-87a111fe:disabled {
  background: #ccc;
  color: #999;
}
.vieworder .content .movie_content .empty_state.data-v-87a111fe,
.vieworder .content .movie_content .loading_state.data-v-87a111fe {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}
.vieworder .content .movie_content .empty_state .empty_icon.data-v-87a111fe,
.vieworder .content .movie_content .empty_state .loading_icon.data-v-87a111fe,
.vieworder .content .movie_content .loading_state .empty_icon.data-v-87a111fe,
.vieworder .content .movie_content .loading_state .loading_icon.data-v-87a111fe {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.vieworder .content .movie_content .empty_state .empty_text.data-v-87a111fe,
.vieworder .content .movie_content .empty_state .loading_text.data-v-87a111fe,
.vieworder .content .movie_content .loading_state .empty_text.data-v-87a111fe,
.vieworder .content .movie_content .loading_state .loading_text.data-v-87a111fe {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 平台特定样式 */
.content.data-v-87a111fe {
  padding-bottom: env(safe-area-inset-bottom);
}