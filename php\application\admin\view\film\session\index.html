

<style>
.bootstrap-datetimepicker-widget tr:hover {
   background-color: #808080;
}
/* 每列 .schedule-day 最小宽度控制： */
#schedule-columns .schedule-day {
    min-width: 325px;
    flex: 0 0 auto;
    padding-right: 0px;
}

</style>



<div class="panel panel-default panel-intro">
    {:build_heading()}
    <div class="panel-heading" style="padding-bottom: 15px;">
        <form class="form-inline" id="form-search">
            <div class="form-group">
                <label>排片日期：</label>
                <div class="input-group">
                    <input id="c-week_start" type="text" class="form-control datetimepicker" data-start="" data-end=""  placeholder="选择周">
                </div>
          
            </div>
            <div class="form-group" style="margin-left: 20px;">
   
                <button type="button" id="btn-copy-template" class="btn btn-primary" disabled>常用方案拷贝</button>

            </div>
            <div class="form-group">
                <label>目标时间：</label>
                <div class="input-group">
                    <input id="c-week_dend" type="text" class="form-control datetimepicker" data-start="" data-end="" placeholder="选择周">
                </div>
            </div>
        </form>
    </div>
    <div class="panel-body">
        <div id="schedule-columns" class="row" style="display: flex; flex-wrap: nowrap; overflow-x: auto;">
    
            <!-- 7列渲染区域 -->
            <!-- 周一至周日容器 -->
            <div class="col-md-2 schedule-day" data-day="1">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周一</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="2">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周二</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="3">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周三</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="4">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周四</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="5">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周五</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="6">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周六</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2 schedule-day" data-day="7">
                <div class="panel panel-default">
                    <div class="panel-heading"><span class="day-label">周日</span> <span class="date-label pull-right"></span></div>
                    <div class="panel-body" data-date="">
                        <div class="schedule-cards">
                            <!-- 排期卡片渲染到此处 -->
                        </div>
                        <div class="card-add text-center text-muted" style="cursor:pointer;border:1px dashed #ccc;padding:10px;">
                            + 添加影片排期
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
