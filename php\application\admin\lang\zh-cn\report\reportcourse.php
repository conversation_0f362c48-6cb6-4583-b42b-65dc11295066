<?php

return [
    'Id'                => '课程场次id',
    'Course_number'     => '课程编号',
    'Course_cover'      => '课程封面',
    'Course_introduce'  => '课程简介',
    'Course_name'       => '课程名称',
    'Course_age_prop'   => '课程适龄',
    'Course_poll'       => '课程票数',
    'Course_address'    => '课程具体地点',
    'Course_state'      => '课程状态影片状态(0:不可预约,1：可预约),新增时默认不可预约状态,可预约状态时票数无法修改,且无法变更为可预约状态。',
    'Course_start_time' => '课程开始时间(时分秒 HH-mm-ss)',
    'Course_end_time'   => '课程结束时间(时分秒 HH-mm-ss)',
    'Week'              => '周几(时间单位)',
    'Inventory_votes'   => '库存票数',
    'Create_by'         => '创建者',
    'Create_time'       => '创建时间',
    'Update_by'         => '更新者',
    'Update_time'       => '更新时间',
    'Remark'            => '备注',
    'Del_flag'          => '逻辑删除(0：正常,2：已删除)',
    'courseName'          => '课程名称',
    'isClose'          => '场馆状态',
    'isDel'          => '课程状态',
    'courseStartTime'          => '课程开始时间',
    'courseEndTime'          => '课程结束时间',
    'week'          => '周',
    'usecoursePoll'          => '预约人数   ',
    'start_end_Date'          => '课程时间段',

    'linkmanAge'              => '联系人年龄',
    'linkmanCertificate'              => '联系人身份证',
    'linkman_name'              => '联系人姓名',
    'linkman_phone'              => '联系人电话',
    'ugentPhone'              => '紧急联系人手机号',
    'subscribeType'              => '人数',
    'signState'              => '状态',



];
