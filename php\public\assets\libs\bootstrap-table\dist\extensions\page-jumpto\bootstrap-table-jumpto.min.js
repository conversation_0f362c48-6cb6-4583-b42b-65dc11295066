/*
* bootstrap-table - v1.11.11 - 2024-03-14
https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(i){"use strict";var s=i.fn.bootstrapTable.utils.sprintf,t=(i.extend(i.fn.bootstrapTable.defaults,{showJumpto:!1,exportOptions:{}}),i.extend(i.fn.bootstrapTable.locales,{formatJumpto:function(){return"GO"}}),i.extend(i.fn.bootstrapTable.defaults,i.fn.bootstrapTable.locales),i.fn.bootstrapTable.Constructor),p=t.prototype.initPagination;t.prototype.initPagination=function(){var t,o,n;this.showToolbar=this.options.showExport,p.apply(this,Array.prototype.slice.apply(arguments)),this.options.showJumpto&&(o=(t=this).$pagination.find("ul.pagination"),(n=o.find("li.jumpto")).length||(n=i(['<li class="jumpto">','<input type="text" class="form-control">','<button class="btn'+s(" btn-%s",this.options.buttonsClass)+s(" btn-%s",this.options.iconSize)+'" title="'+this.options.formatJumpto()+'"  type="button">'+this.options.formatJumpto(),"</button>","</li>"].join("")).appendTo(o)).find("button").click(function(){t.selectPage(parseInt(n.find("input").val()))}))}}(jQuery);