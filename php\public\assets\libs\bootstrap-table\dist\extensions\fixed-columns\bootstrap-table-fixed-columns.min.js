/*
* bootstrap-table - v1.11.11 - 2023-06-28
https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
"use strict";!function(a){var h=10,r=40,f=800;var c=null,i=(a.extend(a.fn.bootstrapTable.defaults,{fixedColumns:!1,fixedNumber:0,fixedRightNumber:0}),a.fn.bootstrapTable.Constructor),e=i.prototype.initBody,t=i.prototype.initContainer,d=i.prototype.trigger,o=i.prototype.hideLoading,s=i.prototype.updateSelected;i.prototype.fixedColumnsSupported=function(){var i=this;return i.options.fixedColumns&&!i.options.detailView&&!i.options.cardView},i.prototype.initFixedContainer=function(){this.options.fixedNumber&&(0==this.$tableContainer.find(".fixed-columns").length&&this.$tableContainer.append('<div class="fixed-columns"></div>'),this.$fixedColumns=this.$tableContainer.find(".fixed-columns")),this.options.fixedRightNumber&&(0==this.$tableContainer.find(".fixed-columns-right").length&&this.$tableContainer.append('<div class="fixed-columns-right"></div>'),this.$fixedColumnsRight=this.$tableContainer.find(".fixed-columns-right"))},i.prototype.initContainer=function(){t.apply(this,Array.prototype.slice.apply(arguments)),this.initFixedContainer()},i.prototype.initBody=function(){e.apply(this,Array.prototype.slice.apply(arguments)),!this.fixedColumnsSupported()||this.options.showHeader&&this.options.height||(this.initFixedColumnsBody(),this.initFixedColumnsEvents())},i.prototype.trigger=function(){d.apply(this,Array.prototype.slice.apply(arguments)),"pre-body"===arguments[0]&&this.options.cardView&&this.$tableBody.css("height","auto"),"toggle"===arguments[0]&&(arguments[1]?(this.$tableBody.css("height","auto"),this.$fixedColumns&&this.$fixedColumns.hide(),this.$fixedColumnsRight&&this.$fixedColumnsRight.hide()):(this.$tableBody.css("height","100%"),this.$fixedColumns&&this.$fixedColumns.show(),this.$fixedColumnsRight&&this.$fixedColumnsRight.show(),this.$fixedHeaderRight&&this.$fixedHeaderRight.scrollLeft(this.$tableBody.find("table").width()),this.$fixedBodyRight&&this.$fixedBodyRight.scrollLeft(this.$tableBody.find("table").width()))),this.fixedColumnsSupported()&&("post-header"===arguments[0]?this.initFixedColumnsHeader():"scroll-body"===arguments[0]?(this.needFixedColumns&&this.options.fixedNumber&&this.$fixedBody&&this.$fixedBody.scrollTop(this.$tableBody.scrollTop()),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedBodyRight&&this.$fixedBodyRight.scrollTop(this.$tableBody.scrollTop())):"load-success"===arguments[0]&&this.hideLoading())},i.prototype.updateSelected=function(){var l=this;s.apply(this,Array.prototype.slice.apply(arguments)),this.fixedColumnsSupported()&&this.$tableBody.find("tr").each(function(i,e){var e=a(e),d=e.data("index"),o=e.attr("class"),s='[name="'+l.options.selectItemName+'"]',n=e.find(s);void 0!==d&&(e=function(i,e){var t=e.find('tr[data-index="'+d+'"]');t.attr("class",o),n.length&&t.find(s).prop("checked",n.prop("checked")),l.$selectAll.length&&i.add(e).find('[name="btSelectAll"]').prop("checked",l.$selectAll.prop("checked"))},l.$fixedBody&&l.options.fixedNumber&&e(l.$fixedHeader,l.$fixedBody),l.$fixedBodyRight)&&l.options.fixedRightNumber&&e(l.$fixedHeaderRight,l.$fixedBodyRight)})},i.prototype.hideLoading=function(){o.apply(this,Array.prototype.slice.apply(arguments)),this.needFixedColumns&&this.options.fixedNumber&&this.$fixedColumns.find(".fixed-table-loading").hide(),this.needFixedColumns&&this.options.fixedRightNumber&&this.$fixedColumnsRight.find(".fixed-table-loading").hide()},i.prototype.initFixedColumnsHeader=function(){function i(i,e){return i.find(".fixed-table-header").remove(),i.append(t.$tableHeader.clone(!0)),i.find(".fixed-table-header").css("margin-right",""),i.css({width:t.getFixedColumnsWidth(e)}),i.find(".fixed-table-header")}var t=this;this.options.height?this.needFixedColumns=this.$tableHeader.outerWidth(!0)<this.$tableHeader.find("table").outerWidth(!0):this.needFixedColumns=this.$tableBody.outerWidth(!0)<this.$tableBody.find("table").outerWidth(!0);this.needFixedColumns&&this.options.fixedNumber?(this.$fixedHeader=i(this.$fixedColumns),this.$fixedHeader.css("margin-right","")):this.$fixedColumns&&this.$fixedColumns.html("").css("width",""),this.needFixedColumns&&this.options.fixedRightNumber?(this.$fixedHeaderRight=i(this.$fixedColumnsRight,!0),this.$fixedHeaderRight.scrollLeft(this.$fixedHeaderRight.find("table").width())):this.$fixedColumnsRight&&this.$fixedColumnsRight.html("").css("width",""),this.initFixedColumnsBody(),this.initFixedColumnsEvents()},i.prototype.initFixedColumnsBody=function(){function i(o,s){function i(){var i,e,t=l.scrollWidth>l.clientWidth?(null===c&&(t=a("<p/>").addClass("fixed-table-scroll-inner"),e=i=void 0,(d=a("<div/>").addClass("fixed-table-scroll-outer")).append(t),a("body").append(d),i=t[0].offsetWidth,d.css("overflow","scroll"),i===(e=t[0].offsetWidth)&&(e=d[0].clientWidth),d.remove(),c=i-e),c):0,d=a(".fixed-table-pagination",h.$tableContainer).height();void 0!==h.options.height&&(d=0),o.css({height:"calc(100% - "+(d+t)+"px)"}),n.css({height:"calc(100% - "+s.height()+"px)",overflow:"hidden"})}o.find(".fixed-table-body").remove(),o.append(h.$tableBody.clone(!0));var n=o.find(".fixed-table-body"),l=h.$tableBody.get(0);return a(window).on("resize",i),i(),n}var h=this;this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody=i(this.$fixedColumns,this.$fixedHeader)),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight=i(this.$fixedColumnsRight,this.$fixedHeaderRight),this.$fixedBodyRight.scrollLeft(this.$fixedBodyRight.find("table").width()),this.$fixedBodyRight.css("overflow-y","hidden"))},i.prototype.getFixedColumnsWidth=function(i){var e=this.getVisibleFields(),t=0,d=this.options.fixedNumber;i&&(e=e.reverse(),d=this.options.fixedRightNumber,this.$fixedColumnsRight.css("right",(i=this.$tableBody)[0].scrollHeight>i[0].clientHeight?15:0));for(var o=0;o<d;o++)t+=this.$header.find('th[data-field="'+e[o]+'"]').outerWidth();return t+1},i.prototype.initFixedColumnsEvents=function(){function e(i,e){var t='tr[data-index="'+a(i.currentTarget).data("index")+'"]',d=l.$tableBody.find(t);l.$fixedBody&&(d=d.add(l.$fixedBody.find(t))),(d=l.$fixedBodyRight?d.add(l.$fixedBodyRight.find(t)):d).css("background-color",e?a(i.currentTarget).css("background-color"):"")}var l=this;this.$tableBody.find("tr").hover(function(i){e(i,!0)},function(i){e(i,!1)});function t(i,e){d=t=n=s=0,"detail"in(o=i)&&(n=o.detail),"wheelDelta"in o&&(n=-o.wheelDelta/120),"wheelDeltaY"in o&&(n=-o.wheelDeltaY/120),"wheelDeltaX"in o&&(s=-o.wheelDeltaX/120),"axis"in o&&o.axis===o.HORIZONTAL_AXIS&&(s=n,n=0),t=s*h,d=n*h,"deltaY"in o&&(d=o.deltaY),((t="deltaX"in o?o.deltaX:t)||d)&&o.deltaMode&&(1===o.deltaMode?(t*=r,d*=r):(t*=f,d*=f));var t,d,o={spinX:s=t&&!s?t<1?-1:1:s,spinY:n=d&&!n?d<1?-1:1:n,pixelX:t,pixelY:d},s=Math.ceil(o.pixelY),n=l.$tableBody.scrollTop()+s;(s<0&&0<n||0<s&&n<e.scrollHeight-e.clientHeight)&&i.preventDefault(),l.$tableBody.scrollTop(n),l.$fixedBody&&l.$fixedBody.scrollTop(n),l.$fixedBodyRight&&l.$fixedBodyRight.scrollTop(n)}var i="undefined"!=typeof navigator&&-1<navigator.userAgent.toLowerCase().indexOf("firefox")?"DOMMouseScroll":"mousewheel";this.needFixedColumns&&this.options.fixedNumber&&(this.$fixedBody.find("tr").hover(function(i){e(i,!0)},function(i){e(i,!1)}),this.$fixedBody[0].addEventListener(i,function(i){t(i,l.$fixedBody[0])}),this.$fixedBody.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(i){i.stopImmediatePropagation();i=a(i.target).data("index");a(l.$selectItem[i]).trigger("click")}),this.$fixedBody.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(i){var e=a(this).closest("tr[data-index]").data("index");a(l.$selectItem[e]).closest("tr[data-index]").find(">td:eq("+a(this).index()+")").trigger("click")})),a("div.fixed-table-body").off("scroll"),this.$tableBody.off("scroll").on("scroll",function(i){l.$tableHeader.scrollLeft(0),0<l.$tableBody.scrollLeft()&&(l.$tableHeader.scrollLeft(l.$tableBody.scrollLeft()),l.options.showFooter)&&!l.options.cardView&&l.$tableFooter.scrollLeft(l.$tableBody.scrollLeft());var e=l.$tableBody.scrollTop();l.$fixedBody&&l.$fixedBody.scrollTop(e),l.$fixedBodyRight&&l.$fixedBodyRight.scrollTop(e)}),this.needFixedColumns&&this.options.fixedRightNumber&&(this.$fixedBodyRight.find("tr").hover(function(i){e(i,!0)},function(i){e(i,!1)}),this.$fixedBodyRight[0].addEventListener(i,function(i){t(i,l.$fixedBodyRight[0])}),this.$fixedBodyRight.find('input[name="'+this.options.selectItemName+'"]').off("click").on("click",function(i){i.stopImmediatePropagation();i=a(i.target).data("index");a(l.$selectItem[i]).trigger("click")}),this.$fixedBodyRight.find("> table > tbody > tr[data-index] > td").off("click dblclick").on("click dblclick",function(i){var e=a(this).closest("tr[data-index]").data("index");a(l.$selectItem[e]).closest("tr[data-index]").find(">td:eq("+a(this).index()+")").trigger("click")})),this.options.filterControl&&a(this.$fixedColumns).off("keyup change").on("keyup change",function(i){var i=a(i.target),e=i.val(),t=i.parents("th").data("field"),t=l.$header.find('th[data-field="'+t+'"]');i.is("input")?t.find("input").val(e):i.is("select")&&((i=t.find("select")).find("option[selected]").removeAttr("selected"),i.find('option[value="'+e+'"]').attr("selected",!0)),l.triggerSearch()})}}(jQuery);