/*
* bootstrap-table - v1.11.10 - 2023-06-14
https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(C){"use strict";function T(t){var i=arguments,e=!0,o=1;return t=t.replace(/%s/g,function(){var t=i[o++];return void 0===t?(e=!1,""):t}),e?t:""}function p(t,e){var o=-1;return C.each(t,function(t,i){return i.field!==e||(o=t,!1)}),o}function a(){var t,i,e;return null===o&&(e=C("<p/>").addClass("fixed-table-scroll-inner"),(t=C("<div/>").addClass("fixed-table-scroll-outer")).append(e),C("body").append(t),i=e[0].offsetWidth,t.css("overflow","scroll"),i===(e=e[0].offsetWidth)&&(e=t[0].clientWidth),t.remove(),o=i-e),o}function A(t,i,e,o){var s,n=i;return"string"==typeof i&&(1<(s=i.split(".")).length?(n=window,C.each(s,function(t,i){n=n[i]})):n=window[i]),"object"==typeof n?n:"function"==typeof n?n.apply(t,e||[]):!n&&"string"==typeof i&&T.apply(this,[i].concat(e))?T.apply(this,[i].concat(e)):o}function i(t,i,e){var o,s=Object.getOwnPropertyNames(t),n=Object.getOwnPropertyNames(i);if(e&&s.length!==n.length)return!1;for(var a=0;a<s.length;a++)if(o=s[a],-1<C.inArray(o,n)&&t[o]!==i[o])return!1;return!0}function R(t){return"string"==typeof t?t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;").replace(/`/g,"&#x60;"):t}function f(t){for(var i in t){var e=i.split(/(?=[A-Z])/).join("-").toLowerCase();e!==i&&(t[e]=t[i],delete t[i])}return t}function _(t,i,e){var o=t;if("string"!=typeof i||t.hasOwnProperty(i))return e?R(t[i]):t[i];var s,n=i.split(".");for(s in n)n.hasOwnProperty(s)&&(o=o&&o[n[s]]);return e?R(o):o}function e(){return!!(0<navigator.userAgent.indexOf("MSIE ")||navigator.userAgent.match(/Trident.*rv\:11\./))}function g(t,i){this.options=i,this.$el=C(t),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0,this.init()}var o=null,r=(g.DEFAULTS={classes:"table table-hover",sortClass:void 0,locale:void 0,height:void 0,undefinedText:"-",sortName:void 0,sortOrder:"asc",sortStable:!1,striped:!1,columns:[[]],data:[],totalField:"total",dataField:"rows",method:"get",url:void 0,ajax:void 0,cache:!0,contentType:"application/json",dataType:"json",ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},pagination:!1,onlyInfoPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",search:!1,searchOnEnterKey:!1,strictSearch:!1,searchAlign:"right",selectItemName:"btSelectItem",showHeader:!0,showFooter:!1,showColumns:!1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,buttonsAlign:"right",smartDisplay:!0,escape:!1,minimumCountColumns:1,idField:void 0,uniqueId:void 0,cardView:!1,detailView:!1,detailFormatter:function(t,i){return""},trimOnSearch:!0,clickToSelect:!1,singleSelect:!1,toolbar:void 0,toolbarAlign:"left",checkboxHeader:!0,sortable:!0,silentSort:!0,maintainSelected:!1,searchTimeOut:500,searchText:"",iconSize:void 0,buttonsClass:"default",iconsPrefix:"glyphicon",icons:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggle:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus"},customSearch:C.noop,customSort:C.noop,rowStyle:function(t,i){return{}},rowAttributes:function(t,i){return{}},footerStyle:function(t,i){return{}},onAll:function(t,i){return!1},onClickCell:function(t,i,e,o){return!1},onDblClickCell:function(t,i,e,o){return!1},onClickRow:function(t,i){return!1},onDblClickRow:function(t,i){return!1},onSort:function(t,i){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,i){return!1},onPageChange:function(t,i){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onExpandRow:function(t,i,e){return!1},onCollapseRow:function(t,i){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1}},(g.LOCALES={})["en-US"]=g.LOCALES.en={formatLoadingMessage:function(){return"Loading, please wait..."},formatRecordsPerPage:function(t){return T("%s rows per page",t)},formatShowingRows:function(t,i,e){return T("Showing %s to %s of %s rows",t,i,e)},formatDetailPagination:function(t){return T("Showing %s rows",t)},formatSearch:function(){return"Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatRefresh:function(){return"Refresh"},formatToggle:function(){return"Toggle"},formatColumns:function(){return"Columns"},formatAllRows:function(){return"All"}},C.extend(g.DEFAULTS,g.LOCALES["en-US"]),g.COLUMN_DEFAULTS={radio:!1,checkbox:!1,checkboxEnabled:!0,field:void 0,title:void 0,titleTooltip:void 0,class:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,width:void 0,sortable:!1,order:"asc",visible:!0,switchable:!0,clickToSelect:!0,formatter:void 0,footerFormatter:void 0,events:void 0,sorter:void 0,sortName:void 0,cellStyle:void 0,searchable:!0,searchFormatter:!0,cardVisible:!0,escape:!1},g.EVENTS={"all.bs.table":"onAll","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh"},g.prototype.init=function(){this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initFooter(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()},g.prototype.initLocale=function(){var t;this.options.locale&&((t=this.options.locale.split(/-|_/))[0].toLowerCase(),t[1]&&t[1].toUpperCase(),C.fn.bootstrapTable.locales[this.options.locale]?C.extend(this.options,C.fn.bootstrapTable.locales[this.options.locale]):C.fn.bootstrapTable.locales[t.join("-")]?C.extend(this.options,C.fn.bootstrapTable.locales[t.join("-")]):C.fn.bootstrapTable.locales[t[0]]&&C.extend(this.options,C.fn.bootstrapTable.locales[t[0]]))},g.prototype.initContainer=function(){this.$container=C(['<div class="bootstrap-table">','<div class="fixed-table-toolbar"></div>',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination" style="clear: both;"></div>':"",'<div class="fixed-table-container">','<div class="fixed-table-header"><table></table></div>','<div class="fixed-table-body">','<div class="fixed-table-loading">',this.options.formatLoadingMessage(),"</div>","</div>",'<div class="fixed-table-footer"><table><tr></tr></table></div>',"bottom"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?'<div class="fixed-table-pagination"></div>':"","</div>","</div>"].join("")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$container.find(".fixed-table-footer"),this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.options.striped&&this.$el.addClass("table-striped"),-1!==C.inArray("table-no-bordered",this.options.classes.split(" "))&&this.$tableContainer.addClass("table-no-bordered")},g.prototype.initTable=function(){for(var h,t,i,l=this,e=[],o=[],s=(this.$header=this.$el.find(">thead"),this.$header.length||(this.$header=C("<thead></thead>").appendTo(this.$el)),this.$header.find("tr").each(function(){var t=[];C(this).find("th").each(function(){void 0!==C(this).data("field")&&C(this).data("field",C(this).data("field")+""),t.push(C.extend({},{title:C(this).html(),class:C(this).attr("class"),titleTooltip:C(this).attr("title"),rowspan:C(this).attr("rowspan")?+C(this).attr("rowspan"):void 0,colspan:C(this).attr("colspan")?+C(this).attr("colspan"):void 0},C(this).data()))}),e.push(t)}),C.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=C.extend(!0,[],e,this.options.columns),this.columns=[],this.options.columns),n=0,a=[],r=0;r<s[0].length;r++)n+=s[0][r].colspan||1;for(r=0;r<s.length;r++)for(a[r]=[],t=0;t<n;t++)a[r][t]=!1;for(r=0;r<s.length;r++)for(t=0;t<s[r].length;t++){var p=s[r][t],c=p.rowspan||1,d=p.colspan||1,u=C.inArray(!1,a[r]);for(1===d&&(p.fieldIndex=u,void 0===p.field)&&(p.field=u),i=0;i<c;i++)a[r+i][u]=!0;for(i=0;i<d;i++)a[r][u+i]=!0}C.each(this.options.columns,function(e,t){C.each(t,function(t,i){void 0!==(i=C.extend({},g.COLUMN_DEFAULTS,i)).fieldIndex&&(l.columns[i.fieldIndex]=i),l.options.columns[e][t]=i})}),this.options.data.length||(h=[],this.$el.find(">tbody>tr").each(function(a){var r={};r._id=C(this).attr("id"),r._class=C(this).attr("class"),r._data=f(C(this).data()),C(this).find(">td").each(function(t){for(var i,e,o=C(this),s=+o.attr("colspan")||1,n=+o.attr("rowspan")||1;h[a]&&h[a][t];t++);for(i=t;i<t+s;i++)for(e=a;e<a+n;e++)h[e]||(h[e]=[]),h[e][i]=!0;o=l.columns[t].field;r[o]=C(this).html(),r["_"+o+"_id"]=C(this).attr("id"),r["_"+o+"_class"]=C(this).attr("class"),r["_"+o+"_rowspan"]=C(this).attr("rowspan"),r["_"+o+"_colspan"]=C(this).attr("colspan"),r["_"+o+"_title"]=C(this).attr("title"),r["_"+o+"_data"]=f(C(this).data())}),o.push(r)}),(this.options.data=o).length&&(this.fromHtml=!0))},g.prototype.initHeader=function(){var l=this,p={},c=[];this.header={fields:[],styles:[],classes:[],formatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},C.each(this.options.columns,function(t,i){c.push("<tr>"),0===t&&!l.options.cardView&&l.options.detailView&&c.push(T('<th class="detail" rowspan="%s"><div class="fht-cell"></div></th>',l.options.columns.length)),C.each(i,function(t,i){var e,o,s,n="",a=T(' class="%s"',i.class),r=(l.options.sortOrder||i.order,"px"),h=i.width;if(void 0===i.width||l.options.cardView||"string"==typeof i.width&&-1!==i.width.indexOf("%")&&(r="%"),i.width&&"string"==typeof i.width&&(h=i.width.replace("%","").replace("px","")),e=T("text-align: %s; ",i.halign||i.align),o=T("text-align: %s; ",i.align),s=T("vertical-align: %s; ",i.valign),s+=T("width: %s; ",!i.checkbox&&!i.radio||h?h?h+r:void 0:"36px"),void 0!==i.fieldIndex){if(l.header.fields[i.fieldIndex]=i.field,l.header.styles[i.fieldIndex]=o+s,l.header.classes[i.fieldIndex]=a,l.header.formatters[i.fieldIndex]=i.formatter,l.header.events[i.fieldIndex]=i.events,l.header.sorters[i.fieldIndex]=i.sorter,l.header.sortNames[i.fieldIndex]=i.sortName,l.header.cellStyles[i.fieldIndex]=i.cellStyle,l.header.searchables[i.fieldIndex]=i.searchable,!i.visible)return;if(l.options.cardView&&!i.cardVisible)return;p[i.field]=i}c.push("<th"+T(' title="%s"',i.titleTooltip),i.checkbox||i.radio?T(' class="bs-checkbox %s"',i.class||""):a,T(' style="%s"',e+s),T(' rowspan="%s"',i.rowspan),T(' colspan="%s"',i.colspan),T(' data-field="%s"',i.field),">"),c.push(T('<div class="th-inner %s">',l.options.sortable&&i.sortable?"sortable both":"")),n=l.options.escape?R(i.title):i.title,i.checkbox&&(!l.options.singleSelect&&l.options.checkboxHeader&&(n='<input name="btSelectAll" type="checkbox" />'),l.header.stateField=i.field),i.radio&&(n="",l.header.stateField=i.field,l.options.singleSelect=!0),c.push(n),c.push("</div>"),c.push('<div class="fht-cell"></div>'),c.push("</div>"),c.push("</th>")}),c.push("</tr>")}),this.$header.html(c.join("")),this.$header.find("th[data-field]").each(function(t){C(this).data(p[C(this).data("field")])}),this.$container.off("click",".th-inner").on("click",".th-inner",function(t){var i=C(this);if(l.options.detailView&&i.closest(".bootstrap-table")[0]!==l.$container[0])return!1;l.options.sortable&&i.parent().data().sortable&&l.onSort(t)}),this.$header.children().children().off("keypress").on("keypress",function(t){l.options.sortable&&C(this).data().sortable&&13==(t.keyCode||t.which)&&l.onSort(t)}),C(window).off("resize.bootstrap-table"),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),C(window).on("resize.bootstrap-table",C.proxy(this.resetWidth,this))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",function(){var t=C(this).prop("checked");l[t?"checkAll":"uncheckAll"](),l.updateSelected()})},g.prototype.initFooter=function(){!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()},g.prototype.initData=function(t,i){this.data="append"===i?this.data.concat(t):"prepend"===i?[].concat(t).concat(this.data):t||this.options.data,this.options.data="append"===i?this.options.data.concat(t):"prepend"===i?[].concat(t).concat(this.options.data):this.data,"server"!==this.options.sidePagination&&this.initSort()},g.prototype.initSort=function(){var n=this,a=this.options.sortName,r="desc"===this.options.sortOrder?-1:1,h=C.inArray(this.options.sortName,this.header.fields);this.options.customSort!==C.noop?this.options.customSort.apply(this,[this.options.sortName,this.options.sortOrder]):-1!==h&&(this.options.sortStable&&C.each(this.data,function(t,i){i.hasOwnProperty("_position")||(i._position=t)}),this.data.sort(function(t,i){n.header.sortNames[h]&&(a=n.header.sortNames[h]);var e=_(t,a,n.options.escape),o=_(i,a,n.options.escape),s=A(n.header,n.header.sorters[h],[e,o]);return void 0!==s?r*s:(null==e&&(e=""),null==o&&(o=""),n.options.sortStable&&e===o&&(e=t._position,o=i._position),C.isNumeric(e)&&C.isNumeric(o)?(e=parseFloat(e))<(o=parseFloat(o))?-1*r:r:e===o?0:-1===(e="string"!=typeof e?e.toString():e).localeCompare(o)?-1*r:r)}),void 0!==this.options.sortClass)&&(clearTimeout(0),setTimeout(function(){n.$el.removeClass(n.options.sortClass);var t=n.$header.find(T('[data-field="%s"]',n.options.sortName).index()+1);n.$el.find(T("tr td:nth-child(%s)",t)).addClass(n.options.sortClass)},250))},g.prototype.onSort=function(t){var t="keypress"===t.type?C(t.currentTarget):C(t.currentTarget).parent(),i=this.$header.find("th").eq(t.index());this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===t.data("field")?this.options.sortOrder="asc"===this.options.sortOrder?"desc":"asc":(this.options.sortName=t.data("field"),this.options.sortOrder="asc"===t.data("order")?"desc":"asc"),this.trigger("sort",this.options.sortName,this.options.sortOrder),t.add(i).data("order",this.options.sortOrder),this.getCaret(),"server"===this.options.sidePagination?this.initServer(this.options.silentSort):(this.initSort(),this.initBody())},g.prototype.initToolbar=function(){var t,o=this,s=[],i=0,n=0;this.$toolbar.find(".bs-bars").children().length&&C("body").append(C(this.options.toolbar)),this.$toolbar.html(""),"string"!=typeof this.options.toolbar&&"object"!=typeof this.options.toolbar||C(T('<div class="bs-bars pull-%s"></div>',this.options.toolbarAlign)).appendTo(this.$toolbar).append(C(this.options.toolbar)),s=[T('<div class="columns columns-%s btn-group pull-%s">',this.options.buttonsAlign,this.options.buttonsAlign)],"string"==typeof this.options.icons&&(this.options.icons=A(null,this.options.icons)),this.options.showPaginationSwitch&&s.push(T('<button class="btn'+T(" btn-%s",this.options.buttonsClass)+T(" btn-%s",this.options.iconSize)+'" type="button" name="paginationSwitch" aria-label="pagination Switch" title="%s">',this.options.formatPaginationSwitch()),T('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.paginationSwitchDown),"</button>"),this.options.showRefresh&&s.push(T('<button class="btn'+T(" btn-%s",this.options.buttonsClass)+T(" btn-%s",this.options.iconSize)+'" type="button" name="refresh" aria-label="refresh" title="%s">',this.options.formatRefresh()),T('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.refresh),"</button>"),this.options.showToggle&&s.push(T('<button class="btn'+T(" btn-%s",this.options.buttonsClass)+T(" btn-%s",this.options.iconSize)+'" type="button" name="toggle" aria-label="toggle" title="%s">',this.options.formatToggle()),T('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.toggle),"</button>"),this.options.showColumns&&(s.push(T('<div class="keep-open btn-group" title="%s">',this.options.formatColumns()),'<button type="button" aria-label="columns" class="btn'+T(" btn-%s",this.options.buttonsClass)+T(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">',T('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.columns),' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'),C.each(this.columns,function(t,i){var e;i.radio||i.checkbox||o.options.cardView&&!i.cardVisible||(e=i.visible?' checked="checked"':"",i.switchable&&(s.push(T('<li role="menuitem"><label><input type="checkbox" data-field="%s" value="%s"%s> %s</label></li>',i.field,t,e,i.title)),n++))}),s.push("</ul>","</div>")),s.push("</div>"),(this.showToolbar||2<s.length)&&this.$toolbar.append(s.join("")),this.options.showPaginationSwitch&&this.$toolbar.find('button[name="paginationSwitch"]').off("click").on("click",C.proxy(this.togglePagination,this)),this.options.showRefresh&&this.$toolbar.find('button[name="refresh"]').off("click").on("click",C.proxy(this.refresh,this)),this.options.showToggle&&this.$toolbar.find('button[name="toggle"]').off("click").on("click",function(){o.toggleView()}),this.options.showColumns&&(t=this.$toolbar.find(".keep-open"),n<=this.options.minimumCountColumns&&t.find("input").prop("disabled",!0),t.find("li").off("click").on("click",function(t){t.stopImmediatePropagation()}),t.find("input").off("click").on("click",function(){var t=C(this);o.toggleColumn(C(this).val(),t.prop("checked"),!1),o.trigger("column-switch",C(this).data("field"),t.prop("checked"))})),this.options.search&&((s=[]).push('<div class="pull-'+this.options.searchAlign+' search">',T('<input class="form-control'+T(" input-%s",this.options.iconSize)+'" type="text" placeholder="%s">',this.options.formatSearch()),"</div>"),this.$toolbar.append(s.join("")),(t=this.$toolbar.find(".search input")).off("keyup drop blur").on("keyup drop blur",function(t){o.options.searchOnEnterKey&&13!==t.keyCode||-1<C.inArray(t.keyCode,[37,38,39,40])||(clearTimeout(i),i=setTimeout(function(){o.onSearch(t)},o.options.searchTimeOut))}),e())&&t.off("mouseup").on("mouseup",function(t){clearTimeout(i),i=setTimeout(function(){o.onSearch(t)},o.options.searchTimeOut)})},g.prototype.onSearch=function(t){var i=C.trim(C(t.currentTarget).val());this.options.trimOnSearch&&C(t.currentTarget).val()!==i&&C(t.currentTarget).val(i),i===this.searchText||""===i&&void 0===this.searchText||(this.searchText=i,this.options.searchText=i,this.options.pageNumber=1,this.initSearch(),this.updatePagination(),this.trigger("search",i))},g.prototype.initSearch=function(){var h,o,l=this;"server"!==this.options.sidePagination&&(this.options.customSearch!==C.noop?this.options.customSearch.apply(this,[this.searchText]):(h=this.searchText&&(this.options.escape?R(this.searchText):this.searchText).toLowerCase(),o=C.isEmptyObject(this.filterColumns)?null:this.filterColumns,this.data=o?C.grep(this.options.data,function(t,i){for(var e in o)if(C.isArray(o[e])&&-1===C.inArray(t[e],o[e])||!C.isArray(o[e])&&t[e]!==o[e])return!1;return!0}):this.options.data,this.data=h?C.grep(this.data,function(t,i){for(var e=0;e<l.header.fields.length;e++)if(l.header.searchables[e]){var o=C.isNumeric(l.header.fields[e])?parseInt(l.header.fields[e],10):l.header.fields[e],s=l.columns[p(l.columns,o)];if("string"==typeof o){for(var n=t,a=o.split("."),r=0;r<a.length;r++)n=n[a[r]];s&&s.searchFormatter&&(n=A(s,l.header.formatters[e],[n,t,i],n))}else n=t[o];if("string"==typeof n||"number"==typeof n)if(l.options.strictSearch){if((n+"").toLowerCase()===h)return!0}else if(-1!==(n+"").toLowerCase().indexOf(h))return!0}return!1}):this.data))},g.prototype.initPagination=function(){if(this.options.pagination){this.$pagination.show();var t,i,e,o,s,n,a,r=this,h=[],l=!1,p=this.getData(),c=this.options.pageList;if("server"!==this.options.sidePagination&&(this.options.totalRows=p.length),this.totalPages=0,this.options.totalRows&&(this.options.pageSize===this.options.formatAllRows()?(this.options.pageSize=this.options.totalRows,l=!0):this.options.pageSize===this.options.totalRows&&(p="string"==typeof this.options.pageList?this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").toLowerCase().split(","):this.options.pageList,-1<C.inArray(this.options.formatAllRows().toLowerCase(),p))&&(l=!0),this.totalPages=1+~~((this.options.totalRows-1)/this.options.pageSize),this.options.totalPages=this.totalPages),0<this.totalPages&&this.options.pageNumber>this.totalPages&&(this.options.pageNumber=this.totalPages),this.pageFrom=(this.options.pageNumber-1)*this.options.pageSize+1,this.pageTo=this.options.pageNumber*this.options.pageSize,this.pageTo>this.options.totalRows&&(this.pageTo=this.options.totalRows),h.push('<div class="pull-'+this.options.paginationDetailHAlign+' pagination-detail">','<span class="pagination-info">',this.options.onlyInfoPagination?this.options.formatDetailPagination(this.options.totalRows):this.options.formatShowingRows(this.pageFrom,this.pageTo,this.options.totalRows),"</span>"),!this.options.onlyInfoPagination){h.push('<span class="page-list">');var d=[T('<span class="btn-group %s">',"top"===this.options.paginationVAlign||"both"===this.options.paginationVAlign?"dropdown":"dropup"),'<button type="button" class="btn'+T(" btn-%s",this.options.buttonsClass)+T(" btn-%s",this.options.iconSize)+' dropdown-toggle" data-toggle="dropdown">','<span class="page-size">',l?this.options.formatAllRows():this.options.pageSize,"</span>",' <span class="caret"></span>',"</button>",'<ul class="dropdown-menu" role="menu">'];for("string"==typeof this.options.pageList&&(p=this.options.pageList.replace("[","").replace("]","").replace(/ /g,"").split(","),c=[],C.each(p,function(t,i){c.push(i.toUpperCase()===r.options.formatAllRows().toUpperCase()?r.options.formatAllRows():+i)})),C.each(c,function(t,i){(!r.options.smartDisplay||0===t||c[t-1]<r.options.totalRows)&&(t=l?i===r.options.formatAllRows()?' class="active"':"":i===r.options.pageSize?' class="active"':"",d.push(T('<li role="menuitem"%s><a href="#">%s</a></li>',t,i)))}),d.push("</ul></span>"),h.push(this.options.formatRecordsPerPage(d.join(""))),h.push("</span>"),h.push("</div>",'<div class="pull-'+this.options.paginationHAlign+' pagination">','<ul class="pagination'+T(" pagination-%s",this.options.iconSize)+'">','<li class="page-pre"><a href="#">'+this.options.paginationPreText+"</a></li>"),this.totalPages<5?(e=1,i=this.totalPages):(i=(e=this.options.pageNumber-2)+4,e<1&&(e=1,i=5),i>this.totalPages&&(e=(i=this.totalPages)-4)),6<=this.totalPages&&(3<=this.options.pageNumber&&(h.push('<li class="page-first'+(1===this.options.pageNumber?" active":"")+'">','<a href="#">',1,"</a>","</li>"),e++),4<=this.options.pageNumber)&&(4==this.options.pageNumber||6==this.totalPages||7==this.totalPages?e--:h.push('<li class="page-first-separator disabled">','<a href="#">...</a>',"</li>"),i--),7<=this.totalPages&&this.options.pageNumber>=this.totalPages-2&&e--,6==this.totalPages?this.options.pageNumber>=this.totalPages-2&&i++:7<=this.totalPages&&(7==this.totalPages||this.options.pageNumber>=this.totalPages-3)&&i++,t=e;t<=i;t++)h.push('<li class="page-number'+(t===this.options.pageNumber?" active":"")+'">','<a href="#">',t,"</a>","</li>");8<=this.totalPages&&this.options.pageNumber<=this.totalPages-4&&h.push('<li class="page-last-separator disabled">','<a href="#">...</a>',"</li>"),6<=this.totalPages&&this.options.pageNumber<=this.totalPages-3&&h.push('<li class="page-last'+(this.totalPages===this.options.pageNumber?" active":"")+'">','<a href="#">',this.totalPages,"</a>","</li>"),h.push('<li class="page-next"><a href="#">'+this.options.paginationNextText+"</a></li>","</ul>","</div>")}this.$pagination.html(h.join("")),this.options.onlyInfoPagination||(p=this.$pagination.find(".page-list a"),e=this.$pagination.find(".page-first"),o=this.$pagination.find(".page-pre"),s=this.$pagination.find(".page-next"),n=this.$pagination.find(".page-last"),a=this.$pagination.find(".page-number"),this.options.smartDisplay&&(this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),(c.length<2||this.options.totalRows<=c[0])&&this.$pagination.find("span.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"]()),this.options.paginationLoop||(1===this.options.pageNumber&&o.addClass("disabled"),this.options.pageNumber===this.totalPages&&s.addClass("disabled")),l&&(this.options.pageSize=this.options.formatAllRows()),p.off("click").on("click",C.proxy(this.onPageListChange,this)),e.off("click").on("click",C.proxy(this.onPageFirst,this)),o.off("click").on("click",C.proxy(this.onPagePre,this)),s.off("click").on("click",C.proxy(this.onPageNext,this)),n.off("click").on("click",C.proxy(this.onPageLast,this)),a.off("click").on("click",C.proxy(this.onPageNumber,this)))}else this.$pagination.hide()},g.prototype.updatePagination=function(t){t&&C(t.currentTarget).hasClass("disabled")||(this.options.maintainSelected||this.resetRows(),this.initPagination(),"server"===this.options.sidePagination?this.initServer():this.initBody(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize))},g.prototype.onPageListChange=function(t){var i=C(t.currentTarget);return i.parent().addClass("active").siblings().removeClass("active"),this.options.pageSize=i.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+i.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(t),!1},g.prototype.onPageFirst=function(t){return this.options.pageNumber=1,this.updatePagination(t),!1},g.prototype.onPagePre=function(t){return this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(t),!1},g.prototype.onPageNext=function(t){return this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(t),!1},g.prototype.onPageLast=function(t){return this.options.pageNumber=this.totalPages,this.updatePagination(t),!1},g.prototype.onPageNumber=function(t){if(this.options.pageNumber!==+C(t.currentTarget).text())return this.options.pageNumber=+C(t.currentTarget).text(),this.updatePagination(t),!1},g.prototype.initRow=function(v,x,t,i){var e,$=this,S=[],k={},P=[],o="",s={},n=[];if(!(-1<C.inArray(v,this.hiddenRows))){if((k=A(this.options,this.options.rowStyle,[v,x],k))&&k.css)for(e in k.css)P.push(e+": "+k.css[e]);if(s=A(this.options,this.options.rowAttributes,[v,x],s))for(e in s)n.push(T('%s="%s"',e,R(s[e])));return v._data&&!C.isEmptyObject(v._data)&&C.each(v._data,function(t,i){"index"!==t&&(o+=T(' data-%s="%s"',t,i))}),S.push("<tr",T(" %s",n.join(" ")),T(' id="%s"',C.isArray(v)?void 0:v._id),T(' class="%s"',k.classes||(C.isArray(v)?void 0:v._class)),T(' data-index="%s"',x),T(' data-uniqueid="%s"',v[this.options.uniqueId]),T("%s",o),">"),this.options.cardView&&S.push(T('<td colspan="%s"><div class="card-views">',this.header.fields.length)),!this.options.cardView&&this.options.detailView&&S.push("<td>",'<a class="detail-icon" href="#">',T('<i class="%s %s"></i>',this.options.iconsPrefix,this.options.icons.detailOpen),"</a>","</td>"),C.each(this.header.fields,function(t,i){var e="",o=_(v,i,$.options.escape),s="",n={},a="",r=$.header.classes[t],h="",l="",p="",c="",d=$.columns[t];if((!$.fromHtml||void 0!==o)&&d.visible&&(!$.options.cardView||d.cardVisible)){if(d.escape&&(o=R(o)),k=T('style="%s"',P.concat($.header.styles[t]).join("; ")),v["_"+i+"_id"]&&(a=T(' id="%s"',v["_"+i+"_id"])),v["_"+i+"_class"]&&(r=T(' class="%s"',v["_"+i+"_class"])),v["_"+i+"_rowspan"]&&(l=T(' rowspan="%s"',v["_"+i+"_rowspan"])),v["_"+i+"_colspan"]&&(p=T(' colspan="%s"',v["_"+i+"_colspan"])),v["_"+i+"_title"]&&(c=T(' title="%s"',v["_"+i+"_title"])),(n=A($.header,$.header.cellStyles[t],[o,v,x,i],n)).classes&&(r=T(' class="%s"',n.classes)),n.css){var u,f=[];for(u in n.css)f.push(u+": "+n.css[u]);k=T('style="%s"',f.concat($.header.styles[t]).join("; "))}var g,b,m,y,w,s=A(d,$.header.formatters[t],[o,v,x],o);v["_"+i+"_data"]&&!C.isEmptyObject(v["_"+i+"_data"])&&C.each(v["_"+i+"_data"],function(t,i){"index"!==t&&(h+=T(' data-%s="%s"',t,i))}),d.checkbox||d.radio?(g=d.checkbox?"checkbox":"",g=d.radio?"radio":g,e=[T($.options.cardView?'<div class="card-view %s">':'<td class="bs-checkbox %s">',d.class||""),"<input"+T(' data-index="%s"',x)+T(' name="%s"',$.options.selectItemName)+T(' type="%s"',g)+T(' value="%s"',v[$.options.idField])+T(' checked="%s"',!0===s||o||s&&s.checked?"checked":void 0)+T(' disabled="%s"',!d.checkboxEnabled||s&&s.disabled?"disabled":void 0)+" />",$.header.formatters[t]&&"string"==typeof s?s:"",$.options.cardView?"</div>":"</td>"].join(""),v[$.header.stateField]=!0===s||s&&s.checked):(s=null==s?$.options.undefinedText:s,e=($.options.cardView?['<div class="card-view">',$.options.showHeader?T('<span class="title" %s>%s</span>',k,(g=$.columns,b="field",m="title",y=i,w="",C.each(g,function(t,i){return i[b]!==y||(w=i[m],!1)}),w)):"",T('<span class="value">%s</span>',s),"</div>"]:[T("<td%s %s %s %s %s %s %s>",a,r,k,h,l,p,c),s,"</td>"]).join(""),$.options.cardView&&$.options.smartDisplay&&""===s&&(e='<div class="card-view"></div>')),S.push(e)}}),this.options.cardView&&S.push("</div></td>"),S.push("</tr>"),S.join(" ")}},g.prototype.initBody=function(t){for(var l=this,s=this.getData(),i=(this.trigger("pre-body",s),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=C("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=s.length),C(document.createDocumentFragment())),e=this.pageFrom-1;e<this.pageTo;e++){var o=s[e],o=this.initRow(o,e,s,i),n=n||!!o;o&&!0!==o&&i.append(o)}n||i.append('<tr class="no-records-found">'+T('<td colspan="%s">%s</td>',this.$header.find("th").length,this.options.formatNoMatches())+"</tr>"),this.$body.html(i),t||this.scrollTo(0),this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",function(t){var i=C(this),e=i.parent(),o=l.data[e.data("index")],s=i[0].cellIndex,s=l.getVisibleFields()[l.options.detailView&&!l.options.cardView?s-1:s],n=l.columns[p(l.columns,s)],a=_(o,s,l.options.escape);i.find(".detail-icon").length||(l.trigger("click"===t.type?"click-cell":"dbl-click-cell",s,a,o,i),l.trigger("click"===t.type?"click-row":"dbl-click-row",o,e,s),"click"===t.type&&l.options.clickToSelect&&n.clickToSelect&&(a=e.find(T('[name="%s"]',l.options.selectItemName))).length&&a[0].click())}),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",function(){var t=C(this),i=t.parent().parent(),e=i.data("index"),o=s[e];return i.next().is("tr.detail-view")?(t.find("i").attr("class",T("%s %s",l.options.iconsPrefix,l.options.icons.detailOpen)),l.trigger("collapse-row",e,o),i.next().remove()):(t.find("i").attr("class",T("%s %s",l.options.iconsPrefix,l.options.icons.detailClose)),i.after(T('<tr class="detail-view"><td colspan="%s"></td></tr>',i.find("td").length)),t=i.next().find("td"),i=A(l.options,l.options.detailFormatter,[e,o,t],""),1===t.length&&t.append(i),l.trigger("expand-row",e,o,t)),l.resetView(),!1}),this.$selectItem=this.$body.find(T('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",function(t){t.stopImmediatePropagation();var t=C(this),i=t.prop("checked"),e=l.data[t.data("index")];l.options.maintainSelected&&C(this).is(":radio")&&C.each(l.options.data,function(t,i){i[l.header.stateField]=!1}),e[l.header.stateField]=i,l.options.singleSelect&&(l.$selectItem.not(this).each(function(){l.data[C(this).data("index")][l.header.stateField]=!1}),l.$selectItem.filter(":checked").not(this).prop("checked",!1)),l.updateSelected(),l.trigger(i?"check":"uncheck",e,t)}),C.each(this.header.events,function(t,o){if(o){"string"==typeof o&&(o=A(null,o));var s,h=l.header.fields[t],n=C.inArray(h,l.getVisibleFields());for(s in l.options.detailView&&!l.options.cardView&&(n+=1),o)l.$body.find(">tr:not(.no-records-found)").each(function(){var a=C(this),t=a.find(l.options.cardView?".card-view":">td").eq(n),i=s.indexOf(" "),e=s.substring(0,i),i=s.substring(i+1),r=o[s];t.find(i).off(e).on(e,function(t){var i=a.data("index"),e=l.data[i],o=e[h],s=h.split(".");if(1<s.length)for(var o=e,n=0;n<s.length;n++)o=o[s[n]];r.apply(this,[t,o,e,i])})})}}),this.updateSelected(),this.resetView(),this.trigger("post-body",s)},g.prototype.initServer=function(i,t,e){var o=this,s={},n={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};this.options.pagination&&(n.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,n.pageNumber=this.options.pageNumber),(e||this.options.url||this.options.ajax)&&("limit"===this.options.queryParamsType&&(n={search:n.searchText,sort:n.sortName,order:n.sortOrder},this.options.pagination)&&(n.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),n.limit=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize),C.isEmptyObject(this.filterColumnsPartial)||(n.filter=JSON.stringify(this.filterColumnsPartial,null)),s=A(this.options,this.options.queryParams,[n],s),C.extend(s,t||{}),!1!==s)&&(i||this.$tableLoading.show(),n=C.extend({},A(null,this.options.ajaxOptions),{type:this.options.method,url:e||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(s):s,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t){t=A(o.options,o.options.responseHandler,[t],t),o.load(t),o.trigger("load-success",t),i||o.$tableLoading.hide()},error:function(t){o.trigger("load-error",t.status,t),i||o.$tableLoading.hide()}}),this.options.ajax?A(this,this.options.ajax,[n],null):(this._xhr&&4!==this._xhr.readyState&&this._xhr.abort(),this._xhr=C.ajax(n)))},g.prototype.initSearchText=function(){var t;this.options.search&&""!==this.options.searchText&&((t=this.$toolbar.find(".search input")).val(this.options.searchText),this.onSearch({currentTarget:t}))},g.prototype.getCaret=function(){var e=this;C.each(this.$header.find("th"),function(t,i){C(i).find(".sortable").removeClass("desc asc").addClass(C(i).data("field")===e.options.sortName?e.options.sortOrder:"both")})},g.prototype.updateSelected=function(){var t=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.each(function(){C(this).closest("tr")[C(this).prop("checked")?"addClass":"removeClass"]("selected")})},g.prototype.updateRows=function(){var t=this;this.$selectItem.each(function(){t.data[C(this).data("index")][t.header.stateField]=C(this).prop("checked")})},g.prototype.resetRows=function(){var e=this;C.each(this.data,function(t,i){e.$selectAll.prop("checked",!1),e.$selectItem.prop("checked",!1),e.header.stateField&&(i[e.header.stateField]=!1)}),this.initHiddenRows()},g.prototype.trigger=function(t){var i=Array.prototype.slice.call(arguments,1);this.options[g.EVENTS[t+=".bs.table"]].apply(this.options,i),this.$el.trigger(C.Event(t),i),this.options.onAll(t,i),this.$el.trigger(C.Event("all.bs.table"),[t,i])},g.prototype.resetHeader=function(){clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(C.proxy(this.fitHeader,this),this.$el.is(":hidden")?100:0)},g.prototype.fitHeader=function(){var t,i,o,s,n=this;n.$el.is(":hidden")?n.timeoutId_=setTimeout(C.proxy(n.fitHeader,n),100):(i=(i=this.$tableBody.get(0)).scrollWidth>i.clientWidth&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?a():0,this.$el.css("margin-top",-this.$header.outerHeight()),0<(t=C(":focus")).length&&0<(t=t.parents("th")).length&&void 0!==(t=t.attr("data-field"))&&0<(t=this.$header.find("[data-field='"+t+"']")).length&&t.find(":input").addClass("focus-temp"),this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css({"margin-right":i}).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),0<(t=C(".focus-temp:visible:eq(0)")).length&&(t.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each(function(t){n.$header_.find(T('th[data-field="%s"]',C(this).data("field"))).data(C(this).data())}),o=this.getVisibleFields(),s=this.$header_.find("th"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(t){var i=C(this),e=t,t=(n.options.detailView&&!n.options.cardView&&(0===t&&n.$header_.find("th.detail").find(".fht-cell").width(i.innerWidth()),e=t-1),n.$header_.find(T('th[data-field="%s"]',o[e])));(t=1<t.length?C(s[i[0].cellIndex]):t).find(".fht-cell").width(i.innerWidth())}),this.$tableBody.off("scroll").on("scroll",function(){n.$tableHeader.scrollLeft(C(this).scrollLeft()),n.options.showFooter&&!n.options.cardView&&n.$tableFooter.scrollLeft(C(this).scrollLeft())}),n.trigger("post-header"))},g.prototype.resetFooter=function(){var h=this,l=h.getData(),p=[];this.options.showFooter&&!this.options.cardView&&(!this.options.cardView&&this.options.detailView&&p.push('<td><div class="th-inner">&nbsp;</div><div class="fht-cell"></div></td>'),C.each(this.columns,function(t,i){var e,o,s,n,a=[],r=T(' class="%s"',i.class);if(i.visible&&(!h.options.cardView||i.cardVisible)){if(o=T("text-align: %s; ",i.falign||i.align),s=T("vertical-align: %s; ",i.valign),(n=A(null,h.options.footerStyle))&&n.css)for(e in n.css)a.push(e+": "+n.css[e]);p.push("<td",r,T(' style="%s"',o+s+a.concat().join("; ")),">"),p.push('<div class="th-inner">'),p.push(A(i,i.footerFormatter,[l],"&nbsp;")||"&nbsp;"),p.push("</div>"),p.push('<div class="fht-cell"></div>'),p.push("</div>"),p.push("</td>")}}),this.$tableFooter.find("tr").html(p.join("")),this.$tableFooter.show(),clearTimeout(this.timeoutFooter_),this.timeoutFooter_=setTimeout(C.proxy(this.fitFooter,this),this.$el.is(":hidden")?100:0))},g.prototype.fitFooter=function(){var e,t,i;clearTimeout(this.timeoutFooter_),this.$el.is(":hidden")?this.timeoutFooter_=setTimeout(C.proxy(this.fitFooter,this),100):(i=(t=this.$el.css("width"))>this.$tableBody.width()?a():0,this.$tableFooter.css({"margin-right":i}).find("table").css("width",t).attr("class",this.$el.attr("class")),e=this.$tableFooter.find("td"),this.$body.find(">tr:first-child:not(.no-records-found) > *").each(function(t){var i=C(this);e.eq(t).find(".fht-cell").width(i.innerWidth())}))},g.prototype.toggleColumn=function(t,i,e){var o;-1!==t&&(this.columns[t].visible=i,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)&&(o=this.$toolbar.find(".keep-open input").prop("disabled",!1),e&&o.filter(T('[value="%s"]',t)).prop("checked",i),o.filter(":checked").length<=this.options.minimumCountColumns)&&o.filter(":checked").prop("disabled",!0)},g.prototype.getVisibleFields=function(){var e=this,o=[];return C.each(this.header.fields,function(t,i){e.columns[p(e.columns,i)].visible&&o.push(i)}),o},g.prototype.resetView=function(t){var i,e=0;t&&t.height&&(this.options.height=t.height),this.$selectAll.prop("checked",0<this.$selectItem.length&&this.$selectItem.length===this.$selectItem.filter(":checked").length),this.options.height&&(t=this.$toolbar.outerHeight(!0),i=this.$pagination.outerHeight(!0),t=this.options.height-t-i,this.$tableContainer.css("height",t+"px")),this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight()):(this.$tableHeader.hide(),this.trigger("post-header")),this.options.showFooter&&(this.resetFooter(),this.options.height)&&(e+=this.$tableFooter.outerHeight()+1),this.getCaret(),this.$tableContainer.css("padding-bottom",e+"px"),this.trigger("reset-view"))},g.prototype.getData=function(t){return!this.searchText&&C.isEmptyObject(this.filterColumns)&&C.isEmptyObject(this.filterColumnsPartial)?t?this.options.data.slice(this.pageFrom-1,this.pageTo):this.options.data:t?this.data.slice(this.pageFrom-1,this.pageTo):this.data},g.prototype.load=function(t){var i=!1;"server"===this.options.sidePagination?(this.options.totalRows=t[this.options.totalField],i=t.fixedScroll,t=t[this.options.dataField]):C.isArray(t)||(i=t.fixedScroll,t=t.data),this.initData(t),this.initSearch(),this.initPagination(),this.initBody(i)},g.prototype.append=function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},g.prototype.prepend=function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},g.prototype.remove=function(t){var i,e,o=this.options.data.length;if(t.hasOwnProperty("field")&&t.hasOwnProperty("values")){for(i=o-1;0<=i;i--)(e=this.options.data[i]).hasOwnProperty(t.field)&&-1!==C.inArray(e[t.field],t.values)&&(this.options.data.splice(i,1),"server"===this.options.sidePagination)&&--this.options.totalRows;o!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},g.prototype.removeAll=function(){0<this.options.data.length&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))},g.prototype.getRowByUniqueId=function(t){for(var i,e,o=this.options.uniqueId,s=null,n=this.options.data.length-1;0<=n;n--){if((i=this.options.data[n]).hasOwnProperty(o))e=i[o];else{if(!i._data.hasOwnProperty(o))continue;e=i._data[o]}if("string"==typeof e?t=t.toString():"number"==typeof e&&(Number(e)===e&&e%1==0?t=parseInt(t):e===Number(e)&&0!==e&&(t=parseFloat(t))),e===t){s=i;break}}return s},g.prototype.removeByUniqueId=function(t){var i=this.options.data.length,t=this.getRowByUniqueId(t);t&&this.options.data.splice(this.options.data.indexOf(t),1),i!==this.options.data.length&&(this.initSearch(),this.initPagination(),this.initBody(!0))},g.prototype.updateByUniqueId=function(t){var o=this,t=C.isArray(t)?t:[t];C.each(t,function(t,i){var e;i.hasOwnProperty("id")&&i.hasOwnProperty("row")&&-1!==(e=C.inArray(o.getRowByUniqueId(i.id),o.options.data))&&C.extend(o.options.data[e],i.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},g.prototype.insertRow=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))},g.prototype.updateRow=function(t){var e=this,t=C.isArray(t)?t:[t];C.each(t,function(t,i){i.hasOwnProperty("index")&&i.hasOwnProperty("row")&&C.extend(e.options.data[i.index],i.row)}),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)},g.prototype.initHiddenRows=function(){this.hiddenRows=[]},g.prototype.showRow=function(t){this.toggleRow(t,!0)},g.prototype.hideRow=function(t){this.toggleRow(t,!1)},g.prototype.toggleRow=function(t,i){var e;t.hasOwnProperty("index")?e=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(e=this.getRowByUniqueId(t.uniqueId)),e&&(t=C.inArray(e,this.hiddenRows),i||-1!==t?i&&-1<t&&this.hiddenRows.splice(t,1):this.hiddenRows.push(e),this.initBody(!0))},g.prototype.getHiddenRows=function(t){var e=this,i=this.getData(),o=[];return C.each(i,function(t,i){-1<C.inArray(i,e.hiddenRows)&&o.push(i)}),this.hiddenRows=o},g.prototype.mergeCells=function(t){var i,e,o=t.index,s=C.inArray(t.field,this.getVisibleFields()),n=t.rowspan||1,a=t.colspan||1,r=this.$body.find(">tr");if(this.options.detailView&&!this.options.cardView&&(s+=1),t=r.eq(o).find(">td").eq(s),!(o<0||s<0||o>=this.data.length)){for(i=o;i<o+n;i++)for(e=s;e<s+a;e++)r.eq(i).find(">td").eq(e).hide();t.attr("rowspan",n).attr("colspan",a).show()}},g.prototype.updateCell=function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.data[t.index][t.field]=t.value,!1!==t.reinit)&&(this.initSort(),this.initBody(!0))},g.prototype.getOptions=function(){return this.options},g.prototype.getSelections=function(){var i=this;return C.grep(this.options.data,function(t){return!0===t[i.header.stateField]})},g.prototype.getAllSelections=function(){var i=this;return C.grep(this.options.data,function(t){return t[i.header.stateField]})},g.prototype.checkAll=function(){this.checkAll_(!0)},g.prototype.uncheckAll=function(){this.checkAll_(!1)},g.prototype.checkInvert=function(){var t=this,i=t.$selectItem.filter(":enabled"),e=i.filter(":checked");i.each(function(){C(this).prop("checked",!C(this).prop("checked"))}),t.updateRows(),t.updateSelected(),t.trigger("uncheck-some",e),e=t.getSelections(),t.trigger("check-some",e)},g.prototype.checkAll_=function(t){var i;t||(i=this.getSelections()),this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),t&&(i=this.getSelections()),this.trigger(t?"check-all":"uncheck-all",i)},g.prototype.check=function(t){this.check_(!0,t)},g.prototype.uncheck=function(t){this.check_(!1,t)},g.prototype.check_=function(t,i){var e=this.$selectItem.filter(T('[data-index="%s"]',i)).prop("checked",t);this.data[i][this.header.stateField]=t,this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[i],e)},g.prototype.checkBy=function(t){this.checkBy_(!0,t)},g.prototype.uncheckBy=function(t){this.checkBy_(!1,t)},g.prototype.checkBy_=function(e,o){var s,n;o.hasOwnProperty("field")&&o.hasOwnProperty("values")&&(n=[],C.each((s=this).options.data,function(t,i){if(!i.hasOwnProperty(o.field))return!1;-1!==C.inArray(i[o.field],o.values)&&(t=s.$selectItem.filter(":enabled").filter(T('[data-index="%s"]',t)).prop("checked",e),i[s.header.stateField]=e,n.push(i),s.trigger(e?"check":"uncheck",i,t))}),this.updateSelected(),this.trigger(e?"check-some":"uncheck-some",n))},g.prototype.destroy=function(){this.$el.insertBefore(this.$container),C(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"")},g.prototype.showLoading=function(){this.$tableLoading.show()},g.prototype.hideLoading=function(){this.$tableLoading.hide()},g.prototype.togglePagination=function(){this.options.pagination=!this.options.pagination;var t=this.$toolbar.find('button[name="paginationSwitch"] i');this.options.pagination?t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchDown):t.attr("class",this.options.iconsPrefix+" "+this.options.icons.paginationSwitchUp),this.updatePagination()},g.prototype.refresh=function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.initServer(t&&t.silent,t&&t.query,t&&t.url),this.trigger("refresh",t)},g.prototype.resetWidth=function(){this.options.showHeader&&this.options.height&&this.fitHeader(),this.options.showFooter&&this.fitFooter()},g.prototype.showColumn=function(t){this.toggleColumn(p(this.columns,t),!0,!0)},g.prototype.hideColumn=function(t){this.toggleColumn(p(this.columns,t),!1,!0)},g.prototype.getHiddenColumns=function(){return C.grep(this.columns,function(t){return!t.visible})},g.prototype.getVisibleColumns=function(){return C.grep(this.columns,function(t){return t.visible})},g.prototype.toggleAllColumns=function(e){var t;C.each(this.columns,function(t,i){this.columns[t].visible=e}),this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns&&(t=this.$toolbar.find(".keep-open input").prop("disabled",!1)).filter(":checked").length<=this.options.minimumCountColumns&&t.filter(":checked").prop("disabled",!0)},g.prototype.showAllColumns=function(){this.toggleAllColumns(!0)},g.prototype.hideAllColumns=function(){this.toggleAllColumns(!1)},g.prototype.filterBy=function(t){this.filterColumns=C.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()},g.prototype.scrollTo=function(t){if("number"==typeof(t="string"==typeof t?"bottom"===t?this.$tableBody[0].scrollHeight:0:t)&&this.$tableBody.scrollTop(t),void 0===t)return this.$tableBody.scrollTop()},g.prototype.getScrollPosition=function(){return this.scrollTo()},g.prototype.selectPage=function(t){0<t&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())},g.prototype.prevPage=function(){1<this.options.pageNumber&&(this.options.pageNumber--,this.updatePagination())},g.prototype.nextPage=function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())},g.prototype.toggleView=function(){this.options.cardView=!this.options.cardView,this.initHeader(),this.initBody(),this.trigger("toggle",this.options.cardView)},g.prototype.refreshOptions=function(t){i(this.options,t,!0)||(this.options=C.extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())},g.prototype.resetSearch=function(t){var i=this.$toolbar.find(".search input");i.val(t||""),this.onSearch({currentTarget:i})},g.prototype.expandRow_=function(t,i){i=this.$body.find(T('> tr[data-index="%s"]',i));i.next().is("tr.detail-view")===!t&&i.find("> td > .detail-icon").click()},g.prototype.expandRow=function(t){this.expandRow_(!0,t)},g.prototype.collapseRow=function(t){this.expandRow_(!1,t)},g.prototype.expandAllRows=function(t){if(t){var t=this.$body.find(T('> tr[data-index="%s"]',0)),i=this,e=null,o=!1,s=-1;if(t.next().is("tr.detail-view")?t.next().next().is("tr.detail-view")||(t.next().find(".detail-icon").click(),o=!0):(t.find("> td > .detail-icon").click(),o=!0),o)try{s=setInterval(function(){0<(e=i.$body.find("tr.detail-view").last().find(".detail-icon")).length?e.click():clearInterval(s)},1)}catch(t){clearInterval(s)}}else for(var n=this.$body.children(),a=0;a<n.length;a++)this.expandRow_(!0,C(n[a]).data("index"))},g.prototype.collapseAllRows=function(t){if(t)this.expandRow_(!1,0);else for(var i=this.$body.children(),e=0;e<i.length;e++)this.expandRow_(!1,C(i[e]).data("index"))},g.prototype.updateFormatText=function(t,i){this.options[T("format%s",t)]&&("string"==typeof i?this.options[T("format%s",t)]=function(){return i}:"function"==typeof i&&(this.options[T("format%s",t)]=i)),this.initToolbar(),this.initPagination(),this.initBody()},["getOptions","getSelections","getAllSelections","getData","load","append","prepend","remove","removeAll","insertRow","updateRow","updateCell","updateByUniqueId","removeByUniqueId","getRowByUniqueId","showRow","hideRow","getHiddenRows","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","resetView","resetWidth","destroy","showLoading","hideLoading","showColumn","hideColumn","getHiddenColumns","getVisibleColumns","showAllColumns","hideAllColumns","filterBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","togglePagination","toggleView","refreshOptions","resetSearch","expandRow","collapseRow","expandAllRows","collapseAllRows","updateFormatText"]);C.fn.bootstrapTable=function(o){var s,n=Array.prototype.slice.call(arguments,1);return this.each(function(){var t=C(this),i=t.data("bootstrap.table"),e=C.extend({},g.DEFAULTS,t.data(),"object"==typeof o&&o);if("string"==typeof o){if(C.inArray(o,r)<0)throw new Error("Unknown method: "+o);if(!i)return;s=i[o].apply(i,n),"destroy"===o&&t.removeData("bootstrap.table")}i||t.data("bootstrap.table",i=new g(this,e))}),void 0===s?this:s},C.fn.bootstrapTable.Constructor=g,C.fn.bootstrapTable.defaults=g.DEFAULTS,C.fn.bootstrapTable.columnDefaults=g.COLUMN_DEFAULTS,C.fn.bootstrapTable.locales=g.LOCALES,C.fn.bootstrapTable.methods=r,C.fn.bootstrapTable.utils={sprintf:T,getFieldIndex:p,compareObjects:i,calculateObjectValue:A,getItemField:_,objectKeys:function(){var s,n,a,r;Object.keys||(Object.keys=(s=Object.prototype.hasOwnProperty,n=!{toString:null}.propertyIsEnumerable("toString"),r=(a=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"]).length,function(t){if("object"!=typeof t&&("function"!=typeof t||null===t))throw new TypeError("Object.keys called on non-object");var i,e,o=[];for(i in t)s.call(t,i)&&o.push(i);if(n)for(e=0;e<r;e++)s.call(t,a[e])&&o.push(a[e]);return o}))},isIEBrowser:e},C(function(){C('[data-toggle="table"]').bootstrapTable()})}(jQuery);