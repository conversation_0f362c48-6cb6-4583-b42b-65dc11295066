<script>
import store from './store'
import './permission.js'

export default {
  globalData: {
    // 基础配置
    baseUrl: 'https://bakjgyyxt.baoan.gov.cn',
    iSzgm: false,
    clientId: 'be7052a7e4f802c20df10a8d131adb12',
    publicKey: 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==',
    privateKey: '**********',
    
    // 应用信息
    appInfo: {
      name: 'baoanquestacon-app',
      version: '2.0.0',
      logo: '/static/favicon.ico'
    },
    
    // 微信小程序配置
    appId: 'wx7fdbf0566b7e1707',
    productionTip: false,
    
    // 宝安位置信息
    baoanLocation: {
      la: 22.55866135902317,
      lo: 113.91141057014467
    },
    
    // 系统信息
    systemInfo: null,
    statusBarHeight: 0,
    customBarHeight: 0
  },
  
  onLaunch: function(options) {
    console.log('App Launch', options)
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 加载自定义字体
    //this.loadCustomFonts()
    
    // 添加内存警告监听
    this.addMemoryWarningListener()
    
    // 初始化应用
    this.initApp()
    
    // 处理启动参数
    if (options && options.query) {
      console.log('启动参数:', options.query)
      // 可以在这里处理分享链接等启动参数
    }
  },
  
  onShow: function(options) {
    console.log('App Show', options)
    
    // 应用从后台进入前台时的处理
    this.handleAppShow(options)
  },
  
  onHide: function() {
    console.log('App Hide')
    
    // 应用从前台进入后台时的处理
    this.handleAppHide()
  },
  
  onError: function(error) {
    console.error('App Error:', error)
    
    // 全局错误处理
    this.handleGlobalError(error)
  },
  
  methods: {
    // 获取系统信息
    getSystemInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync()
        this.globalData.systemInfo = systemInfo
        this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0
        
        // 计算自定义导航栏高度
        // #ifdef MP-WEIXIN
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
        this.globalData.customBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - systemInfo.statusBarHeight
        // #endif
        
        // #ifndef MP-WEIXIN
        this.globalData.customBarHeight = systemInfo.statusBarHeight + 44
        // #endif
        
        console.log('系统信息获取成功:', systemInfo)
      } catch (error) {
        console.error('获取系统信息失败:', error)
      }
    },
    
    // 加载自定义字体
    loadCustomFonts() {
      uni.loadFontFace({
        global: true,
        family: 'PingFang SC',
        source: 'url("https://wesalt-ai-digial-dev.oss-cn-shenzhen.aliyuncs.com/font/PINGFANG%20REGULAR.TTF")',
        success: () => {
          console.log('PingFang SC 字体加载成功')
        },
        fail: (err) => {
          console.error('PingFang SC 字体加载失败:', err)
        }
      })
    },
    
    // 添加内存警告监听
    addMemoryWarningListener() {
      uni.onMemoryWarning(() => {
        console.log('内存不足，进行清理')
        this.clearCache()
      })
    },
    
    // 初始化应用
    initApp() {
      console.log('应用初始化开始')
      
      // 检查更新（仅小程序）
      // #ifdef MP-WEIXIN
      this.checkForUpdate()
      // #endif
      
      // 初始化全局数据
      this.initGlobalData()
      
      // 初始化插件
      this.initPlugins()
      
      console.log('应用初始化完成')
    },
    
    // 检查小程序更新
    // #ifdef MP-WEIXIN
    checkForUpdate() {
      const updateManager = uni.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate)
      })
      
      updateManager.onUpdateReady(() => {
        uni.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本下载失败')
      })
    },
    // #endif
    
    // 初始化全局数据
    initGlobalData() {
      // 可以在这里初始化一些全局状态
      console.log('全局数据初始化完成')
    },
    
    // 初始化插件
    initPlugins() {
      // 初始化各种插件和工具
      console.log('插件初始化完成')
    },
    
    // 处理应用显示
    handleAppShow(options) {
      // 应用从后台回到前台时的处理逻辑
      if (options && options.scene) {
        console.log('应用场景值:', options.scene)
      }
    },
    
    // 处理应用隐藏
    handleAppHide() {
      // 应用进入后台时的处理逻辑
      // 可以在这里保存一些状态数据
    },
    
    // 全局错误处理
    handleGlobalError(error) {
      // 统一的错误处理逻辑
      console.error('全局错误:', error)
      
      // 可以在这里上报错误信息
      // 或者显示用户友好的错误提示
    },
    
    // 清理缓存数据
    clearCache() {
      try {
        // 清理一些非必要的缓存数据
        console.log('开始清理缓存')
        
        // 清理图片缓存等
        // uni.clearStorage() // 谨慎使用，会清理所有本地数据
        
        console.log('缓存清理完成')
      } catch (error) {
        console.error('缓存清理失败:', error)
      }
    }
  }
}
</script>

<style>
/* 导入字体图标 */
@import './static/config/iconfont.css';

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

/* 全局页面样式 */
page {
  background-color: #f1f1f1;
  color: #333;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 全局输入框样式 */
.uni-input {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, sans-serif !important;
}

/* 通用样式类 */
.round {
  border-radius: 5000rpx;
}

.radius {
  border-radius: 6rpx;
}

.radius-lg {
  border-radius: 12rpx;
}

/* Flex布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-normal {
  font-weight: normal;
}

/* 文本颜色 */
.text-primary {
  color: #007aff;
}

.text-success {
  color: #4cd964;
}

.text-warning {
  color: #f0ad4e;
}

.text-danger {
  color: #dd524d;
}

.text-info {
  color: #5bc0de;
}

.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.text-gray {
  color: #999999;
}

.text-gray-light {
  color: #cccccc;
}

.text-gray-dark {
  color: #666666;
}

/* 文本大小 */
.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-base {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 40rpx;
}

/* 背景颜色 */
.bg-primary {
  background-color: #007aff;
}

.bg-success {
  background-color: #4cd964;
}

.bg-warning {
  background-color: #f0ad4e;
}

.bg-danger {
  background-color: #dd524d;
}

.bg-info {
  background-color: #5bc0de;
}

.bg-white {
  background-color: #ffffff;
}

.bg-gray {
  background-color: #f8f8f8;
}

.bg-gray-light {
  background-color: #fafafa;
}

/* 边距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }
.mt-4 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }
.mb-4 { margin-bottom: 40rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 10rpx; }
.ml-2 { margin-left: 20rpx; }
.ml-3 { margin-left: 30rpx; }
.ml-4 { margin-left: 40rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 10rpx; }
.mr-2 { margin-right: 20rpx; }
.mr-3 { margin-right: 30rpx; }
.mr-4 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }
.pt-4 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }
.pb-4 { padding-bottom: 40rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 10rpx; }
.pl-2 { padding-left: 20rpx; }
.pl-3 { padding-left: 30rpx; }
.pl-4 { padding-left: 40rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 10rpx; }
.pr-2 { padding-right: 20rpx; }
.pr-3 { padding-right: 30rpx; }
.pr-4 { padding-right: 40rpx; }

/* 按钮样式 */
.cu-btn {
  position: relative;
  border: 0rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.cu-btn.block {
  display: flex;
  width: 100%;
}

.cu-btn.lg {
  padding: 0 40rpx;
  height: 80rpx;
  font-size: 32rpx;
}

.cu-btn.sm {
  padding: 0 20rpx;
  height: 48rpx;
  font-size: 24rpx;
}

.cu-btn.round {
  border-radius: 5000rpx;
}

.cu-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 输入框样式 */
.input-item {
  background-color: #fff;
  margin: 20rpx 0;
  padding: 20rpx;
  border-radius: 10rpx;
  border: 1rpx solid #e5e5e5;
  transition: border-color 0.3s ease;
}

.input-item.focus {
  border-color: #007aff;
}

.input-item .icon {
  margin-right: 20rpx;
  font-size: 32rpx;
  color: #999;
}

.input-item .input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.input-item .input::placeholder {
  color: #999;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fafafa;
}

/* 列表样式 */
.list-item {
  background-color: #fff;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.list-item:active {
  background-color: #f8f8f8;
}

.list-item:last-child {
  border-bottom: none;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #999;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
}

/* 安全区域适配 */
.safe-area-inset-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
</style>
