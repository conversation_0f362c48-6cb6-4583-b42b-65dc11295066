/**
 * Bootstrap Table Thai translation
 * Author: <PERSON><PERSON><PERSON><<EMAIL>>
 */
(function ($) {
    'use strict';

    $.fn.bootstrapTable.locales['th-TH'] = {
        formatLoadingMessage: function () {
            return 'กำลังโหลดข้อมูล, กรุณารอสักครู่...';
        },
        formatRecordsPerPage: function (pageNumber) {
            return pageNumber + ' รายการต่อหน้า';
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
            return 'รายการที่ ' + pageFrom + ' ถึง ' + pageTo + ' จากทั้งหมด ' + totalRows + ' รายการ';
        },
        formatSearch: function () {
            return 'ค้นหา';
        },
        formatNoMatches: function () {
            return 'ไม่พบรายการที่ค้นหา !';
        },
        formatRefresh: function () {
            return 'รีเฟรส';
        },
        formatToggle: function () {
            return 'สลับมุมมอง';
        },
        formatColumns: function () {
            return 'คอลัมน์';
        }
    };

    $.extend($.fn.bootstrapTable.defaults, $.fn.bootstrapTable.locales['th-TH']);

})(jQuery);
