"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  name: "VenueScheme",
  data() {
    return {
      venueList: [],
      statusTextList: ["去签到", "已过期(未检票)", "已取消", "去检票", "已完成", "已过期(未签到)", "场次取消"],
      // 分页参数
      pageNum: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      // 安全区域
      safeAreaBottom: 0
    };
  },
  onLoad() {
    var _a;
    const systemInfo = common_vendor.index.getSystemInfoSync();
    this.safeAreaBottom = ((_a = systemInfo.safeAreaInsets) == null ? void 0 : _a.bottom) || 0;
  },
  onShow() {
    this.resetData();
    this.getVenueList();
  },
  methods: {
    // 重置数据
    resetData() {
      this.venueList = [];
      this.pageNum = 1;
      this.total = 0;
      this.hasMore = true;
    },
    // 获取参观记录列表
    async getVenueList() {
      if (this.loading || !this.hasMore)
        return;
      this.loading = true;
      try {
        const res = await this.$myRequest({
          url: "/web/venueSession/personalCenterVenue",
          method: "get",
          data: {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        });
        if (res.code === 200) {
          const dataList = res.data.data.rows || [];
          this.total = res.data.data.total || 0;
          const processedList = dataList.map((item) => ({
            ...item,
            date: this.formatDate(item.venueArrangedDate),
            venueStartTime: this.formatTime(item.venueStartTime),
            venueEndTime: this.formatTime(item.venueEndTime)
          }));
          if (this.pageNum === 1) {
            this.venueList = processedList;
          } else {
            this.venueList.push(...processedList);
          }
          this.hasMore = this.venueList.length < this.total;
        } else {
          throw new Error(res.msg || "获取记录失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/user/venuescheme.vue:172", "获取参观记录失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取记录失败",
          icon: "error"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.pageNum++;
      this.getVenueList();
    },
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr)
        return "";
      try {
        const date = new Date(dateStr.replace(/-/g, "/"));
        const weekList = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        const weekDay = weekList[date.getDay()];
        return `${dateStr} ${weekDay}`;
      } catch (error) {
        return dateStr;
      }
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      try {
        const date = new Date(timeStr.replace(/-/g, "/"));
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        return `${hours}:${minutes}`;
      } catch (error) {
        return timeStr;
      }
    },
    // 获取状态文本
    getStatusText(state) {
      if (state >= 0 && state < this.statusTextList.length) {
        return this.statusTextList[state];
      }
      return "未知状态";
    },
    // 获取状态样式类
    getStatusClass(state) {
      const statusMap = {
        0: "status-pending",
        // 去签到
        1: "status-expired",
        // 已过期(未检票)
        2: "status-cancelled",
        // 已取消
        3: "status-checkin",
        // 去检票
        4: "status-completed",
        // 已完成
        5: "status-expired",
        // 已过期(未签到)
        6: "status-cancelled"
        // 场次取消
      };
      return statusMap[state] || "status-unknown";
    },
    // 是否显示操作按钮
    showActionButtons(state) {
      return state === 1 || state === 4 || this.canCancel(state);
    },
    // 是否可以取消
    canCancel(state) {
      return state === 1 || state === 4;
    },
    // 是否可以查看二维码
    canViewQRCode(state) {
      return state === 1 || state === 4;
    },
    // 取消预约
    cancelBooking(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/venuecancel?vote=${item.subscribeType}&batchNumber=${item.batchNumber}&venueSessionId=${item.venueSessionId}`
      });
    },
    // 查看二维码
    viewQRCode(item) {
      common_vendor.index.navigateTo({
        url: `/pages_app/schemesuccess/venuesuccess?batchNumber=${item.batchNumber}&venueSessionId=${item.venueSessionId}`
      });
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      title: "参观记录",
      isBack: true,
      isShowHome: true,
      background: "#ffffff",
      color: "#333333"
    }),
    b: $data.venueList.length === 0 && !$data.loading
  }, $data.venueList.length === 0 && !$data.loading ? {
    c: common_assets._imports_0$2
  } : {}, {
    d: common_vendor.f($data.venueList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.getStatusText(item.subscribeState)),
        b: common_vendor.n($options.getStatusClass(item.subscribeState)),
        c: common_vendor.t(item.date),
        d: common_vendor.t(item.venueStartTime),
        e: common_vendor.t(item.venueEndTime),
        f: common_vendor.t(item.subscribeType),
        g: item.linkmanName
      }, item.linkmanName ? {
        h: common_vendor.t(item.linkmanName)
      } : {}, {
        i: $options.showActionButtons(item.subscribeState)
      }, $options.showActionButtons(item.subscribeState) ? common_vendor.e({
        j: $options.canCancel(item.subscribeState)
      }, $options.canCancel(item.subscribeState) ? {
        k: common_vendor.o(($event) => $options.cancelBooking(item), item.venueSessionId || index)
      } : {}, {
        l: $options.canViewQRCode(item.subscribeState)
      }, $options.canViewQRCode(item.subscribeState) ? {
        m: common_vendor.o(($event) => $options.viewQRCode(item), item.venueSessionId || index)
      } : {}) : {}, {
        n: item.venueSessionId || index
      });
    }),
    e: $data.loading
  }, $data.loading ? {} : {}, {
    f: !$data.hasMore && $data.venueList.length > 0
  }, !$data.hasMore && $data.venueList.length > 0 ? {} : {}, {
    g: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    h: $data.safeAreaBottom + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2c878099"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/user/venuescheme.js.map
