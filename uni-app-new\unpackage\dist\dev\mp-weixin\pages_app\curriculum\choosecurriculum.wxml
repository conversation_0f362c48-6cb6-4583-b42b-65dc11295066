<view class="choose-curriculum data-v-110d03ff"><my-header wx:if="{{a}}" class="data-v-110d03ff" u-i="110d03ff-0" bind:__l="__l" u-p="{{a}}"/><view class="curriculum-container data-v-110d03ff"><view class="curriculum-header data-v-110d03ff"><image class="curriculum-bg data-v-110d03ff" mode="aspectFill" src="{{b}}"/><view class="curriculum-info data-v-110d03ff"><image wx:if="{{c}}" class="course-cover data-v-110d03ff" mode="aspectFill" src="{{d}}"/><view class="course-details data-v-110d03ff"><text class="course-name data-v-110d03ff">{{e}}</text><view class="course-meta data-v-110d03ff"><text class="meta-item data-v-110d03ff">适龄：{{f}}</text><text class="meta-item data-v-110d03ff">时间：{{g}} - {{h}}</text><text class="meta-item data-v-110d03ff">地点：{{i}}</text><text class="meta-item data-v-110d03ff">剩余名额：{{j}}</text></view></view></view></view><view class="curriculum-content data-v-110d03ff"><view class="content-section data-v-110d03ff"><view class="section-title data-v-110d03ff"><image class="title-icon data-v-110d03ff" src="{{k}}" mode="aspectFit"/><text class="title-text data-v-110d03ff">课程介绍</text></view><view class="section-content data-v-110d03ff"><rich-text class="data-v-110d03ff" nodes="{{l}}"></rich-text></view></view><view class="content-section data-v-110d03ff"><view class="section-title data-v-110d03ff"><image class="title-icon data-v-110d03ff" src="{{m}}" mode="aspectFit"/><text class="title-text data-v-110d03ff">预约须知</text></view><view class="section-content notice-content data-v-110d03ff"><view class="notice-item data-v-110d03ff"><text class="notice-title data-v-110d03ff">一、报名方式</text><view class="notice-list data-v-110d03ff"><text class="notice-text data-v-110d03ff">1、本次公益培训课程仅接受微信报名，名额有限，先到先得，额满为止</text><text class="notice-text data-v-110d03ff">2、课程咨询热线：0755-27880235</text><text class="notice-text data-v-110d03ff">3、公益课开始上课后自动关闭报名入口</text><text class="notice-text data-v-110d03ff">4、学员可通过个人中心-课程预约-查看凭证，管理查询个人预约信息</text></view></view><view class="notice-item data-v-110d03ff"><text class="notice-title data-v-110d03ff">二、温馨提示</text><view class="notice-list data-v-110d03ff"><text class="notice-text data-v-110d03ff">1、学员必须以本人真实信息报名，如现场确认时发现报名信息不符合则取消资格</text><text class="notice-text data-v-110d03ff">2、课程期间，请学员严格遵守上课时间，为保证教学质量迟到超过10分钟则不能进入教室学习</text></view></view></view></view></view><view class="contacts-section data-v-110d03ff"><view class="section-title data-v-110d03ff"><image class="title-icon data-v-110d03ff" src="{{n}}" mode="aspectFit"/><text class="title-text data-v-110d03ff">选择联系人</text></view><view wx:if="{{o}}" class="contacts-list data-v-110d03ff"><view wx:for="{{p}}" wx:for-item="contact" wx:key="d" class="contact-item data-v-110d03ff" bindtap="{{contact.e}}"><view class="contact-info data-v-110d03ff"><text class="contact-name data-v-110d03ff">{{contact.a}}</text><text class="contact-id data-v-110d03ff">{{contact.b}}</text></view><view class="contact-select data-v-110d03ff"><view class="{{['data-v-110d03ff', 'select-circle', contact.c]}}"></view></view></view></view><view wx:else class="no-contacts data-v-110d03ff"><text class="no-contacts-text data-v-110d03ff">暂无联系人，请先添加联系人</text><button class="add-contact-btn data-v-110d03ff" bindtap="{{q}}">添加联系人</button></view></view></view><view class="bottom-bar data-v-110d03ff"><button class="submit-btn data-v-110d03ff" disabled="{{s}}" bindtap="{{t}}">{{r}}</button></view></view>