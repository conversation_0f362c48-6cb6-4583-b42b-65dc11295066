<view bindtap="{{I}}" class="{{['uni-calendar-item__weeks-box','data-v-9fa7f734',C&&'uni-calendar-item--disable',D&&'uni-calendar-item--isDay',E&&'uni-calendar-item--checked',F&&'uni-calendar-item--before-checked',G&&'uni-calendar-item--multiple',H&&'uni-calendar-item--after-checked']}}">
    <view class="uni-calendar-item__weeks-box-item data-v-9fa7f734">
        <text class="{{['uni-calendar-item__weeks-box-text','data-v-9fa7f734',b&&'uni-calendar-item--isDay-text',c&&'uni-calendar-item--isDay',d&&'uni-calendar-item--checked',e&&'uni-calendar-item--before-checked',f&&'uni-calendar-item--multiple',g&&'uni-calendar-item--after-checked',h&&'uni-calendar-item--disable']}}">{{a}}</text>
        <text class="{{['uni-calendar-item__weeks-lunar-text','data-v-9fa7f734',k&&'uni-calendar-item--isDay-text',l&&'uni-calendar-item--isDay',m&&'uni-calendar-item--checked',n&&'uni-calendar-item--before-checked',o&&'uni-calendar-item--multiple',p&&'uni-calendar-item--after-checked',q&&'uni-calendar-item--disable']}}" wx:if="{{i}}">{{j}}</text>
        <text class="{{['uni-calendar-item__weeks-lunar-text','data-v-9fa7f734',t&&'uni-calendar-item--extra',v&&'uni-calendar-item--isDay-text',w&&'uni-calendar-item--isDay',x&&'uni-calendar-item--checked',y&&'uni-calendar-item--before-checked',z&&'uni-calendar-item--multiple',A&&'uni-calendar-item--after-checked',B&&'uni-calendar-item--disable']}}" wx:if="{{r}}">{{s}}</text>
    </view>
</view>
