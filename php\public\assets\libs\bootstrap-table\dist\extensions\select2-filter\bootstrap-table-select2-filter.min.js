/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";function b(a){var b=a.$header;return a.options.height&&(b=a.$tableHeader),b}function c(a){return b(a).find("[data-filter-field]")}function d(b){var d=c(b);a.isEmptyObject(b.filterColumnsPartial)||d.each(function(c,d){var e=a(d),f=e.attr("data-filter-field"),g=b.filterColumnsPartial[f];e.is("select")?e.val(g).trigger("change"):e.val(g)})}function e(b,c){var d,e,f=!1,g=0;if(a.each(b.columns,function(b,g){if(d="hidden",e=[],g.visible){if(g.filter){var h=g.filter["class"]?" "+g.filter["class"]:"";switch(e.push('<div style="margin: 0px 2px 2px 2px;" class="filter'+h+'">'),g.searchable&&(f=!0,d="visible"),g.filter.type.toLowerCase()){case"input":e.push('<input type="text" data-filter-field="'+g.field+'" style="width: 100%; visibility:'+d+'">');break;case"select":e.push('<select data-filter-field="'+g.field+'" style="width: 100%; visibility:'+d+'"></select>')}}else e.push('<div class="no-filter"></div>');a.each(c.children().children(),function(b,c){return c=a(c),c.data("field")===g.field?(c.find(".fht-cell").append(e.join("")),!1):void 0})}}),f){var h=c.find("input"),i=c.find("select");h.length>0&&(h.off("keyup").on("keyup",function(a){clearTimeout(g),g=setTimeout(function(){b.onColumnSearch(a)},b.options.searchTimeOut)}),h.off("mouseup").on("mouseup",function(c){var d=a(this),e=d.val();""!==e&&setTimeout(function(){var a=d.val();""===a&&(clearTimeout(g),g=setTimeout(function(){b.onColumnSearch(c)},b.options.searchTimeOut))},1)})),i.length>0&&i.on("select2:select",function(a){b.onColumnSearch(a)})}else c.find(".filter").hide()}function f(c){var d=b(c);a.each(c.columns,function(a,b){if(b.filter&&"select"===b.filter.type){var e=d.find("select[data-filter-field="+b.field+"]");if(e.length>0&&!e.data().select2){b.filter.data.unshift("");var f={placeholder:"",allowClear:!0,data:b.filter.data,dropdownParent:c.$el.closest(".bootstrap-table")};e.select2(f),e.on("select2:unselecting",function(a){a.preventDefault(),e.val(null).trigger("change"),c.searchText=void 0,c.onColumnSearch(a)})}}})}a.extend(a.fn.bootstrapTable.defaults,{filter:!1,filterValues:{}}),a.extend(a.fn.bootstrapTable.COLUMN_DEFAULTS,{filter:void 0});var g=a.fn.bootstrapTable.Constructor,h=g.prototype.init,i=g.prototype.initHeader,j=g.prototype.initSearch;g.prototype.init=function(){if(this.options.filter){var b=this;a.isEmptyObject(b.options.filterValues)||(b.filterColumnsPartial=b.options.filterValues,b.options.filterValues={}),this.$el.on("reset-view.bs.table",function(){b.options.height&&(b.$tableHeader.find("select").length>0||b.$tableHeader.find("input").length>0||e(b,b.$tableHeader))}).on("post-header.bs.table",function(){var a=0;f(b),clearTimeout(a),a=setTimeout(function(){d(b)},b.options.searchTimeOut-1e3)}).on("column-switch.bs.table",function(){d(b)})}h.apply(this,Array.prototype.slice.apply(arguments))},g.prototype.initHeader=function(){i.apply(this,Array.prototype.slice.apply(arguments)),this.options.filter&&e(this,this.$header)},g.prototype.initSearch=function(){j.apply(this,Array.prototype.slice.apply(arguments));var b=this,c=b.filterColumnsPartial;"client"===b.options.sidePagination&&(this.data=a.grep(this.data,function(d,e){for(var f in c){var g=b.columns[a.fn.bootstrapTable.utils.getFieldIndex(b.columns,f)],h=c[f].toLowerCase(),i=d[f];if(i=a.fn.bootstrapTable.utils.calculateObjectValue(b.header,b.header.formatters[a.inArray(f,b.header.fields)],[i,d,e],i),g.filterStrictSearch){if(-1===a.inArray(f,b.header.fields)||"string"!=typeof i&&"number"!=typeof i||i.toString().toLowerCase()!==h.toString().toLowerCase())return!1}else if(-1===a.inArray(f,b.header.fields)||"string"!=typeof i&&"number"!=typeof i||-1===(i+"").toLowerCase().indexOf(h))return!1}return!0}))},g.prototype.onColumnSearch=function(b){var c=a(b.currentTarget).attr("data-filter-field"),d=a.trim(a(b.currentTarget).val());a.isEmptyObject(this.filterColumnsPartial)&&(this.filterColumnsPartial={}),d?this.filterColumnsPartial[c]=d:delete this.filterColumnsPartial[c],this.options.pageNumber=1,this.onSearch(b)},g.prototype.setFilterData=function(c,d){var e=this,f=b(e),g=f.find('select[data-filter-field="'+c+'"]');d.unshift(""),g.empty(),g.select2({data:d,placeholder:"",allowClear:!0,dropdownParent:e.$el.closest(".bootstrap-table")}),a.each(this.columns,function(a,b){return b.field===c?(b.filter.data=d,!1):void 0})},g.prototype.setFilterValues=function(a){this.filterColumnsPartial=a},a.fn.bootstrapTable.methods.push("setFilterData"),a.fn.bootstrapTable.methods.push("setFilterValues")}(jQuery);