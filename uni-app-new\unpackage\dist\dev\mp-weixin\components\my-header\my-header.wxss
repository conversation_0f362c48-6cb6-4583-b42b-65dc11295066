
.my-header.data-v-3f622259 {
  color: #333;
  font-family: <PERSON>Fang SC;
  font-size: 35rpx;
  width: 100%;
}
.my-header .header_content.data-v-3f622259 {
  height: 100%;
  position: relative;
  width: 100%;
}
.my-header .header_content .header_btns.data-v-3f622259 {
  align-items: center;
  background-color: transparent;
  border-radius: 100rpx;
  display: flex;
  height: 100%;
  justify-content: flex-start;
  margin-left: 29rpx;
  position: relative;
  text-align: center;
  width: 160rpx;
}
.my-header .header_content .header_btns .back.data-v-3f622259 {
  font-size: 33rpx;
  height: 100%;
  margin-top: -30px;
  position: relative;
  width: 50%;
}
.my-header .header_content .header_btns .back.data-v-3f622259::after {
  background-color: transparent;
  content: "";
  display: inline-block;
  height: 36rpx;
  position: absolute;
  right: 1rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
}
.my-header .header_content .header_btns .home.data-v-3f622259 {
  font-size: 33rpx;
  height: 100%;
  margin-top: -30px;
  width: 50%;
}
.my-header .header_content .title.data-v-3f622259 {
  display: inline-block;
  font-weight: 700;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 图标样式 */
.icon_back.data-v-3f622259::before {
  content: "‹";
  font-size: 40rpx;
  font-weight: bold;
}
.icon_home.data-v-3f622259::before {
  content: "⌂";
  font-size: 36rpx;
}
