"use strict";
const common_vendor = require("../../common/vendor.js");
const MyHeader = () => "../../components/my-header/my-header.js";
const _sfc_main = {
  name: "EnterVenue",
  components: {
    MyHeader
  },
  data() {
    return {
      // 时间选择相关
      timeSection: {
        start: "",
        end: ""
      },
      selected: [],
      checkDate: "",
      // 场次选择相关
      localdata: [],
      venueId: "",
      venueInfo: [],
      isChooseVenue: false,
      // 人数选择相关
      checkItem: [
        { label: "1人", value: 1 },
        { label: "2人", value: 2 },
        { label: "3人", value: 3 },
        { label: "4人", value: 4 },
        { label: "5人", value: 5 }
      ],
      checkIndex: -1,
      selectedNum: 0,
      // 联系人相关
      contactList: [],
      // 提交状态
      isSubmitting: false
    };
  },
  onLoad() {
    this.initTimeSection();
    this.getContactList();
  },
  onShow() {
    this.getContactList();
  },
  methods: {
    // 初始化时间范围
    initTimeSection() {
      const today = /* @__PURE__ */ new Date();
      const endDate = /* @__PURE__ */ new Date();
      endDate.setDate(today.getDate() + 30);
      this.timeSection = {
        start: this.formatDate(today),
        end: this.formatDate(endDate)
      };
      this.checkDate = this.formatDate(today);
      this.selected = [{
        date: this.formatDate(today),
        info: "今天"
      }];
      this.getVenueDetail({ fulldate: this.formatDate(today) });
    },
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 获取场次详情
    async getVenueDetail(e) {
      try {
        this.checkDate = e.fulldate;
        const res = await this.$myRequest({
          url: "/web/venue/getVenueByDate",
          method: "get",
          data: {
            venueDate: e.fulldate
          }
        });
        if (res.code === 200 && res.data.data) {
          this.venueInfo = res.data.data;
          this.localdata = res.data.data.map((item) => ({
            value: item.id,
            text: `${item.venueStartTime}-${item.venueEndTime} (剩余${item.inventoryVotes})`
          }));
        } else {
          this.venueInfo = [];
          this.localdata = [];
          common_vendor.index.showToast({
            title: "该日期暂无可预约场次",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/entervenue/index.vue:212", "获取场次失败:", error);
        common_vendor.index.showToast({
          title: "获取场次失败",
          icon: "error"
        });
      }
    },
    // 选择场次
    bindPickerChange(e) {
      this.venueId = e.detail.value;
      this.isChooseVenue = true;
    },
    // 选择人数
    checkNum(num) {
      this.selectedNum = num;
      this.checkIndex = this.checkItem.findIndex((item) => item.value === num);
    },
    // 获取联系人列表
    async getContactList() {
      try {
        const res = await this.$myRequest({
          url: "/web/linkman/getLinkmanList",
          method: "get"
        });
        if (res.code === 200) {
          this.contactList = (res.data.data || []).map((item) => ({
            ...item,
            linkCheck: false
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/entervenue/index.vue:247", "获取联系人失败:", error);
      }
    },
    // 切换联系人选择状态
    toggleContact(index) {
      const selectedCount = this.contactList.filter((item) => item.linkCheck).length;
      if (!this.contactList[index].linkCheck && selectedCount >= this.selectedNum) {
        common_vendor.index.showToast({
          title: `最多只能选择${this.selectedNum}人`,
          icon: "none"
        });
        return;
      }
      this.contactList[index].linkCheck = !this.contactList[index].linkCheck;
    },
    // 添加联系人
    addContact() {
      common_vendor.index.navigateTo({
        url: "/pages_app/contacts/addcontact"
      });
    },
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8)
        return idCard;
      return idCard.replace(idCard.substring(4, 15), "*******");
    },
    // 提交预约
    async submitReservation() {
      if (this.isSubmitting)
        return;
      if (!this.venueId) {
        common_vendor.index.showToast({
          title: "请选择预约场次",
          icon: "none"
        });
        return;
      }
      if (this.selectedNum === 0) {
        common_vendor.index.showToast({
          title: "请选择预约人数",
          icon: "none"
        });
        return;
      }
      const selectedContacts = this.contactList.filter((item) => item.linkCheck);
      if (selectedContacts.length === 0) {
        common_vendor.index.showToast({
          title: "请选择联系人",
          icon: "none"
        });
        return;
      }
      if (selectedContacts.length !== this.selectedNum) {
        common_vendor.index.showToast({
          title: `请选择${this.selectedNum}位联系人`,
          icon: "none"
        });
        return;
      }
      try {
        this.isSubmitting = true;
        const res = await this.$myRequest({
          url: "/web/venue/createVenueOrder",
          method: "post",
          data: {
            venueId: this.venueId,
            venueDate: this.checkDate,
            linkmanIds: selectedContacts.map((item) => item.linkId).join(","),
            peopleNum: this.selectedNum
          }
        });
        if (res.code === 200) {
          common_vendor.index.showToast({
            title: "预约成功",
            icon: "success",
            duration: 2e3
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: `/pages_app/schemesuccess/venuesuccess?orderId=${res.data.data.orderId}`
            });
          }, 2e3);
        } else {
          throw new Error(res.msg || "预约失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages_app/entervenue/index.vue:347", "提交预约失败:", error);
        common_vendor.index.showToast({
          title: error.message || "预约失败",
          icon: "error"
        });
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  const _component_uni_calendar = common_vendor.resolveComponent("uni-calendar");
  const _component_uni_data_picker = common_vendor.resolveComponent("uni-data-picker");
  (_easycom_my_header2 + _component_uni_calendar + _component_uni_data_picker)();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      isBack: true,
      isShowHome: true,
      title: "参观预约",
      background: "transparent"
    }),
    b: common_vendor.o($options.getVenueDetail),
    c: common_vendor.p({
      date: $data.timeSection.start,
      insert: true,
      lunar: false,
      showMonth: false,
      ["start-date"]: $data.timeSection.start,
      ["end-date"]: $data.timeSection.end,
      selected: $data.selected
    }),
    d: common_vendor.o($options.bindPickerChange),
    e: common_vendor.o(($event) => $data.venueId = $event),
    f: common_vendor.p({
      localdata: $data.localdata,
      placeholder: "点击选择入馆时间",
      ["popup-title"]: "请选择时间",
      modelValue: $data.venueId
    }),
    g: common_vendor.f(20, (item, index, i0) => {
      return {
        a: index
      };
    }),
    h: common_vendor.f($data.checkItem, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: common_vendor.n($data.checkIndex === index ? "isCheck" : ""),
        c: index,
        d: common_vendor.o(($event) => $options.checkNum(item.value), index)
      };
    }),
    i: $data.isChooseVenue
  }, $data.isChooseVenue ? {
    j: common_vendor.f($data.contactList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.linkmanName),
        b: common_vendor.t(item.linkmanPhone),
        c: common_vendor.t($options.hideIdCard(item.linkmanCertificate)),
        d: item.linkCheck
      }, item.linkCheck ? {} : {}, {
        e: common_vendor.n(item.linkCheck ? "checked" : ""),
        f: common_vendor.o(($event) => $options.toggleContact(index), item.linkId),
        g: item.linkId
      });
    }),
    k: common_vendor.o((...args) => $options.addContact && $options.addContact(...args))
  } : {}, {
    l: $data.isChooseVenue
  }, $data.isChooseVenue ? {
    m: common_vendor.t($data.isSubmitting ? "提交中..." : "确认预约"),
    n: common_vendor.o((...args) => $options.submitReservation && $options.submitReservation(...args)),
    o: $data.isSubmitting
  } : {}, {
    p: common_vendor.n($data.isChooseVenue ? "isChooseVenue" : "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-62d6126f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/entervenue/index.js.map
