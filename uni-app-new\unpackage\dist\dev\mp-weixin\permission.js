"use strict";
const common_vendor = require("./common/vendor.js");
const utils_auth = require("./utils/auth.js");
const whiteList = [
  "/pages_app/login/index",
  "/pages_app/register/index",
  "/pages/index/index",
  "/pages_app/entervenue/index",
  "/pages_app/vieworder/index",
  "/pages_app/curriculum/index",
  "/pages_app/user/index"
];
const routerInterceptor = {
  invoke(to) {
    const token = utils_auth.getToken();
    if (token) {
      if (to.url === "/pages_app/login/index") {
        common_vendor.index.reLaunch({
          url: "/pages/index/index"
        });
        return false;
      }
      return true;
    } else {
      const path = to.url.split("?")[0];
      if (whiteList.includes(path)) {
        return true;
      } else {
        common_vendor.index.reLaunch({
          url: "/pages/index/index"
        });
        return false;
      }
    }
  },
  fail(err) {
    common_vendor.index.__f__("error", "at permission.js:44", "路由拦截失败:", err);
  }
};
const routerMethods = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
routerMethods.forEach((method) => {
  common_vendor.index.addInterceptor(method, routerInterceptor);
});
//# sourceMappingURL=../.sourcemap/mp-weixin/permission.js.map
