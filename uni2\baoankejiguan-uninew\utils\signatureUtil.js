// signatureUtil.js
import md5 from "js-md5";
export function signatureGenerate({data, url, headers}){
    // 参数签名 密钥 + 时间戳 + header参数 + url

    // 密钥
    // let secret = Math.random().toString(36).substr(2)
    // 时间戳
    let timestamp = new Date().getTime();     
    // post参数
    // let dataStr = dataSerialize(dataSort(data))
	let dataStr = dataSerialize(data)
    // 生成签名
    // let str = dataStr + "secret=" + secret + "&timestamp=" + timestamp + "&url=" + url
	let str = "&timestamp=" + timestamp + "&url=" + url + dataStr;
	// console.log(str,'88888')
    
    const sign = md5(str)
    
    return {
        signature: sign.toUpperCase(), // 将签名字母转为大写
        timestamp,
        // secret
    }
}

// 参数排序
function dataSort(obj){
    if (JSON.stringify(obj) == "{}" || obj == null) {
        return {}
    }
    let key = Object.keys(obj)?.sort()
    let newObj = {}
    for (let i = 0; i < key.length; i++) {
        newObj[key[i]] = obj[key[i]]        
    }
    return newObj
}

// 参数序列化
function dataSerialize(sortObj){
	if (typeof(sortObj) == 'string') {
		var sortObj = JSON.parse(sortObj);
		let strJoin = ''
		for(let key in sortObj){
			strJoin += key + "=" + sortObj[key] + "&"
		}
		return strJoin
	} else {
		let strJoin = ''
		for(let key in sortObj){
			strJoin += key + "=" + sortObj[key] + "&"
		}
		return strJoin
	}
}
