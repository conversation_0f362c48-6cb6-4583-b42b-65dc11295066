/*
* bootstrap-table - v1.11.1 - 2017-02-22
* https://github.com/wenzhixin/bootstrap-table
* Copyright (c) 2017 zhixin wen
* Licensed MIT License
*/
!function(a){"use strict";var b=function(){Array.prototype.filter||(Array.prototype.filter=function(a){if(void 0===this||null===this)throw new TypeError;var b=Object(this),c=b.length>>>0;if("function"!=typeof a)throw new TypeError;for(var d=[],e=arguments.length>=2?arguments[1]:void 0,f=0;c>f;f++)if(f in b){var g=b[f];a.call(e,g,f,b)&&d.push(g)}return d})};a.extend(a.fn.bootstrapTable.defaults,{reorderableColumns:!1,maxMovingRows:10,onReorderColumn:function(){return!1},dragaccept:null}),a.extend(a.fn.bootstrapTable.Constructor.EVENTS,{"reorder-column.bs.table":"onReorderColumn"});var c=a.fn.bootstrapTable.Constructor,d=c.prototype.initHeader,e=c.prototype.toggleColumn,f=c.prototype.toggleView,g=c.prototype.resetView;c.prototype.initHeader=function(){d.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&this.makeRowsReorderable()},c.prototype.toggleColumn=function(){e.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&this.makeRowsReorderable()},c.prototype.toggleView=function(){f.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&(this.options.cardView||this.makeRowsReorderable())},c.prototype.resetView=function(){g.apply(this,Array.prototype.slice.apply(arguments)),this.options.reorderableColumns&&this.makeRowsReorderable()},c.prototype.makeRowsReorderable=function(){var c=this;try{a(this.$el).dragtable("destroy")}catch(d){}a(this.$el).dragtable({maxMovingRows:c.options.maxMovingRows,dragaccept:c.options.dragaccept,clickDelay:200,beforeStop:function(){var d=[],e=[],f=[],g=[],h=-1,i=[];if(c.$header.find("th").each(function(){d.push(a(this).data("field")),e.push(a(this).data("formatter"))}),d.length<c.columns.length){g=a.grep(c.columns,function(a){return!a.visible});for(var j=0;j<g.length;j++)d.push(g[j].field),e.push(g[j].formatter)}for(var j=0;j<d.length;j++)h=a.fn.bootstrapTable.utils.getFieldIndex(c.columns,d[j]),-1!==h&&(c.columns[h].fieldIndex=j,f.push(c.columns[h]),c.columns.splice(h,1));c.columns=c.columns.concat(f),b(),a.each(c.columns,function(a,b){var d=!1,e=b.field;c.options.columns[0].filter(function(a){return d||a.field!=e?!0:(i.push(a),d=!0,!1)})}),c.options.columns[0]=i,c.header.fields=d,c.header.formatters=e,c.initHeader(),c.initToolbar(),c.initBody(),c.resetView(),c.trigger("reorder-column",d)}})}}(jQuery);