/**
 * 隐私组件工具函数
 * 适配uni-app环境
 */

export function getComponent(instance, selector) {
  if (instance && instance.selectComponent) {
    const component = instance.selectComponent(selector)
    return component && component.$vm
  }
  return null
}

export function getContext() {
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

// 默认导出
export default {
  getComponent,
  getContext
}