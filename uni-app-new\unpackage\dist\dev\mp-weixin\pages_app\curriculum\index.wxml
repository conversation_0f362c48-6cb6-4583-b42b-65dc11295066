<view class="curriculum"><my-header wx:if="{{a}}" u-i="0b0505e8-0" bind:__l="__l" u-p="{{b}}"/><view class="curriculum_order_box"><view class="calendar_content"><view class="calendar"><uni-calendar wx:if="{{d}}" bindchange="{{c}}" u-i="0b0505e8-1" bind:__l="__l" u-p="{{d}}"/></view></view><view class="curriculum_list_content"><image class="curriculum_title" mode="aspectFill" src="{{e}}"/><view wx:if="{{f}}" class="curriculum_list"><view wx:for="{{g}}" wx:for-item="item" wx:key="i" class="{{['curriculum_item', item.j]}}" bindtap="{{item.k}}"><view class="itemLeft"><image class="cover" mode="aspectFill" src="{{item.a}}"/><view class="curriculumInfo"><text class="curriculumName">{{item.b}}</text><text class="curriculumType">适龄：{{item.c}}</text><text class="curriculumTime">时间：{{item.d}}-{{item.e}}</text><text class="curriculumPlace">地点：{{item.f}}</text></view></view><view class="itemRight"><view class="ticketType" style="display:none"><text class="n_text">倒计时</text><text class="t_text">25:31</text></view><view class="{{['order_button', item.h]}}"> 剩余:{{item.g}}</view></view></view></view><view wx:else class="curriculum_tip"> 暂无课程数据 </view></view></view><view wx:if="{{h}}" class="mask"><view class="maskContent"><view class="noticeTitle">报名须知</view><view class="noticeView"><view wx:for="{{i}}" wx:for-item="notice" wx:key="c" class="noticeItem"><view class="itemTitle">{{notice.a}}</view><view wx:for="{{notice.b}}" wx:for-item="item" wx:key="b" class="itemContent">{{item.a}}</view></view></view><view class="{{['agreeBtn', k]}}" bindtap="{{l}}">{{j}}</view></view></view></view>