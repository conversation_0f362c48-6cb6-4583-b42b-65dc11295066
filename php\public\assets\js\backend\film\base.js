define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'moment',
        '/assets/libs/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js'
], function ($, undefined, Backend, Table, Form, moment) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'film/base/index' + location.search,
                    add_url: 'film/base/add',
                    edit_url: 'film/base/edit',
                    del_url: 'film/base/del',
                    multi_url: 'film/base/multi',
                    import_url: 'film/base/import',
                    table: 'film',
                }
            });

            var table = $("#table");
    // 判断是否为选择模式(影片排期用)
    var isSelectMode = Fast.api.query('select') == '1';    

    if (isSelectMode) {
        // 只显示刷新按钮，  隐藏 toolbar 中无关按钮，
        $('#toolbar .btn').not('.btn-refresh').hide();
            // 显示 影片排期 区域
        $('#schedule-form').slideDown();
    }

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                searchFormVisible: true,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'film_cover', title: __('Film_cover'), operate: false, table: table,
                            width: 150, // 指定列宽
                            cellStyle: function(){
                              return { css: { "width": "150px", "padding": "5px" } };
                            },
                            formatter: Table.api.formatter.image,
                            events: Table.api.events.image
                        },   
                  
                        {
                            field: 'film_type',
                            title: __('Film_type'),
                            searchList: {1: __('Film_type_1'), 2: __('Film_type_2')},
                            formatter: Table.api.formatter.normal
                        },
                        
                        {field: 'film_name', title: __('Film_name'), operate: 'LIKE'},
                        {
                            field: 'file_introduce',
                            title: __('File_introduce'),
                            operate: false,
                            cellStyle: {
                                css: {
                                    'white-space': 'normal',   // 允许自动换行
                                    'word-break': 'break-all', // 单词长也能换行
                                    'line-height': '1.5em',
                                    'min-width': '200px',
                                    'max-width': '400px',
                                }
                            },
                            formatter: function (value) {
                                return `<div style="white-space: normal; word-break: break-all;">${value || ''}</div>`;
                            }
                        }                        ,
                           
                     
                    //  {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                     {
                        field: 'operate',
                        title: __('Operate'),
                        table: table,
                        events: Table.api.events.operate,
                        formatter: function (value, row, index) {
                            if (isSelectMode) {
                                return `<a href="javascript:;" class="btn btn-xs btn-success btn-select" data-id="${row.id}" data-name="${row.film_name}" data-cover="${row.film_cover}" data-type="${row.film_type_text}">选择影片</a>`;
                            } else {
                                // 使用默认操作列 formatter
                                return Table.api.formatter.operate.call(this, value, row, index);
                            }
                        }
                    }
                    ]
                ]
            });

            Table.api.bindevent(table);

            // 优化时间选择器
            // $('.timepicker').datetimepicker({
            //     format: 'HH:mm',
            //     useCurrent: false,
            //     locale: 'zh-cn',
            //     showTodayButton: false,
            //     showClose: false,
            //     showClear: false,
            //     icons: {
            //         time: 'fa fa-clock-o',
            //         up: 'fa fa-chevron-up',
            //         down: 'fa fa-chevron-down'
            //     }
            // });
            //点击 选择影片
            $(document).on('click', '.btn-select', function () {
                const film_id = $(this).data('id');
                const film_name = $(this).data('name');
                const film_cover = $(this).data('cover');
            
                // 保存选中信息
                $('#schedule-form').data('film-id', film_id);
                $('#schedule-film-name').text(film_name);
                $('#schedule-film-cover').attr('src', film_cover);
            
                // 重置字段
                $('#film_poll').val('');
                $('#film_start_time').val('');
                $('#film_end_time').val('');
            
               // 显示输入区域
                $('#schedule-form').slideDown(200, function () {
                    // 平滑滚动到排期表单
                    const el = document.getElementById('schedule-form');
                    if (el && el.scrollIntoView) {
                        el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                });
            });
            //提交排期数据：
            $('#btn-submit-schedule').on('click', function () {
                const film_id = $('#schedule-form').data('film-id');
                const poll = $('#film_poll').val();
                const start = $('#film_start_time').val();
                const end = $('#film_end_time').val();
            
                if (!film_id || !poll || !start || !end) {
                    Layer.msg('请填写完整信息');
                    return;
                }
                // 校验时间格式正确且开始 < 结束
                const startMoment = moment(start, 'HH:mm');
                const endMoment = moment(end, 'HH:mm');

                if (!startMoment.isValid() || !endMoment.isValid()) {
                    Layer.msg('请输入合法的时间格式');
                    return;
                }

                if (!startMoment.isBefore(endMoment)) {
                    Layer.msg('开始时间必须小于结束时间');
                    return;
                }
                // 获取当前选中的排期日期（从主页面传来的）
                const arrangedDate = Fast.api.query('date'); // 例如 2025-07-15
            
                // 拼接完整开始时间、结束时间
                const startTime = arrangedDate + ' ' + start + ':00';
                const endTime = arrangedDate + ' ' + end + ':00';
            
                // 提交
                Fast.api.ajax({
                    url: 'film/session/add', // 请确认接口路径
                    method: 'POST',
                    data: {
                        'row[film_id]': film_id,
                        'row[film_poll]': poll,
                        'row[film_start_time]': startTime,
                        'row[film_end_time]': endTime,
                        'row[film_arranged_date]': arrangedDate,
                    }
                }, function (res) {
                    console.log(res);
                    if (typeof parent.Toastr !== 'undefined') {
                        parent.Toastr.success(res || "成功");
                      } else {
                        Toastr.success(res || "成功");
                      }
                   // ✅ 关闭当前页面，并将返回值传回主页面
                    Fast.api.close({
                        success: true,
                        film_id: film_id,
                        arranged_date: arrangedDate
                    });
 
                    return false;// 防止 Layer 自动关闭
                });
            });
            
            
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
