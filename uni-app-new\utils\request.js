import config from '../config.js'
import { getToken } from './auth.js'
import { errorCode } from './errorCode.js'
import { tansParams, toast } from './common.js'
import { generateAesKey, encryptWithAes, encryptBase64 } from './crypto.js'
import { encrypt } from './jsencrypt.js'

const baseUrl = config.baseUrl
const iSzgm = config.iSzgm
const appId = config.appId

// 请求封装 - 适配uni-app并添加平台兼容性处理
export function request(options) {
  return new Promise((resolve, reject) => {
    // 设置请求头
    options.header = options.header || {}

    const isToken = !(options.headers || {}).isToken === false
    const isEncrypt = (options.headers || {}).isEncrypt === true

    // 添加token
    if (!options.url.includes(`/wx/user/${appId}/login`) && getToken() && isToken) {
      options.header.Authorization = 'wx ' + getToken()
    }

    // 处理参数
    if (options.params) {
      let paramStr = options.url + '?' + tansParams(options.params)
      options.url = paramStr.slice(0, -1)
    }

    // 处理加密
    if (isEncrypt && (options.method === 'POST' || options.method === 'PUT')) {
      const aesKey = generateAesKey()
      options.header['encrypt-key'] = encrypt(encryptBase64(aesKey))
      options.data = typeof options.data === 'object'
        ? encryptWithAes(JSON.stringify(options.data), aesKey)
        : encryptWithAes(options.data, aesKey)
    }

    // 平台兼容性处理
    const requestConfig = {
      method: options.method || 'GET',
      timeout: options.timeout || 10000,
      url: baseUrl + options.url,
      data: options.data,
      header: options.header,
      dataType: 'json'
    }

    // #ifdef H5
    // H5平台可能需要处理跨域
    requestConfig.header['Content-Type'] = 'application/json'
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序特殊处理
    if (options.enableHttp2) {
      requestConfig.enableHttp2 = true
    }
    // #endif

    // #ifdef APP-PLUS
    // App平台可能需要特殊配置
    requestConfig.sslVerify = false // 根据需要配置SSL验证
    // #endif

    // 发起请求 - 关键适配点：使用uni.request
    uni.request({
      ...requestConfig,
      success: (response) => {
        const data = response.data
        const code = data.code || 200
        const msg = errorCode[code] || data.msg || errorCode.default

        if (code === 200) {
          resolve(data)
        } else if (code === 401) {
          // 401错误特殊处理 - 可能需要重新登录
          handleAuthError()
          reject('无效的会话，或者会话已过期，请重新登录。')
        } else if (code === 500) {
          toast(msg)
          reject('500')
        } else {
          toast(msg)
          reject(code)
        }
      },
      fail: (error) => {
        let errMsg = error.errMsg || '请求失败'
        
        // 平台特定错误处理
        // #ifdef MP-WEIXIN
        if (errMsg === 'request:fail url not in domain list') {
          errMsg = '请求域名不在白名单中，请联系管理员'
        }
        // #endif
        
        // #ifdef H5
        if (errMsg.includes('CORS')) {
          errMsg = '跨域请求被阻止，请联系管理员'
        }
        // #endif
        
        // 通用错误处理
        if (errMsg === 'Network Error' || errMsg === 'request:fail') {
          errMsg = '后端接口连接异常'
        } else if (errMsg.includes('timeout')) {
          errMsg = '系统接口请求超时'
        } else if (errMsg.includes('Request failed with status code')) {
          errMsg = '系统接口' + errMsg.substr(errMsg.length - 3) + '异常'
        }
        
        toast(errMsg)
        reject(error)
      }
    })
  })
}

// 处理认证错误
function handleAuthError() {
  // 清除token
  import('./auth.js').then(auth => {
    auth.removeToken()
  })
  
  // 跳转到登录页面
  // #ifdef MP-WEIXIN
  uni.reLaunch({
    url: '/pages_app/login/index'
  })
  // #endif
  
  // #ifdef H5
  uni.reLaunch({
    url: '/pages_app/login/index'
  })
  // #endif
  
  // #ifdef APP-PLUS
  uni.reLaunch({
    url: '/pages_app/login/index'
  })
  // #endif
}

// 封装常用请求方法
export function get(url, params, options = {}) {
  return request({
    url,
    method: 'GET',
    params,
    ...options
  })
}

export function post(url, data, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

export function put(url, data, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

export function del(url, params, options = {}) {
  return request({
    url,
    method: 'DELETE',
    params,
    ...options
  })
}

export { baseUrl, iSzgm }