@font-face {
	font-family: 'iconBack';
	src: url('~@/common/font/iconfont.ttf') format('truetype');
}

@font-face {
	font-family: 'iconHome';
	src: url('~@/common/font/iconfont.ttf') format('truetype');
}

@font-face {
	font-family: 'icongou';
	src: url('~@/common/font/gou.ttf') format('truetype');
}

@font-face {
	font-family: 'iconwarn';
	src: url('~@/common/font/iconwarn.ttf') format('truetype');
}

.icon_back {
	font-family: "iconBack" !important;
	font-size: 16upx;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon_home {
	font-family: "iconHome" !important;
	font-size: 16upx;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon_warn {
	font-family: "iconWarn" !important;
	font-size: 16upx;
}

.icon_back:before {
	content: "\e607";
}

.icon_home:before {
	content: "\e606";
}

.icon_warn:before {
	content: "\e605";
}
