/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.qrcode-container.data-v-14454ffc {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}
.qrcode-container .left-bottom-sign.data-v-14454ffc {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0 200rpx 0 0;
}
.qrcode-container .right-top-sign.data-v-14454ffc {
  position: absolute;
  right: 0;
  top: 0;
  width: 150rpx;
  height: 150rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0 0 0 150rpx;
}
.qrcode-container .back-btn.data-v-14454ffc {
  position: absolute;
  left: 30rpx;
  top: 100rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.qrcode-container .back-btn .back-icon.data-v-14454ffc {
  font-size: 40rpx;
  color: white;
  font-weight: bold;
}
.qrcode-container .wrapper.data-v-14454ffc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 60rpx 40rpx;
}
.qrcode-container .wrapper .left-top-sign.data-v-14454ffc {
  position: absolute;
  left: 40rpx;
  top: 200rpx;
  font-size: 48rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.3);
  letter-spacing: 4rpx;
}
.qrcode-container .wrapper .welcome.data-v-14454ffc {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 80rpx;
  text-align: center;
}
.qrcode-container .wrapper .qrcode-content.data-v-14454ffc {
  margin-bottom: 60rpx;
}
.qrcode-container .wrapper .qrcode-content .qrcode-wrapper.data-v-14454ffc {
  width: 480rpx;
  height: 480rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
.qrcode-container .wrapper .qrcode-content .qrcode-wrapper .qrcode-image.data-v-14454ffc {
  width: 400rpx;
  height: 400rpx;
}
.qrcode-container .wrapper .qrcode-content .qrcode-wrapper .qrcode-loading.data-v-14454ffc {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 400rpx;
  height: 400rpx;
}
.qrcode-container .wrapper .qrcode-content .qrcode-wrapper .qrcode-loading .loading-text.data-v-14454ffc {
  font-size: 32rpx;
  color: #999;
}
.qrcode-container .wrapper .refresh-btn.data-v-14454ffc {
  width: 400rpx;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 44rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 40rpx;
}
.qrcode-container .wrapper .refresh-btn.data-v-14454ffc::after {
  border: none;
}
.qrcode-container .wrapper .refresh-btn.data-v-14454ffc:disabled {
  background: rgba(255, 255, 255, 0.5);
  color: #999;
}
.qrcode-container .wrapper .tips.data-v-14454ffc {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}
.qrcode-container .wrapper .tips .tips-text.data-v-14454ffc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

/* 平台特定样式 */
.wrapper.data-v-14454ffc {
  padding-bottom: env(safe-area-inset-bottom);
}