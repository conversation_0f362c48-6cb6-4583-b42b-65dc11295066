"use strict";
const common_vendor = require("../../common/vendor.js");
const api_api = require("../../api/api.js");
const config = require("../../config.js");
const _sfc_main = {
  name: "ContactManager",
  data() {
    return {
      maxNum: 0,
      linkList: [],
      checkAllNum: 0,
      startX: "",
      startY: ""
    };
  },
  computed: {
    config() {
      return config.config;
    }
  },
  onShow() {
    this.getLinkList();
  },
  onLoad(options) {
    this.maxNum = options.num || 0;
  },
  methods: {
    // 获取联系人列表
    getLinkList() {
      api_api.myRequest({
        url: "/auth/linkman/list"
      }).then((res) => {
        if (res.data && res.data.rows) {
          this.linkList = res.data.rows.map((item) => ({
            ...item,
            isMove: false
          }));
        }
      }).catch((err) => {
        common_vendor.index.__f__("error", "at pages_app/contactmanager/index.vue:90", "获取联系人列表失败:", err);
        common_vendor.index.showToast({
          title: "获取联系人列表失败",
          icon: "error"
        });
      });
    },
    // 隐藏身份证号中间部分
    hideCardNum(index) {
      const certificate = this.linkList[index].linkmanCertificate;
      const length = certificate.length;
      if (length < 4)
        return certificate;
      if (length > 4 && length !== 18) {
        let result = certificate.substring(0, 2);
        for (let i = 0; i < length; i++) {
          if (i >= 2 && i < length - 2) {
            result += "*";
          }
        }
        result += certificate.substring(length - 2, length);
        return result;
      }
      return certificate.replace(certificate.substring(4, 15), "*******");
    },
    // 添加联系人
    addContact() {
      common_vendor.index.navigateTo({
        url: "/pages_app/contacts/addcontact"
      });
    },
    // 删除联系人
    deleteItem(id) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个联系人吗？",
        success: (res) => {
          if (res.confirm) {
            api_api.myRequest({
              url: `/auth/linkman/${id}`,
              method: "DELETE"
            }).then((res2) => {
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
              this.getLinkList();
            }).catch((err) => {
              common_vendor.index.__f__("error", "at pages_app/contactmanager/index.vue:143", "删除联系人失败:", err);
              common_vendor.index.showToast({
                title: "删除失败",
                icon: "error"
              });
            });
          }
        }
      });
    },
    // 编辑联系人
    editItem(id) {
      common_vendor.index.navigateTo({
        url: `/pages_app/contacts/addcontact?id=${id}`
      });
    },
    // 触摸开始
    touchstart(e) {
      this.linkList.forEach((item) => {
        if (item.isMove) {
          item.isMove = false;
        }
      });
      this.startX = e.changedTouches[0].clientX;
      this.startY = e.changedTouches[0].clientY;
    },
    // 触摸移动
    touchmove(e, index) {
      const startX = this.startX;
      const startY = this.startY;
      const touchX = e.changedTouches[0].clientX;
      const touchY = e.changedTouches[0].clientY;
      const angle = this.angle({ x: startX, y: startY }, { x: touchX, y: touchY });
      if (Math.abs(angle) <= 30 && startX - touchX >= 30) {
        this.linkList[index].isMove = true;
      }
    },
    // 计算角度
    angle(start, end) {
      const x = end.x - start.x;
      const y = end.y - start.y;
      return Math.atan(y / x) * 360 / Math.PI;
    }
  }
};
if (!Array) {
  const _easycom_my_header2 = common_vendor.resolveComponent("my-header");
  _easycom_my_header2();
}
const _easycom_my_header = () => "../../components/my-header/my-header.js";
if (!Math) {
  _easycom_my_header();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$options.config.iSzgm
  }, !$options.config.iSzgm ? {
    b: common_vendor.p({
      menuClass: "bor",
      isFixed: true,
      isBack: true,
      isShowHome: true,
      title: "联系人管理",
      color: "#000"
    })
  } : {}, {
    c: common_vendor.o((...args) => $options.addContact && $options.addContact(...args)),
    d: common_vendor.f($data.linkList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.linkmanName),
        b: common_vendor.t($options.hideCardNum(index)),
        c: common_vendor.t(item.linkmanPhone),
        d: common_vendor.t(item.linkmanAge),
        e: common_vendor.o(($event) => $options.editItem(item.id), item.id),
        f: common_vendor.o(($event) => $options.deleteItem(item.id), item.id),
        g: item.id,
        h: item.isMove ? 1 : "",
        i: common_vendor.o((...args) => $options.touchstart && $options.touchstart(...args), item.id),
        j: common_vendor.o(($event) => $options.touchmove($event, index), item.id),
        k: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-82585584"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages_app/contactmanager/index.js.map
