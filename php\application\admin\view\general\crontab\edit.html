<style type="text/css">
    #schedulepicker {
        padding-top: 7px;
    }
    #schedulepicker h5 {
        line-height: 30px;
    }
    #schedulepicker .list-group {
        margin-bottom: 0;
    }
</style>
<style data-render="darktheme">
    body.darktheme #schedulepicker pre {
        background-color: #4c4c4c;
        border-color: #262626;
        color: #ccc;
    }
</style>
<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="title" name="row[title]" value="{$row.title|htmlentities}" data-rule="required"/>
        </div>
    </div>
    <div class="form-group">
        <label for="type" class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[type]', $typeList, $row['type'], ['class'=>'form-control', 'data-rule'=>'required'])}
        </div>
    </div>
    <div class="form-group">
        <label for="c-content" class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea name="row[content]" id="c-content" cols="30" rows="5" class="form-control" data-rule="required">{$row.content|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label for="schedule" class="control-label col-xs-12 col-sm-2">{:__('Schedule')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group margin-bottom-sm">
                <input type="text" class="form-control" id="schedule" style="font-size:12px;font-family: Verdana;word-spacing:23px;" name="row[schedule]" value="{$row.schedule|htmlentities}" data-rule="required; remote(general/crontab/check_schedule)"/>
                <span class="input-group-btn">
                    <a href="https://www.fastadmin.net/store/crontab.html" target="_blank" class="btn btn-default"><i class="fa fa-info-circle"></i> {:__('Crontab rules')}</a>
                </span>
                <span class="msg-box n-right"></span>
            </div>

            <div id="schedulepicker">
                <pre><code>*    *    *    *    *
-    -    -    -    -
|    |    |    |    +--- day of week (0 - 7) (Sunday=0 or 7)
|    |    |    +-------- month (1 - 12)
|    |    +------------- day of month (1 - 31)
|    +------------------ hour (0 - 23)
+----------------------- min (0 - 59)</code></pre>
                <h5>{:__('The next %s times the execution time', '<input type="number" id="pickdays" class="form-control text-center" value="7" style="display: inline-block;width:80px;">')}</h5>
                <ol id="scheduleresult" class="list-group">
                </ol>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="maximums" class="control-label col-xs-12 col-sm-2">{:__('Maximums')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input type="number" class="form-control" id="maximums" name="row[maximums]" value="{$row.maximums}" data-rule="required" size="6"/>
        </div>
    </div>
    <div class="form-group">
        <label for="begintime" class="control-label col-xs-12 col-sm-2">{:__('Begin time')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input type="text" class="form-control datetimepicker" id="begintime" name="row[begintime]" value="{$row.begintime|datetime}" data-rule="{:__('Begin time')}:required" size="6"/>
        </div>
    </div>
    <div class="form-group">
        <label for="endtime" class="control-label col-xs-12 col-sm-2">{:__('End time')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input type="text" class="form-control datetimepicker" id="endtime" name="row[endtime]" value="{$row.endtime|datetime}" data-rule="{:__('End time')}:required;match(gte, row[begintime], datetime)" size="6"/>
        </div>
    </div>
    <div class="form-group">
        <label for="weigh" class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
        <div class="col-xs-12 col-sm-4">
            <input type="text" class="form-control" id="weigh" name="row[weigh]" value="{$row.weigh}" data-rule="required" size="6"/>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'completed'=>__('Completed'), 'expired'=>__('Expired'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group hide layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>

</form>
