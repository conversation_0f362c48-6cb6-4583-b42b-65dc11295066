#FROM php:7.4-fpm
FROM docker.m.daocloud.io/php:7.4-fpm


# 引入构建参数（从 docker-compose 传入）
ARG CONTAINER_PATH=/var/www/html

# 设置运行环境变量（也可被容器内部引用）
ENV CONTAINER_PATH=${CONTAINER_PATH}

ENV TZ=Asia/Shanghai

# 使用官方 Composer 镜像做拷贝
#COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 安装系统依赖和 PHP 扩展
RUN sed -i 's|http://deb.debian.org|http://mirrors.aliyun.com|g' /etc/apt/sources.list && \
    apt-get update && apt-get install -y --fix-missing \
    zip unzip \
    git \
    libzip-dev \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libbz2-dev \
    libxml2-dev \
    libonig-dev \
    libcurl4-openssl-dev \
    libssl-dev \
    pkg-config \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install \
        bcmath \
        curl \
        gd \
        zip \
        bz2 \
        xml \
        mbstring \
        json \
        pdo_mysql \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置 PHP-FPM 监听所有地址，便于宿主机访问
RUN sed -i 's|listen = .*|listen = 0.0.0.0:9000|' /usr/local/etc/php-fpm.d/zz-docker.conf


# 设置工作目录（保持一致）
WORKDIR ${CONTAINER_PATH}

EXPOSE 9000

# 默认启动 php-fpm
CMD ["php-fpm"]
