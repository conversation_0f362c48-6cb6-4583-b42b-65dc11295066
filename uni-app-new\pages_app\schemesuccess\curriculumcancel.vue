<template>
  <view class="curriculum-cancel">
    <!-- 自定义头部 -->
    <my-header 
      title="取消预约" 
      :isBack="true" 
      :isShowHome="true"
      background="#ffffff"
      color="#333333"
    />
    
    <view class="content">
      <!-- 课程信息 -->
      <view class="course-info">
        <view class="info-header">
          <text class="venue-name">宝安科技馆 e宝课堂</text>
        </view>
        
        <view class="course-details">
          <view class="course-poster">
            <image 
              :src="courseInfo.courseCover" 
              mode="aspectFill"
              class="poster-image"
            />
          </view>
          <view class="course-meta">
            <text class="course-name">{{ courseInfo.courseName }}</text>
            <text class="course-teacher" v-if="courseInfo.courseTeacher">
              授课老师：{{ courseInfo.courseTeacher }}
            </text>
            <text class="course-location" v-if="courseInfo.courseLocation">
              上课地点：{{ courseInfo.courseLocation }}
            </text>
            <text class="course-time">
              上课时间：{{ courseInfo.courseStartTime }} - {{ courseInfo.courseEndTime }}
            </text>
            <text class="course-date">上课日期：{{ courseInfo.date }}</text>
          </view>
        </view>
      </view>
      
      <!-- 联系人选择 -->
      <view class="contacts-section">
        <view class="section-title">选择要取消的联系人</view>
        <view class="contacts-list">
          <view 
            v-for="(contact, index) in contactsList" 
            :key="contact.linkId"
            class="contact-item"
            @tap="toggleContact(index)"
          >
            <view class="contact-info">
              <text class="contact-name">{{ contact.linkmanName }}</text>
              <text class="contact-phone">{{ contact.linkmanPhone }}</text>
              <text class="contact-id">{{ hideIdCard(contact.linkmanCertificate) }}</text>
            </view>
            <view :class="['checkbox', { checked: contact.linkCheck }]">
              <text v-if="contact.linkCheck" class="check-icon">✓</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 取消按钮 -->
      <view class="action-section">
        <button 
          class="cancel-btn"
          @tap="confirmCancel"
          :disabled="!hasSelectedContacts || isCancelling"
        >
          {{ isCancelling ? '取消中...' : '确认取消预约' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CurriculumCancel',
  data() {
    return {
      courseInfo: {},
      contactsList: [],
      weekList: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
      
      // 参数
      sessionId: null,
      batchNumber: null,
      
      // 状态
      isCancelling: false
    }
  },
  
  computed: {
    // 是否有选中的联系人
    hasSelectedContacts() {
      return this.contactsList.some(contact => contact.linkCheck)
    }
  },
  
  onLoad(options) {
    this.sessionId = options.sessionId
    this.batchNumber = options.batchNumber
    
    this.getCourseInfo()
    this.getContactsList()
  },
  
  methods: {
    // 获取课程信息
    async getCourseInfo() {
      try {
        const res = await this.$myRequest({
          url: '/web/session/showVoucher',
          method: 'get',
          data: {
            courseId: this.sessionId
          }
        })
        
        if (res.code === 200) {
          const data = res.data.data
          const startTime = data.courseStartTime
          const date = startTime.slice(0, 10).replace(/-/g, '.') + '  ' + 
                      this.weekList[new Date(startTime).getDay()]
          
          this.courseInfo = {
            ...data,
            date,
            courseStartTime: this.formatTime(data.courseStartTime),
            courseEndTime: this.formatTime(data.courseEndTime)
          }
        }
      } catch (error) {
        console.error('获取课程信息失败:', error)
        uni.showToast({
          title: '获取信息失败',
          icon: 'error'
        })
      }
    },
    
    // 获取联系人列表
    async getContactsList() {
      try {
        const res = await this.$myRequest({
          url: '/web/session/getCourseSubscribePeoples',
          method: 'get',
          data: {
            batchNumber: this.batchNumber
          }
        })
        
        if (res.code === 200) {
          this.contactsList = (res.data.data || []).map(contact => ({
            ...contact,
            linkCheck: false
          }))
        }
      } catch (error) {
        console.error('获取联系人失败:', error)
        uni.showToast({
          title: '获取联系人失败',
          icon: 'error'
        })
      }
    },
    
    // 切换联系人选择状态
    toggleContact(index) {
      this.contactsList[index].linkCheck = !this.contactsList[index].linkCheck
    },
    
    // 确认取消预约
    confirmCancel() {
      if (!this.hasSelectedContacts) {
        uni.showToast({
          title: '请选择要取消的联系人',
          icon: 'error'
        })
        return
      }
      
      uni.showModal({
        title: '确认取消',
        content: '确定要取消选中联系人的预约吗？',
        success: (res) => {
          if (res.confirm) {
            this.performCancel()
          }
        }
      })
    },
    
    // 执行取消操作
    async performCancel() {
      if (this.isCancelling) return
      
      this.isCancelling = true
      
      try {
        const selectedIds = this.contactsList
          .filter(contact => contact.linkCheck)
          .map(contact => contact.linkId)
        
        const res = await this.$myRequest({
          url: '/web/session/cancelCourseSession',
          method: 'get',
          data: {
            sessionId: this.sessionId,
            batchNumber: this.batchNumber,
            peopleIds: selectedIds.join(',')
          }
        })
        
        if (res.code === 200) {
          uni.showToast({
            title: '取消预约成功',
            icon: 'success',
            duration: 2000
          })
          
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages_app/user/curriculumscheme'
            })
          }, 2000)
        } else {
          throw new Error(res.msg || '取消失败')
        }
      } catch (error) {
        console.error('取消预约失败:', error)
        uni.showToast({
          title: error.message || '取消失败',
          icon: 'error'
        })
      } finally {
        this.isCancelling = false
      }
    },
    
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''
      
      try {
        const date = new Date(timeStr.replace(/-/g, '/'))
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      } catch (error) {
        return timeStr
      }
    },
    
    // 隐藏身份证号中间部分
    hideIdCard(idCard) {
      if (!idCard || idCard.length < 8) return idCard
      
      return idCard.replace(idCard.substring(4, 15), '*******')
    }
  }
}
</script>

<style lang="scss" scoped>
.curriculum-cancel {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .content {
    padding: 20rpx;
    padding-top: 120rpx; // 为头部留出空间

    // 课程信息
    .course-info {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .info-header {
        margin-bottom: 20rpx;

        .venue-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .course-details {
        display: flex;

        .course-poster {
          width: 120rpx;
          height: 160rpx;
          margin-right: 20rpx;
          border-radius: 12rpx;
          overflow: hidden;

          .poster-image {
            width: 100%;
            height: 100%;
          }
        }

        .course-meta {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .course-name {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 8rpx;
            line-height: 1.4;
          }

          .course-teacher,
          .course-location,
          .course-time,
          .course-date {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 6rpx;
            line-height: 1.3;
          }
        }
      }
    }

    // 联系人选择
    .contacts-section {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20rpx;
      padding: 30rpx;
      margin-bottom: 30rpx;
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 30rpx;
      }

      .contacts-list {
        .contact-item {
          display: flex;
          align-items: center;
          padding: 20rpx 0;
          border-bottom: 1rpx solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .contact-info {
            flex: 1;

            .contact-name {
              display: block;
              font-size: 30rpx;
              font-weight: 500;
              color: #333;
              margin-bottom: 8rpx;
            }

            .contact-phone {
              display: block;
              font-size: 26rpx;
              color: #666;
              margin-bottom: 6rpx;
            }

            .contact-id {
              display: block;
              font-size: 24rpx;
              color: #999;
            }
          }

          .checkbox {
            width: 48rpx;
            height: 48rpx;
            border: 2rpx solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &.checked {
              background: linear-gradient(135deg, #667eea, #764ba2);
              border-color: #667eea;

              .check-icon {
                color: white;
                font-size: 28rpx;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    // 操作区域
    .action-section {
      .cancel-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        border-radius: 44rpx;
        font-size: 32rpx;
        font-weight: 500;
        border: none;

        &::after {
          border: none;
        }

        &:disabled {
          background: #ccc;
          color: #999;
        }
      }
    }
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.curriculum-cancel {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */
</style>
