<template>
  <view class="login-container">
    <!-- 自定义头部 -->
    <my-header 
      title="用户登录" 
      :isBack="true" 
      background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      color="#ffffff"
    />
    
    <view class="login-content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit" />
        <text class="app-name">宝安科技馆</text>
        <text class="subtitle">用户登录</text>
      </view>
      
      <!-- 登录表单 -->
      <view class="form-section">
        <!-- 账号输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon-user"></text>
            </view>
            <input
              class="form-input"
              placeholder="请输入账号"
              type="text"
              v-model="loginForm.username"
              maxlength="30"
              @blur="validateUsername"
            />
          </view>
          <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
        </view>
        
        <!-- 密码输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="icon-password"></text>
            </view>
            <input
              class="form-input"
              placeholder="请输入密码"
              type="password"
              v-model="loginForm.password"
              maxlength="20"
              @blur="validatePassword"
            />
          </view>
          <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
        </view>

        <!-- 验证码输入 -->
        <view class="input-group" v-if="captchaEnabled">
          <view class="input-wrapper captcha-wrapper">
            <view class="input-icon">
              <text class="icon-code"></text>
            </view>
            <input
              class="form-input captcha-input"
              placeholder="请输入验证码"
              type="number"
              v-model="loginForm.code"
              maxlength="4"
              @blur="validateCode"
            />
            <view class="captcha-image">
              <image
                class="captcha-img"
                :src="codeUrl"
                mode="aspectFit"
                v-if="codeUrl"
                @tap="getCode"
              />
              <text v-else class="refresh-text" @tap="getCode">点击刷新</text>
            </view>
          </view>
          <text v-if="errors.code" class="error-text">{{ errors.code }}</text>
        </view>

        <!-- 登录按钮 -->
        <view class="button-group">
          <button
            class="login-btn"
            @tap="handleLogin"
            :disabled="isLoading"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </button>
        </view>

        <!-- 注册链接 -->
        <view class="register-link">
          <text class="link-text">没有账号？</text>
          <text class="link-button" @tap="handleUserRegister">立即注册</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { login, getCodeImg } from '@/api/login.js'
import { encrypt } from '@/utils/jsencrypt.js'
import config from '@/config.js'
import MyHeader from '@/components/my-header/my-header.vue'

export default {
  name: 'Login',
  components: {
    MyHeader
  },
  data() {
    return {
      // 表单数据
      loginForm: {
        username: '',
        password: '',
        code: '',
        uuid: ''
      },

      // 验证码相关
      codeUrl: '',
      captchaEnabled: true,

      // 控制状态
      register: true,
      isLoading: false,
      showWechatLogin: true,

      // 表单验证错误
      errors: {
        username: '',
        password: '',
        code: ''
      }
    }
  },

  created() {
    this.getCode()
  },

  methods: {
    // 获取验证码
    getCode() {
      getCodeImg().then(res => {
        if (res.code === 200) {
          this.captchaEnabled = res.captchaEnabled !== false
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        }
      }).catch(err => {
        console.error('获取验证码失败:', err)
      })
    },

    // 表单验证
    validateUsername() {
      if (!this.loginForm.username.trim()) {
        this.errors.username = '请输入账号'
        return false
      }
      this.errors.username = ''
      return true
    },

    validatePassword() {
      if (!this.loginForm.password.trim()) {
        this.errors.password = '请输入密码'
        return false
      }
      this.errors.password = ''
      return true
    },

    validateCode() {
      if (this.captchaEnabled && !this.loginForm.code.trim()) {
        this.errors.code = '请输入验证码'
        return false
      }
      this.errors.code = ''
      return true
    },

    // 表单整体验证
    validateForm() {
      const usernameValid = this.validateUsername()
      const passwordValid = this.validatePassword()
      const codeValid = this.validateCode()

      return usernameValid && passwordValid && codeValid
    },

    // 处理登录
    async handleLogin() {
      if (!this.validateForm()) {
        return
      }

      this.isLoading = true

      try {
        uni.showLoading({
          title: '正在登录中'
        })

        await this.pwdLogin()

      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: '登录失败',
          icon: 'error'
        })

        // 刷新验证码
        if (this.captchaEnabled) {
          this.getCode()
        }
      } finally {
        this.isLoading = false
        uni.hideLoading()
      }
    },

    // 密码登录
    async pwdLogin() {
      const encryptedPassword = encrypt(this.loginForm.password)

      const res = await login(
        this.loginForm.username,
        encryptedPassword,
        this.loginForm.code,
        this.loginForm.uuid
      )

      if (res.code === 200) {
        uni.showToast({
          title: '登录成功'
        })

        // 保存token
        uni.setStorageSync('token', res.token)

        // 获取用户信息
        try {
          const userInfo = await this.getUserInfo()
          uni.setStorageSync('userInfo', userInfo)

          // 跳转到首页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 即使获取用户信息失败，也跳转到首页
          uni.reLaunch({
            url: '/pages/index/index'
          })
        }
      } else {
        throw new Error(res.msg || '登录失败')
      }
    },

    // 获取用户信息
    async getUserInfo() {
      // 这里应该调用获取用户信息的API
      // 暂时返回默认用户信息
      return {
        avatar: null,
        nickName: null,
        sex: null,
        phonenumber: null
      }
    },

    // 微信登录
    wechatLogin(e) {
      if (!e.detail.userInfo) {
        uni.showToast({
          title: '您拒绝了授权',
          icon: 'error'
        })
        return
      }

      this.isLoading = true

      uni.login({
        provider: 'weixin',
        success: (res) => {
          const params = {
            data: {
              code: res.code,
              appid: config.appId
            },
            url: '/api/user/wxlogin',
            method: 'get'
          }

          this.$myRequest(params).then(result => {
            uni.showToast({
              title: '登录成功'
            })

            const user = result.data.data.user
            uni.setStorageSync('token', result.data.data.token)
            uni.setStorageSync('userInfo', user)

            // 跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }).catch(err => {
            console.error('微信登录失败:', err)
            uni.showToast({
              title: '微信登录失败',
              icon: 'error'
            })
          }).finally(() => {
            this.isLoading = false
          })
        },
        fail: () => {
          this.isLoading = false
          uni.showToast({
            title: '微信登录失败',
            icon: 'error'
          })
        }
      })
    },

    // 跳转注册页面
    handleUserRegister() {
      uni.navigateTo({
        url: '/pages_app/register/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}
.login-content {
  padding: 40rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
  margin-top: 80rpx;
  padding-top: 60rpx;

  .logo {
    width: 140rpx;
    height: 140rpx;
    border-radius: 30rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  }

  .app-name {
    display: block;
    font-size: 52rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 20rpx;
    text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.3);
  }

  .subtitle {
    display: block;
    font-size: 30rpx;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
  }
}

.form-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 40rpx 40rpx 0 0;
  padding: 80rpx 50rpx 60rpx;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  margin-top: auto;
}

.input-group {
  margin-bottom: 50rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
  border: 2rpx solid #e8ecf4;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

  &:focus-within {
    border-color: #667eea;
    box-shadow: 0 4rpx 25rpx rgba(102, 126, 234, 0.2);
    transform: translateY(-2rpx);
  }

  &.captcha-wrapper {
    padding-right: 20rpx;
  }
}

.input-icon {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 36rpx;
}

.form-input {
  flex: 1;
  height: 100rpx;
  padding: 0 20rpx;
  font-size: 32rpx;
  color: #333333;
  background: transparent;
  border: none;
  font-weight: 500;
}

.form-input::placeholder {
  color: #a8b2c8;
  font-weight: 400;
}

.captcha-image {
  width: 140rpx;
  height: 80rpx;
  margin-left: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e8ecf4;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #667eea;
  }

  .captcha-img {
    width: 100%;
    height: 100%;
  }

  .refresh-text {
    font-size: 22rpx;
    color: #667eea;
    font-weight: 500;
  }
}

.error-text {
  display: block;
  color: #ff4757;
  font-size: 26rpx;
  margin-top: 15rpx;
  margin-left: 100rpx;
  font-weight: 500;
}

.button-group {
  margin: 80rpx 0 50rpx;
}

.login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 20rpx;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);

  &:not([disabled]):active {
    transform: translateY(2rpx);
    box-shadow: 0 12rpx 35rpx rgba(102, 126, 234, 0.4);
  }

  &[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.register-link {
  text-align: center;
  margin-bottom: 40rpx;

  .link-text {
    color: #8892b0;
    font-size: 30rpx;
    font-weight: 400;
  }

  .link-button {
    color: #667eea;
    font-size: 30rpx;
    font-weight: bold;
    margin-left: 10rpx;
    text-decoration: underline;
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .login-content {
    padding: 30rpx;
  }

  .logo-section {
    margin-top: 40rpx;
    padding-top: 40rpx;
  }

  .form-section {
    padding: 60rpx 40rpx 50rpx;
  }

  .logo {
    width: 120rpx;
    height: 120rpx;
  }

  .app-name {
    font-size: 46rpx;
  }
}

/* 平台特定样式 */
/* #ifdef H5 */
.login-container {
  background-attachment: fixed;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.login-content {
  padding-bottom: env(safe-area-inset-bottom);
}
/* #endif */

/* 图标字体优化 */
.icon-user::before {
  content: '👤';
  font-size: 36rpx;
}

.icon-password::before {
  content: '🔒';
  font-size: 36rpx;
}

.icon-code::before {
  content: '🔢';
  font-size: 36rpx;
}
</style>