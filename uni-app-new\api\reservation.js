import { request } from '../utils/request.js'

// 地址位置相关API
export function checkLocation() {
  return request({
    url: '/apitp/movie/check-location',
    method: 'GET'
  })
}

// 电影预约相关API
export function getMovieSchedule(params) {
  return request({
    url: '/apitp/movie/schedule',
    method: 'GET',
    params
  })
}

// 根据场次ID获取电影信息
export function getFilmBySessionId(sessionId) {
  return request({
    url: `/apitp/movie/session/${sessionId}`,
    method: 'GET'
  })
}

// 提交电影预约订单
export function submitFilmOrder(orderData) {
  return request({
    url: '/apitp/movie/order',
    method: 'POST',
    data: orderData
  })
}

export function createMovieReservation(reservationData) {
  return request({
    url: '/apitp/movie/reservation',
    method: 'POST',
    data: reservationData
  })
}

// 场馆预约相关API
export function getVenueInfo() {
  return request({
    url: '/apitp/venue/info',
    method: 'GET'
  })
}

export function getVenueSchedule(date) {
  return request({
    url: '/apitp/venue/schedule',
    method: 'GET',
    params: { date }
  })
}

export function createVenueReservation(reservationData) {
  return request({
    url: '/apitp/venue/reservation',
    method: 'POST',
    data: reservationData
  })
}

// 课程预约相关API
export function getCourseSchedule(params) {
  return request({
    url: '/apitp/course/schedule',
    method: 'GET',
    params
  })
}

export function createCourseReservation(reservationData) {
  return request({
    url: '/apitp/course/reservation',
    method: 'POST',
    data: reservationData
  })
}

// 通用预约管理API
export function getMyReservations(type, status) {
  return request({
    url: '/apitp/reservation/my',
    method: 'GET',
    params: { type, status }
  })
}

export function getReservationDetail(reservationId) {
  return request({
    url: `/apitp/reservation/detail/${reservationId}`,
    method: 'GET'
  })
}

export function cancelMyReservation(reservationId, reason) {
  return request({
    url: `/apitp/reservation/cancel/${reservationId}`,
    method: 'PUT',
    data: { reason }
  })
}

export function getReservationQRCode(reservationId) {
  return request({
    url: `/apitp/reservation/qrcode/${reservationId}`,
    method: 'GET'
  })
}

// 预约统计API
export function getReservationStats() {
  return request({
    url: '/apitp/reservation/stats',
    method: 'GET'
  })
}