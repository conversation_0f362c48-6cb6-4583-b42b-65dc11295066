<?php

declare(strict_types=1);

/*
 * This file is part of the nelexa/zip package.
 * (c) Ne-Lexa <https://github.com/Ne-Lexa/php-zip>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace PhpZip\Model\Extra;

use Php<PERSON><PERSON>\Model\ZipEntry;

/**
 * Extra Field in a Local or Central Header of a ZIP archive.
 * It defines the common properties of all Extra Fields and how to
 * serialize/unserialize them to/from byte arrays.
 */
interface ZipExtraField
{
    /**
     * Returns the Header ID (type) of this Extra Field.
     * The Header ID is an unsigned short integer (two bytes)
     * which must be constant during the life cycle of this object.
     */
    public function getHeaderId(): int;

    /**
     * Populate data from this array as if it was in local file data.
     *
     * @param string        $buffer the buffer to read data from
     * @param ZipEntry|null $entry  optional zip entry
     *
     * @return static
     */
    public static function unpackLocalFileData(string $buffer, ?ZipEntry $entry = null): self;

    /**
     * Populate data from this array as if it was in central directory data.
     *
     * @param string        $buffer the buffer to read data from
     * @param ZipEntry|null $entry  optional zip entry
     *
     * @return static
     */
    public static function unpackCentralDirData(string $buffer, ?ZipEntry $entry = null): self;

    /**
     * The actual data to put into local file data - without Header-ID
     * or length specifier.
     *
     * @return string the data
     */
    public function packLocalFileData(): string;

    /**
     * The actual data to put into central directory - without Header-ID or
     * length specifier.
     *
     * @return string the data
     */
    public function packCentralDirData(): string;

    public function __toString(): string;
}
