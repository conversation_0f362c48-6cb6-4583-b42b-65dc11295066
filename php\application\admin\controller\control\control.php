<?php

namespace app\admin\controller\control;

use app\common\controller\Backend;

class Control extends Backend
{
    public function index()
    {
        $dbTableList = Db::query("SHOW TABLE STATUS");
        'dbtablenums'       => count($dbTableList),
        'dbsize'            => array_sum(array_map(function ($item) {
            return $item['Data_length'] + $item['Index_length'];
        }, $dbTableList)),

        return $this->view->fetch();
    }
}
