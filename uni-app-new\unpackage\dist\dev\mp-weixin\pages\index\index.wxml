<view class="content data-v-1cf27b2a"><my-header wx:if="{{a}}" class="data-v-1cf27b2a" u-i="1cf27b2a-0" bind:__l="__l" u-p="{{b}}"></my-header><view class="title data-v-1cf27b2a">线上智能服务平台</view><view class="btn_list data-v-1cf27b2a"><button wx:for="{{c}}" wx:for-item="item" wx:key="c" class="data-v-1cf27b2a" type="default" style="{{'background:' + item.d + ';' + ('background-size:' + '100% 100%')}}" bindtap="{{item.e}}"><view class="nav_name data-v-1cf27b2a">{{item.a}}</view><view class="icon data-v-1cf27b2a"><image class="set_icon data-v-1cf27b2a" mode="aspectFill" src="{{item.b}}"></image></view></button></view><uni-notice-bar wx:if="{{d}}" class="scrollText data-v-1cf27b2a" u-i="1cf27b2a-1" bind:__l="__l" u-p="{{e}}"></uni-notice-bar><privacy-popup wx:if="{{f}}" class="data-v-1cf27b2a" id="privacy-popup" bindagree="{{g}}" bindreject="{{h}}" u-i="1cf27b2a-2" bind:__l="__l" u-p="{{i}}"></privacy-popup></view>