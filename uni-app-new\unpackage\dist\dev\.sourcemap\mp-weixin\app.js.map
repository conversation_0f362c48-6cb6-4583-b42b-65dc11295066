{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\nimport store from './store'\r\nimport './permission.js'\r\n\r\nexport default {\r\n  globalData: {\r\n    // 基础配置\r\n    baseUrl: 'https://bakjgyyxt.baoan.gov.cn',\r\n    iSzgm: false,\r\n    clientId: 'be7052a7e4f802c20df10a8d131adb12',\r\n    publicKey: 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==',\r\n    privateKey: '**********',\r\n    \r\n    // 应用信息\r\n    appInfo: {\r\n      name: 'baoanquestacon-app',\r\n      version: '2.0.0',\r\n      logo: '/static/favicon.ico'\r\n    },\r\n    \r\n    // 微信小程序配置\r\n    appId: 'wx7fdbf0566b7e1707',\r\n    productionTip: false,\r\n    \r\n    // 宝安位置信息\r\n    baoanLocation: {\r\n      la: 22.55866135902317,\r\n      lo: 113.91141057014467\r\n    },\r\n    \r\n    // 系统信息\r\n    systemInfo: null,\r\n    statusBarHeight: 0,\r\n    customBarHeight: 0\r\n  },\r\n  \r\n  onLaunch: function(options) {\r\n    console.log('App Launch', options)\r\n    \r\n    // 获取系统信息\r\n    this.getSystemInfo()\r\n    \r\n    // 加载自定义字体\r\n    //this.loadCustomFonts()\r\n    \r\n    // 添加内存警告监听\r\n    this.addMemoryWarningListener()\r\n    \r\n    // 初始化应用\r\n    this.initApp()\r\n    \r\n    // 处理启动参数\r\n    if (options && options.query) {\r\n      console.log('启动参数:', options.query)\r\n      // 可以在这里处理分享链接等启动参数\r\n    }\r\n  },\r\n  \r\n  onShow: function(options) {\r\n    console.log('App Show', options)\r\n    \r\n    // 应用从后台进入前台时的处理\r\n    this.handleAppShow(options)\r\n  },\r\n  \r\n  onHide: function() {\r\n    console.log('App Hide')\r\n    \r\n    // 应用从前台进入后台时的处理\r\n    this.handleAppHide()\r\n  },\r\n  \r\n  onError: function(error) {\r\n    console.error('App Error:', error)\r\n    \r\n    // 全局错误处理\r\n    this.handleGlobalError(error)\r\n  },\r\n  \r\n  methods: {\r\n    // 获取系统信息\r\n    getSystemInfo() {\r\n      try {\r\n        const systemInfo = uni.getSystemInfoSync()\r\n        this.globalData.systemInfo = systemInfo\r\n        this.globalData.statusBarHeight = systemInfo.statusBarHeight || 0\r\n        \r\n        // 计算自定义导航栏高度\r\n        // #ifdef MP-WEIXIN\r\n        const menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n        this.globalData.customBarHeight = menuButtonInfo.bottom + menuButtonInfo.top - systemInfo.statusBarHeight\r\n        // #endif\r\n        \r\n        // #ifndef MP-WEIXIN\r\n        this.globalData.customBarHeight = systemInfo.statusBarHeight + 44\r\n        // #endif\r\n        \r\n        console.log('系统信息获取成功:', systemInfo)\r\n      } catch (error) {\r\n        console.error('获取系统信息失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 加载自定义字体\r\n    loadCustomFonts() {\r\n      uni.loadFontFace({\r\n        global: true,\r\n        family: 'PingFang SC',\r\n        source: 'url(\"https://wesalt-ai-digial-dev.oss-cn-shenzhen.aliyuncs.com/font/PINGFANG%20REGULAR.TTF\")',\r\n        success: () => {\r\n          console.log('PingFang SC 字体加载成功')\r\n        },\r\n        fail: (err) => {\r\n          console.error('PingFang SC 字体加载失败:', err)\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 添加内存警告监听\r\n    addMemoryWarningListener() {\r\n      uni.onMemoryWarning(() => {\r\n        console.log('内存不足，进行清理')\r\n        this.clearCache()\r\n      })\r\n    },\r\n    \r\n    // 初始化应用\r\n    initApp() {\r\n      console.log('应用初始化开始')\r\n      \r\n      // 检查更新（仅小程序）\r\n      // #ifdef MP-WEIXIN\r\n      this.checkForUpdate()\r\n      // #endif\r\n      \r\n      // 初始化全局数据\r\n      this.initGlobalData()\r\n      \r\n      // 初始化插件\r\n      this.initPlugins()\r\n      \r\n      console.log('应用初始化完成')\r\n    },\r\n    \r\n    // 检查小程序更新\r\n    // #ifdef MP-WEIXIN\r\n    checkForUpdate() {\r\n      const updateManager = uni.getUpdateManager()\r\n      \r\n      updateManager.onCheckForUpdate((res) => {\r\n        console.log('检查更新结果:', res.hasUpdate)\r\n      })\r\n      \r\n      updateManager.onUpdateReady(() => {\r\n        uni.showModal({\r\n          title: '更新提示',\r\n          content: '新版本已经准备好，是否重启应用？',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              updateManager.applyUpdate()\r\n            }\r\n          }\r\n        })\r\n      })\r\n      \r\n      updateManager.onUpdateFailed(() => {\r\n        console.error('新版本下载失败')\r\n      })\r\n    },\r\n    // #endif\r\n    \r\n    // 初始化全局数据\r\n    initGlobalData() {\r\n      // 可以在这里初始化一些全局状态\r\n      console.log('全局数据初始化完成')\r\n    },\r\n    \r\n    // 初始化插件\r\n    initPlugins() {\r\n      // 初始化各种插件和工具\r\n      console.log('插件初始化完成')\r\n    },\r\n    \r\n    // 处理应用显示\r\n    handleAppShow(options) {\r\n      // 应用从后台回到前台时的处理逻辑\r\n      if (options && options.scene) {\r\n        console.log('应用场景值:', options.scene)\r\n      }\r\n    },\r\n    \r\n    // 处理应用隐藏\r\n    handleAppHide() {\r\n      // 应用进入后台时的处理逻辑\r\n      // 可以在这里保存一些状态数据\r\n    },\r\n    \r\n    // 全局错误处理\r\n    handleGlobalError(error) {\r\n      // 统一的错误处理逻辑\r\n      console.error('全局错误:', error)\r\n      \r\n      // 可以在这里上报错误信息\r\n      // 或者显示用户友好的错误提示\r\n    },\r\n    \r\n    // 清理缓存数据\r\n    clearCache() {\r\n      try {\r\n        // 清理一些非必要的缓存数据\r\n        console.log('开始清理缓存')\r\n        \r\n        // 清理图片缓存等\r\n        // uni.clearStorage() // 谨慎使用，会清理所有本地数据\r\n        \r\n        console.log('缓存清理完成')\r\n      } catch (error) {\r\n        console.error('缓存清理失败:', error)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 导入字体图标 */\r\n@import './static/config/iconfont.css';\r\n\r\n/* 全局样式重置 */\r\n* {\r\n  box-sizing: border-box;\r\n}\r\n\r\n/* 全局页面样式 */\r\npage {\r\n  background-color: #f1f1f1;\r\n  color: #333;\r\n  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, sans-serif;\r\n  font-size: 28rpx;\r\n  line-height: 1.6;\r\n}\r\n\r\n/* 全局输入框样式 */\r\n.uni-input {\r\n  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, sans-serif !important;\r\n}\r\n\r\n/* 通用样式类 */\r\n.round {\r\n  border-radius: 5000rpx;\r\n}\r\n\r\n.radius {\r\n  border-radius: 6rpx;\r\n}\r\n\r\n.radius-lg {\r\n  border-radius: 12rpx;\r\n}\r\n\r\n/* Flex布局 */\r\n.flex {\r\n  display: flex;\r\n}\r\n\r\n.flex-column {\r\n  flex-direction: column;\r\n}\r\n\r\n.flex-wrap {\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.align-start {\r\n  align-items: flex-start;\r\n}\r\n\r\n.align-center {\r\n  align-items: center;\r\n}\r\n\r\n.align-end {\r\n  align-items: flex-end;\r\n}\r\n\r\n.justify-start {\r\n  justify-content: flex-start;\r\n}\r\n\r\n.justify-center {\r\n  justify-content: center;\r\n}\r\n\r\n.justify-end {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.justify-between {\r\n  justify-content: space-between;\r\n}\r\n\r\n.justify-around {\r\n  justify-content: space-around;\r\n}\r\n\r\n.flex-1 {\r\n  flex: 1;\r\n}\r\n\r\n/* 文本样式 */\r\n.text-center {\r\n  text-align: center;\r\n}\r\n\r\n.text-left {\r\n  text-align: left;\r\n}\r\n\r\n.text-right {\r\n  text-align: right;\r\n}\r\n\r\n.text-bold {\r\n  font-weight: bold;\r\n}\r\n\r\n.text-normal {\r\n  font-weight: normal;\r\n}\r\n\r\n/* 文本颜色 */\r\n.text-primary {\r\n  color: #007aff;\r\n}\r\n\r\n.text-success {\r\n  color: #4cd964;\r\n}\r\n\r\n.text-warning {\r\n  color: #f0ad4e;\r\n}\r\n\r\n.text-danger {\r\n  color: #dd524d;\r\n}\r\n\r\n.text-info {\r\n  color: #5bc0de;\r\n}\r\n\r\n.text-white {\r\n  color: #ffffff;\r\n}\r\n\r\n.text-black {\r\n  color: #000000;\r\n}\r\n\r\n.text-gray {\r\n  color: #999999;\r\n}\r\n\r\n.text-gray-light {\r\n  color: #cccccc;\r\n}\r\n\r\n.text-gray-dark {\r\n  color: #666666;\r\n}\r\n\r\n/* 文本大小 */\r\n.text-xs {\r\n  font-size: 20rpx;\r\n}\r\n\r\n.text-sm {\r\n  font-size: 24rpx;\r\n}\r\n\r\n.text-base {\r\n  font-size: 28rpx;\r\n}\r\n\r\n.text-lg {\r\n  font-size: 32rpx;\r\n}\r\n\r\n.text-xl {\r\n  font-size: 36rpx;\r\n}\r\n\r\n.text-xxl {\r\n  font-size: 40rpx;\r\n}\r\n\r\n/* 背景颜色 */\r\n.bg-primary {\r\n  background-color: #007aff;\r\n}\r\n\r\n.bg-success {\r\n  background-color: #4cd964;\r\n}\r\n\r\n.bg-warning {\r\n  background-color: #f0ad4e;\r\n}\r\n\r\n.bg-danger {\r\n  background-color: #dd524d;\r\n}\r\n\r\n.bg-info {\r\n  background-color: #5bc0de;\r\n}\r\n\r\n.bg-white {\r\n  background-color: #ffffff;\r\n}\r\n\r\n.bg-gray {\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n.bg-gray-light {\r\n  background-color: #fafafa;\r\n}\r\n\r\n/* 边距样式 */\r\n.m-0 { margin: 0; }\r\n.m-1 { margin: 10rpx; }\r\n.m-2 { margin: 20rpx; }\r\n.m-3 { margin: 30rpx; }\r\n.m-4 { margin: 40rpx; }\r\n\r\n.mt-0 { margin-top: 0; }\r\n.mt-1 { margin-top: 10rpx; }\r\n.mt-2 { margin-top: 20rpx; }\r\n.mt-3 { margin-top: 30rpx; }\r\n.mt-4 { margin-top: 40rpx; }\r\n\r\n.mb-0 { margin-bottom: 0; }\r\n.mb-1 { margin-bottom: 10rpx; }\r\n.mb-2 { margin-bottom: 20rpx; }\r\n.mb-3 { margin-bottom: 30rpx; }\r\n.mb-4 { margin-bottom: 40rpx; }\r\n\r\n.ml-0 { margin-left: 0; }\r\n.ml-1 { margin-left: 10rpx; }\r\n.ml-2 { margin-left: 20rpx; }\r\n.ml-3 { margin-left: 30rpx; }\r\n.ml-4 { margin-left: 40rpx; }\r\n\r\n.mr-0 { margin-right: 0; }\r\n.mr-1 { margin-right: 10rpx; }\r\n.mr-2 { margin-right: 20rpx; }\r\n.mr-3 { margin-right: 30rpx; }\r\n.mr-4 { margin-right: 40rpx; }\r\n\r\n.p-0 { padding: 0; }\r\n.p-1 { padding: 10rpx; }\r\n.p-2 { padding: 20rpx; }\r\n.p-3 { padding: 30rpx; }\r\n.p-4 { padding: 40rpx; }\r\n\r\n.pt-0 { padding-top: 0; }\r\n.pt-1 { padding-top: 10rpx; }\r\n.pt-2 { padding-top: 20rpx; }\r\n.pt-3 { padding-top: 30rpx; }\r\n.pt-4 { padding-top: 40rpx; }\r\n\r\n.pb-0 { padding-bottom: 0; }\r\n.pb-1 { padding-bottom: 10rpx; }\r\n.pb-2 { padding-bottom: 20rpx; }\r\n.pb-3 { padding-bottom: 30rpx; }\r\n.pb-4 { padding-bottom: 40rpx; }\r\n\r\n.pl-0 { padding-left: 0; }\r\n.pl-1 { padding-left: 10rpx; }\r\n.pl-2 { padding-left: 20rpx; }\r\n.pl-3 { padding-left: 30rpx; }\r\n.pl-4 { padding-left: 40rpx; }\r\n\r\n.pr-0 { padding-right: 0; }\r\n.pr-1 { padding-right: 10rpx; }\r\n.pr-2 { padding-right: 20rpx; }\r\n.pr-3 { padding-right: 30rpx; }\r\n.pr-4 { padding-right: 40rpx; }\r\n\r\n/* 按钮样式 */\r\n.cu-btn {\r\n  position: relative;\r\n  border: 0rpx;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-sizing: border-box;\r\n  padding: 0 30rpx;\r\n  font-size: 28rpx;\r\n  height: 64rpx;\r\n  line-height: 1;\r\n  text-align: center;\r\n  text-decoration: none;\r\n  overflow: visible;\r\n  margin-left: initial;\r\n  transform: translate(0rpx, 0rpx);\r\n  margin-right: initial;\r\n  border-radius: 6rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cu-btn.block {\r\n  display: flex;\r\n  width: 100%;\r\n}\r\n\r\n.cu-btn.lg {\r\n  padding: 0 40rpx;\r\n  height: 80rpx;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.cu-btn.sm {\r\n  padding: 0 20rpx;\r\n  height: 48rpx;\r\n  font-size: 24rpx;\r\n}\r\n\r\n.cu-btn.round {\r\n  border-radius: 5000rpx;\r\n}\r\n\r\n.cu-btn.disabled {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n/* 输入框样式 */\r\n.input-item {\r\n  background-color: #fff;\r\n  margin: 20rpx 0;\r\n  padding: 20rpx;\r\n  border-radius: 10rpx;\r\n  border: 1rpx solid #e5e5e5;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.input-item.focus {\r\n  border-color: #007aff;\r\n}\r\n\r\n.input-item .icon {\r\n  margin-right: 20rpx;\r\n  font-size: 32rpx;\r\n  color: #999;\r\n}\r\n\r\n.input-item .input {\r\n  flex: 1;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.input-item .input::placeholder {\r\n  color: #999;\r\n}\r\n\r\n/* 卡片样式 */\r\n.card {\r\n  background-color: #fff;\r\n  border-radius: 10rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.card-header {\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.card-body {\r\n  padding: 30rpx;\r\n}\r\n\r\n.card-footer {\r\n  padding: 30rpx;\r\n  border-top: 1rpx solid #f0f0f0;\r\n  background-color: #fafafa;\r\n}\r\n\r\n/* 列表样式 */\r\n.list-item {\r\n  background-color: #fff;\r\n  padding: 30rpx;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.list-item:active {\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n.list-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40rpx;\r\n  color: #999;\r\n}\r\n\r\n/* 空状态 */\r\n.empty {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80rpx 40rpx;\r\n  color: #999;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 80rpx;\r\n  margin-bottom: 20rpx;\r\n  opacity: 0.5;\r\n}\r\n\r\n.empty-text {\r\n  font-size: 28rpx;\r\n}\r\n\r\n/* 安全区域适配 */\r\n.safe-area-inset-top {\r\n  padding-top: constant(safe-area-inset-top);\r\n  padding-top: env(safe-area-inset-top);\r\n}\r\n\r\n.safe-area-inset-bottom {\r\n  padding-bottom: constant(safe-area-inset-bottom);\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* 隐藏滚动条 */\r\n::-webkit-scrollbar {\r\n  display: none;\r\n  width: 0 !important;\r\n  height: 0 !important;\r\n  -webkit-appearance: none;\r\n  background: transparent;\r\n}\r\n</style>\r\n", "import App from './App'\r\nimport store from './store'\r\nimport { request } from './utils/request'\r\n\r\n// 导入全局组件\r\nimport MyHeader from './components/my-header/my-header.vue'\r\n\r\n// #ifndef VUE3\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\n\r\n// 全局配置\r\nVue.config.productionTip = false\r\n\r\n// 注册全局组件\r\nVue.component('my-header', MyHeader)\r\n\r\n// 挂载全局方法\r\nVue.prototype.$store = store\r\nVue.prototype.$myRequest = request\r\n\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  store,\r\n  ...App\r\n})\r\napp.$mount()\r\n// #endif\r\n\r\n// #ifdef VUE3\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n  const app = createSSRApp(App)\r\n\r\n  // 使用store\r\n  app.use(store)\r\n\r\n  // 注册全局组件\r\n  app.component('my-header', MyHeader)\r\n\r\n  // 全局属性\r\n  app.config.globalProperties.$myRequest = request\r\n\r\n  return {\r\n    app\r\n  }\r\n}\r\n// #endif"], "names": ["uni", "createSSRApp", "App", "store", "request"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,MAAK,YAAU;AAAA,EACb,YAAY;AAAA;AAAA,IAEV,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY;AAAA;AAAA,IAGZ,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM;AAAA,IACP;AAAA;AAAA,IAGD,OAAO;AAAA,IACP,eAAe;AAAA;AAAA,IAGf,eAAe;AAAA,MACb,IAAI;AAAA,MACJ,IAAI;AAAA,IACL;AAAA;AAAA,IAGD,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EAED,UAAU,SAAS,SAAS;AAC1BA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,cAAc,OAAO;AAGjC,SAAK,cAAc;AAMnB,SAAK,yBAAyB;AAG9B,SAAK,QAAQ;AAGb,QAAI,WAAW,QAAQ,OAAO;AAC5BA,oBAAA,MAAA,MAAA,OAAA,iBAAY,SAAS,QAAQ,KAAK;AAAA,IAEpC;AAAA,EACD;AAAA,EAED,QAAQ,SAAS,SAAS;AACxBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,YAAY,OAAO;AAG/B,SAAK,cAAc,OAAO;AAAA,EAC3B;AAAA,EAED,QAAQ,WAAW;AACjBA,kBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAGtB,SAAK,cAAc;AAAA,EACpB;AAAA,EAED,SAAS,SAAS,OAAO;AACvBA,kBAAAA,MAAc,MAAA,SAAA,iBAAA,cAAc,KAAK;AAGjC,SAAK,kBAAkB,KAAK;AAAA,EAC7B;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,gBAAgB;AACd,UAAI;AACF,cAAM,aAAaA,cAAG,MAAC,kBAAkB;AACzC,aAAK,WAAW,aAAa;AAC7B,aAAK,WAAW,kBAAkB,WAAW,mBAAmB;AAIhE,cAAM,iBAAiBA,cAAG,MAAC,gCAAgC;AAC3D,aAAK,WAAW,kBAAkB,eAAe,SAAS,eAAe,MAAM,WAAW;AAO1FA,sBAAAA,MAAY,MAAA,OAAA,iBAAA,aAAa,UAAU;AAAA,MACnC,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,kBAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,kBAAkB;AAChBA,oBAAAA,MAAI,aAAa;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS,MAAM;AACbA,wBAAAA,MAAA,MAAA,OAAA,kBAAY,oBAAoB;AAAA,QACjC;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAA,MAAA,SAAA,kBAAc,uBAAuB,GAAG;AAAA,QAC1C;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,2BAA2B;AACzBA,oBAAG,MAAC,gBAAgB,MAAM;AACxBA,sBAAAA,qCAAY,WAAW;AACvB,aAAK,WAAW;AAAA,OACjB;AAAA,IACF;AAAA;AAAA,IAGD,UAAU;AACRA,oBAAAA,qCAAY,SAAS;AAIrB,WAAK,eAAe;AAIpB,WAAK,eAAe;AAGpB,WAAK,YAAY;AAEjBA,oBAAAA,qCAAY,SAAS;AAAA,IACtB;AAAA;AAAA,IAID,iBAAiB;AACf,YAAM,gBAAgBA,cAAG,MAAC,iBAAiB;AAE3C,oBAAc,iBAAiB,CAAC,QAAQ;AACtCA,sBAAY,MAAA,MAAA,OAAA,kBAAA,WAAW,IAAI,SAAS;AAAA,OACrC;AAED,oBAAc,cAAc,MAAM;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,4BAAc,YAAY;AAAA,YAC5B;AAAA,UACF;AAAA,SACD;AAAA,OACF;AAED,oBAAc,eAAe,MAAM;AACjCA,sBAAAA,MAAc,MAAA,SAAA,kBAAA,SAAS;AAAA,OACxB;AAAA,IACF;AAAA;AAAA,IAID,iBAAiB;AAEfA,oBAAAA,MAAA,MAAA,OAAA,kBAAY,WAAW;AAAA,IACxB;AAAA;AAAA,IAGD,cAAc;AAEZA,oBAAAA,qCAAY,SAAS;AAAA,IACtB;AAAA;AAAA,IAGD,cAAc,SAAS;AAErB,UAAI,WAAW,QAAQ,OAAO;AAC5BA,sBAAY,MAAA,MAAA,OAAA,kBAAA,UAAU,QAAQ,KAAK;AAAA,MACrC;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AAAA,IAGf;AAAA;AAAA,IAGD,kBAAkB,OAAO;AAEvBA,oBAAAA,MAAc,MAAA,SAAA,kBAAA,SAAS,KAAK;AAAA,IAI7B;AAAA;AAAA,IAGD,aAAa;AACX,UAAI;AAEFA,sBAAAA,MAAA,MAAA,OAAA,kBAAY,QAAQ;AAKpBA,sBAAAA,MAAA,MAAA,OAAA,kBAAY,QAAQ;AAAA,MACpB,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,kBAAc,WAAW,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;ACxNA,MAAM,WAAW,MAAW;AA0BrB,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAG5B,MAAI,IAAIC,iBAAK;AAGb,MAAI,UAAU,aAAa,QAAQ;AAGnC,MAAI,OAAO,iBAAiB,aAAaC,cAAO;AAEhD,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}