const TOKEN_KEY = 'token'
const REQUEST_CODE_KEY = 'requestCode'

// 获取token
export function getToken() {
  try {
    return uni.getStorageSync(TOKEN_KEY)
  } catch (error) {
    console.error('获取token失败:', error)
    return ''
  }
}

// 设置token
export function setToken(token) {
  try {
    return uni.setStorageSync(TOKEN_KEY, token)
  } catch (error) {
    console.error('设置token失败:', error)
    return false
  }
}

// 移除token
export function removeToken() {
  try {
    uni.removeStorageSync(REQUEST_CODE_KEY)
    uni.removeStorageSync(TOKEN_KEY)
    return true
  } catch (error) {
    console.error('移除token失败:', error)
    return false
  }
}

// 检查token是否存在
export function hasToken() {
  const token = getToken()
  return token && token.length > 0
}

// 清除所有认证相关数据
export function clearAuth() {
  try {
    removeToken()
    // 清除其他认证相关的存储数据
    uni.removeStorageSync('userInfo')
    uni.removeStorageSync('openid')
    return true
  } catch (error) {
    console.error('清除认证数据失败:', error)
    return false
  }
}

// 平台兼容性处理
export function getOpenId() {
  try {
    // #ifdef MP-WEIXIN
    return uni.getStorageSync('openid')
    // #endif
    
    // #ifndef MP-WEIXIN
    return ''
    // #endif
  } catch (error) {
    console.error('获取openid失败:', error)
    return ''
  }
}

export function setOpenId(openid) {
  try {
    // #ifdef MP-WEIXIN
    return uni.setStorageSync('openid', openid)
    // #endif
    
    // #ifndef MP-WEIXIN
    return true
    // #endif
  } catch (error) {
    console.error('设置openid失败:', error)
    return false
  }
}