<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Course_cover')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-course_cover" class="form-control" size="50" name="row[course_cover]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-course_cover" class="btn btn-danger plupload"
                            data-input-id="c-course_cover" data-mimetype="image/*" data-multiple="false"
                            data-preview-id="p-course_cover">
                            <i class="fa fa-upload"></i> 上传
                        </button></span>
                </div>
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" class="btn btn-primary fachoose" data-input-id="c-course_cover"
                            data-mimetype="image/*" data-multiple="false">
                            <i class="fa fa-list"></i> 选择
                        </button></span>
                </div>
            </div>
            <ul class="row list-inline plupload-preview" id="p-course_cover"></ul>
            <div data-v-ce13e118="" slot="tip" style="
    line-height: 1.2;
    font-size: 12px;
    color: #606266;
    margin-top: 7px;
"> 请上传 大小不超过 <b data-v-ce13e118="" style="color: rgb(245, 108, 108);">2MB</b> 格式为 <b data-v-ce13e118=""
                    style="color: rgb(245, 108, 108);">png/jpg/jpeg</b> 的文件 </div>
            <div data-v-ce13e118="" slot="tip" style="
    line-height: 1.2;
    font-size: 12px;
    color: #606266;
    margin-top: 7px;
"> 请上传 比例为 <b data-v-ce13e118="" style="color: rgb(245, 108, 108);">3:4</b> 尺寸如 <b data-v-ce13e118=""
                    style="color: rgb(245, 108, 108);">720x960、1440x1920、2160x2880</b> 的图片 </div>
        </div>
    </div>


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span> {:__('Course_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_name" data-rule="required" class="form-control" name="row[course_name]" type="text"
                value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span>
            {:__('Course_introduce')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_introduce" data-rule="required" class="form-control" name="row[course_introduce]"
                type="text" value="">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span>
            {:__('Course_age_prop')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_age_prop" data-rule="required" class="form-control" name="row[course_age_prop]"
                type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span> {:__('Course_poll')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_poll" class="form-control" name="row[course_poll]" type="number" value="1" min="1"
                step="1" data-rule="required;digits;min:1" />


        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span>
            {:__('Course_address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_address" data-rule="required" class="form-control" name="row[course_address]"
                type="text">
        </div>
    </div>


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span>
            {:__('Course_start_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_start_time" data-rule="required" class="form-control datetimepicker"
                data-date-format="HH:mm" data-use-current="true" data-date-type="time" name="row[course_start_time]"
                type="text" value="{:date('H:i')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="text-danger">*</span>
            {:__('Course_end_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-course_end_time" data-rule="required" class="form-control datetimepicker"
                data-date-format="HH:mm" data-use-current="true" data-date-type="time" name="row[course_end_time]"
                type="text" value="{:date('H:i')}">
        </div>
    </div>



    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" class="form-control" name="row[remark]" type="text" value="">
        </div>
    </div>



    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>