{"version": 3, "file": "getters.js", "sources": ["store/getters.js"], "sourcesContent": ["const getters = {\r\n  token: (state) => state.user.token,\r\n  tenantId: (state) => state.user.tenantId,\r\n  rememberMe: (state) => state.user.rememberMe,\r\n  avatar: (state) => state.user.avatar,\r\n  username: (state) => state.user.username,\r\n  password: (state) => state.user.password,\r\n  roles: (state) => state.user.roles,\r\n  permissions: (state) => state.user.permissions,\r\n  \r\n  // 新增一些有用的getters\r\n  isLoggedIn: (state) => !!state.user.token,\r\n  hasRole: (state) => (role) => {\r\n    return state.user.roles && state.user.roles.includes(role)\r\n  },\r\n  hasPermission: (state) => (permission) => {\r\n    return state.user.permissions && state.user.permissions.includes(permission)\r\n  },\r\n  userInfo: (state) => ({\r\n    username: state.user.username,\r\n    avatar: state.user.avatar,\r\n    roles: state.user.roles,\r\n    permissions: state.user.permissions\r\n  })\r\n}\r\n\r\nexport default getters"], "names": [], "mappings": ";AAAK,MAAC,UAAU;AAAA,EACd,OAAO,CAAC,UAAU,MAAM,KAAK;AAAA,EAC7B,UAAU,CAAC,UAAU,MAAM,KAAK;AAAA,EAChC,YAAY,CAAC,UAAU,MAAM,KAAK;AAAA,EAClC,QAAQ,CAAC,UAAU,MAAM,KAAK;AAAA,EAC9B,UAAU,CAAC,UAAU,MAAM,KAAK;AAAA,EAChC,UAAU,CAAC,UAAU,MAAM,KAAK;AAAA,EAChC,OAAO,CAAC,UAAU,MAAM,KAAK;AAAA,EAC7B,aAAa,CAAC,UAAU,MAAM,KAAK;AAAA;AAAA,EAGnC,YAAY,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK;AAAA,EACpC,SAAS,CAAC,UAAU,CAAC,SAAS;AAC5B,WAAO,MAAM,KAAK,SAAS,MAAM,KAAK,MAAM,SAAS,IAAI;AAAA,EAC1D;AAAA,EACD,eAAe,CAAC,UAAU,CAAC,eAAe;AACxC,WAAO,MAAM,KAAK,eAAe,MAAM,KAAK,YAAY,SAAS,UAAU;AAAA,EAC5E;AAAA,EACD,UAAU,CAAC,WAAW;AAAA,IACpB,UAAU,MAAM,KAAK;AAAA,IACrB,QAAQ,MAAM,KAAK;AAAA,IACnB,OAAO,MAAM,KAAK;AAAA,IAClB,aAAa,MAAM,KAAK;AAAA,EAC5B;AACA;;"}