"use strict";
const Utils = {
  changeTime: function(time, isYear) {
    let times = new Date(time);
    let y = times.getFullYear();
    let m = times.getMonth() + 1 < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
    let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();
    let h = times.getHours() < 10 ? "0" + times.getHours() : times.getHours();
    let mm = times.getMinutes() < 10 ? "0" + times.getMinutes() : times.getMinutes();
    times.getSeconds() < 10 ? "0" + times.getSeconds() : times.getSeconds();
    if (isYear) {
      return y + "-" + m + "-" + d;
    } else {
      return h + ":" + mm;
    }
  },
  changeTime2: function(time) {
    let times = new Date(time);
    let y = times.getFullYear();
    let m = times.getMonth() + 1 < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
    let d = times.getDate() < 10 ? "0" + times.getDate() : times.getDate();
    let h = times.getHours() < 10 ? "0" + times.getHours() : times.getHours();
    let mm = times.getMinutes() < 10 ? "0" + times.getMinutes() : times.getMinutes();
    let s = times.getSeconds() < 10 ? "0" + times.getSeconds() : times.getSeconds();
    return y + "-" + m + "-" + d + "  " + h + ":" + mm + ":" + s;
  },
  formatTime: function(time) {
    let times = new Date(time);
    times.getFullYear();
    times.getMonth() + 1 < 10 ? "0" + (times.getMonth() + 1) : times.getMonth() + 1;
    times.getDate() < 10 ? "0" + times.getDate() : times.getDate();
    let h = times.getHours() < 10 ? "0" + times.getHours() : times.getHours();
    let mm = times.getMinutes() < 10 ? "0" + times.getMinutes() : times.getMinutes();
    let s = times.getSeconds() < 10 ? "0" + times.getSeconds() : times.getSeconds();
    return h + ":" + mm + ":" + s;
  },
  checkMobile(str) {
    var reg = /^1[34578]\d{9}$/;
    if (reg.test(str)) {
      return true;
    } else {
      return false;
    }
  },
  distance: function(la1, lo1, la2, lo2) {
    var La1 = la1 * Math.PI / 180;
    var La2 = la2 * Math.PI / 180;
    var La3 = La1 - La2;
    var Lb3 = lo1 * Math.PI / 180 - lo2 * Math.PI / 180;
    var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 1e4) / 1e4;
    return s;
  },
  verifyIdCard: function(str, isLand = true) {
    var Land = /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/;
    var Hongkong = /([A-Za-z](\d{6})\d)|(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
    var Taiwan = /^[a-zA-Z][0-9]{9}$/;
    var Macao = /^[1|5|7][0-9]{6}[09Aa]$/;
    if (isLand) {
      return Boolean(Land.test(str));
    } else {
      return Boolean(Hongkong.test(str) || Taiwan.test(str) || Macao.test(str));
    }
  },
  timeThenNow: function(times) {
    let now = /* @__PURE__ */ new Date();
    return new Date(times) > now;
  },
  // 节流
  throttle: function(fn, interval) {
    var enterTime = 0;
    var gapTime = interval || 3e3;
    return function() {
      var context = this;
      var backTime = /* @__PURE__ */ new Date();
      if (backTime - enterTime > gapTime) {
        fn.call(context, arguments);
        enterTime = backTime;
      }
    };
  },
  // 防抖
  debounce: function(fn, time) {
    var timer;
    var lastTime = 0;
    return function(path) {
      clearTimeout(timer);
      var nowTime = Date.now();
      var _this = this;
      var e = arguments[0];
      timer = setTimeout(function() {
        if (nowTime - lastTime < time) {
          return;
        }
        fn.call(_this, e, path);
      }, time);
    };
  }
};
exports.Utils = Utils;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/index.js.map
