server {
    listen       80;
    server_name  localhost;

    #access_log  /var/log/nginx/host.access.log  main;
    location /profile/ {
           alias /usr/share/nginx/html/file-upload/;
        }
    location / {
          root   /usr/share/nginx/html/web;
          try_files $uri $uri/ /index.html;
          index  index.html index.htm;
    }
  
    location /baoan-api/ {
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**********:8825/;
    }

    # 免跳转访问验证文件
    location /.well-known/pki-validation/ {
        alias /usr/share/nginx/html/web/.well-known/pki-validation/;
        try_files $uri =404;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}

#宝安科技馆小程序请求 
server {
    listen 80;
    server_name bakjgyyxt.baoan.gov.cn;

    # 免跳转访问验证文件
    location /.well-known/pki-validation/ {
        alias /usr/share/nginx/html/web/.well-known/pki-validation/;
        try_files $uri =404;
    }

    # 其余请求跳转 HTTPS
    location / {
        return 307 https://$host$request_uri;
    }



   # return 307 https://$host$request_uri;

   #location / {
   #  proxy_pass http://**********:8825/;
   # }
}


server {
    listen 443 ssl;
    #ssl on;
    server_name bakjgyyxt.baoan.gov.cn;
    #server_tokens off;
    #ssl证书的pem文件路径
    ssl_certificate  cert/nanshanrenda/bakjgyyxt.baoan.gov.cn.pem;
    #ssl证书的key文件路径
    ssl_certificate_key   cert/nanshanrenda/bakjgyyxt.baoan.gov.cn.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    add_header Content-Security-Policy "default-src 'self' smtp: pasc: https:; script-src 'self' 'unsafe-eval' 'unsafe-inline' isz-open.sz.gov.cn; img-src 'self' https: data: blob:;";

#文件路径

#    location /profile/ {
#       alias /usr/share/nginx/html/file-upload/;
#   }

#微信小程序
    location / {
        proxy_pass http://**********:8825/;    
    }

#i深圳h5
    location /h5 {
        alias   /usr/share/nginx/html/h5/;
        
    }

    # 免跳转访问验证文件
    location /.well-known/pki-validation/ {
        alias /usr/share/nginx/html/web/.well-known/pki-validation/;
        try_files $uri =404;
    }

    location /baoan-api/ {
      proxy_set_header Host $http_host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**********:8825/;
    }

    location /kppt/ {
        proxy_pass http://**********:8081/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # 嵌套的location块，专门处理静态文件
        location ~* \.(jpg|jpeg|png|gif|ico|css|js|jspx|jhtml)$ {

            proxy_pass http://**********:8081$request_uri;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }

    location /r/cms/ {
        proxy_pass http://**********:8081/r/cms/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /u/cms/ {
        proxy_pass http://**********:8081/u/cms/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location /t/cms/ {
        proxy_pass http://**********:8081/t/cms/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location ~* \.(jspx|jhtml)$ {
        proxy_pass http://**********:8081$request_uri;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }    
}

