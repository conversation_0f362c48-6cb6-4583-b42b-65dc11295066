
.contactContent.data-v-82585584 {
  background: #f3f4f6;
  font-family: PingFang SC;
  height: 100vh;
  overflow: scroll;
  width: 100%;
}
.content.data-v-82585584 {
  height: auto;
  width: 100%;
}
.contactBox.data-v-82585584 {
  background: #f3f4f6;
  box-sizing: border-box;
  height: auto;
  padding: 29rpx 28rpx;
  width: 100%;
}
.addContact.data-v-82585584 {
  background-color: #fff;
  border-radius: 10rpx;
  color: #5cb7ff;
  font-size: 35rpx;
  font-weight: 600;
  height: 87rpx;
  line-height: 87rpx;
  margin-bottom: 28rpx;
  text-align: center;
  width: 100%;
}
.add.data-v-82585584 {
  font-size: 42rpx;
  margin-right: 10rpx;
}
.contactList.data-v-82585584 {
  height: auto;
  overflow: hidden;
  width: 100%;
}
.contactItem.data-v-82585584 {
  background-color: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  display: flex;
  height: 158rpx;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 18rpx 28rpx;
  position: relative;
  width: 100%;
}
.contactItem.isMove .left.data-v-82585584 {
  transform: translate(-40%);
}
.contactItem.isMove .handlePlace.data-v-82585584 {
  width: 40%;
}
.left.data-v-82585584 {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  transition: all 0.3s;
  width: auto;
}
.peopleName.data-v-82585584 {
  color: #000;
  font-size: 29rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}
.peopleCard.data-v-82585584,
.peopleMablie.data-v-82585584 {
  color: #888;
  font-size: 23rpx;
}
.peopleMablie text.data-v-82585584:first-child {
  display: inline-block;
  margin-right: 96rpx;
}
.handlePlace.data-v-82585584 {
  color: #fff;
  display: flex;
  font-size: 28rpx;
  height: 100%;
  line-height: 158rpx;
  position: absolute;
  right: 0;
  text-align: center;
  top: 0;
  transition: all 0.3s;
  width: 0;
  z-index: 99;
}
.edit.data-v-82585584 {
  background-color: #5cb7ff;
  height: 100%;
  width: 50%;
}
.del.data-v-82585584 {
  background-color: #fc5531;
  height: 100%;
  width: 50%;
}
