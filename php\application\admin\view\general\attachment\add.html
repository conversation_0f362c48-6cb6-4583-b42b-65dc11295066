<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    {if $config.upload.cdnurl}
    <div class="form-group">
        <label for="c-third" class="control-label col-xs-12 col-sm-2">{:__('Upload to third')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[third]" id="c-third" class="form-control"/>
            <ul class="row list-inline faupload-preview" id="p-third"></ul>
        </div>
    </div>

    <div class="form-group">
        <label for="c-third" class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <div style="width:180px;display:inline-block;">
                <select name="category-third" id="category-third" class="form-control selectpicker">
                    <option value="">{:__('Please select category')}</option>
                    {foreach name="categoryList" id="item"}
                    <option value="{$key|htmlentities}">{$item|htmlentities}</option>
                    {/foreach}
                </select>
            </div>
            <button type="button" id="faupload-third" class="btn btn-danger faupload" data-multiple="true" data-input-id="c-third" data-preview-id="p-third"><i class="fa fa-upload"></i> {:__("Upload to third")}</button>
            {if $config.upload.chunking}
            <button type="button" id="faupload-third-chunking" class="btn btn-danger faupload" data-chunking="true" data-maxsize="1gb" data-multiple="true" data-input-id="c-third" data-preview-id="p-third"><i class="fa fa-upload"></i> {:__("Upload to third by chunk")}</button>
            {/if}
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label for="c-local" class="control-label col-xs-12 col-sm-2">{:__('Upload to local')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[local]" id="c-local" class="form-control"/>
            <ul class="row list-inline faupload-preview" id="p-local"></ul>
        </div>
    </div>

    <div class="form-group">
        <label for="c-local" class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <div style="width:180px;display:inline-block;">
                <select name="category-local" id="category-local" class="form-control selectpicker">
                    <option value="">{:__('Please select category')}</option>
                    {foreach name="categoryList" id="item"}
                    <option value="{$key|htmlentities}">{$item|htmlentities}</option>
                    {/foreach}
                </select>
            </div>
            <button type="button" id="faupload-local" class="btn btn-primary faupload" data-input-id="c-local" data-multiple="true" data-preview-id="p-local" data-url="{:url('ajax/upload')}" data-cdnurl=""><i class="fa fa-upload"></i> {:__("Upload to local")}</button>
            {if $config.upload.chunking}
            <button type="button" id="faupload-local-chunking" class="btn btn-primary faupload" data-chunking="true" data-maxsize="1gb" data-input-id="c-local" data-multiple="true" data-preview-id="p-local" data-url="{:url('ajax/upload')}" data-cdnurl=""><i class="fa fa-upload"></i> {:__("Upload to local by chunk")}</button>
            {/if}
        </div>
    </div>

    <div class="form-group hidden layer-footer">
        <div class="col-xs-2"></div>
        <div class="col-xs-12 col-sm-8">
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
