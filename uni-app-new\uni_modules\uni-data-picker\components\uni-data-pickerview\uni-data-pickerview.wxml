<view class="uni-data-pickerview">
    <scroll-view class="selected-area" scrollX="true" wx:if="{{a}}">
        <view class="selected-list">
            <view bindtap="{{item.d}}" class="{{['selected-item',item.c&&'selected-item-active']}}" wx:for="{{b}}" wx:key="b">
                <text>{{item.a}}</text>
            </view>
        </view>
    </scroll-view>
    <view class="tab-c">
        <scroll-view class="list" scrollY="{{true}}">
            <view bindtap="{{item.e}}" class="{{['item',item.c&&'is-disabled']}}" wx:for="{{c}}" wx:key="d">
                <text class="item-text">{{item.a}}</text>
                <view class="check" wx:if="{{item.b}}"></view>
            </view>
        </scroll-view>
        <view class="loading-cover" wx:if="{{d}}">
            <uni-load-more bind:__l="__l" class="load-more" uI="6e24d127-0" uP="{{e}}" wx:if="{{e}}"></uni-load-more>
        </view>
        <view class="error-message" wx:if="{{f}}">
            <text class="error-text">{{g}}</text>
        </view>
    </view>
</view>
