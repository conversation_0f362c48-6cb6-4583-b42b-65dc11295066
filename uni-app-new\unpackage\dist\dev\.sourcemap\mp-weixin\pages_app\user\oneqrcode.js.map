{"version": 3, "file": "oneqrcode.js", "sources": ["pages_app/user/oneqrcode.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXHVzZXJcb25lcXJjb2RlLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"qrcode-container\">\r\n    <!-- 装饰元素 -->\r\n    <view class=\"left-bottom-sign\"></view>\r\n    <view class=\"right-top-sign\"></view>\r\n    \r\n    <!-- 返回按钮 -->\r\n    <view class=\"back-btn\" @tap=\"goBack\">\r\n      <text class=\"back-icon\">‹</text>\r\n    </view>\r\n    \r\n    <!-- 主体内容 -->\r\n    <view class=\"wrapper\">\r\n      <view class=\"left-top-sign\">QRCODE</view>\r\n      <view class=\"welcome\">管理员二维码</view>\r\n      \r\n      <!-- 二维码显示区域 -->\r\n      <view class=\"qrcode-content\">\r\n        <view class=\"qrcode-wrapper\">\r\n          <image \r\n            v-if=\"qrcodeImage\" \r\n            :src=\"qrcodeImage\" \r\n            class=\"qrcode-image\"\r\n            mode=\"aspectFit\"\r\n          />\r\n          <view v-else class=\"qrcode-loading\">\r\n            <text class=\"loading-text\">生成中...</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 刷新按钮 -->\r\n      <button \r\n        class=\"refresh-btn\" \r\n        :disabled=\"refreshing\"\r\n        @tap=\"refreshQRCode\"\r\n      >\r\n        {{ refreshing ? '刷新中...' : '刷新二维码' }}\r\n      </button>\r\n      \r\n      <!-- 提示信息 -->\r\n      <view class=\"tips\">\r\n        <text class=\"tips-text\">二维码每60秒自动刷新</text>\r\n        <text class=\"tips-text\">请保持屏幕亮度适中</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'OneQRCode',\r\n  data() {\r\n    return {\r\n      qrcodeImage: '',\r\n      refreshing: false,\r\n      refreshTimer: null,\r\n      originalBrightness: 0\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    this.initPage()\r\n  },\r\n  \r\n  onUnload() {\r\n    this.cleanup()\r\n  },\r\n  \r\n  methods: {\r\n    // 初始化页面\r\n    async initPage() {\r\n      try {\r\n        // 保存原始亮度并设置适中亮度\r\n        await this.setBrightness()\r\n        \r\n        // 生成二维码\r\n        await this.generateQRCode()\r\n        \r\n        // 设置自动刷新\r\n        this.startAutoRefresh()\r\n      } catch (error) {\r\n        console.error('初始化失败:', error)\r\n        uni.showToast({\r\n          title: '初始化失败',\r\n          icon: 'error'\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 设置屏幕亮度\r\n    async setBrightness() {\r\n      try {\r\n        // 获取当前亮度\r\n        const brightness = await this.getScreenBrightness()\r\n        this.originalBrightness = brightness\r\n        \r\n        // 设置适中亮度\r\n        await this.setScreenBrightness(0.5)\r\n      } catch (error) {\r\n        console.warn('设置亮度失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 获取屏幕亮度\r\n    getScreenBrightness() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.getScreenBrightness({\r\n          success: (res) => resolve(res.value),\r\n          fail: reject\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 设置屏幕亮度\r\n    setScreenBrightness(value) {\r\n      return new Promise((resolve, reject) => {\r\n        uni.setScreenBrightness({\r\n          value,\r\n          success: resolve,\r\n          fail: reject\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 生成二维码\r\n    async generateQRCode() {\r\n      if (this.refreshing) return\r\n      \r\n      this.refreshing = true\r\n      \r\n      try {\r\n        const res = await this.$myRequest({\r\n          url: '/auth/venue/getAdminCode',\r\n          method: 'get'\r\n        })\r\n        \r\n        if (res.code === 200) {\r\n          const qrData = res.data.data || ''\r\n          this.qrcodeImage = this.drawQRCode(qrData)\r\n        } else {\r\n          throw new Error(res.msg || '获取二维码失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('生成二维码失败:', error)\r\n        uni.showToast({\r\n          title: error.message || '生成二维码失败',\r\n          icon: 'error'\r\n        })\r\n      } finally {\r\n        this.refreshing = false\r\n      }\r\n    },\r\n    \r\n    // 绘制二维码\r\n    drawQRCode(data) {\r\n      try {\r\n        // 这里需要引入二维码生成库\r\n        // 由于uni-app环境限制，这里使用简化的实现\r\n        // 实际项目中应该使用专门的二维码生成库\r\n        \r\n        // 创建canvas绘制二维码\r\n        const canvas = uni.createCanvasContext('qrcode-canvas')\r\n        \r\n        // 简化实现：返回一个占位图片\r\n        // 实际应该使用qrcode.js等库生成\r\n        return this.generateQRCodeImage(data)\r\n      } catch (error) {\r\n        console.error('绘制二维码失败:', error)\r\n        return ''\r\n      }\r\n    },\r\n    \r\n    // 生成二维码图片（简化实现）\r\n    generateQRCodeImage(data) {\r\n      // 这里应该集成真正的二维码生成库\r\n      // 例如：qrcode.js, qrious等\r\n      // 当前返回占位实现\r\n      \r\n      try {\r\n        // 使用第三方二维码API或本地库\r\n        const size = 400\r\n        const qrcodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}`\r\n        return qrcodeUrl\r\n      } catch (error) {\r\n        console.error('生成二维码图片失败:', error)\r\n        return ''\r\n      }\r\n    },\r\n    \r\n    // 开始自动刷新\r\n    startAutoRefresh() {\r\n      this.refreshTimer = setInterval(() => {\r\n        this.generateQRCode()\r\n      }, 60000) // 60秒刷新一次\r\n    },\r\n    \r\n    // 停止自动刷新\r\n    stopAutoRefresh() {\r\n      if (this.refreshTimer) {\r\n        clearInterval(this.refreshTimer)\r\n        this.refreshTimer = null\r\n      }\r\n    },\r\n    \r\n    // 手动刷新二维码\r\n    refreshQRCode() {\r\n      this.generateQRCode()\r\n    },\r\n    \r\n    // 返回上一页\r\n    goBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 清理资源\r\n    async cleanup() {\r\n      // 停止自动刷新\r\n      this.stopAutoRefresh()\r\n      \r\n      // 恢复原始亮度\r\n      if (this.originalBrightness > 0) {\r\n        try {\r\n          await this.setScreenBrightness(this.originalBrightness)\r\n        } catch (error) {\r\n          console.warn('恢复亮度失败:', error)\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.qrcode-container {\r\n  position: relative;\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  overflow: hidden;\r\n\r\n  // 装饰元素\r\n  .left-bottom-sign {\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 0;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 0 200rpx 0 0;\r\n  }\r\n\r\n  .right-top-sign {\r\n    position: absolute;\r\n    right: 0;\r\n    top: 0;\r\n    width: 150rpx;\r\n    height: 150rpx;\r\n    background: rgba(255, 255, 255, 0.1);\r\n    border-radius: 0 0 0 150rpx;\r\n  }\r\n\r\n  // 返回按钮\r\n  .back-btn {\r\n    position: absolute;\r\n    left: 30rpx;\r\n    top: 100rpx;\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    background: rgba(255, 255, 255, 0.2);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 10;\r\n\r\n    .back-icon {\r\n      font-size: 40rpx;\r\n      color: white;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n\r\n  // 主体内容\r\n  .wrapper {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    min-height: 100vh;\r\n    padding: 60rpx 40rpx;\r\n\r\n    .left-top-sign {\r\n      position: absolute;\r\n      left: 40rpx;\r\n      top: 200rpx;\r\n      font-size: 48rpx;\r\n      font-weight: bold;\r\n      color: rgba(255, 255, 255, 0.3);\r\n      letter-spacing: 4rpx;\r\n    }\r\n\r\n    .welcome {\r\n      font-size: 48rpx;\r\n      font-weight: 600;\r\n      color: white;\r\n      margin-bottom: 80rpx;\r\n      text-align: center;\r\n    }\r\n\r\n    // 二维码内容区域\r\n    .qrcode-content {\r\n      margin-bottom: 60rpx;\r\n\r\n      .qrcode-wrapper {\r\n        width: 480rpx;\r\n        height: 480rpx;\r\n        background: white;\r\n        border-radius: 20rpx;\r\n        padding: 40rpx;\r\n        box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .qrcode-image {\r\n          width: 400rpx;\r\n          height: 400rpx;\r\n        }\r\n\r\n        .qrcode-loading {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          width: 400rpx;\r\n          height: 400rpx;\r\n\r\n          .loading-text {\r\n            font-size: 32rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 刷新按钮\r\n    .refresh-btn {\r\n      width: 400rpx;\r\n      height: 88rpx;\r\n      background: rgba(255, 255, 255, 0.9);\r\n      border-radius: 44rpx;\r\n      border: none;\r\n      font-size: 32rpx;\r\n      font-weight: 500;\r\n      color: #333;\r\n      margin-bottom: 40rpx;\r\n\r\n      &::after {\r\n        border: none;\r\n      }\r\n\r\n      &:disabled {\r\n        background: rgba(255, 255, 255, 0.5);\r\n        color: #999;\r\n      }\r\n    }\r\n\r\n    // 提示信息\r\n    .tips {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      gap: 10rpx;\r\n\r\n      .tips-text {\r\n        font-size: 24rpx;\r\n        color: rgba(255, 255, 255, 0.7);\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 平台特定样式 */\r\n/* #ifdef H5 */\r\n.qrcode-container {\r\n  background-attachment: fixed;\r\n}\r\n/* #endif */\r\n\r\n/* #ifdef MP-WEIXIN */\r\n.wrapper {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n/* #endif */\r\n</style>\r\n", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/user/oneqrcode.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAkDA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,oBAAoB;AAAA,IACtB;AAAA,EACD;AAAA,EAED,SAAS;AACP,SAAK,SAAS;AAAA,EACf;AAAA,EAED,WAAW;AACT,SAAK,QAAQ;AAAA,EACd;AAAA,EAED,SAAS;AAAA;AAAA,IAEP,MAAM,WAAW;AACf,UAAI;AAEF,cAAM,KAAK,cAAc;AAGzB,cAAM,KAAK,eAAe;AAG1B,aAAK,iBAAiB;AAAA,MACtB,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,sCAAc,UAAU,KAAK;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,MACH;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,gBAAgB;AACpB,UAAI;AAEF,cAAM,aAAa,MAAM,KAAK,oBAAoB;AAClD,aAAK,qBAAqB;AAG1B,cAAM,KAAK,oBAAoB,GAAG;AAAA,MAClC,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,QAAA,uCAAa,WAAW,KAAK;AAAA,MAC/B;AAAA,IACD;AAAA;AAAA,IAGD,sBAAsB;AACpB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,oBAAoB;AAAA,UACtB,SAAS,CAAC,QAAQ,QAAQ,IAAI,KAAK;AAAA,UACnC,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,oBAAoB,OAAO;AACzB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,sBAAAA,MAAI,oBAAoB;AAAA,UACtB;AAAA,UACA,SAAS;AAAA,UACT,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACF;AAAA;AAAA,IAGD,MAAM,iBAAiB;AACrB,UAAI,KAAK;AAAY;AAErB,WAAK,aAAa;AAElB,UAAI;AACF,cAAM,MAAM,MAAM,KAAK,WAAW;AAAA,UAChC,KAAK;AAAA,UACL,QAAQ;AAAA,SACT;AAED,YAAI,IAAI,SAAS,KAAK;AACpB,gBAAM,SAAS,IAAI,KAAK,QAAQ;AAChC,eAAK,cAAc,KAAK,WAAW,MAAM;AAAA,eACpC;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,SAAS;AAAA,QACtC;AAAA,MACA,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,SACP;AAAA,MACH,UAAU;AACR,aAAK,aAAa;AAAA,MACpB;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,MAAM;AACf,UAAI;AAMF,cAAM,SAASA,cAAAA,MAAI,oBAAoB,eAAe;AAItD,eAAO,KAAK,oBAAoB,IAAI;AAAA,MACpC,SAAO,OAAO;AACdA,sBAAAA,MAAc,MAAA,SAAA,uCAAA,YAAY,KAAK;AAC/B,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,oBAAoB,MAAM;AAKxB,UAAI;AAEF,cAAM,OAAO;AACb,cAAM,YAAY,oDAAoD,IAAI,IAAI,IAAI,SAAS,mBAAmB,IAAI,CAAC;AACnH,eAAO;AAAA,MACP,SAAO,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,cAAc,KAAK;AACjC,eAAO;AAAA,MACT;AAAA,IACD;AAAA;AAAA,IAGD,mBAAmB;AACjB,WAAK,eAAe,YAAY,MAAM;AACpC,aAAK,eAAe;AAAA,MACrB,GAAE,GAAK;AAAA,IACT;AAAA;AAAA,IAGD,kBAAkB;AAChB,UAAI,KAAK,cAAc;AACrB,sBAAc,KAAK,YAAY;AAC/B,aAAK,eAAe;AAAA,MACtB;AAAA,IACD;AAAA;AAAA,IAGD,gBAAgB;AACd,WAAK,eAAe;AAAA,IACrB;AAAA;AAAA,IAGD,SAAS;AACPA,oBAAAA,MAAI,aAAa;AAAA,IAClB;AAAA;AAAA,IAGD,MAAM,UAAU;AAEd,WAAK,gBAAgB;AAGrB,UAAI,KAAK,qBAAqB,GAAG;AAC/B,YAAI;AACF,gBAAM,KAAK,oBAAoB,KAAK,kBAAkB;AAAA,QACtD,SAAO,OAAO;AACdA,wBAAAA,MAAa,MAAA,QAAA,uCAAA,WAAW,KAAK;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;ACrOA,GAAG,WAAW,eAAe;"}