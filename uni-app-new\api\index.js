// 导出所有API模块
export * from './login.js'
export * from './user.js'
export * from './reservation.js'
export * from './contact.js'
export * from './common.js'
export * from './api.js'

// 默认导出常用API
import * as loginApi from './login.js'
import * as userApi from './user.js'
import * as reservationApi from './reservation.js'
import * as contactApi from './contact.js'
import * as commonApi from './common.js'
import * as legacyApi from './api.js'

export default {
  login: loginApi,
  user: userApi,
  reservation: reservationApi,
  contact: contactApi,
  common: commonApi,
  legacy: legacyApi
}

// 创建API实例，方便在组件中使用
export const api = {
  // 认证相关
  login: loginApi.login,
  wxLogin: loginApi.wxLogin,
  logout: loginApi.logout,
  register: loginApi.register,
  getInfo: loginApi.getInfo,
  
  // 用户相关
  getUserProfile: userApi.getUserProfile,
  updateUserProfile: userApi.updateUserProfile,
  getUserReservationHistory: userApi.getUserReservationHistory,
  
  // 预约相关
  getVenueInfo: reservationApi.getVenueInfo,
  createVenueReservation: reservationApi.createVenueReservation,
  createMovieReservation: reservationApi.createMovieReservation,
  createCourseReservation: reservationApi.createCourseReservation,
  getMyReservations: reservationApi.getMyReservations,
  cancelMyReservation: reservationApi.cancelMyReservation,
  
  // 联系人相关
  getContactList: contactApi.getContactList,
  addContact: contactApi.addContact,
  updateContact: contactApi.updateContact,
  deleteContact: contactApi.deleteContact,
  
  // 通用接口
  getAnnouncements: commonApi.getAnnouncements,
  getBanners: commonApi.getBanners,
  sendSmsCode: commonApi.sendSmsCode,
  uploadFile: commonApi.uploadFile
}