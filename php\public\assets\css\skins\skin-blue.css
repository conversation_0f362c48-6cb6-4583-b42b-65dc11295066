/*
 * Skin: Blue
 * -----------
 */
.skin-blue .main-header .navbar-toggle {
  color: #333;
}
.skin-blue .main-header .navbar-brand {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-blue .main-header .navbar {
  background-color: #fff;
}
.skin-blue .main-header .navbar .nav > li > a {
  color: #444;
}
.skin-blue .main-header .navbar .nav > li > a:hover,
.skin-blue .main-header .navbar .nav > li > a:active,
.skin-blue .main-header .navbar .nav > li > a:focus,
.skin-blue .main-header .navbar .nav .open > a,
.skin-blue .main-header .navbar .nav .open > a:hover,
.skin-blue .main-header .navbar .nav .open > a:focus,
.skin-blue .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.02);
  color: #4e73df;
}
.skin-blue .main-header .navbar .nav-addtabs li > .close-tab {
  color: #4e73df;
}
.skin-blue .main-header .navbar .sidebar-toggle {
  color: #444;
}
.skin-blue .main-header .navbar .sidebar-toggle:hover {
  color: #4e73df;
  background: rgba(0, 0, 0, 0.02);
}
.skin-blue .main-header .navbar > .sidebar-toggle {
  color: #333;
  border-right: 1px solid #eee;
}
.skin-blue .main-header .navbar .navbar-nav > li > a {
  border-right: 1px solid #eee;
}
.skin-blue .main-header .navbar .navbar-custom-menu .navbar-nav > li > a,
.skin-blue .main-header .navbar .navbar-right > li > a {
  border-left: 1px solid #eee;
  border-left: none;
  border-right-width: 0;
}
.skin-blue .main-header > .logo {
  background-color: #4e73df;
  color: #fff;
  border-bottom: 0 solid transparent;
  border-right: 1px solid #4e73df;
  box-shadow: none;
}
.skin-blue .main-header > .logo:hover {
  background-color: #4a70de;
}
@media (max-width: 767px) {
  .skin-blue .main-header > .logo {
    background-color: #fff;
    color: #222;
    border-bottom: 0 solid transparent;
    border-right: none;
  }
  .skin-blue .main-header > .logo:hover {
    background-color: #fcfcfc;
  }
}
.skin-blue .main-header li.user-header {
  background-color: #4e73df;
}
.skin-blue .main-header .nav-addtabs > li > a,
.skin-blue .main-header .nav-addtabs > li.active > a {
  border-right-color: transparent;
}
.skin-blue .content-header {
  background: transparent;
  box-shadow: none;
}
.skin-blue .wrapper,
.skin-blue .main-sidebar,
.skin-blue .left-side {
  background-color: #4e73df;
}
.skin-blue .user-panel > .info,
.skin-blue .user-panel > .info > a {
  color: #fff;
}
.skin-blue .sidebar-menu .treeview-menu {
  padding-left: 3px;
}
.skin-blue .sidebar-menu > li.header {
  color: #a4b7ef;
  background: #3d65dc;
}
.skin-blue .sidebar-menu > li:hover > a,
.skin-blue .sidebar-menu > li.active > a {
  color: #fff;
  background: #456cdd;
  border-left-color: #fff;
}
.skin-blue .sidebar-menu > li > .treeview-menu {
  background: #4169dd;
}
.skin-blue .sidebar a {
  color: #ccd9ff;
}
.skin-blue .sidebar a:hover {
  text-decoration: none;
}
.skin-blue .treeview-menu > li > a {
  color: #ccd9ff;
}
.skin-blue .treeview-menu > li.active > a,
.skin-blue .treeview-menu > li > a:hover {
  color: #fff;
}
.skin-blue .sidebar-form {
  border-radius: 3px;
  border: 1px solid #7995e7;
  background-color: #7995e7;
  margin: 10px 10px;
}
.skin-blue .sidebar-form input[type="text"],
.skin-blue .sidebar-form .btn {
  box-shadow: none;
  background-color: #7995e7;
  border: 1px solid transparent;
  height: 35px;
}
.skin-blue .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-blue .sidebar-form input[type="text"]:focus,
.skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn {
  background: #fff;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-blue .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-blue .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
.skin-blue .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  padding-left: 12px;
}
@media (min-width: 768px) {
  .skin-blue.sidebar-mini.sidebar-collapse .sidebar-menu > li:hover > a > span:not(.pull-right) {
    margin-left: -3px;
  }
}
.skin-blue .sidebar-form input[type="text"]::-moz-placeholder {
  color: #fff;
  opacity: 1;
}
.skin-blue .sidebar-form input[type="text"]:-ms-input-placeholder {
  color: #fff;
}
.skin-blue .sidebar-form input[type="text"]::-webkit-input-placeholder {
  color: #fff;
}
.skin-blue .sidebar-form input[type="text"],
.skin-blue .sidebar-form .btn {
  color: #fff;
}
@media (max-width: 767px) {
  .skin-blue.multiplenav .main-header .navbar {
    background-color: #4e73df;
  }
  .skin-blue.multiplenav .main-header .navbar .nav > li > a {
    color: #fff;
  }
  .skin-blue.multiplenav .main-header .navbar .nav > li > a:hover,
  .skin-blue.multiplenav .main-header .navbar .nav > li > a:active,
  .skin-blue.multiplenav .main-header .navbar .nav > li > a:focus,
  .skin-blue.multiplenav .main-header .navbar .nav .open > a,
  .skin-blue.multiplenav .main-header .navbar .nav .open > a:hover,
  .skin-blue.multiplenav .main-header .navbar .nav .open > a:focus,
  .skin-blue.multiplenav .main-header .navbar .nav > .active > a {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
  }
  .skin-blue.multiplenav .main-header .navbar .nav-addtabs li > .close-tab {
    color: #f6f6f6;
  }
  .skin-blue.multiplenav .main-header .navbar .sidebar-toggle {
    color: #fff;
  }
  .skin-blue.multiplenav .main-header .navbar .sidebar-toggle:hover {
    color: #f6f6f6;
    background: rgba(0, 0, 0, 0.1);
  }
  .skin-blue.multiplenav .main-header > .logo {
    background-color: #4e73df;
    color: #fff;
    border-bottom: 0 solid transparent;
  }
  .skin-blue.multiplenav .main-header > .logo:hover {
    background-color: #4a70de;
  }
  .skin-blue.multiplenav .sidebar .mobilenav a.btn-app {
    background: #7995e7;
    color: #fff;
  }
  .skin-blue.multiplenav .sidebar .mobilenav a.btn-app.active {
    background: #fff;
    color: #7995e7;
  }
}
/*# sourceMappingURL=skin-blue.css.map */