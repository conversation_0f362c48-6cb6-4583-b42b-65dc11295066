{"version": 3, "file": "index.js", "sources": ["pages_app/curriculum/index.vue", "../../../下载/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXNfYXBwXGN1cnJpY3VsdW1caW5kZXgudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"curriculum\">\r\n    <!-- 自定义头部 -->\r\n    <my-header \r\n      v-if=\"!config.iSzgm\"\r\n      :is-back=\"true\"\r\n      :is-show-home=\"true\"\r\n      title=\"宝安科技馆\"\r\n      background=\"transparent\"\r\n      menu-class=\"df\"\r\n    />\r\n    \r\n    <!-- 课程预约主体内容 -->\r\n    <view class=\"curriculum_order_box\">\r\n      <!-- 日历选择区域 -->\r\n      <view class=\"calendar_content\">\r\n        <view class=\"calendar\">\r\n          <uni-calendar\r\n            :date=\"timeSection.start\"\r\n            :insert=\"true\"\r\n            :lunar=\"false\"\r\n            :show-month=\"false\"\r\n            :start-date=\"timeSection.start\"\r\n            :end-date=\"timeSection.end\"\r\n            :selected=\"selected\"\r\n            @change=\"getSessionDetail\"\r\n          />\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 课程列表区域 -->\r\n      <view class=\"curriculum_list_content\">\r\n        <image \r\n          class=\"curriculum_title\" \r\n          mode=\"aspectFill\" \r\n          src=\"/static/img/curriculum/curriculum_title.png\"\r\n        />\r\n        \r\n        <!-- 课程列表 -->\r\n        <view v-if=\"curriculumList.length > 0\" class=\"curriculum_list\">\r\n          <view \r\n            v-for=\"item in curriculumList\" \r\n            :key=\"item.id\"\r\n            :class=\"['curriculum_item', { 'disNone': !item.isShow }]\"\r\n            @tap=\"chooseCourse(item.id)\"\r\n          >\r\n            <view class=\"itemLeft\">\r\n              <image \r\n                class=\"cover\" \r\n                mode=\"aspectFill\" \r\n                :src=\"getImages(item.courseCover)\"\r\n              />\r\n              <view class=\"curriculumInfo\">\r\n                <text class=\"curriculumName\">{{ item.courseName }}</text>\r\n                <text class=\"curriculumType\">适龄：{{ item.courseAgeProp }}</text>\r\n                <text class=\"curriculumTime\">时间：{{ item.courseStartTime }}-{{ item.courseEndTime }}</text>\r\n                <text class=\"curriculumPlace\">地点：{{ item.courseAddress }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"itemRight\">\r\n              <view class=\"ticketType\" style=\"display:none\">\r\n                <text class=\"n_text\">倒计时</text>\r\n                <text class=\"t_text\">25:31</text>\r\n              </view>\r\n              <view \r\n                :class=\"['order_button', { 'gray': item.inventoryVotes == 0 }]\"\r\n              >\r\n                剩余:{{ item.inventoryVotes }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 无数据提示 -->\r\n        <view v-else class=\"curriculum_tip\">\r\n          暂无课程数据\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 报名须知弹窗 -->\r\n    <view v-if=\"isShowMask\" class=\"mask\">\r\n      <view class=\"maskContent\">\r\n        <view class=\"noticeTitle\">报名须知</view>\r\n        <view class=\"noticeView\">\r\n          <view \r\n            v-for=\"(notice, index) in noticeList\" \r\n            :key=\"index\"\r\n            class=\"noticeItem\"\r\n          >\r\n            <view class=\"itemTitle\">{{ notice.title }}</view>\r\n            <view \r\n              v-for=\"(item, itemIndex) in notice.view\" \r\n              :key=\"itemIndex\"\r\n              class=\"itemContent\"\r\n            >\r\n              {{ item }}\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view \r\n          :class=\"['agreeBtn', { 'gray': isShowGray }]\"\r\n          @tap=\"agree\"\r\n        >\r\n          {{ readText }}\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getImages, myRequest } from '../../api/api.js'\r\nimport config from '../../config.js'\r\nimport Utils from '../../utils/index.js'\r\n\r\nexport default {\r\n  name: 'CurriculumIndex',\r\n  components: {\r\n    MyHeader: () => import('../../components/my-header/my-header.vue')\r\n  },\r\n  data() {\r\n    return {\r\n      config,\r\n      userInfo: {\r\n        avatar: null,\r\n        nickName: null,\r\n        sex: null,\r\n        phonenumber: null\r\n      },\r\n      isShowMask: true,\r\n      noticeList: [\r\n        {\r\n          title: \"一、报名方式\",\r\n          view: [\r\n            \"1、本次公益培训课程仅接受微信报名，名额有限，先到先得，额满为止\",\r\n            \"2、课程咨询热线：0755-27880235。\",\r\n            \"3、公益课开始上课后自动关闭报名入口。\",\r\n            \"4、学员可通过个人中心-课程预约-查看凭证，管理查询个人预约信息。\"\r\n          ]\r\n        },\r\n        {\r\n          title: \"二、温馨提示\",\r\n          view: [\r\n            \"1、学员必须以本人真实信息报名，如现场确认时发现报名信息不符合则取消资格\",\r\n            \"2、课程期间，请学员严格遵守上课时间，为保证教学质量迟到超过10分钟则不能进入教室学习。\"\r\n          ]\r\n        }\r\n      ],\r\n      selected: [],\r\n      times: null,\r\n      readText: \"确定已阅读（5s）\",\r\n      readTime: 5,\r\n      timeSection: {\r\n        start: \"\",\r\n        end: \"\"\r\n      },\r\n      curriculumList: [],\r\n      isShowGray: false,\r\n      fulldate: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 兼容原有的config计算属性\r\n    configComputed() {\r\n      return config\r\n    }\r\n  },\r\n  created() {\r\n    this.getTimeSection()\r\n    this.showMask()\r\n  },\r\n  onShow() {\r\n    const token = uni.getStorageSync('token')\r\n    if (token) {\r\n      this.getDayInfo()\r\n    } else {\r\n      this.showLoginModal()\r\n    }\r\n  },\r\n  methods: {\r\n    getImages,\r\n    \r\n    // 获取时间区间\r\n    getTimeSection() {\r\n      const now = new Date()\r\n      const start = now.getFullYear() + \"-\" + (now.getMonth() + 1) + \"-\" + now.getDate()\r\n      const endDate = new Date(now.getTime() + 6 * 24 * 60 * 60 * 1000) // 6天后\r\n      const end = endDate.getFullYear() + \"-\" + (endDate.getMonth() + 1) + \"-\" + endDate.getDate()\r\n      \r\n      this.timeSection.start = start\r\n      this.timeSection.end = end\r\n    },\r\n    \r\n    // 同意须知\r\n    agree() {\r\n      if (this.readTime === 1) {\r\n        setTimeout(() => {\r\n          this.isShowMask = false\r\n        }, 300)\r\n      }\r\n    },\r\n    \r\n    // 显示须知弹窗\r\n    showMask() {\r\n      // #ifdef MP-WEIXIN\r\n      const accountInfo = uni.getAccountInfoSync()\r\n      this.isShowMask = accountInfo.miniProgram.envVersion !== 'develop'\r\n      // #endif\r\n      \r\n      // #ifndef MP-WEIXIN\r\n      this.isShowMask = true\r\n      // #endif\r\n      \r\n      this.isShowGray = true\r\n      this.readTime = 5\r\n      this.times = setInterval(() => {\r\n        if (this.readTime === 1) {\r\n          this.readText = \"确定已阅读\"\r\n          this.isShowGray = false\r\n          clearInterval(this.times)\r\n        } else {\r\n          this.readTime--\r\n          this.readText = `确定已阅读（${this.readTime}s）`\r\n        }\r\n      }, 1000)\r\n    },\r\n    \r\n    // 选择课程\r\n    chooseCourse(courseId) {\r\n      uni.navigateTo({\r\n        url: `/pages_app/curriculum/choosecurriculum?id=${courseId}`\r\n      })\r\n    },\r\n    \r\n    // 显示登录弹窗\r\n    showLoginModal() {\r\n      uni.showModal({\r\n        title: \"温馨提示\",\r\n        content: \"授权微信登录后才能正常使用小程序功能\",\r\n        success: (res) => {\r\n          if (res.cancel) {\r\n            uni.showToast({\r\n              title: \"您拒绝了请求，不能正常使用小程序\",\r\n              icon: \"error\",\r\n              duration: 2000\r\n            })\r\n          } else {\r\n            this.performLogin()\r\n          }\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 执行登录\r\n    performLogin() {\r\n      // #ifdef MP-WEIXIN\r\n      uni.login({\r\n        provider: \"weixin\",\r\n        success: (res) => {\r\n          const requestData = {\r\n            data: { code: res.code },\r\n            url: `/wx/user/${config.appId}/login`,\r\n            method: \"get\"\r\n          }\r\n          \r\n          myRequest(requestData).then((response) => {\r\n            uni.hideLoading()\r\n            uni.showToast({ title: \"登录成功\" })\r\n            \r\n            const user = response.data.data.user\r\n            uni.setStorageSync(\"token\", response.data.data.token)\r\n            uni.setStorageSync(\"userInfo\", user)\r\n            \r\n            this.userInfo = user || {\r\n              avatar: null,\r\n              nickName: null,\r\n              sex: null,\r\n              phonenumber: null\r\n            }\r\n            \r\n            this.getDayInfo()\r\n          }).catch((error) => {\r\n            console.log(\"登录异常:\", error)\r\n            uni.showToast({\r\n              title: \"登录异常:\" + error.errMsg,\r\n              icon: \"error\",\r\n              duration: 5000\r\n            })\r\n          })\r\n        }\r\n      })\r\n      // #endif\r\n      \r\n      // #ifndef MP-WEIXIN\r\n      // 非微信小程序平台的登录处理\r\n      uni.navigateTo({\r\n        url: '/pages_app/login/index'\r\n      })\r\n      // #endif\r\n    },\r\n    \r\n    // 获取场馆开放信息\r\n    async getDayInfo() {\r\n      try {\r\n        const response = await myRequest({\r\n          url: \"/auth/venue/getVenueInfo\"\r\n        })\r\n        \r\n        if (response.data.code === 200) {\r\n          const data = response.data.data\r\n          data.forEach(item => {\r\n            const date = Utils.changeTime(item.day, true)\r\n            if (item.isClose === \"0\") {\r\n              this.selected.push({\r\n                date: date,\r\n                info: \"闭馆\"\r\n              })\r\n            }\r\n          })\r\n          \r\n          // 找到第一个开放日\r\n          for (let i = 0; i < data.length; i++) {\r\n            if (data[i].isClose === \"1\") {\r\n              this.timeSection.start = Utils.changeTime(data[i].day, true)\r\n              break\r\n            }\r\n          }\r\n          \r\n          if (this.timeSection.start) {\r\n            this.getSessionDetail({\r\n              fulldate: this.fulldate || this.timeSection.start\r\n            })\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取场馆信息失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 获取课程详情\r\n    getSessionDetail(event) {\r\n      myRequest({\r\n        url: \"/web/session/getSessionDetail\",\r\n        method: \"post\",\r\n        data: {\r\n          date: event.fulldate\r\n        }\r\n      }).then((response) => {\r\n        if (response.data.code === 200) {\r\n          this.fulldate = event.fulldate\r\n          const data = response.data.data\r\n          const now = new Date()\r\n          \r\n          data.forEach(item => {\r\n            item.isShow = new Date(item.courseStartTime) > now\r\n            item.courseEndTime = Utils.changeTime(item.courseEndTime)\r\n            item.courseStartTime = Utils.changeTime(item.courseStartTime)\r\n          })\r\n          \r\n          this.curriculumList = data.filter(item => item.isShow)\r\n        }\r\n      }).catch((error) => {\r\n        console.error('获取课程详情失败:', error)\r\n        uni.showToast({\r\n          title: '获取课程信息失败',\r\n          icon: 'error'\r\n        })\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style sco\r\nped>\r\n.curriculum {\r\n  background-color: #f5f5f5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.curriculum_order_box {\r\n  padding-top: 20rpx;\r\n}\r\n\r\n.calendar_content {\r\n  background-color: #ffffff;\r\n  margin: 0 30rpx 20rpx;\r\n  border-radius: 20rpx;\r\n  overflow: hidden;\r\n}\r\n\r\n.calendar {\r\n  padding: 20rpx;\r\n}\r\n\r\n.curriculum_list_content {\r\n  background-color: #ffffff;\r\n  margin: 0 30rpx;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n}\r\n\r\n.curriculum_title {\r\n  width: 100%;\r\n  height: 80rpx;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.curriculum_list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30rpx;\r\n}\r\n\r\n.curriculum_item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  background-color: #f8f9fa;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.curriculum_item:active {\r\n  background-color: #e9ecef;\r\n}\r\n\r\n.curriculum_item.disNone {\r\n  display: none;\r\n}\r\n\r\n.itemLeft {\r\n  display: flex;\r\n  flex: 1;\r\n  align-items: center;\r\n}\r\n\r\n.cover {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 15rpx;\r\n  margin-right: 30rpx;\r\n  object-fit: cover;\r\n}\r\n\r\n.curriculumInfo {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  gap: 8rpx;\r\n}\r\n\r\n.curriculumName {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  line-height: 1.4;\r\n}\r\n\r\n.curriculumType,\r\n.curriculumTime,\r\n.curriculumPlace {\r\n  font-size: 26rpx;\r\n  color: #666666;\r\n  line-height: 1.3;\r\n}\r\n\r\n.itemRight {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-left: 20rpx;\r\n}\r\n\r\n.ticketType {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.n_text {\r\n  font-size: 22rpx;\r\n  color: #999999;\r\n}\r\n\r\n.t_text {\r\n  font-size: 28rpx;\r\n  color: #ff6b6b;\r\n  font-weight: 600;\r\n}\r\n\r\n.order_button {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  padding: 15rpx 25rpx;\r\n  border-radius: 25rpx;\r\n  font-size: 24rpx;\r\n  font-weight: 500;\r\n  text-align: center;\r\n  min-width: 120rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.order_button.gray {\r\n  background: #cccccc;\r\n  color: #999999;\r\n}\r\n\r\n.curriculum_tip {\r\n  text-align: center;\r\n  color: #999999;\r\n  font-size: 28rpx;\r\n  padding: 100rpx 0;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.maskContent {\r\n  background-color: #ffffff;\r\n  width: 600rpx;\r\n  max-height: 80vh;\r\n  border-radius: 20rpx;\r\n  padding: 40rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.noticeTitle {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.noticeView {\r\n  flex: 1;\r\n  max-height: 500rpx;\r\n  overflow-y: auto;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.noticeItem {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.itemTitle {\r\n  font-size: 30rpx;\r\n  font-weight: 600;\r\n  color: #333333;\r\n  margin-bottom: 15rpx;\r\n}\r\n\r\n.itemContent {\r\n  font-size: 26rpx;\r\n  color: #666666;\r\n  line-height: 1.6;\r\n  margin-bottom: 10rpx;\r\n  padding-left: 20rpx;\r\n}\r\n\r\n.agreeBtn {\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  color: #ffffff;\r\n  padding: 25rpx;\r\n  border-radius: 15rpx;\r\n  text-align: center;\r\n  font-size: 30rpx;\r\n  font-weight: 500;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.agreeBtn.gray {\r\n  background: #cccccc;\r\n  color: #999999;\r\n}\r\n\r\n.agreeBtn:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media screen and (max-width: 750rpx) {\r\n  .curriculum_item {\r\n    padding: 25rpx;\r\n  }\r\n  \r\n  .cover {\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n    margin-right: 25rpx;\r\n  }\r\n  \r\n  .curriculumName {\r\n    font-size: 30rpx;\r\n  }\r\n  \r\n  .curriculumType,\r\n  .curriculumTime,\r\n  .curriculumPlace {\r\n    font-size: 24rpx;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/my_project/kejiguan/uni-app-new/pages_app/curriculum/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["config", "uni", "getImages", "myRequest", "Utils"], "mappings": ";;;;;;AAoHA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,YAAY;AAAA,IACV,UAAU,MAAa;AAAA,EACxB;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,QAAAA,OAAM;AAAA,MACN,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,KAAK;AAAA,QACL,aAAa;AAAA,MACd;AAAA,MACD,YAAY;AAAA,MACZ,YAAY;AAAA,QACV;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACD;AAAA,QACD;AAAA,UACE,OAAO;AAAA,UACP,MAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACD;AAAA,MACD,UAAU,CAAE;AAAA,MACZ,OAAO;AAAA,MACP,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACN;AAAA,MACD,gBAAgB,CAAE;AAAA,MAClB,YAAY;AAAA,MACZ,UAAU;AAAA,IACZ;AAAA,EACD;AAAA,EACD,UAAU;AAAA;AAAA,IAER,iBAAiB;AACf,aAAOA,OAAK;AAAA,IACd;AAAA,EACD;AAAA,EACD,UAAU;AACR,SAAK,eAAe;AACpB,SAAK,SAAS;AAAA,EACf;AAAA,EACD,SAAS;AACP,UAAM,QAAQC,cAAAA,MAAI,eAAe,OAAO;AACxC,QAAI,OAAO;AACT,WAAK,WAAW;AAAA,WACX;AACL,WAAK,eAAe;AAAA,IACtB;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,WAAAC,QAAS;AAAA;AAAA,IAGT,iBAAiB;AACf,YAAM,MAAM,oBAAI,KAAK;AACrB,YAAM,QAAQ,IAAI,YAAW,IAAK,OAAO,IAAI,SAAQ,IAAK,KAAK,MAAM,IAAI,QAAQ;AACjF,YAAM,UAAU,IAAI,KAAK,IAAI,QAAO,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI;AAChE,YAAM,MAAM,QAAQ,YAAW,IAAK,OAAO,QAAQ,SAAQ,IAAK,KAAK,MAAM,QAAQ,QAAQ;AAE3F,WAAK,YAAY,QAAQ;AACzB,WAAK,YAAY,MAAM;AAAA,IACxB;AAAA;AAAA,IAGD,QAAQ;AACN,UAAI,KAAK,aAAa,GAAG;AACvB,mBAAW,MAAM;AACf,eAAK,aAAa;AAAA,QACnB,GAAE,GAAG;AAAA,MACR;AAAA,IACD;AAAA;AAAA,IAGD,WAAW;AAET,YAAM,cAAcD,cAAG,MAAC,mBAAmB;AAC3C,WAAK,aAAa,YAAY,YAAY,eAAe;AAOzD,WAAK,aAAa;AAClB,WAAK,WAAW;AAChB,WAAK,QAAQ,YAAY,MAAM;AAC7B,YAAI,KAAK,aAAa,GAAG;AACvB,eAAK,WAAW;AAChB,eAAK,aAAa;AAClB,wBAAc,KAAK,KAAK;AAAA,eACnB;AACL,eAAK;AACL,eAAK,WAAW,SAAS,KAAK,QAAQ;AAAA,QACxC;AAAA,MACD,GAAE,GAAI;AAAA,IACR;AAAA;AAAA,IAGD,aAAa,UAAU;AACrBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,6CAA6C,QAAQ;AAAA,OAC3D;AAAA,IACF;AAAA;AAAA,IAGD,iBAAiB;AACfA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,QAAQ;AACdA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AAAA,iBACI;AACL,iBAAK,aAAa;AAAA,UACpB;AAAA,QACF;AAAA,OACD;AAAA,IACF;AAAA;AAAA,IAGD,eAAe;AAEbA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,SAAS,CAAC,QAAQ;AAChB,gBAAM,cAAc;AAAA,YAClB,MAAM,EAAE,MAAM,IAAI,KAAM;AAAA,YACxB,KAAK,YAAYD,cAAO,KAAK;AAAA,YAC7B,QAAQ;AAAA,UACV;AAEAG,kBAAAA,UAAU,WAAW,EAAE,KAAK,CAAC,aAAa;AACxCF,0BAAAA,MAAI,YAAY;AAChBA,0BAAAA,MAAI,UAAU,EAAE,OAAO,OAAK,CAAG;AAE/B,kBAAM,OAAO,SAAS,KAAK,KAAK;AAChCA,0BAAG,MAAC,eAAe,SAAS,SAAS,KAAK,KAAK,KAAK;AACpDA,gCAAI,eAAe,YAAY,IAAI;AAEnC,iBAAK,WAAW,QAAQ;AAAA,cACtB,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAEA,iBAAK,WAAW;AAAA,UAClB,CAAC,EAAE,MAAM,CAAC,UAAU;AAClBA,0BAAAA,MAAY,MAAA,OAAA,yCAAA,SAAS,KAAK;AAC1BA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,UAAU,MAAM;AAAA,cACvB,MAAM;AAAA,cACN,UAAU;AAAA,aACX;AAAA,WACF;AAAA,QACH;AAAA,OACD;AAAA,IASF;AAAA;AAAA,IAGD,MAAM,aAAa;AACjB,UAAI;AACF,cAAM,WAAW,MAAME,kBAAU;AAAA,UAC/B,KAAK;AAAA,SACN;AAED,YAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,gBAAM,OAAO,SAAS,KAAK;AAC3B,eAAK,QAAQ,UAAQ;AACnB,kBAAM,OAAOC,YAAAA,MAAM,WAAW,KAAK,KAAK,IAAI;AAC5C,gBAAI,KAAK,YAAY,KAAK;AACxB,mBAAK,SAAS,KAAK;AAAA,gBACjB;AAAA,gBACA,MAAM;AAAA,eACP;AAAA,YACH;AAAA,WACD;AAGD,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,KAAK,CAAC,EAAE,YAAY,KAAK;AAC3B,mBAAK,YAAY,QAAQA,kBAAM,WAAW,KAAK,CAAC,EAAE,KAAK,IAAI;AAC3D;AAAA,YACF;AAAA,UACF;AAEA,cAAI,KAAK,YAAY,OAAO;AAC1B,iBAAK,iBAAiB;AAAA,cACpB,UAAU,KAAK,YAAY,KAAK,YAAY;AAAA,aAC7C;AAAA,UACH;AAAA,QACF;AAAA,MACA,SAAO,OAAO;AACdH,sBAAAA,MAAc,MAAA,SAAA,yCAAA,aAAa,KAAK;AAAA,MAClC;AAAA,IACD;AAAA;AAAA,IAGD,iBAAiB,OAAO;AACtBE,wBAAU;AAAA,QACR,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,UACJ,MAAM,MAAM;AAAA,QACd;AAAA,MACF,CAAC,EAAE,KAAK,CAAC,aAAa;AACpB,YAAI,SAAS,KAAK,SAAS,KAAK;AAC9B,eAAK,WAAW,MAAM;AACtB,gBAAM,OAAO,SAAS,KAAK;AAC3B,gBAAM,MAAM,oBAAI,KAAK;AAErB,eAAK,QAAQ,UAAQ;AACnB,iBAAK,SAAS,IAAI,KAAK,KAAK,eAAe,IAAI;AAC/C,iBAAK,gBAAgBC,YAAAA,MAAM,WAAW,KAAK,aAAa;AACxD,iBAAK,kBAAkBA,YAAAA,MAAM,WAAW,KAAK,eAAe;AAAA,WAC7D;AAED,eAAK,iBAAiB,KAAK,OAAO,UAAQ,KAAK,MAAM;AAAA,QACvD;AAAA,MACF,CAAC,EAAE,MAAM,CAAC,UAAU;AAClBH,sBAAAA,MAAc,MAAA,SAAA,yCAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,SACP;AAAA,OACF;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClXA,GAAG,WAAW,eAAe;"}