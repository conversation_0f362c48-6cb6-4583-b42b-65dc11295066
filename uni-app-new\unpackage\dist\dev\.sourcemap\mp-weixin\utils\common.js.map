{"version": 3, "file": "common.js", "sources": ["utils/common.js"], "sourcesContent": ["/**\r\n * 通用工具函数\r\n * 适配uni-app环境\r\n */\r\n\r\n/**\r\n * 转换参数为URL查询字符串\r\n * @param {Object} params 参数对象\r\n * @returns {string} 查询字符串\r\n */\r\nexport function tansParams(params) {\r\n  if (!params || typeof params !== 'object') {\r\n    return ''\r\n  }\r\n\r\n  let result = ''\r\n\r\n  for (const key in params) {\r\n    if (params.hasOwnProperty(key)) {\r\n      const value = params[key]\r\n      const encodedKey = encodeURIComponent(key) + '='\r\n\r\n      if (value !== null && value !== '' && value !== undefined) {\r\n        if (typeof value === 'object') {\r\n          // 处理对象类型的参数\r\n          for (const subKey in value) {\r\n            if (value.hasOwnProperty(subKey)) {\r\n              const subValue = value[subKey]\r\n              if (subValue !== null && subValue !== '' && subValue !== undefined) {\r\n                result += encodeURIComponent(key + '[' + subKey + ']') + '=' + encodeURIComponent(subValue) + '&'\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          result += encodedKey + encodeURIComponent(value) + '&'\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return result\r\n}\r\n\r\n/**\r\n * 显示提示信息\r\n * @param {string} message 提示信息\r\n * @param {string} icon 图标类型\r\n */\r\nexport function toast(message, icon = 'none') {\r\n  uni.showToast({\r\n    icon: icon,\r\n    title: message,\r\n    duration: 2000\r\n  })\r\n}\r\n\r\n/**\r\n * 显示加载提示\r\n * @param {string} title 加载提示文字\r\n */\r\nexport function showLoading(title = '加载中...') {\r\n  uni.showLoading({\r\n    title: title,\r\n    mask: true\r\n  })\r\n}\r\n\r\n/**\r\n * 隐藏加载提示\r\n */\r\nexport function hideLoading() {\r\n  uni.hideLoading()\r\n}\r\n\r\n/**\r\n * 显示确认对话框\r\n * @param {string} content 对话框内容\r\n * @param {string} title 对话框标题\r\n * @returns {Promise} Promise对象\r\n */\r\nexport function showConfirm(content, title = '提示') {\r\n  return new Promise((resolve, reject) => {\r\n    uni.showModal({\r\n      title: title,\r\n      content: content,\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          resolve(true)\r\n        } else {\r\n          resolve(false)\r\n        }\r\n      },\r\n      fail: (error) => {\r\n        reject(error)\r\n      }\r\n    })\r\n  })\r\n}\r\n\r\n/**\r\n * 格式化日期\r\n * @param {Date|string} date 日期对象或字符串\r\n * @param {string} format 格式化模板\r\n * @returns {string} 格式化后的日期字符串\r\n */\r\nexport function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {\r\n  if (!date) return ''\r\n\r\n  const d = new Date(date)\r\n  if (isNaN(d.getTime())) return ''\r\n\r\n  const year = d.getFullYear()\r\n  const month = String(d.getMonth() + 1).padStart(2, '0')\r\n  const day = String(d.getDate()).padStart(2, '0')\r\n  const hours = String(d.getHours()).padStart(2, '0')\r\n  const minutes = String(d.getMinutes()).padStart(2, '0')\r\n  const seconds = String(d.getSeconds()).padStart(2, '0')\r\n\r\n  return format\r\n    .replace('YYYY', year)\r\n    .replace('MM', month)\r\n    .replace('DD', day)\r\n    .replace('HH', hours)\r\n    .replace('mm', minutes)\r\n    .replace('ss', seconds)\r\n}\r\n\r\n/**\r\n * 格式化时间\r\n * @param {Date|string} time 时间对象或字符串\r\n * @returns {string} 格式化后的时间字符串 (HH:mm)\r\n */\r\nexport function formatTime(time) {\r\n  if (!time) return ''\r\n\r\n  try {\r\n    const date = new Date(time.replace(/-/g, '/'))\r\n    const hours = date.getHours().toString().padStart(2, '0')\r\n    const minutes = date.getMinutes().toString().padStart(2, '0')\r\n    return `${hours}:${minutes}`\r\n  } catch (error) {\r\n    console.warn('格式化时间失败:', error)\r\n    return time\r\n  }\r\n}\r\n\r\n/**\r\n * 防抖函数\r\n * @param {Function} func 要防抖的函数\r\n * @param {number} delay 延迟时间\r\n * @returns {Function} 防抖后的函数\r\n */\r\nexport function debounce(func, delay = 300) {\r\n  let timer = null\r\n  return function(...args) {\r\n    if (timer) clearTimeout(timer)\r\n    timer = setTimeout(() => {\r\n      func.apply(this, args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\n/**\r\n * 节流函数\r\n * @param {Function} func 要节流的函数\r\n * @param {number} delay 延迟时间\r\n * @returns {Function} 节流后的函数\r\n */\r\nexport function throttle(func, delay = 300) {\r\n  let timer = null\r\n  return function(...args) {\r\n    if (!timer) {\r\n      timer = setTimeout(() => {\r\n        func.apply(this, args)\r\n        timer = null\r\n      }, delay)\r\n    }\r\n  }\r\n}\r\n\r\n// 默认导出\r\nexport default {\r\n  tansParams,\r\n  toast,\r\n  showLoading,\r\n  hideLoading,\r\n  showConfirm,\r\n  formatDate,\r\n  formatTime,\r\n  debounce,\r\n  throttle\r\n}"], "names": ["uni"], "mappings": ";;AAUO,SAAS,WAAW,QAAQ;AACjC,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,WAAO;AAAA,EACR;AAED,MAAI,SAAS;AAEb,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,YAAM,QAAQ,OAAO,GAAG;AACxB,YAAM,aAAa,mBAAmB,GAAG,IAAI;AAE7C,UAAI,UAAU,QAAQ,UAAU,MAAM,UAAU,QAAW;AACzD,YAAI,OAAO,UAAU,UAAU;AAE7B,qBAAW,UAAU,OAAO;AAC1B,gBAAI,MAAM,eAAe,MAAM,GAAG;AAChC,oBAAM,WAAW,MAAM,MAAM;AAC7B,kBAAI,aAAa,QAAQ,aAAa,MAAM,aAAa,QAAW;AAClE,0BAAU,mBAAmB,MAAM,MAAM,SAAS,GAAG,IAAI,MAAM,mBAAmB,QAAQ,IAAI;AAAA,cAC/F;AAAA,YACF;AAAA,UACF;AAAA,QACX,OAAe;AACL,oBAAU,aAAa,mBAAmB,KAAK,IAAI;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAED,SAAO;AACT;AAOO,SAAS,MAAM,SAAS,OAAO,QAAQ;AAC5CA,gBAAAA,MAAI,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,IACP,UAAU;AAAA,EACd,CAAG;AACH;AA8EO,SAAS,WAAW,MAAM;AAC/B,MAAI,CAAC;AAAM,WAAO;AAElB,MAAI;AACF,UAAM,OAAO,IAAI,KAAK,KAAK,QAAQ,MAAM,GAAG,CAAC;AAC7C,UAAM,QAAQ,KAAK,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACxD,UAAM,UAAU,KAAK,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC5D,WAAO,GAAG,KAAK,IAAI,OAAO;AAAA,EAC3B,SAAQ,OAAO;AACdA,kBAAAA,MAAA,MAAA,QAAA,0BAAa,YAAY,KAAK;AAC9B,WAAO;AAAA,EACR;AACH;;;;"}