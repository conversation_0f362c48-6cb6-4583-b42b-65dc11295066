/**
 * 错误码配置
 * 适配uni-app环境
 */

export const errorCode = {
  200: "操作成功",
  400: "请求参数错误",
  401: "认证失败，无法访问系统资源",
  403: "当前操作没有权限",
  404: "访问资源不存在",
  500: "服务器内部错误",
  502: "网关错误",
  503: "服务不可用",
  504: "网关超时",
  default: "系统未知错误，请反馈给管理员"
}

/**
 * 获取错误信息
 * @param {number} code 错误码
 * @returns {string} 错误信息
 */
export function getErrorMessage(code) {
  return errorCode[code] || errorCode.default
}

/**
 * 检查是否为成功状态码
 * @param {number} code 状态码
 * @returns {boolean} 是否成功
 */
export function isSuccess(code) {
  return code === 200
}

/**
 * 检查是否为客户端错误
 * @param {number} code 状态码
 * @returns {boolean} 是否为客户端错误
 */
export function isClientError(code) {
  return code >= 400 && code < 500
}

/**
 * 检查是否为服务器错误
 * @param {number} code 状态码
 * @returns {boolean} 是否为服务器错误
 */
export function isServerError(code) {
  return code >= 500 && code < 600
}

// 默认导出
export default {
  errorCode,
  getErrorMessage,
  isSuccess,
  isClientError,
  isServerError
}