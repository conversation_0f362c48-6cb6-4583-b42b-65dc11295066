<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_type')}:</label>
        <div class="col-xs-12 col-sm-8">

            <select id="c-film_type" name="row[film_type]" class="form-control" data-rule="required">
                <option value="1" <?= isset($row['film_type']) && $row['film_type']==1 ? 'selected' : '' ?>>
                    {:__('Film_type_1')}
                </option>
                <option value="2" <?= isset($row['film_type']) && $row['film_type']==2 ? 'selected' : '' ?>>
                    {:__('Film_type_2')}
                </option>
              </select>
              
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-film_name" data-rule="required" class="form-control" name="row[film_name]" type="text" value="{$row.film_name}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('File_introduce')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-file_introduce" data-rule="required" class="form-control " rows="5" name="row[file_introduce]" cols="50">{$row.file_introduce}</textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Film_cover')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-film_cover" class="form-control" size="50" name="row[film_cover]" type="text" value="{$row.film_cover}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-film_cover" class="btn btn-danger plupload"
                            data-input-id="c-film_cover" data-mimetype="image/*" data-multiple="false"
                            data-preview-id="p-film_cover">
                            <i class="fa fa-upload"></i> 上传
                        </button></span>
                </div>
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" class="btn btn-primary fachoose" data-input-id="c-film_cover"
                            data-mimetype="image/*" data-multiple="false">
                            <i class="fa fa-list"></i> 选择
                        </button></span>
                </div>
            </div>
            <ul class="row list-inline plupload-preview" id="p-film_cover"></ul>
            <div data-v-ce13e118="" slot="tip" style="
    line-height: 1.2;
    font-size: 12px;
    color: #606266;
    margin-top: 7px;
"> 请上传 大小不超过 <b data-v-ce13e118="" style="color: rgb(245, 108, 108);">2MB</b> 格式为 <b data-v-ce13e118=""
                    style="color: rgb(245, 108, 108);">png/jpg/jpeg</b> 的文件 </div>
            <div data-v-ce13e118="" slot="tip" style="
    line-height: 1.2;
    font-size: 12px;
    color: #606266;
    margin-top: 7px;
"> 请上传 比例为 <b data-v-ce13e118="" style="color: rgb(245, 108, 108);">3:4</b> 尺寸如 <b data-v-ce13e118=""
                    style="color: rgb(245, 108, 108);">720x960、1440x1920、2160x2880</b> 的图片 </div>
        </div>
    </div>



    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
<script>
    console.log("当前film_type: ", "{$row.film_type}");
</script>
