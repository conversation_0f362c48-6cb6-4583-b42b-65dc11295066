.uni-noticebar.data-v-05e45343 {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    padding: 10px 12px;
    width: 100%
}

.uni-noticebar-close.data-v-05e45343 {
    margin-left: 8px;
    margin-right: 5px
}

.uni-noticebar-icon.data-v-05e45343 {
    margin-right: 5px
}

.uni-noticebar__content-wrapper.data-v-05e45343 {
    flex: 1;
    flex-direction: column;
    overflow: hidden
}

.uni-noticebar__content-wrapper--single.data-v-05e45343 {
    line-height: 18px
}

.uni-noticebar__content-wrapper--scrollable.data-v-05e45343,.uni-noticebar__content-wrapper--single.data-v-05e45343 {
    flex-direction: row
}

.uni-noticebar__content-wrapper--scrollable.data-v-05e45343 {
    position: relative
}

.uni-noticebar__content--scrollable.data-v-05e45343 {
    display: block;
    flex: 1;
    overflow: hidden
}

.uni-noticebar__content--single.data-v-05e45343 {
    display: flex;
    flex: none;
    justify-content: center;
    width: 100%
}

.uni-noticebar__content-text.data-v-05e45343 {
    font-size: 14px;
    line-height: 18px;
    word-break: break-all
}

.uni-noticebar__content-text--single.data-v-05e45343 {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.uni-noticebar__content-text--scrollable.data-v-05e45343 {
    animation: notice-05e45343 10s linear 0s infinite both;
    animation-play-state: paused;
    display: block;
    height: 18px;
    line-height: 18px;
    padding-left: 100%;
    position: absolute;
    white-space: nowrap
}

.uni-noticebar__more.data-v-05e45343 {
    align-items: center;
    display: inline-flex;
    flex-direction: row;
    flex-wrap: nowrap;
    padding-left: 5px
}

@keyframes notice-05e45343 {
    to {
        transform: translate3d(-100%,0,0)
    }
}