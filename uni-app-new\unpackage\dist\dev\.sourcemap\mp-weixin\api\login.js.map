{"version": 3, "file": "login.js", "sources": ["api/login.js"], "sourcesContent": ["import { request } from '../utils/request.js'\r\n\r\n// 获取验证码图片\r\nexport function getCodeImg() {\r\n  return request({\r\n    url: '/captchaImage',\r\n    headers: {\r\n      isToken: false\r\n    },\r\n    method: 'GET',\r\n    timeout: 20000\r\n  })\r\n}\r\n\r\n// 获取用户信息\r\nexport function getInfo() {\r\n  return request({\r\n    url: '/getInfo',\r\n    method: 'GET'\r\n  })\r\n}\r\n\r\n// 用户登录\r\nexport function login(tenantId, username, password, code, uuid) {\r\n  const data = {\r\n    tenantId,\r\n    username,\r\n    password,\r\n    code,\r\n    uuid\r\n  }\r\n  \r\n  return request({\r\n    url: '/login',\r\n    headers: {\r\n      isToken: false,\r\n      isEncrypt: false\r\n    },\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 微信登录\r\nexport function wxLogin(appId, code, openid) {\r\n  const data = {\r\n    code,\r\n    openid\r\n  }\r\n  \r\n  return request({\r\n    url: `/wx/user/${appId}/login`,\r\n    headers: {\r\n      isToken: false,\r\n      isEncrypt: false\r\n    },\r\n    method: 'POST',\r\n    data\r\n  })\r\n}\r\n\r\n// 用户登出\r\nexport function logout() {\r\n  return request({\r\n    url: '/logout',\r\n    method: 'POST'\r\n  })\r\n}\r\n\r\n// 用户注册\r\nexport function register(userData) {\r\n  return request({\r\n    url: '/register',\r\n    headers: {\r\n      isToken: false,\r\n      isEncrypt: false\r\n    },\r\n    method: 'POST',\r\n    data: userData\r\n  })\r\n}\r\n\r\n// 手机号注册\r\nexport function registerWithPhone(userData) {\r\n  return request({\r\n    url: '/wx/user/register',\r\n    headers: {\r\n      isToken: false,\r\n      isEncrypt: false\r\n    },\r\n    method: 'POST',\r\n    data: userData\r\n  })\r\n}\r\n\r\n// 刷新token\r\nexport function refreshToken() {\r\n  return request({\r\n    url: '/refresh',\r\n    method: 'POST'\r\n  })\r\n}\r\n\r\n// 修改密码\r\nexport function changePassword(oldPassword, newPassword) {\r\n  const data = {\r\n    oldPassword,\r\n    newPassword\r\n  }\r\n  \r\n  return request({\r\n    url: '/user/changePassword',\r\n    method: 'PUT',\r\n    data\r\n  })\r\n}\r\n\r\n// 忘记密码\r\nexport function forgotPassword(phone, code, newPassword) {\r\n  const data = {\r\n    phone,\r\n    code,\r\n    newPassword\r\n  }\r\n  \r\n  return request({\r\n    url: '/user/forgotPassword',\r\n    headers: {\r\n      isToken: false\r\n    },\r\n    method: 'POST',\r\n    data\r\n  })\r\n}"], "names": ["request"], "mappings": ";;AAGO,SAAS,aAAa;AAC3B,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,MACP,SAAS;AAAA,IACV;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,EACb,CAAG;AACH;AAGO,SAAS,UAAU;AACxB,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ,CAAG;AACH;AAGO,SAAS,MAAM,UAAU,UAAU,UAAU,MAAM,MAAM;AAC9D,QAAM,OAAO;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAED,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACZ;AAAA,IACD,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAqBO,SAAS,SAAS;AACvB,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ,CAAG;AACH;AAgBO,SAAS,kBAAkB,UAAU;AAC1C,SAAOA,sBAAQ;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,MACP,SAAS;AAAA,MACT,WAAW;AAAA,IACZ;AAAA,IACD,QAAQ;AAAA,IACR,MAAM;AAAA,EACV,CAAG;AACH;;;;;;"}